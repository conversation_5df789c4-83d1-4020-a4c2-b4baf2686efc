import 'package:freezed_annotation/freezed_annotation.dart';
import 'user.dart';
import 'post.dart';

part 'reading_session.freezed.dart';
part 'reading_session.g.dart';

@freezed
class ReadingSession with _$ReadingSession {
  const factory ReadingSession({
    required int id,
    required User user,
    required Post post,
    @J<PERSON><PERSON><PERSON>(name: 'start_time') required DateTime startTime,
    @<PERSON>son<PERSON><PERSON>(name: 'end_time') DateTime? endTime,
    @<PERSON>son<PERSON>ey(name: 'progress_percentage') @Default(0.0) double progressPercentage,
    @<PERSON>son<PERSON><PERSON>(name: 'reading_speed_wpm') int? readingSpeedWpm,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'session_duration') @Default(0) int sessionDuration,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'scroll_depth') @Default(0.0) double scrollDepth,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_completed') @Default(false) bool isCompleted,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'device_type') @Default('unknown') String deviceType,
  }) = _ReadingSession;

  factory ReadingSession.fromJson(Map<String, dynamic> json) => _$ReadingSessionFromJson(json);
}
