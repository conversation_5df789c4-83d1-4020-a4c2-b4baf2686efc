#!/usr/bin/env python3
"""
Trendy App Data Population Script
This script populates the Django database with sample data for development and testing.
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta
import random

# Add the Django project to the Python path
import pathlib
script_dir = pathlib.Path(__file__).parent.absolute()
django_dir = script_dir / 'trendy_web_and_api' / 'trendy'
sys.path.append(str(django_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')

# Setup Django
django.setup()

# Import models after Django setup
from django.contrib.auth import get_user_model
from blog.models import Post, Category, Tag, Comment
from accounts.models import CustomUser
from gamification.models import (
    Badge, UserBadge, PointTransaction,
    PayPalReward, UserLevel, PayPalSettings
)
from monetization.models import (
    VirtualItem, PointBoostPurchase, PremiumSubscription,
    MonetizationSettings
)
from wallet.models import (
    UserWallet, WalletTransaction, WalletSettings,
    WalletDepositRequest, WalletWithdrawalRequest
)
from social.models import Follow, Notification

User = get_user_model()

class DataPopulator:
    def __init__(self):
        self.users = []
        self.posts = []
        self.categories = []
        self.tags = []
        
    def create_users(self, count=20):
        """Create sample users"""
        print(f"Creating {count} users...")
        
        # Create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True,
                'is_email_verified': True,
                'bio': 'System Administrator',
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
        self.users.append(admin_user)
        
        # Create regular users
        sample_users = [
            ('john_doe', '<EMAIL>', 'John', 'Doe', 'Tech enthusiast and blogger'),
            ('jane_smith', '<EMAIL>', 'Jane', 'Smith', 'Travel writer and photographer'),
            ('mike_wilson', '<EMAIL>', 'Mike', 'Wilson', 'Food critic and chef'),
            ('sarah_jones', '<EMAIL>', 'Sarah', 'Jones', 'Fashion and lifestyle blogger'),
            ('david_brown', '<EMAIL>', 'David', 'Brown', 'Sports journalist'),
            ('lisa_davis', '<EMAIL>', 'Lisa', 'Davis', 'Health and wellness coach'),
            ('tom_miller', '<EMAIL>', 'Tom', 'Miller', 'Movie and entertainment reviewer'),
            ('emma_garcia', '<EMAIL>', 'Emma', 'Garcia', 'Art and culture writer'),
            ('alex_rodriguez', '<EMAIL>', 'Alex', 'Rodriguez', 'Business and finance expert'),
            ('olivia_martinez', '<EMAIL>', 'Olivia', 'Martinez', 'Science and technology writer'),
        ]
        
        for i, (username, email, first_name, last_name, bio) in enumerate(sample_users):
            if i >= count - 1:  # -1 because we already created admin
                break
                
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'is_email_verified': True,
                    'bio': bio,
                }
            )
            if created:
                user.set_password('password123')
                user.save()
            self.users.append(user)
        
        print(f"✅ Created {len(self.users)} users")
        
    def create_categories(self):
        """Create blog categories"""
        print("Creating categories...")
        
        categories_data = [
            ('Technology', 'Latest tech news and reviews'),
            ('Travel', 'Travel guides and experiences'),
            ('Food', 'Recipes and restaurant reviews'),
            ('Fashion', 'Style trends and fashion tips'),
            ('Sports', 'Sports news and analysis'),
            ('Health', 'Health and wellness tips'),
            ('Entertainment', 'Movies, TV, and celebrity news'),
            ('Art', 'Art exhibitions and cultural events'),
            ('Business', 'Business news and entrepreneurship'),
            ('Science', 'Scientific discoveries and research'),
        ]
        
        for name, description in categories_data:
            category, created = Category.objects.get_or_create(
                name=name,
                defaults={'description': description}
            )
            self.categories.append(category)
        
        print(f"✅ Created {len(self.categories)} categories")
        
    def create_tags(self):
        """Create blog tags"""
        print("Creating tags...")
        
        tags_data = [
            'trending', 'popular', 'featured', 'breaking', 'exclusive',
            'review', 'tutorial', 'guide', 'tips', 'news',
            'analysis', 'opinion', 'interview', 'behind-scenes', 'viral'
        ]
        
        for tag_name in tags_data:
            tag, created = Tag.objects.get_or_create(name=tag_name)
            self.tags.append(tag)
        
        print(f"✅ Created {len(self.tags)} tags")
        
    def create_posts(self, count=50):
        """Create sample blog posts"""
        print(f"Creating {count} posts...")
        
        sample_posts = [
            ("The Future of AI Technology", "Exploring the latest developments in artificial intelligence and machine learning..."),
            ("Top 10 Travel Destinations for 2024", "Discover the most amazing places to visit this year..."),
            ("Healthy Cooking Made Simple", "Easy recipes for a healthier lifestyle..."),
            ("Fashion Trends This Season", "What's hot in fashion right now..."),
            ("Sports Update: Championship Results", "Latest results from major sporting events..."),
            ("Mental Health Awareness", "Tips for maintaining good mental health..."),
            ("Movie Review: Latest Blockbuster", "Our take on the newest Hollywood release..."),
            ("Art Gallery Opening", "Featuring contemporary artists from around the world..."),
            ("Startup Success Stories", "How these entrepreneurs built their empires..."),
            ("Climate Change Research", "New findings on environmental impact..."),
        ]
        
        for i in range(count):
            if i < len(sample_posts):
                title, content_start = sample_posts[i]
            else:
                title = f"Sample Post {i+1}"
                content_start = f"This is sample content for post {i+1}..."
            
            # Generate longer content
            content = content_start + " " + " ".join([
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                "Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.",
                "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum.",
                "Excepteur sint occaecat cupidatat non proident, sunt in culpa qui.",
            ] * 3)
            
            author = random.choice(self.users)
            category = random.choice(self.categories) if self.categories else None
            
            post, created = Post.objects.get_or_create(
                title=title,
                defaults={
                    'content': content,
                    'author': author,
                    'category': category,
                    'status': 'published',
                    'created_at': datetime.now() - timedelta(days=random.randint(0, 30))
                }
            )
            
            if created and self.tags:
                # Add random tags
                post_tags = random.sample(self.tags, k=random.randint(1, 3))
                post.tags.set(post_tags)
            
            self.posts.append(post)
        
        print(f"✅ Created {len(self.posts)} posts")
        
    def create_social_interactions(self):
        """Create likes, comments, and follows"""
        print("Creating social interactions...")
        
        # Create follows
        follow_count = 0
        for user in self.users[:10]:  # First 10 users follow others
            followers = random.sample([u for u in self.users if u != user], k=random.randint(3, 8))
            for follower in followers:
                follow, created = Follow.objects.get_or_create(
                    follower=follower,
                    following=user
                )
                if created:
                    follow_count += 1
        
        # Create likes (using ManyToMany relationship)
        like_count = 0
        for post in self.posts:
            max_likers = min(len(self.users), 15)
            num_likers = random.randint(1, max_likers)
            likers = random.sample(self.users, k=num_likers)
            for user in likers:
                if user not in post.likes.all():
                    post.likes.add(user)
                    like_count += 1
        
        # Create comments
        comment_count = 0
        sample_comments = [
            "Great post! Thanks for sharing.",
            "Very informative, learned a lot.",
            "I completely agree with your points.",
            "Interesting perspective on this topic.",
            "Could you elaborate more on this?",
            "This is exactly what I was looking for.",
            "Well written and engaging content.",
            "Thanks for the detailed explanation.",
        ]

        for post in self.posts:
            max_commenters = min(len(self.users), 8)
            num_commenters = random.randint(1, max_commenters)
            commenters = random.sample(self.users, k=num_commenters)
            for user in commenters:
                comment_text = random.choice(sample_comments)
                comment, created = Comment.objects.get_or_create(
                    post=post,
                    author=user,
                    defaults={'content': comment_text}
                )
                if created:
                    comment_count += 1
        
        print(f"✅ Created {follow_count} follows, {like_count} likes, {comment_count} comments")

    def create_wallets_and_transactions(self):
        """Create wallets and sample transactions"""
        print("Creating wallets and transactions...")

        # Create wallet settings
        settings, created = WalletSettings.objects.get_or_create(
            defaults={
                'minimum_deposit': Decimal('5.00'),
                'maximum_deposit': Decimal('500.00'),
                'minimum_withdrawal': Decimal('10.00'),
                'maximum_withdrawal': Decimal('1000.00'),
                'withdrawal_fee_percentage': Decimal('2.5'),
                'withdrawal_fee_fixed': Decimal('1.00'),
                'deposit_fee_percentage': Decimal('0.0'),
                'wallets_enabled': True,
                'deposits_enabled': True,
                'withdrawals_enabled': True,
                'require_verification': False,
                'daily_withdrawal_limit': Decimal('100.00'),
                'monthly_withdrawal_limit': Decimal('1000.00'),
            }
        )

        wallet_count = 0
        transaction_count = 0

        for user in self.users:
            # Create wallet for each user
            from wallet.services import WalletService
            wallet = WalletService.get_or_create_wallet(user)
            wallet_count += 1

            # Add some initial balance for testing
            initial_amounts = [50.00, 100.00, 25.00, 75.00, 150.00]
            initial_amount = Decimal(str(random.choice(initial_amounts)))

            # Create initial deposit transaction
            transaction = WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='credit',
                purpose='deposit',
                amount=initial_amount,
                balance_before=Decimal('0.00'),
                balance_after=initial_amount,
                status='completed',
                description=f'Initial deposit for {user.username}',
                payment_method='paypal',
                created_at=datetime.now() - timedelta(days=random.randint(1, 30))
            )
            transaction_count += 1

            # Update wallet balance
            wallet.balance = initial_amount
            wallet.save()

            # Create some random transactions
            current_balance = initial_amount
            for _ in range(random.randint(2, 8)):
                transaction_type = random.choice(['credit', 'debit'])

                if transaction_type == 'credit':
                    amount = Decimal(str(random.uniform(5.0, 50.0)))
                    purpose = random.choice(['deposit', 'reward', 'refund'])
                    new_balance = current_balance + amount
                else:
                    max_debit = min(float(current_balance), 30.0)
                    if max_debit <= 0:
                        continue
                    amount = Decimal(str(random.uniform(1.0, max_debit)))
                    purpose = random.choice(['purchase', 'subscription', 'withdrawal'])
                    new_balance = current_balance - amount

                transaction = WalletTransaction.objects.create(
                    wallet=wallet,
                    transaction_type=transaction_type,
                    purpose=purpose,
                    amount=amount,
                    balance_before=current_balance,
                    balance_after=new_balance,
                    status='completed',
                    description=f'{purpose.title()} transaction',
                    payment_method='paypal',
                    created_at=datetime.now() - timedelta(days=random.randint(0, 15))
                )
                transaction_count += 1
                current_balance = new_balance

            # Update final wallet balance
            wallet.balance = current_balance
            wallet.save()

        print(f"✅ Created {wallet_count} wallets and {transaction_count} transactions")

    def create_gamification_data(self):
        """Create gamification badges and settings"""
        print("Creating gamification data...")

        # Create PayPal settings
        settings, created = PayPalSettings.objects.get_or_create(
            defaults={
                'rewards_enabled': True,
                'minimum_payout': Decimal('5.00'),
                'maximum_monthly_payout_per_user': Decimal('100.00'),
                'require_email_verification': True,
                'minimum_account_age_days': 30,
                'minimum_activity_score': 100,
            }
        )

        # Create badges
        badges_data = [
            ('First Post', 'Write your first blog post', 'writing', 'common', 50),
            ('Prolific Writer', 'Write 10 blog posts', 'writing', 'uncommon', 200),
            ('Social Butterfly', 'Get 50 likes on your posts', 'engagement', 'rare', 150),
            ('Commenter', 'Leave 25 comments', 'engagement', 'common', 100),
            ('Popular Author', 'Get 100 likes on your posts', 'engagement', 'epic', 300),
            ('Daily Visitor', 'Login for 7 consecutive days', 'milestone', 'uncommon', 75),
            ('Trendsetter', 'Write 5 trending posts', 'special', 'rare', 250),
            ('Community Leader', 'Get 100 followers', 'community', 'legendary', 400),
        ]

        badge_count = 0
        for name, description, badge_type, rarity, points in badges_data:
            badge, created = Badge.objects.get_or_create(
                name=name,
                defaults={
                    'description': description,
                    'badge_type': badge_type,
                    'rarity': rarity,
                    'points_reward': points,
                    'is_active': True,
                }
            )
            if created:
                badge_count += 1

        # Create user levels and award some badges
        user_badge_count = 0
        for user in self.users:
            # Create user level
            level, created = UserLevel.objects.get_or_create(
                user=user,
                defaults={
                    'total_points': random.randint(50, 500),
                    'current_level': random.randint(1, 10),
                    'total_posts_written': random.randint(0, 20),
                    'total_comments_made': random.randint(5, 50),
                    'total_likes_given': random.randint(10, 200),
                    'reading_streak': random.randint(1, 15),
                }
            )

            # Award some random badges
            available_badges = Badge.objects.all()
            user_badges = random.sample(list(available_badges), k=random.randint(1, 4))

            for badge in user_badges:
                user_badge, created = UserBadge.objects.get_or_create(
                    user=user,
                    badge=badge,
                    defaults={
                        'earned_at': datetime.now() - timedelta(days=random.randint(0, 30))
                    }
                )
                if created:
                    user_badge_count += 1

        print(f"✅ Created {badge_count} badges and {user_badge_count} user badges")

    def create_monetization_data(self):
        """Create monetization items and settings"""
        print("Creating monetization data...")

        # Create monetization settings
        settings, created = MonetizationSettings.objects.get_or_create(
            defaults={
                'premium_monthly_price': Decimal('9.99'),
                'premium_yearly_price': Decimal('99.99'),
                'virtual_items_enabled': True,
                'point_boosts_enabled': True,
                'premium_subscriptions_enabled': True,
                'ads_enabled': True,
            }
        )

        # Create virtual items
        virtual_items_data = [
            ('Profile Badge: Gold Star', 'Show off with a golden star badge', Decimal('2.99'), 'cosmetic'),
            ('Profile Theme: Dark Mode', 'Exclusive dark theme for your profile', Decimal('1.99'), 'cosmetic'),
            ('Post Highlight', 'Highlight your post for 24 hours', Decimal('4.99'), 'temporary'),
            ('Custom Avatar Frame', 'Unique frame for your avatar', Decimal('3.99'), 'cosmetic'),
            ('Priority Support', '24/7 priority customer support', Decimal('9.99'), 'functional'),
        ]

        item_count = 0
        for name, description, price, category in virtual_items_data:
            item, created = VirtualItem.objects.get_or_create(
                name=name,
                defaults={
                    'description': description,
                    'price': price,
                    'category': category,
                    'is_active': True,
                }
            )
            if created:
                item_count += 1

        print(f"✅ Created {item_count} virtual items")

    def run_all(self):
        """Run all data population methods"""
        print("🚀 Starting data population...")
        print("=" * 50)

        self.create_users(15)
        self.create_categories()
        self.create_tags()
        self.create_posts(30)
        self.create_social_interactions()
        self.create_wallets_and_transactions()
        self.create_gamification_data()
        self.create_monetization_data()

        print("=" * 50)
        print("✅ Data population completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Users: {len(self.users)}")
        print(f"   - Posts: {len(self.posts)}")
        print(f"   - Categories: {len(self.categories)}")
        print(f"   - Tags: {len(self.tags)}")
        print(f"   - Wallets: {len(self.users)} (one per user)")
        print(f"   - Badges: {Badge.objects.count()}")
        print(f"   - Virtual Items: {VirtualItem.objects.count()}")

if __name__ == '__main__':
    populator = DataPopulator()
    populator.run_all()
