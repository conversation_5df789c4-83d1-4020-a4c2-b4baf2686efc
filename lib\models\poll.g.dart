// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poll.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PollImpl _$$PollImplFromJson(Map<String, dynamic> json) => _$PollImpl(
      id: (json['id'] as num).toInt(),
      question: json['question'] as String,
      allowMultipleChoices: json['allow_multiple_choices'] as bool? ?? false,
      showResultsImmediately: json['show_results_immediately'] as bool? ?? true,
      expiresAt: json['expires_at'] == null
          ? null
          : DateTime.parse(json['expires_at'] as String),
      isAnonymous: json['is_anonymous'] as bool? ?? false,
      totalVotes: (json['total_votes'] as num?)?.toInt() ?? 0,
      uniqueVoters: (json['unique_voters'] as num?)?.toInt() ?? 0,
      isExpired: json['is_expired'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      options: (json['options'] as List<dynamic>?)
              ?.map((e) => PollOption.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      userVote: (json['user_vote'] as List<dynamic>?)
              ?.map((e) => PollVote.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$PollImplToJson(_$PollImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'question': instance.question,
      'allow_multiple_choices': instance.allowMultipleChoices,
      'show_results_immediately': instance.showResultsImmediately,
      'expires_at': instance.expiresAt?.toIso8601String(),
      'is_anonymous': instance.isAnonymous,
      'total_votes': instance.totalVotes,
      'unique_voters': instance.uniqueVoters,
      'is_expired': instance.isExpired,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'options': instance.options,
      'user_vote': instance.userVote,
    };

_$PollOptionImpl _$$PollOptionImplFromJson(Map<String, dynamic> json) =>
    _$PollOptionImpl(
      id: (json['id'] as num).toInt(),
      text: json['text'] as String,
      imageUrl: json['image_url'] as String?,
      voteCount: (json['vote_count'] as num?)?.toInt() ?? 0,
      votePercentage: (json['vote_percentage'] as num?)?.toDouble() ?? 0.0,
      position: (json['position'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$PollOptionImplToJson(_$PollOptionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'image_url': instance.imageUrl,
      'vote_count': instance.voteCount,
      'vote_percentage': instance.votePercentage,
      'position': instance.position,
    };

_$PollVoteImpl _$$PollVoteImplFromJson(Map<String, dynamic> json) =>
    _$PollVoteImpl(
      id: (json['id'] as num).toInt(),
      option: (json['option'] as num).toInt(),
      votedAt: DateTime.parse(json['voted_at'] as String),
    );

Map<String, dynamic> _$$PollVoteImplToJson(_$PollVoteImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'option': instance.option,
      'voted_at': instance.votedAt.toIso8601String(),
    };

_$CreatePollOptionImpl _$$CreatePollOptionImplFromJson(
        Map<String, dynamic> json) =>
    _$CreatePollOptionImpl(
      text: json['text'] as String,
      imageUrl: json['image_url'] as String?,
      position: (json['position'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CreatePollOptionImplToJson(
        _$CreatePollOptionImpl instance) =>
    <String, dynamic>{
      'text': instance.text,
      'image_url': instance.imageUrl,
      'position': instance.position,
    };

_$CreatePollImpl _$$CreatePollImplFromJson(Map<String, dynamic> json) =>
    _$CreatePollImpl(
      question: json['question'] as String,
      allowMultipleChoices: json['allow_multiple_choices'] as bool? ?? false,
      showResultsImmediately: json['show_results_immediately'] as bool? ?? true,
      expiresAt: json['expires_at'] == null
          ? null
          : DateTime.parse(json['expires_at'] as String),
      isAnonymous: json['is_anonymous'] as bool? ?? false,
      options: (json['options'] as List<dynamic>)
          .map((e) => CreatePollOption.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CreatePollImplToJson(_$CreatePollImpl instance) =>
    <String, dynamic>{
      'question': instance.question,
      'allow_multiple_choices': instance.allowMultipleChoices,
      'show_results_immediately': instance.showResultsImmediately,
      'expires_at': instance.expiresAt?.toIso8601String(),
      'is_anonymous': instance.isAnonymous,
      'options': instance.options,
    };
