import 'package:freezed_annotation/freezed_annotation.dart';

part 'content_analytics.freezed.dart';
part 'content_analytics.g.dart';

@freezed
class ContentAnalytics with _$ContentAnalytics {
  const factory ContentAnalytics({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'post_title') required String postTitle,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'post_slug') required String postSlug,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'estimated_reading_time') required int estimatedReadingTime,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'complexity_score') @Default(0.0) double complexityScore,
    @<PERSON>son<PERSON>ey(name: 'word_count') @Default(0) int wordCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sentence_count') @Default(0) int sentenceCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'paragraph_count') @Default(0) int paragraphCount,
    @<PERSON>son<PERSON><PERSON>(name: 'readability_score') @Default(0.0) double readabilityScore,
    @<PERSON>sonKey(name: 'reading_level') @Default('Unknown') String readingLevel,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'average_words_per_sentence') @Default(0.0) double averageWordsPerSentence,
    @Json<PERSON>ey(name: 'average_syllables_per_word') @Default(0.0) double averageSyllablesPerWord,
    @JsonKey(name: 'total_reading_time') @Default(0) int totalReadingTime,
    @JsonKey(name: 'completion_rate') @Default(0.0) double completionRate,
    @JsonKey(name: 'average_session_duration') @Default(0) int averageSessionDuration,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _ContentAnalytics;

  factory ContentAnalytics.fromJson(Map<String, dynamic> json) => _$ContentAnalyticsFromJson(json);
}
