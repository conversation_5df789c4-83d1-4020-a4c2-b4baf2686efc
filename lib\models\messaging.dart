import 'package:freezed_annotation/freezed_annotation.dart';
import 'user.dart';

part 'messaging.freezed.dart';
part 'messaging.g.dart';

@freezed
class Message with _$Message {
  const factory Message({
    required int id,
    required String content,
    required User sender,
    @J<PERSON><PERSON>ey(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read') required bool isRead,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_deleted') required bool isDeleted,
    @<PERSON>son<PERSON><PERSON>(name: 'is_own_message') required bool isOwnMessage,
  }) = _Message;

  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);
}

@freezed
class Conversation with _$Conversation {
  const factory Conversation({
    required int id,
    required List<User> participants,
    @<PERSON><PERSON><PERSON>ey(name: 'created_at') required DateTime createdAt,
    @Json<PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') required bool isActive,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_message') Message? lastMessage,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'unread_count') required int unreadCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'other_participant') User? otherParticipant,
  }) = _Conversation;

  factory Conversation.fromJson(Map<String, dynamic> json) =>
      _$ConversationFromJson(json);
}

@freezed
class ConversationResponse with _$ConversationResponse {
  const factory ConversationResponse({
    required int count,
    String? next,
    String? previous,
    required List<Conversation> results,
  }) = _ConversationResponse;

  factory ConversationResponse.fromJson(Map<String, dynamic> json) =>
      _$ConversationResponseFromJson(json);
}

@freezed
class MessageListResponse with _$MessageListResponse {
  const factory MessageListResponse({
    required int count,
    String? next,
    String? previous,
    required List<Message> results,
  }) = _MessageListResponse;

  factory MessageListResponse.fromJson(Map<String, dynamic> json) =>
      _$MessageListResponseFromJson(json);
}

@freezed
class StartConversationRequest with _$StartConversationRequest {
  const factory StartConversationRequest({
    required String username,
    String? message,
  }) = _StartConversationRequest;

  factory StartConversationRequest.fromJson(Map<String, dynamic> json) =>
      _$StartConversationRequestFromJson(json);
}

@freezed
class StartConversationResponse with _$StartConversationResponse {
  const factory StartConversationResponse({
    required Conversation conversation,
    required bool created,
    required String message,
  }) = _StartConversationResponse;

  factory StartConversationResponse.fromJson(Map<String, dynamic> json) =>
      _$StartConversationResponseFromJson(json);
}

@freezed
class SendMessageRequest with _$SendMessageRequest {
  const factory SendMessageRequest({
    required String content,
  }) = _SendMessageRequest;

  factory SendMessageRequest.fromJson(Map<String, dynamic> json) =>
      _$SendMessageRequestFromJson(json);
}

@freezed
class UnreadCountResponse with _$UnreadCountResponse {
  const factory UnreadCountResponse({
    @JsonKey(name: 'unread_count') required int unreadCount,
  }) = _UnreadCountResponse;

  factory UnreadCountResponse.fromJson(Map<String, dynamic> json) =>
      _$UnreadCountResponseFromJson(json);
}
