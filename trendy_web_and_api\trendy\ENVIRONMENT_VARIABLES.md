# Trendy Blog Platform - Environment Variables Documentation

This document provides detailed information about all environment variables used in the Trendy Blog Platform.

## Quick Setup

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update the values in `.env` according to your environment and requirements.

## Environment Variables Reference

### Basic Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DEBUG` | Enable Django debug mode | `True` | Yes |
| `SECRET_KEY` | Django secret key for cryptographic signing | - | Yes |
| `ALLOWED_HOSTS` | Comma-separated list of allowed hosts | `localhost,127.0.0.1` | Yes |

### Database Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DATABASE_URL` | PostgreSQL connection URL for production | - | No (uses SQLite if not set) |

**Example PostgreSQL URL:**
```
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require
```

### Email Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `EMAIL_BACKEND` | Django email backend | `console.EmailBackend` | No |
| `EMAIL_HOST` | SMTP server hostname | `smtp.gmail.com` | No |
| `EMAIL_PORT` | SMTP server port | `587` | No |
| `EMAIL_USE_TLS` | Enable TLS for SMTP | `True` | No |
| `EMAIL_HOST_USER` | SMTP username | - | No |
| `EMAIL_HOST_PASSWORD` | SMTP password/app password | - | No |
| `DEFAULT_FROM_EMAIL` | Default sender email | `<EMAIL>` | No |
| `SERVER_EMAIL` | Server error email sender | `<EMAIL>` | No |

### AI Writing Assistant

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `OPENAI_API_KEY` | OpenAI API key for GPT models | - | No |
| `ANTHROPIC_API_KEY` | Anthropic API key for Claude models | - | No |

### Blockchain Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `BLOCKCHAIN_ADMIN_PRIVATE_KEY` | Private key for blockchain admin operations | Development key | No |
| `WALLET_ENCRYPTION_KEY` | Base64 encoded key for wallet encryption | Development key | No |
| `DEFAULT_BLOCKCHAIN_NETWORK` | Default blockchain network | `polygon_testnet` | No |

### PayPal Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PAYPAL_CLIENT_ID` | PayPal API client ID | - | No |
| `PAYPAL_CLIENT_SECRET` | PayPal API client secret | - | No |
| `PAYPAL_MODE` | PayPal environment (sandbox/live) | `sandbox` | No |
| `PAYPAL_BUSINESS_EMAIL` | PayPal business account email | - | No |
| `PAYPAL_BUSINESS_NAME` | PayPal business name | `Trendy App LLC` | No |
| `PAYPAL_WEBHOOK_ID` | PayPal webhook ID | - | No |
| `PAYPAL_WEBHOOK_URL` | PayPal webhook URL | - | No |

### Security Settings

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SECURE_SSL_REDIRECT` | Force HTTPS redirects | `False` | No |
| `SESSION_COOKIE_SECURE` | Secure session cookies | `False` | No |
| `CSRF_COOKIE_SECURE` | Secure CSRF cookies | `False` | No |
| `SECURE_BROWSER_XSS_FILTER` | Enable XSS filter | `True` | No |
| `SECURE_CONTENT_TYPE_NOSNIFF` | Prevent MIME sniffing | `True` | No |

### Cache Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `REDIS_URL` | Redis connection URL for caching | - | No |
| `CACHE_BACKEND` | Django cache backend | `db.DatabaseCache` | No |

### API Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `API_THROTTLE_ANON` | Rate limit for anonymous users | `100/hour` | No |
| `API_THROTTLE_USER` | Rate limit for authenticated users | `1000/hour` | No |
| `API_PAGE_SIZE` | Default API pagination size | `10` | No |

### Third-Party Services

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `AWS_ACCESS_KEY_ID` | AWS access key for S3 storage | - | No |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key for S3 storage | - | No |
| `AWS_STORAGE_BUCKET_NAME` | S3 bucket name for media files | - | No |
| `AWS_S3_REGION_NAME` | AWS S3 region | `us-east-1` | No |
| `GOOGLE_ANALYTICS_ID` | Google Analytics tracking ID | - | No |

### Gamification & Rewards

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PAYPAL_REWARDS_ENABLED` | Enable PayPal rewards system | `True` | No |
| `PAYPAL_MINIMUM_PAYOUT` | Minimum payout amount | `5.00` | No |
| `PAYPAL_MAXIMUM_MONTHLY_PAYOUT` | Maximum monthly payout per user | `150.00` | No |
| `ENGAGEMENT_REWARDS_ENABLED` | Enable engagement rewards | `True` | No |
| `MINIMUM_ACCOUNT_AGE_DAYS` | Minimum account age for rewards | `14` | No |
| `MINIMUM_ACTIVITY_SCORE` | Minimum activity score for rewards | `50` | No |

### Voice Features

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `TTS_DEFAULT_VOICE_TYPE` | Default TTS voice type | `female` | No |
| `TTS_DEFAULT_LANGUAGE` | Default TTS language | `en-US` | No |
| `TTS_DEFAULT_SPEECH_RATE` | Default TTS speech rate | `0.5` | No |
| `VOICE_COMMENTS_ENABLED` | Enable voice comments | `True` | No |
| `VOICE_COMMENTS_MAX_DURATION` | Max voice comment duration (seconds) | `300` | No |
| `VOICE_COMMENTS_AUTO_TRANSCRIBE` | Auto-transcribe voice comments | `True` | No |

### Regional Content

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DEFAULT_REGION` | Default content region | `global` | No |
| `ENABLE_REGIONAL_FILTERING` | Enable regional content filtering | `True` | No |
| `AUTO_DETECT_LOCATION` | Auto-detect user location | `True` | No |

### Development Settings

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ENABLE_DEBUG_TOOLBAR` | Enable Django Debug Toolbar | `False` | No |
| `ENABLE_DJANGO_EXTENSIONS` | Enable Django Extensions | `False` | No |
| `DEV_SERVER_HOST` | Development server host | `**************` | No |
| `DEV_SERVER_PORT` | Development server port | `8000` | No |

## Production Deployment Checklist

When deploying to production, ensure you:

1. ✅ Set `DEBUG=False`
2. ✅ Generate a new `SECRET_KEY`
3. ✅ Configure `DATABASE_URL` for PostgreSQL
4. ✅ Set up proper `ALLOWED_HOSTS`
5. ✅ Configure email settings with real SMTP
6. ✅ Set security settings to `True`
7. ✅ Configure Redis for caching
8. ✅ Set up AWS S3 for media files
9. ✅ Configure PayPal with live credentials
10. ✅ Set up proper API keys for AI services

## Security Notes

- Never commit `.env` files to version control
- Use strong, unique passwords and API keys
- Regularly rotate API keys and secrets
- Use environment-specific configurations
- Enable all security settings in production
- Monitor API usage and rate limits

## Support

For questions about environment configuration, please refer to the Django documentation or contact the development team.
