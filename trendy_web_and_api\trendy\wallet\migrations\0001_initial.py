# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WalletSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('minimum_deposit', models.DecimalField(decimal_places=2, default=Decimal('5.00'), max_digits=10)),
                ('maximum_deposit', models.DecimalField(decimal_places=2, default=Decimal('500.00'), max_digits=10)),
                ('deposit_fee_percentage', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('minimum_withdrawal', models.DecimalField(decimal_places=2, default=Decimal('10.00'), max_digits=10)),
                ('maximum_withdrawal', models.DecimalField(decimal_places=2, default=Decimal('1000.00'), max_digits=10)),
                ('withdrawal_fee_percentage', models.DecimalField(decimal_places=2, default=Decimal('2.50'), max_digits=5)),
                ('withdrawal_fee_fixed', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10)),
                ('wallets_enabled', models.BooleanField(default=True)),
                ('deposits_enabled', models.BooleanField(default=True)),
                ('withdrawals_enabled', models.BooleanField(default=True)),
                ('require_verification', models.BooleanField(default=True)),
                ('daily_withdrawal_limit', models.DecimalField(decimal_places=2, default=Decimal('100.00'), max_digits=10)),
                ('monthly_withdrawal_limit', models.DecimalField(decimal_places=2, default=Decimal('1000.00'), max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Wallet Settings',
                'verbose_name_plural': 'Wallet Settings',
            },
        ),
        migrations.CreateModel(
            name='UserWallet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('verification_date', models.DateTimeField(blank=True, null=True)),
                ('daily_spend_limit', models.DecimalField(decimal_places=2, default=Decimal('100.00'), max_digits=10)),
                ('monthly_spend_limit', models.DecimalField(decimal_places=2, default=Decimal('500.00'), max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='wallet', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='WalletDepositRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(default='paypal', max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending Payment'), ('processing', 'Processing Payment'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=15)),
                ('payment_order_id', models.CharField(blank=True, max_length=200)),
                ('external_transaction_id', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('wallet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deposit_requests', to='wallet.userwallet')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WalletTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('transaction_type', models.CharField(choices=[('credit', 'Credit (Money In)'), ('debit', 'Debit (Money Out)')], max_length=10)),
                ('purpose', models.CharField(choices=[('deposit', 'Wallet Deposit'), ('withdrawal', 'Wallet Withdrawal'), ('purchase_subscription', 'Premium Subscription'), ('purchase_points', 'Point Boost Purchase'), ('purchase_virtual_item', 'Virtual Item Purchase'), ('refund', 'Refund'), ('admin_adjustment', 'Admin Adjustment'), ('reward_payout', 'Reward Payout')], max_length=30)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('balance_before', models.DecimalField(decimal_places=2, max_digits=10)),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=15)),
                ('description', models.TextField(blank=True)),
                ('reference_id', models.CharField(blank=True, max_length=100)),
                ('payment_method', models.CharField(blank=True, max_length=50)),
                ('external_transaction_id', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('wallet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='wallet.userwallet')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WalletWithdrawalRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('withdrawal_method', models.CharField(default='paypal', max_length=50)),
                ('paypal_email', models.EmailField(max_length=254)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('approved', 'Approved'), ('processing', 'Processing Payment'), ('completed', 'Completed'), ('rejected', 'Rejected'), ('failed', 'Failed')], default='pending', max_length=15)),
                ('external_transaction_id', models.CharField(blank=True, max_length=200)),
                ('admin_notes', models.TextField(blank=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_withdrawals', to=settings.AUTH_USER_MODEL)),
                ('wallet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to='wallet.userwallet')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='userwallet',
            index=models.Index(fields=['user', 'is_active'], name='wallet_user_user_id_5763f7_idx'),
        ),
        migrations.AddIndex(
            model_name='userwallet',
            index=models.Index(fields=['balance'], name='wallet_user_balance_890455_idx'),
        ),
        migrations.AddIndex(
            model_name='walletdepositrequest',
            index=models.Index(fields=['wallet', 'status'], name='wallet_wall_wallet__a8bb79_idx'),
        ),
        migrations.AddIndex(
            model_name='walletdepositrequest',
            index=models.Index(fields=['status', 'created_at'], name='wallet_wall_status_2d7004_idx'),
        ),
        migrations.AddIndex(
            model_name='wallettransaction',
            index=models.Index(fields=['wallet', 'transaction_type'], name='wallet_wall_wallet__4de8fb_idx'),
        ),
        migrations.AddIndex(
            model_name='wallettransaction',
            index=models.Index(fields=['wallet', 'status'], name='wallet_wall_wallet__235478_idx'),
        ),
        migrations.AddIndex(
            model_name='wallettransaction',
            index=models.Index(fields=['wallet', 'created_at'], name='wallet_wall_wallet__83a8d3_idx'),
        ),
        migrations.AddIndex(
            model_name='wallettransaction',
            index=models.Index(fields=['purpose', 'status'], name='wallet_wall_purpose_a87183_idx'),
        ),
        migrations.AddIndex(
            model_name='walletwithdrawalrequest',
            index=models.Index(fields=['wallet', 'status'], name='wallet_wall_wallet__061da1_idx'),
        ),
        migrations.AddIndex(
            model_name='walletwithdrawalrequest',
            index=models.Index(fields=['status', 'created_at'], name='wallet_wall_status_86761a_idx'),
        ),
    ]
