from django.contrib import admin
from .models import (
    Badge, UserBadge, Challenge, ChallengeParticipation, UserLevel, PointTransaction,
    PayPalReward, UserPayPalReward, PayPalSettings,
    EngagementSettings, PostReadingHistory, EngagementHistory,
    UserEngagementTracker, SuspiciousActivityLog,
    PointConversionSettings, UserStorePoints, PointConversionTransaction
)


@admin.register(Badge)
class BadgeAdmin(admin.ModelAdmin):
    list_display = ['name', 'badge_type', 'rarity', 'is_active', 'is_secret']
    list_filter = ['badge_type', 'rarity', 'is_active', 'is_secret']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at']


@admin.register(UserBadge)
class UserBadgeAdmin(admin.ModelAdmin):
    list_display = ['user', 'badge', 'earned_at']
    list_filter = ['badge__badge_type', 'badge__rarity', 'earned_at']
    search_fields = ['user__username', 'badge__name']
    readonly_fields = ['earned_at']


@admin.register(Challenge)
class ChallengeAdmin(admin.ModelAdmin):
    list_display = ['title', 'challenge_type', 'start_date', 'end_date', 'is_active', 'is_featured', 'participant_count']
    list_filter = ['challenge_type', 'is_active', 'is_featured', 'start_date']
    search_fields = ['title', 'description']
    readonly_fields = ['created_at', 'participant_count']
    date_hierarchy = 'start_date'


@admin.register(ChallengeParticipation)
class ChallengeParticipationAdmin(admin.ModelAdmin):
    list_display = ['user', 'challenge', 'is_completed', 'is_active', 'joined_at']
    list_filter = ['is_completed', 'is_active', 'challenge__challenge_type', 'joined_at']
    search_fields = ['user__username', 'challenge__title']
    readonly_fields = ['joined_at', 'completed_at']


@admin.register(UserLevel)
class UserLevelAdmin(admin.ModelAdmin):
    list_display = ['user', 'current_level', 'total_points', 'points_to_next_level', 'reading_streak', 'writing_streak']
    list_filter = ['current_level', 'last_reading_date', 'last_writing_date']
    search_fields = ['user__username']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('User Info', {
            'fields': ('user', 'current_level', 'total_points', 'points_to_next_level')
        }),
        ('Activity Stats', {
            'fields': ('total_posts_read', 'total_posts_written', 'total_comments', 'total_likes_given')
        }),
        ('Streaks', {
            'fields': ('reading_streak', 'writing_streak', 'last_reading_date', 'last_writing_date')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(PointTransaction)
class PointTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'transaction_type', 'points', 'description', 'created_at']
    list_filter = ['transaction_type', 'created_at']
    search_fields = ['user__username', 'description']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'


@admin.register(PayPalReward)
class PayPalRewardAdmin(admin.ModelAdmin):
    list_display = ['name', 'usd_amount', 'reward_type', 'points_required', 'level_required', 'status', 'current_claims', 'max_total_claims']
    list_filter = ['reward_type', 'status', 'is_featured']
    search_fields = ['name', 'description']
    readonly_fields = ['current_claims', 'created_at', 'updated_at']
    fieldsets = [
        ('Basic Information', {
            'fields': ['name', 'description', 'reward_type', 'status', 'is_featured']
        }),
        ('Requirements', {
            'fields': ['points_required', 'level_required', 'streak_required', 'requirements', 'minimum_account_age_days']
        }),
        ('Reward Details', {
            'fields': ['usd_amount', 'currency']
        }),
        ('Limits & Controls', {
            'fields': ['max_claims_per_user', 'max_total_claims', 'current_claims', 'requires_verification']
        }),
        ('Timing', {
            'fields': ['start_date', 'end_date']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(UserPayPalReward)
class UserPayPalRewardAdmin(admin.ModelAdmin):
    list_display = ['user', 'reward', 'usd_amount', 'paypal_email', 'status', 'claim_date', 'paid_date']
    list_filter = ['status', 'claim_date', 'reward__reward_type']
    search_fields = ['user__username', 'paypal_email', 'reward__name']
    readonly_fields = ['claim_date', 'user_points_at_claim', 'user_level_at_claim']
    actions = ['approve_claims', 'process_payments']

    fieldsets = [
        ('Claim Information', {
            'fields': ['user', 'reward', 'paypal_email', 'usd_amount', 'status']
        }),
        ('User Context', {
            'fields': ['user_points_at_claim', 'user_level_at_claim', 'claim_date']
        }),
        ('Processing', {
            'fields': ['approved_date', 'approved_by', 'paid_date']
        }),
        ('PayPal Details', {
            'fields': ['paypal_transaction_id', 'paypal_batch_id']
        }),
        ('Notes', {
            'fields': ['verification_notes', 'admin_notes']
        }),
    ]

    def approve_claims(self, request, queryset):
        """Admin action to approve selected claims"""
        from .paypal_service import PayPalRewardService

        approved_count = 0
        for claim in queryset.filter(status='pending'):
            success, message = PayPalRewardService.approve_reward_claim(claim.id, request.user)
            if success:
                approved_count += 1

        self.message_user(request, f"Approved {approved_count} claims")
    approve_claims.short_description = "Approve selected claims"

    def process_payments(self, request, queryset):
        """Admin action to process payments for approved claims"""
        from .paypal_service import PayPalRewardService

        processed_count = 0
        for claim in queryset.filter(status='approved'):
            success, message = PayPalRewardService.process_paypal_payment(claim.id)
            if success:
                processed_count += 1

        self.message_user(request, f"Processed {processed_count} payments")
    process_payments.short_description = "Process PayPal payments"


@admin.register(PayPalSettings)
class PayPalSettingsAdmin(admin.ModelAdmin):
    fieldsets = [
        ('General Settings', {
            'fields': ['rewards_enabled', 'minimum_payout', 'maximum_monthly_payout_per_user']
        }),
        ('Verification Requirements', {
            'fields': ['require_email_verification', 'require_phone_verification', 'minimum_account_age_days', 'minimum_activity_score']
        }),
        ('Fraud Prevention', {
            'fields': ['max_claims_per_day', 'max_claims_per_month', 'cooldown_period_days']
        }),
        ('PayPal API', {
            'fields': ['paypal_client_id', 'paypal_client_secret', 'paypal_mode'],
            'classes': ['collapse']
        }),
        ('Notifications', {
            'fields': ['admin_email_notifications', 'user_email_notifications']
        }),
    ]

    def has_add_permission(self, request):
        # Only allow one settings instance
        return not PayPalSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False


@admin.register(EngagementSettings)
class EngagementSettingsAdmin(admin.ModelAdmin):
    fieldsets = [
        ('Daily Limits', {
            'fields': ['max_posts_read_per_day', 'max_comments_per_day', 'max_likes_per_day', 'max_actions_per_minute']
        }),
        ('Reading Requirements', {
            'fields': ['min_reading_time_seconds', 'min_scroll_percentage']
        }),
        ('Cooldown Periods', {
            'fields': ['like_cooldown', 'comment_cooldown']
        }),
        ('Fraud Detection', {
            'fields': ['enable_fraud_detection', 'auto_flag_suspicious']
        }),
        ('Point Values', {
            'fields': ['reading_points', 'comment_points', 'like_points']
        }),
    ]

    def has_add_permission(self, request):
        # Only allow one settings instance
        return not EngagementSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False


@admin.register(PostReadingHistory)
class PostReadingHistoryAdmin(admin.ModelAdmin):
    list_display = ['user', 'post_id', 'read_count', 'reward_given', 'points_awarded', 'first_read_at', 'time_spent_seconds']
    list_filter = ['reward_given', 'first_read_at']
    search_fields = ['user__username', 'post_id']
    readonly_fields = ['first_read_at', 'last_read_at']

    fieldsets = [
        ('Reading Info', {
            'fields': ['user', 'post_id', 'read_count', 'first_read_at', 'last_read_at']
        }),
        ('Reward Tracking', {
            'fields': ['points_awarded', 'reward_given']
        }),
        ('Quality Metrics', {
            'fields': ['time_spent_seconds', 'scroll_percentage']
        }),
    ]


@admin.register(EngagementHistory)
class EngagementHistoryAdmin(admin.ModelAdmin):
    list_display = ['user', 'engagement_type', 'target_type', 'target_id', 'points_awarded', 'is_valid', 'created_at']
    list_filter = ['engagement_type', 'target_type', 'is_valid', 'created_at']
    search_fields = ['user__username', 'target_id']
    readonly_fields = ['created_at', 'time_since_last_action']
    date_hierarchy = 'created_at'

    fieldsets = [
        ('Engagement Info', {
            'fields': ['user', 'engagement_type', 'target_type', 'target_id']
        }),
        ('Tracking', {
            'fields': ['created_at', 'points_awarded', 'is_valid', 'time_since_last_action']
        }),
    ]


@admin.register(UserEngagementTracker)
class UserEngagementTrackerAdmin(admin.ModelAdmin):
    list_display = ['user', 'posts_read_today', 'comments_today', 'likes_today', 'is_flagged', 'last_reading_reset']
    list_filter = ['is_flagged', 'last_reading_reset', 'last_engagement_reset']
    search_fields = ['user__username', 'flag_reason']
    readonly_fields = ['created_at', 'updated_at', 'last_activity_timestamp', 'last_reading_reset', 'last_engagement_reset']

    fieldsets = [
        ('User Info', {
            'fields': ['user']
        }),
        ('Daily Counters', {
            'fields': ['posts_read_today', 'last_reading_reset', 'comments_today', 'likes_today', 'last_engagement_reset']
        }),
        ('Fraud Detection', {
            'fields': ['is_flagged', 'flag_reason', 'flagged_at']
        }),
        ('Rate Limiting', {
            'fields': ['last_activity_timestamp', 'activity_count_last_minute']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(SuspiciousActivityLog)
class SuspiciousActivityLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'activity_type', 'severity', 'is_resolved', 'created_at']
    list_filter = ['activity_type', 'severity', 'is_resolved', 'created_at']
    search_fields = ['user__username', 'description']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'
    actions = ['mark_resolved', 'mark_unresolved']

    fieldsets = [
        ('Activity Info', {
            'fields': ['user', 'activity_type', 'description', 'severity']
        }),
        ('Context', {
            'fields': ['related_object_type', 'related_object_id']
        }),
        ('Resolution', {
            'fields': ['is_resolved', 'resolved_at', 'resolved_by']
        }),
    ]

    def mark_resolved(self, request, queryset):
        """Mark selected activities as resolved"""
        from django.utils import timezone
        updated = queryset.update(
            is_resolved=True,
            resolved_at=timezone.now(),
            resolved_by=request.user
        )
        self.message_user(request, f"Marked {updated} activities as resolved")
    mark_resolved.short_description = "Mark selected activities as resolved"

    def mark_unresolved(self, request, queryset):
        """Mark selected activities as unresolved"""
        updated = queryset.update(
            is_resolved=False,
            resolved_at=None,
            resolved_by=None
        )
        self.message_user(request, f"Marked {updated} activities as unresolved")
    mark_unresolved.short_description = "Mark selected activities as unresolved"


@admin.register(PointConversionSettings)
class PointConversionSettingsAdmin(admin.ModelAdmin):
    fieldsets = [
        ('Conversion Rates', {
            'fields': ['base_conversion_rate', 'conversion_fee_percentage', 'conversion_fee_fixed']
        }),
        ('Level Bonuses', {
            'fields': ['level_bonus_enabled', 'level_bonus_threshold', 'level_bonus_reduction', 'max_level_bonus']
        }),
        ('Premium Bonuses', {
            'fields': ['premium_bonus_enabled', 'premium_conversion_bonus']
        }),
        ('Limits & Restrictions', {
            'fields': ['daily_conversion_limit', 'minimum_conversion_amount', 'maximum_conversion_amount']
        }),
        ('System Controls', {
            'fields': ['conversion_enabled', 'maintenance_mode']
        }),
    ]

    def has_add_permission(self, request):
        # Only allow one settings instance
        return not PointConversionSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False


@admin.register(UserStorePoints)
class UserStorePointsAdmin(admin.ModelAdmin):
    list_display = ['user', 'balance', 'total_earned', 'total_spent', 'total_converted', 'daily_conversions_today']
    list_filter = ['last_conversion_reset', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = [
        ('User Info', {
            'fields': ['user', 'balance']
        }),
        ('Statistics', {
            'fields': ['total_earned', 'total_spent', 'total_converted']
        }),
        ('Daily Tracking', {
            'fields': ['daily_conversions_today', 'last_conversion_reset']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]


@admin.register(PointConversionTransaction)
class PointConversionTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'gamification_points_spent', 'store_points_received', 'conversion_rate', 'total_fee', 'status', 'created_at']
    list_filter = ['status', 'was_premium', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at', 'completed_at']
    date_hierarchy = 'created_at'
    actions = ['mark_completed', 'mark_failed']

    fieldsets = [
        ('Transaction Info', {
            'fields': ['user', 'status', 'created_at', 'completed_at']
        }),
        ('Conversion Details', {
            'fields': ['gamification_points_spent', 'store_points_received', 'conversion_rate']
        }),
        ('Fees', {
            'fields': ['percentage_fee', 'fixed_fee', 'total_fee']
        }),
        ('User Context', {
            'fields': ['user_level_at_conversion', 'user_total_points_at_conversion', 'was_premium']
        }),
    ]

    def mark_completed(self, request, queryset):
        """Mark selected transactions as completed"""
        from django.utils import timezone
        updated = queryset.filter(status='pending').update(
            status='completed',
            completed_at=timezone.now()
        )
        self.message_user(request, f"Marked {updated} transactions as completed")
    mark_completed.short_description = "Mark selected transactions as completed"

    def mark_failed(self, request, queryset):
        """Mark selected transactions as failed"""
        updated = queryset.filter(status='pending').update(status='failed')
        self.message_user(request, f"Marked {updated} transactions as failed")
    mark_failed.short_description = "Mark selected transactions as failed"
