import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../services/platform_storage_service.dart';
import '../services/api_service.dart';
import '../models/user.dart';

/// Authentication service for managing user authentication state
class AuthService {
  static const _storage = FlutterSecureStorage();
  static final ApiService _apiService = ApiService();

  /// Check if user is currently logged in
  static Future<bool> isLoggedIn() async {
    try {
      final token = await PlatformStorageService.getSecureData('token');
      if (token == null || token.isEmpty) {
        return false;
      }
      
      // Verify token is still valid by trying to get current user
      final user = await _apiService.getCurrentUser();
      return user != null;
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }

  /// Get current authenticated user
  static Future<User?> getCurrentUser() async {
    try {
      return await _apiService.getCurrentUser();
    } catch (e) {
      print('Error getting current user: $e');
      return null;
    }
  }

  /// Get stored authentication token
  static Future<String?> getToken() async {
    return await PlatformStorageService.getSecureData('token');
  }

  /// Clear authentication data (logout)
  static Future<void> logout() async {
    try {
      await PlatformStorageService.deleteSecureData('token');
      await PlatformStorageService.deleteSecureData('user_id');
    } catch (e) {
      print('Error during logout: $e');
    }
  }
}
