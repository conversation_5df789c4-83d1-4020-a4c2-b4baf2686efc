from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Badge, UserBadge, Challenge, ChallengeParticipation,
    UserLevel, PointTransaction
)

User = get_user_model()

class BadgeSerializer(serializers.ModelSerializer):
    rarity_display = serializers.CharField(source='get_rarity_display', read_only=True)
    type_display = serializers.CharField(source='get_badge_type_display', read_only=True)
    
    class Meta:
        model = Badge
        fields = [
            'id', 'name', 'description', 'badge_type', 'type_display',
            'rarity', 'rarity_display', 'icon', 'color', 'image_url',
            'points_reward', 'is_secret', 'requirements'
        ]

class UserBadgeSerializer(serializers.ModelSerializer):
    badge = BadgeSerializer(read_only=True)
    
    class Meta:
        model = UserBadge
        fields = ['id', 'badge', 'earned_at', 'progress_data']

class ChallengeSerializer(serializers.ModelSerializer):
    type_display = serializers.Char<PERSON><PERSON>(source='get_challenge_type_display', read_only=True)
    difficulty_display = serializers.CharField(source='get_difficulty_display', read_only=True)
    is_ongoing = serializers.ReadOnlyField()
    participant_count = serializers.ReadOnlyField()
    user_participation = serializers.SerializerMethodField()
    badge_reward = BadgeSerializer(read_only=True)
    
    class Meta:
        model = Challenge
        fields = [
            'id', 'title', 'description', 'challenge_type', 'type_display',
            'difficulty', 'difficulty_display', 'requirements', 'points_reward',
            'badge_reward', 'start_date', 'end_date', 'duration_days',
            'max_participants', 'is_ongoing', 'participant_count',
            'user_participation', 'is_featured'
        ]
    
    def get_user_participation(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                participation = ChallengeParticipation.objects.get(
                    user=request.user,
                    challenge=obj
                )
                return ChallengeParticipationSerializer(participation).data
            except ChallengeParticipation.DoesNotExist:
                return None
        return None

class ChallengeParticipationSerializer(serializers.ModelSerializer):
    challenge_title = serializers.CharField(source='challenge.title', read_only=True)
    
    class Meta:
        model = ChallengeParticipation
        fields = [
            'id', 'challenge_title', 'progress', 'completion_percentage',
            'is_completed', 'completed_at', 'joined_at', 'updated_at'
        ]

class UserLevelSerializer(serializers.ModelSerializer):
    level_progress_percentage = serializers.ReadOnlyField()
    next_level_points = serializers.SerializerMethodField()
    
    class Meta:
        model = UserLevel
        fields = [
            'total_points', 'current_level', 'points_to_next_level',
            'level_progress_percentage', 'next_level_points',
            'reading_streak', 'writing_streak', 'engagement_streak',
            'total_posts_read', 'total_posts_written', 'total_comments_made',
            'total_likes_given', 'total_voice_comments'
        ]
    
    def get_next_level_points(self, obj):
        return UserLevel.get_points_for_level(obj.current_level + 1)

class PointTransactionSerializer(serializers.ModelSerializer):
    type_display = serializers.CharField(source='get_transaction_type_display', read_only=True)
    
    class Meta:
        model = PointTransaction
        fields = [
            'id', 'transaction_type', 'type_display', 'points',
            'description', 'created_at'
        ]

class UserProfileSerializer(serializers.ModelSerializer):
    """Extended user profile with gamification data"""
    level = UserLevelSerializer(read_only=True)
    earned_badges = UserBadgeSerializer(many=True, read_only=True)
    recent_transactions = serializers.SerializerMethodField()
    active_challenges = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'date_joined',
            'level', 'earned_badges', 'recent_transactions', 'active_challenges'
        ]
    
    def get_recent_transactions(self, obj):
        transactions = PointTransaction.objects.filter(user=obj).order_by('-created_at')[:10]
        return PointTransactionSerializer(transactions, many=True).data
    
    def get_active_challenges(self, obj):
        participations = ChallengeParticipation.objects.filter(
            user=obj,
            is_active=True,
            is_completed=False
        ).select_related('challenge')[:5]
        return ChallengeParticipationSerializer(participations, many=True).data

class LeaderboardSerializer(serializers.Serializer):
    """Leaderboard data serializer"""
    user_id = serializers.IntegerField()
    username = serializers.CharField()
    total_points = serializers.IntegerField()
    current_level = serializers.IntegerField()
    rank = serializers.IntegerField()
    badge_count = serializers.IntegerField()

class ChallengeStatsSerializer(serializers.Serializer):
    """Challenge statistics serializer"""
    total_challenges = serializers.IntegerField()
    active_challenges = serializers.IntegerField()
    completed_challenges = serializers.IntegerField()
    total_participants = serializers.IntegerField()
    completion_rate = serializers.FloatField()

class GamificationStatsSerializer(serializers.Serializer):
    """Overall gamification statistics"""
    total_users = serializers.IntegerField()
    total_points_awarded = serializers.IntegerField()
    total_badges_earned = serializers.IntegerField()
    average_level = serializers.FloatField()
    most_popular_badge = serializers.DictField()
    top_challenge = serializers.DictField()

# Simplified serializers for API responses
class BadgeListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Badge
        fields = ['id', 'name', 'icon', 'color', 'rarity', 'points_reward']

class ChallengeListSerializer(serializers.ModelSerializer):
    participant_count = serializers.ReadOnlyField()
    is_ongoing = serializers.ReadOnlyField()
    
    class Meta:
        model = Challenge
        fields = [
            'id', 'title', 'challenge_type', 'difficulty',
            'points_reward', 'start_date', 'end_date',
            'participant_count', 'is_ongoing', 'is_featured'
        ]

class UserLevelSummarySerializer(serializers.ModelSerializer):
    """Minimal user level info for quick display"""
    class Meta:
        model = UserLevel
        fields = [
            'current_level', 'total_points', 'level_progress_percentage'
        ]

# Create/Update serializers
class JoinChallengeSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChallengeParticipation
        fields = ['challenge']
    
    def create(self, validated_data):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        return super().create(validated_data)

class UpdateChallengeProgressSerializer(serializers.Serializer):
    progress_data = serializers.DictField()
    
    def validate_progress_data(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Progress data must be a dictionary")
        return value
