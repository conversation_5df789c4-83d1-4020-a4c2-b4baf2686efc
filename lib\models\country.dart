import 'package:freezed_annotation/freezed_annotation.dart';

part 'country.freezed.dart';
part 'country.g.dart';

@freezed
class Region with _$Region {
  const factory Region({
    required int id,
    required String name,
    required String code,
    String? description,
    @Default(0) int countryCount,
    @Default(true) bool isActive,
  }) = _Region;

  factory Region.fromJson(Map<String, dynamic> json) => _$RegionFromJson(json);
}

@freezed
class Country with _$Country {
  const factory Country({
    required int id,
    required String name,
    required String code,
    @JsonKey(name: 'code_3') String? code3,
    Region? region,
    String? currencyCode,
    String? currencyName,
    String? phoneCode,
    String? flagEmoji,
    String? displayName,
    @Default(true) bool isActive,
    @Default(true) bool allowGlobalContent,
    @Default(0) int priority,
  }) = _Country;

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);
}

@freezed
class RegionalPreferences with _$RegionalPreferences {
  const factory RegionalPreferences({
    @JsonKey(name: 'preferred_country') Country? preferredCountry,
    @JsonKey(name: 'detected_country') Country? detectedCountry,
    @JsonKey(name: 'effective_country') Country? effectiveCountry,
    @JsonKey(name: 'show_global_content') @Default(true) bool showGlobalContent,
    @JsonKey(name: 'auto_detect_location')
    @Default(true)
    bool autoDetectLocation,
  }) = _RegionalPreferences;

  factory RegionalPreferences.fromJson(Map<String, dynamic> json) =>
      _$RegionalPreferencesFromJson(json);
}

@freezed
class RegionalStats with _$RegionalStats {
  const factory RegionalStats({
    String? userCountry,
    @Default(0) int totalPosts,
    @Default(0) int globalPosts,
    @Default(0) int regionalPosts,
    @Default(0) int availableCountries,
  }) = _RegionalStats;

  factory RegionalStats.fromJson(Map<String, dynamic> json) =>
      _$RegionalStatsFromJson(json);
}

@freezed
class LocationHistory with _$LocationHistory {
  const factory LocationHistory({
    required int id,
    Country? country,
    required String ipAddress,
    required String detectionMethod,
    @Default(0.0) double confidenceScore,
    required DateTime createdAt,
  }) = _LocationHistory;

  factory LocationHistory.fromJson(Map<String, dynamic> json) =>
      _$LocationHistoryFromJson(json);
}
