import 'package:flutter/material.dart';

class OnboardingPage {
  final String title;
  final String subtitle;
  final String description;
  final String imagePath;
  final Color backgroundColor;
  final Color primaryColor;
  final Color secondaryColor;
  final IconData icon;
  final List<String> features;
  final List<OnboardingAction>? actions;
  final Widget? customWidget;
  final bool showProgress;
  final String? route;

  const OnboardingPage({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.imagePath,
    required this.backgroundColor,
    required this.primaryColor,
    required this.secondaryColor,
    required this.icon,
    required this.features,
    this.actions,
    this.customWidget,
    this.showProgress = true,
    this.route,
  });
}

class OnboardingAction {
  final String label;
  final IconData icon;
  final VoidCallback onTap;
  final Color? color;
  final bool isPrimary;

  const OnboardingAction({
    required this.label,
    required this.icon,
    required this.onTap,
    this.color,
    this.isPrimary = false,
  });
}

class OnboardingStats {
  final String label;
  final String value;
  final IconData icon;
  final Color color;

  const OnboardingStats({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
  });
}

class OnboardingData {
  static List<OnboardingStats> get appStats => [
        const OnboardingStats(
          label: "Active Users",
          value: "50K+",
          icon: Icons.people,
          color: Color(0xFF6C5CE7),
        ),
        const OnboardingStats(
          label: "Posts Created",
          value: "1M+",
          icon: Icons.article,
          color: Color(0xFF00B894),
        ),
        const OnboardingStats(
          label: "Rewards Earned",
          value: "\$100K+",
          icon: Icons.monetization_on,
          color: Color(0xFFE17055),
        ),
        const OnboardingStats(
          label: "Countries",
          value: "150+",
          icon: Icons.public,
          color: Color(0xFF0984E3),
        ),
      ];

  static List<OnboardingPage> get pages => [
        const OnboardingPage(
          title: "Welcome to Trendy",
          subtitle: "Where Creativity Meets Rewards",
          description:
              "Join the revolutionary platform that transforms your passion for content creation into real earnings through blockchain technology and gamification.",
          imagePath: "assets/images/onboarding_welcome.png",
          backgroundColor: Color(0xFF6C5CE7),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFFA29BFE),
          icon: Icons.rocket_launch,
          features: [
            "🚀 Revolutionary Content Platform",
            "💎 Blockchain-Powered Rewards",
            "🎮 Gamified Experience",
            "🌟 Global Community"
          ],
        ),
        const OnboardingPage(
          title: "Discover & Engage",
          subtitle: "Explore Trending Content",
          description:
              "Browse through curated posts from creators worldwide. Like, comment, and share content while earning points for every interaction. Your engagement matters!",
          imagePath: "assets/images/onboarding_discover.png",
          backgroundColor: Color(0xFF00B894),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFF55EFC4),
          icon: Icons.explore,
          route: "/",
          features: [
            "📱 Browse Trending Posts",
            "❤️ Like & Comment to Earn",
            "🔍 Smart Content Discovery",
            "🌍 Regional Content Filtering"
          ],
        ),
        const OnboardingPage(
          title: "Create & Monetize",
          subtitle: "Turn Your Creativity Into Cash",
          description:
              "Share your stories, photos, and videos while earning real money. Every post you create has the potential to generate income through our reward system.",
          imagePath: "assets/images/onboarding_create.png",
          backgroundColor: Color(0xFF6C5CE7),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFFA29BFE),
          icon: Icons.create,
          features: [
            "✍️ Create Engaging Posts",
            "📸 Share Photos & Videos",
            "💰 Earn from Every Post",
            "📊 Track Your Performance"
          ],
        ),
        const OnboardingPage(
          title: "Blockchain Wallet",
          subtitle: "Your Digital Asset Hub",
          description:
              "Experience Web3 with our secure blockchain wallet. Earn TRD tokens, collect NFT achievements, manage transactions, and trade digital assets safely.",
          imagePath: "assets/images/onboarding_blockchain.png",
          backgroundColor: Color(0xFF0984E3),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFF74B9FF),
          icon: Icons.account_balance_wallet,
          route: "/wallet",
          features: [
            "🪙 Earn & Store TRD Tokens",
            "🏆 Collect NFT Achievements",
            "🔐 Secure Transactions",
            "💱 Trade Digital Assets"
          ],
        ),
        const OnboardingPage(
          title: "Level Up & Achieve",
          subtitle: "Gamification at Its Best",
          description:
              "Complete daily challenges, unlock achievements, and level up your profile. Our gamification system rewards consistent engagement with exclusive perks and bonuses.",
          imagePath: "assets/images/onboarding_gamification.png",
          backgroundColor: Color(0xFFE17055),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFFFF7675),
          icon: Icons.emoji_events,
          route: "/gamification",
          features: [
            "🎮 Daily Challenges",
            "🏅 Achievement Badges",
            "⭐ Level Progression",
            "🎁 Exclusive Rewards"
          ],
        ),
        const OnboardingPage(
          title: "Shop & Rewards",
          subtitle: "Spend Your Earnings Wisely",
          description:
              "Use your earned points and tokens in our integrated store. Purchase premium features, exclusive content, or convert earnings to real money rewards.",
          imagePath: "assets/images/onboarding_store.png",
          backgroundColor: Color(0xFF00CEC9),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFF81ECEC),
          icon: Icons.store,
          route: "/store",
          features: [
            "🛍️ Premium Features",
            "💎 Exclusive Content",
            "💰 Cash Rewards",
            "🎯 Smart Spending"
          ],
        ),
        const OnboardingPage(
          title: "Invite & Prosper",
          subtitle: "Build Your Network",
          description:
              "Share your unique referral code and earn bonus rewards when friends join. Build your network, multiply your earnings, and grow together as a community.",
          imagePath: "assets/images/onboarding_referral.png",
          backgroundColor: Color(0xFFE84393),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFFFF7675),
          icon: Icons.people,
          route: "/referral",
          features: [
            "🤝 Invite Friends",
            "💸 Earn Bonuses",
            "📈 Multiply Earnings",
            "🌐 Build Community"
          ],
        ),
        const OnboardingPage(
          title: "Connect & Socialize",
          subtitle: "Join the Community",
          description:
              "Connect with like-minded creators, follow your favorites, and build meaningful relationships. Our community features make networking fun and rewarding.",
          imagePath: "assets/images/onboarding_community.png",
          backgroundColor: Color(0xFF6C5CE7),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFFA29BFE),
          icon: Icons.groups,
          route: "/community",
          features: [
            "👥 Follow Creators",
            "💬 Real-time Chat",
            "🔔 Smart Notifications",
            "🤝 Build Connections"
          ],
        ),
        const OnboardingPage(
          title: "Ready to Start?",
          subtitle: "Your Success Journey Begins Now",
          description:
              "You're all set! Join thousands of creators who are already earning, learning, and growing. Your journey to financial freedom and creative fulfillment starts today!",
          imagePath: "assets/images/onboarding_start.png",
          backgroundColor: Color(0xFF00B894),
          primaryColor: Color(0xFFFFFFFF),
          secondaryColor: Color(0xFF55EFC4),
          icon: Icons.flag,
          showProgress: false,
          features: [
            "🌟 Join 50K+ Active Users",
            "💪 Start Earning Immediately",
            "🚀 Achieve Financial Goals",
            "🎯 Unlock Your Potential"
          ],
        ),
      ];

  // Dynamic content based on user preferences or app state
  static List<OnboardingPage> getPersonalizedPages({
    bool showBlockchain = true,
    bool showGamification = true,
    bool showCommunity = true,
    String? userCountry,
  }) {
    List<OnboardingPage> personalizedPages = [...pages];

    // Remove blockchain page if user doesn't want crypto features
    if (!showBlockchain) {
      personalizedPages.removeWhere((page) => page.route == "/wallet");
    }

    // Remove gamification if user prefers simple experience
    if (!showGamification) {
      personalizedPages.removeWhere((page) => page.route == "/gamification");
    }

    // Remove community features if user wants solo experience
    if (!showCommunity) {
      personalizedPages.removeWhere((page) => page.route == "/community");
    }

    return personalizedPages;
  }

  // Get feature highlights based on current app capabilities
  static List<String> get currentFeatures => [
        "📱 Cross-platform (Web & Mobile)",
        "🌍 Global content with regional filtering",
        "💰 Multiple earning opportunities",
        "🔐 Secure blockchain integration",
        "🎮 Comprehensive gamification",
        "👥 Active community features",
        "🛍️ Integrated marketplace",
        "📊 Advanced analytics",
      ];
}
