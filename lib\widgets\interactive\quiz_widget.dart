import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/quiz.dart';
import '../../services/interactive_service.dart';
import '../../theme/app_theme.dart';

class QuizWidget extends ConsumerStatefulWidget {
  final Quiz quiz;
  final Function(QuizAttempt)? onCompleted;

  const QuizWidget({
    super.key,
    required this.quiz,
    this.onCompleted,
  });

  @override
  ConsumerState<QuizWidget> createState() => _QuizWidgetState();
}

class _QuizWidgetState extends ConsumerState<QuizWidget>
    with TickerProviderStateMixin {
  final InteractiveService _interactiveService = InteractiveService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _currentQuestionIndex = 0;
  Map<int, List<int>> _selectedAnswers = {}; // questionId -> list of answer IDs
  bool _isSubmitting = false;
  bool _showResults = false;
  QuizAttempt? _completedAttempt;
  DateTime? _startTime;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
    _startTime = DateTime.now();

    // Check if user has already completed this quiz
    if (widget.quiz.bestAttempt != null) {
      _showResults = true;
      _completedAttempt = widget.quiz.bestAttempt;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  int get _timeTaken {
    if (_startTime == null) return 0;
    return DateTime.now().difference(_startTime!).inSeconds;
  }

  bool get _canSubmit {
    if (_showResults) return false;
    // Check if all questions have been answered
    for (final question in widget.quiz.questions) {
      if (!_selectedAnswers.containsKey(question.id) ||
          _selectedAnswers[question.id]!.isEmpty) {
        return false;
      }
    }
    return true;
  }

  void _selectAnswer(int questionId, int answerId, bool isMultipleChoice) {
    setState(() {
      if (isMultipleChoice) {
        _selectedAnswers[questionId] ??= [];
        if (_selectedAnswers[questionId]!.contains(answerId)) {
          _selectedAnswers[questionId]!.remove(answerId);
        } else {
          _selectedAnswers[questionId]!.add(answerId);
        }
      } else {
        _selectedAnswers[questionId] = [answerId];
      }
    });
  }

  Future<void> _submitQuiz() async {
    if (!_canSubmit || _isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final attempt = await _interactiveService.submitQuiz(
        widget.quiz.id,
        _selectedAnswers.map((key, value) => MapEntry(key.toString(), value)),
        _timeTaken,
      );

      setState(() {
        _completedAttempt = attempt;
        _showResults = true;
        _isSubmitting = false;
      });

      widget.onCompleted?.call(attempt);

      // Show success message with gamification rewards
      if (mounted) {
        final scorePercentage =
            _interactiveService.calculateQuizScorePercentage(attempt);
        final passed = scorePercentage >= widget.quiz.passingScore;

        // Calculate points earned
        int basePoints = 20; // Base points for quiz completion
        int bonusPoints = 0;
        String bonusMessage = '';

        if (scorePercentage >= 90) {
          bonusPoints = 10;
          bonusMessage = ' + 10 bonus points for excellent performance!';
        } else if (scorePercentage >= 70) {
          bonusPoints = 5;
          bonusMessage = ' + 5 bonus points for good performance!';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  passed
                      ? 'Quiz completed! Score: ${scorePercentage.toStringAsFixed(1)}%'
                      : 'Quiz completed. Score: ${scorePercentage.toStringAsFixed(1)}% (${widget.quiz.passingScore}% needed to pass)',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  '🎯 Earned ${basePoints + bonusPoints} points$bonusMessage',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            backgroundColor: passed ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isSubmitting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < widget.quiz.questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 20),
              if (!_showResults) ...[
                _buildQuestionNavigation(),
                const SizedBox(height: 16),
                _buildCurrentQuestion(),
                const SizedBox(height: 24),
                _buildNavigationButtons(),
              ] else ...[
                _buildResults(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.quiz,
                    size: 16,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Quiz',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            if (!_showResults && widget.quiz.timeLimit != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.timer, size: 14, color: Colors.orange),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.quiz.timeLimit! ~/ 60}min',
                      style: const TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (widget.quiz.instructions.isNotEmpty)
          Text(
            widget.quiz.instructions,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
      ],
    );
  }

  Widget _buildQuestionNavigation() {
    return Row(
      children: [
        Text(
          'Question ${_currentQuestionIndex + 1} of ${widget.quiz.questions.length}',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        const Spacer(),
        Container(
          height: 4,
          width: 100,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor:
                (_currentQuestionIndex + 1) / widget.quiz.questions.length,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentQuestion() {
    if (widget.quiz.questions.isEmpty) {
      return const Text('No questions available');
    }

    final question = widget.quiz.questions[_currentQuestionIndex];
    final isMultipleChoice = question.questionType == 'multiple_choice';
    final selectedAnswers = _selectedAnswers[question.id] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          question.questionText,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
            height: 1.4,
          ),
        ),
        const SizedBox(height: 16),
        ...question.answers.map((answer) {
          final isSelected = selectedAnswers.contains(answer.id);

          return GestureDetector(
            onTap: () =>
                _selectAnswer(question.id, answer.id, isMultipleChoice),
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor.withOpacity(0.1)
                    : Colors.grey.shade50,
                border: Border.all(
                  color:
                      isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      shape: isMultipleChoice
                          ? BoxShape.rectangle
                          : BoxShape.circle,
                      borderRadius:
                          isMultipleChoice ? BorderRadius.circular(4) : null,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.transparent,
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor : Colors.grey,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Icon(
                            isMultipleChoice ? Icons.check : Icons.circle,
                            size: 12,
                            color: Colors.white,
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      answer.answerText,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isSelected ? AppTheme.primaryColor : Colors.black87,
                        fontWeight:
                            isSelected ? FontWeight.w500 : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        if (_currentQuestionIndex > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: _previousQuestion,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Previous'),
            ),
          ),
        if (_currentQuestionIndex > 0) const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _currentQuestionIndex < widget.quiz.questions.length - 1
                ? _nextQuestion
                : (_canSubmit ? _submitQuiz : null),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    _currentQuestionIndex < widget.quiz.questions.length - 1
                        ? 'Next'
                        : 'Submit Quiz',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildResults() {
    if (_completedAttempt == null) {
      return const Text('No results available');
    }

    final attempt = _completedAttempt!;
    final scorePercentage =
        _interactiveService.calculateQuizScorePercentage(attempt);
    final passed = scorePercentage >= widget.quiz.passingScore;
    final timeFormatted =
        _interactiveService.formatTimeTaken(attempt.timeTaken);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: passed
                ? Colors.green.withOpacity(0.1)
                : Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: passed ? Colors.green : Colors.orange,
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                passed ? Icons.check_circle : Icons.info,
                size: 48,
                color: passed ? Colors.green : Colors.orange,
              ),
              const SizedBox(height: 12),
              Text(
                passed ? 'Quiz Passed!' : 'Quiz Completed',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: passed ? Colors.green : Colors.orange,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${scorePercentage.toStringAsFixed(1)}%',
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Text(
                '${attempt.earnedPoints} out of ${attempt.totalPoints} points',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Time Taken',
                timeFormatted,
                Icons.timer,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Passing Score',
                '${widget.quiz.passingScore}%',
                Icons.flag,
                Colors.purple,
              ),
            ),
          ],
        ),
        if (widget.quiz.showCorrectAnswers) ...[
          const SizedBox(height: 20),
          const Text(
            'Review Answers',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.quiz.questions.asMap().entries.map((entry) {
            final index = entry.key;
            final question = entry.value;
            return _buildQuestionReview(question, index + 1);
          }).toList(),
        ],
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionReview(QuizQuestion question, int questionNumber) {
    final userAnswers = _selectedAnswers[question.id] ?? [];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Question $questionNumber',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            question.questionText,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          ...question.answers.map((answer) {
            final isCorrect = answer.isCorrect;
            final wasSelected = userAnswers.contains(answer.id);

            Color backgroundColor = Colors.transparent;
            Color borderColor = Colors.grey.shade300;
            Color textColor = Colors.black87;

            if (isCorrect) {
              backgroundColor = Colors.green.withOpacity(0.1);
              borderColor = Colors.green;
              textColor = Colors.green.shade700;
            } else if (wasSelected && !isCorrect) {
              backgroundColor = Colors.red.withOpacity(0.1);
              borderColor = Colors.red;
              textColor = Colors.red.shade700;
            }

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: backgroundColor,
                border: Border.all(color: borderColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    isCorrect
                        ? Icons.check_circle
                        : wasSelected
                            ? Icons.cancel
                            : Icons.radio_button_unchecked,
                    size: 20,
                    color: isCorrect
                        ? Colors.green
                        : wasSelected
                            ? Colors.red
                            : Colors.grey,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      answer.answerText,
                      style: TextStyle(
                        fontSize: 14,
                        color: textColor,
                        fontWeight: isCorrect || wasSelected
                            ? FontWeight.w500
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          if (question.explanation.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.lightbulb,
                    size: 16,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      question.explanation,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.blue,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
