{% extends 'blog/base.html' %}
{% load static %}

{% block title %}Update Post - Trendy Blog{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h1 class="h3 mb-4">Update Post</h1>
                    
                    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <h5 class="alert-heading">Please correct the errors below:</h5>
                            <ul class="mb-0">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- Title -->
                        <div class="mb-4">
                            <label for="{{ form.title.id_for_label }}" class="form-label">Title</label>
                            {{ form.title }}
                            {% if form.title.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.title.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Slug -->
                        <div class="mb-4">
                            <label for="{{ form.slug.id_for_label }}" class="form-label">Slug</label>
                            {{ form.slug }}
                            <div class="form-text">Leave empty to auto-generate from title</div>
                            {% if form.slug.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.slug.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Category -->
                        <div class="mb-4">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                            {{ form.category }}
                            {% if form.category.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.category.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Content -->
                        <div class="mb-4">
                            <label for="{{ form.content.id_for_label }}" class="form-label">Content</label>
                            {{ form.content }}
                            {% if form.content.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.content.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Existing Images -->
                        {% if post.images.exists %}
                        <div class="mb-4">
                            <label class="form-label">Existing Images</label>
                            <div class="row g-3">
                                {% for image in post.images.all %}
                                <div class="col-md-4">
                                    <div class="preview-item">
                                        <img src="{{ image.image.url }}" alt="{{ image.caption|default:'Post image' }}" class="img-fluid rounded">
                                        <div class="form-group">
                                            <input type="text" name="image_captions[]" class="form-control" value="{{ image.caption }}" placeholder="Image caption">
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" name="delete_images" value="{{ image.id }}" class="form-check-input" id="delete_image_{{ image.id }}">
                                            <label class="form-check-label" for="delete_image_{{ image.id }}">Delete this image</label>
                                        </div>
                                        <button type="button" class="remove-preview" onclick="this.parentElement.remove()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- New Images -->
                        <div class="mb-4">
                            <label class="form-label">Add New Images</label>
                            <div class="row g-3" id="image-preview-container">
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center p-4">
                                            <div class="image-upload-preview mb-3">
                                                <img src="{% static 'blog/images/placeholder.jpg' %}" class="img-fluid rounded" alt="Preview">
                                            </div>
                                            <div class="d-grid">
                                                <label class="btn btn-outline-primary">
                                                    <i class="fas fa-camera me-2"></i>Choose Image
                                                    <input type="file" name="images" multiple accept="image/*" class="d-none" onchange="previewImages(this)">
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="selected-images" class="mt-3"></div>
                        </div>

                        <!-- Existing Videos -->
                        {% if post.videos.exists %}
                        <div class="mb-4">
                            <label class="form-label">Existing Videos</label>
                            <div class="row g-3">
                                {% for video in post.videos.all %}
                                <div class="col-md-6">
                                    <div class="preview-item">
                                        <video src="{{ video.video.url }}" controls></video>
                                        <div class="form-group">
                                            <input type="text" name="video_titles[]" class="form-control" value="{{ video.title }}" placeholder="Video title">
                                        </div>
                                        <div class="form-group">
                                            <textarea name="video_descriptions[]" class="form-control" rows="3" placeholder="Video description">{{ video.description }}</textarea>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" name="delete_videos" value="{{ video.id }}" class="form-check-input" id="delete_video_{{ video.id }}">
                                            <label class="form-check-label" for="delete_video_{{ video.id }}">Delete this video</label>
                                        </div>
                                        <button type="button" class="remove-preview" onclick="this.parentElement.remove()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- New Videos -->
                        <div class="mb-4">
                            <label class="form-label">Add New Videos</label>
                            <div class="row g-3" id="video-preview-container">
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-body text-center p-4">
                                            <div class="video-upload-preview mb-3">
                                                <i class="fas fa-video fa-3x text-muted"></i>
                                            </div>
                                            <div class="d-grid">
                                                <label class="btn btn-outline-primary">
                                                    <i class="fas fa-upload me-2"></i>Choose Video
                                                    <input type="file" name="videos" multiple accept="video/*" class="d-none" onchange="previewVideos(this)">
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="selected-videos" class="mt-3"></div>
                        </div>

                        <!-- References -->
                        <div class="mb-4">
                            <label for="{{ form.references.id_for_label }}" class="form-label">References</label>
                            {{ form.references }}
                            <div class="form-text">Add any references or sources for your content</div>
                            {% if form.references.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.references.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Tags -->
                        <div class="mb-4">
                            <label for="{{ form.tags.id_for_label }}" class="form-label">Tags</label>
                            {{ form.tags }}
                            <div class="form-text">Separate tags with commas</div>
                            {% if form.tags.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.tags.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Status -->
                        <div class="mb-4">
                            <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                            {{ form.status }}
                            {% if form.status.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.status.errors.0 }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Featured -->
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                {{ form.is_featured }}
                                <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">Mark as Featured Post</label>
                            </div>
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Post
                            </button>
                            <a href="{% url 'blog:post_detail' post.slug %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control, .form-select {
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        border: 1px solid var(--border-color);
        background-color: var(--light-bg);
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.15);
    }
    
    .image-upload-preview img, .video-upload-preview {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 0.75rem;
        background: var(--light-bg);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .preview-item {
        position: relative;
        margin-bottom: 1rem;
        background: var(--light-bg);
        border-radius: 0.75rem;
        padding: 1rem;
    }
    
    .preview-item img, .preview-item video {
        width: 100%;
        height: 200px;
        object-fit: cover;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    .preview-item .form-control {
        margin-bottom: 0.5rem;
    }
    
    .remove-preview {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: rgba(0,0,0,0.5);
        color: white;
        border: none;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .remove-preview:hover {
        background: rgba(0,0,0,0.7);
    }
</style>

<script>
    // Initialize CKEditor
    CKEDITOR.replace('{{ form.content.id_for_label }}', {
        height: 400,
        removeButtons: 'Source',
        removePlugins: 'elementspath,resize',
        toolbarGroups: [
            { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
            { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
            { name: 'editing', groups: [ 'find', 'selection', 'spellchecker', 'editing' ] },
            { name: 'forms', groups: [ 'forms' ] },
            '/',
            { name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ] },
            { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
            { name: 'links', groups: [ 'links' ] },
            { name: 'insert', groups: [ 'insert' ] },
            '/',
            { name: 'styles', groups: [ 'styles' ] },
            { name: 'colors', groups: [ 'colors' ] },
            { name: 'tools', groups: [ 'tools' ] },
            { name: 'others', groups: [ 'others' ] },
            { name: 'about', groups: [ 'about' ] }
        ]
    });

    // Auto-generate slug from title
    document.getElementById('{{ form.title.id_for_label }}').addEventListener('input', function(e) {
        if (!document.getElementById('{{ form.slug.id_for_label }}').value) {
            const slug = e.target.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '');
            document.getElementById('{{ form.slug.id_for_label }}').value = slug;
        }
    });

    // Image preview functionality
    function previewImages(input) {
        const container = document.getElementById('selected-images');
        container.innerHTML = '';
        
        for (let i = 0; i < input.files.length; i++) {
            const file = input.files[i];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'preview-item';
                div.innerHTML = `
                    <img src="${e.target.result}" alt="Preview ${i + 1}">
                    <div class="form-group">
                        <input type="text" name="image_captions[]" class="form-control" placeholder="Image caption">
                    </div>
                    <button type="button" class="remove-preview" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(div);
            }
            
            reader.readAsDataURL(file);
        }
    }

    // Video preview functionality
    function previewVideos(input) {
        const container = document.getElementById('selected-videos');
        container.innerHTML = '';
        
        for (let i = 0; i < input.files.length; i++) {
            const file = input.files[i];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const div = document.createElement('div');
                div.className = 'preview-item';
                div.innerHTML = `
                    <video src="${e.target.result}" controls></video>
                    <div class="form-group">
                        <input type="text" name="video_titles[]" class="form-control" placeholder="Video title">
                    </div>
                    <div class="form-group">
                        <textarea name="video_descriptions[]" class="form-control" rows="3" placeholder="Video description"></textarea>
                    </div>
                    <button type="button" class="remove-preview" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(div);
            }
            
            reader.readAsDataURL(file);
        }
    }

    // Form validation
    (function () {
        'use strict'
        var forms = document.querySelectorAll('.needs-validation')
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                form.classList.add('was-validated')
            }, false)
        })
    })()
</script>
{% endblock %} 