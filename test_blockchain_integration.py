#!/usr/bin/env python3
"""
Test script for blockchain integration with automated transactions
"""

import os
import sys
import django
import requests
import json
from decimal import Decimal

# Add the Django project to the Python path
import pathlib
script_dir = pathlib.Path(__file__).parent.absolute()
django_dir = script_dir / 'trendy_web_and_api' / 'trendy'
sys.path.append(str(django_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from blockchain.services import BlockchainService, RewardService
from blockchain.models import UserWalletAddress, TokenBalance, NFTAsset, StakingPool
from wallet.services.automated_transactions import AutomatedTransactionService

User = get_user_model()

class BlockchainTester:
    def __init__(self):
        self.test_user = None
        self.blockchain_service = None
        self.reward_service = None
        
    def setup_test_user(self):
        """Create or get test user"""
        try:
            self.test_user = User.objects.get(username='blockchain_test_user')
        except User.DoesNotExist:
            self.test_user = User.objects.create_user(
                username='blockchain_test_user',
                email='<EMAIL>',
                password='testpass123',
                first_name='Blockchain',
                last_name='Tester',
                is_email_verified=True
            )
        
        print(f"✅ Test user: {self.test_user.username}")
        
        # Initialize services
        self.blockchain_service = BlockchainService()
        self.reward_service = RewardService(self.blockchain_service)
        
    def test_wallet_creation(self):
        """Test blockchain wallet creation"""
        print("\n🔑 Testing Wallet Creation...")
        
        # Create wallet
        wallet = self.blockchain_service.get_user_wallet(self.test_user)
        
        if wallet:
            print(f"✅ Wallet created: {wallet.address}")
            print(f"🌐 Network: {wallet.network.name}")
            print(f"🔐 Is Primary: {wallet.is_primary}")
            return True
        else:
            print("❌ Failed to create wallet")
            return False
    
    def test_token_rewards(self):
        """Test token reward minting"""
        print("\n🪙 Testing Token Rewards...")
        
        # Test different reward amounts
        test_rewards = [
            {'amount': 10, 'description': 'Daily login bonus'},
            {'amount': 25, 'description': 'Post creation reward'},
            {'amount': 50, 'description': 'Achievement unlock'},
        ]
        
        for reward in test_rewards:
            success, tx_hash = self.blockchain_service.mint_reward_tokens(
                user=self.test_user,
                amount=reward['amount'],
                description=reward['description']
            )
            
            if success:
                print(f"✅ Rewarded {reward['amount']} TRD - {reward['description']}")
                print(f"🔗 Mock TX: {tx_hash}")
            else:
                print(f"❌ Failed to reward {reward['amount']} TRD")
        
        # Check token balance
        token_balance = TokenBalance.objects.filter(
            user=self.test_user,
            contract__contract_type='token'
        ).first()
        
        if token_balance:
            print(f"💰 Total Token Balance: {token_balance.balance} TRD")
            return True
        else:
            print("❌ No token balance found")
            return False
    
    def test_nft_achievements(self):
        """Test NFT achievement minting"""
        print("\n🖼️  Testing NFT Achievements...")
        
        # Test different achievement rarities
        achievements = [
            {
                'name': 'First Post',
                'description': 'Created your first post on Trendy',
                'rarity': 1,
                'image_url': 'https://trendy.app/achievements/first_post.png',
                'attributes': {'category': 'social', 'difficulty': 'easy'}
            },
            {
                'name': 'Social Butterfly',
                'description': 'Received 100 likes on your posts',
                'rarity': 3,
                'image_url': 'https://trendy.app/achievements/social_butterfly.png',
                'attributes': {'category': 'engagement', 'difficulty': 'medium'}
            },
            {
                'name': 'Trendy Legend',
                'description': 'Reached legendary status on the platform',
                'rarity': 5,
                'image_url': 'https://trendy.app/achievements/legend.png',
                'attributes': {'category': 'milestone', 'difficulty': 'legendary'}
            }
        ]
        
        for achievement in achievements:
            result = self.reward_service.process_achievement_reward(
                user=self.test_user,
                achievement_type='manual_test',
                achievement_data=achievement
            )
            
            if result['success']:
                print(f"✅ Minted NFT: {achievement['name']} (Rarity: {achievement['rarity']})")
                if result.get('token_reward'):
                    print(f"   🪙 Token Bonus: {result['token_reward']['amount']} TRD")
                if result.get('nft_reward'):
                    nft = result['nft_reward']['nft_asset']
                    print(f"   🖼️  NFT ID: {nft.token_id}")
            else:
                print(f"❌ Failed to mint NFT: {achievement['name']}")
        
        # Check NFT collection
        nfts = NFTAsset.objects.filter(user=self.test_user)
        print(f"🎨 Total NFTs Owned: {nfts.count()}")
        
        for nft in nfts:
            print(f"   • {nft.name} (Rarity: {nft.get_rarity_display()})")
        
        return nfts.count() > 0
    
    def test_staking_functionality(self):
        """Test token staking"""
        print("\n🏊 Testing Staking Functionality...")
        
        # Get staking pool
        staking_pool = StakingPool.objects.filter(is_active=True).first()
        
        if not staking_pool:
            print("❌ No active staking pool found")
            return False
        
        print(f"📊 Staking Pool: {staking_pool.name}")
        print(f"💰 APY: {staking_pool.apy_percentage}%")
        print(f"🔢 Min Stake: {staking_pool.minimum_stake} TRD")
        
        # For testing, we'll simulate staking without actual blockchain transaction
        from blockchain.models import UserStake, BlockchainTransaction
        
        # Create mock staking transaction
        mock_stake = UserStake.objects.create(
            user=self.test_user,
            pool=staking_pool,
            amount_staked=Decimal('50.00'),  # Stake 50 TRD
            is_active=True
        )
        
        print(f"✅ Mock Stake Created: {mock_stake.amount_staked} TRD")
        
        # Calculate pending rewards
        pending_rewards = mock_stake.calculate_pending_rewards()
        print(f"💎 Pending Rewards: {pending_rewards:.6f} TRD")
        
        return True
    
    def test_automated_transaction_integration(self):
        """Test integration with automated transaction system"""
        print("\n🔄 Testing Automated Transaction Integration...")
        
        # Test deposit with blockchain bonus
        deposit_result = AutomatedTransactionService.process_automated_deposit(
            user=self.test_user,
            amount=Decimal('100.00'),
            payment_method='paypal'
        )
        
        if deposit_result['success']:
            print(f"✅ Deposit initiated: ${deposit_result.get('amount', 'N/A')}")
            
            # Simulate verification and completion
            if deposit_result.get('requires_verification'):
                from wallet.models import TransactionVerificationCode
                verification = TransactionVerificationCode.objects.filter(
                    user=self.test_user,
                    transaction_type='deposit',
                    is_used=False
                ).first()
                
                if verification:
                    verify_result = AutomatedTransactionService.verify_transaction_code(
                        user=self.test_user,
                        transaction_id=verification.transaction_id,
                        verification_code=verification.verification_code,
                        transaction_type='deposit'
                    )
                    
                    if verify_result['success']:
                        print("✅ Deposit completed with blockchain bonus")
                        
                        # Check if blockchain bonus was awarded
                        latest_balance = TokenBalance.objects.filter(
                            user=self.test_user,
                            contract__contract_type='token'
                        ).first()
                        
                        if latest_balance:
                            print(f"🪙 Updated Token Balance: {latest_balance.balance} TRD")
        
        return True
    
    def test_api_endpoints(self):
        """Test blockchain API endpoints"""
        print("\n🌐 Testing API Endpoints...")
        
        # Note: This would require the Django server to be running
        # For now, we'll just verify the endpoints exist
        
        from blockchain.urls import urlpatterns
        
        endpoint_names = [pattern.name for pattern in urlpatterns if hasattr(pattern, 'name')]
        
        print("📋 Available API Endpoints:")
        for name in endpoint_names:
            print(f"   • {name}")
        
        print("💡 To test endpoints, start server: python manage.py runserver")
        print("💡 Then use tools like curl or Postman to test the APIs")
        
        return True
    
    def display_summary(self):
        """Display test summary"""
        print("\n📊 Blockchain Integration Summary")
        print("=" * 50)
        
        # User wallet info
        wallet = UserWalletAddress.objects.filter(user=self.test_user).first()
        if wallet:
            print(f"👤 User: {self.test_user.username}")
            print(f"🔑 Wallet: {wallet.address}")
            print(f"🌐 Network: {wallet.network.name}")
        
        # Token balance
        token_balance = TokenBalance.objects.filter(
            user=self.test_user,
            contract__contract_type='token'
        ).first()
        if token_balance:
            print(f"🪙 TRD Balance: {token_balance.balance}")
            print(f"🏊 Staked Balance: {token_balance.staked_balance}")
        
        # NFT collection
        nfts = NFTAsset.objects.filter(user=self.test_user)
        print(f"🖼️  NFT Collection: {nfts.count()} achievements")
        
        # Staking positions
        stakes = UserStake.objects.filter(user=self.test_user, is_active=True)
        print(f"🏊 Active Stakes: {stakes.count()}")
        
        print("\n🎉 Blockchain integration is working!")
        print("🚀 Ready for Flutter app integration")
    
    def run_all_tests(self):
        """Run all blockchain tests"""
        print("🚀 Starting Blockchain Integration Tests")
        print("=" * 60)
        
        try:
            # Setup
            self.setup_test_user()
            
            # Run tests
            tests = [
                ("Wallet Creation", self.test_wallet_creation),
                ("Token Rewards", self.test_token_rewards),
                ("NFT Achievements", self.test_nft_achievements),
                ("Staking Functionality", self.test_staking_functionality),
                ("Automated Transaction Integration", self.test_automated_transaction_integration),
                ("API Endpoints", self.test_api_endpoints),
            ]
            
            results = []
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} failed: {str(e)}")
                    results.append((test_name, False))
            
            # Display results
            print("\n" + "=" * 60)
            print("📋 Test Results:")
            for test_name, result in results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"   {status} - {test_name}")
            
            # Summary
            passed = sum(1 for _, result in results if result)
            total = len(results)
            print(f"\n🎯 Tests Passed: {passed}/{total}")
            
            if passed == total:
                self.display_summary()
                print("\n✅ All blockchain tests passed!")
            else:
                print(f"\n⚠️  {total - passed} tests failed. Check the output above.")
            
        except Exception as e:
            print(f"\n❌ Test suite failed: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    tester = BlockchainTester()
    tester.run_all_tests()
