#!/usr/bin/env python3
"""
Test script to verify the fixes for blockchain 403 errors and user discovery type casting issues
"""

import requests
import json
import sys

# API Configuration
API_BASE = "http://127.0.0.1:8000"

def test_authentication():
    """Test user authentication and get token"""
    print("🔐 Testing Authentication...")
    
    # Try to login with admin credentials
    login_data = {
        'email_or_username': '<EMAIL>',
        'password': 'admin123'
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/v1/accounts/login/", json=login_data)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            user = data.get('user', {})
            print(f"✅ Login successful! Token: {token[:20]}...")
            print(f"✅ User: {user.get('username')} ({user.get('email')})")
            return token
        else:
            print(f"❌ Login failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Login error: {e}")
    
    return None

def test_blockchain_endpoints(token):
    """Test blockchain wallet endpoints"""
    print("\n🔗 Testing Blockchain Endpoints...")
    
    headers = {'Authorization': f'Token {token}'}
    
    # Test wallet endpoint
    try:
        response = requests.get(f"{API_BASE}/api/v1/blockchain/wallet/", headers=headers)
        print(f"Blockchain wallet status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Blockchain wallet endpoint working!")
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        elif response.status_code == 403:
            print("❌ Blockchain wallet still forbidden")
            print(f"Response: {response.text}")
        elif response.status_code == 404:
            print("⚠️ Blockchain wallet not found (expected for new users)")
        else:
            print(f"❌ Blockchain wallet failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Blockchain wallet error: {e}")

def test_social_endpoints(token):
    """Test social discovery endpoints"""
    print("\n👥 Testing Social Endpoints...")
    
    headers = {'Authorization': f'Token {token}'}
    
    # Test discover users
    try:
        response = requests.get(f"{API_BASE}/api/v1/social/discover/", headers=headers)
        print(f"Discover users status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                users = data.get('data', [])
                print(f"✅ Discover users working! Found {len(users)} users")
                
                # Check for type casting issues
                if users:
                    user = users[0]
                    print(f"Sample user data:")
                    print(f"  - Username: {user.get('username')}")
                    print(f"  - Email verified: {user.get('is_email_verified')} (type: {type(user.get('is_email_verified'))})")
                    print(f"  - Profile public: {user.get('is_profile_public')} (type: {type(user.get('is_profile_public'))})")
                    print(f"  - Staff: {user.get('is_staff')} (type: {type(user.get('is_staff'))})")
            else:
                print(f"❌ Discover users failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ Discover users failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Discover users error: {e}")
    
    # Test trending users
    try:
        response = requests.get(f"{API_BASE}/api/v1/social/trending/", headers=headers)
        print(f"Trending users status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                users = data.get('data', [])
                print(f"✅ Trending users working! Found {len(users)} users")
                
                # Check for type casting issues
                if users:
                    user = users[0]
                    print(f"Sample trending user data:")
                    print(f"  - Username: {user.get('username')}")
                    print(f"  - Email verified: {user.get('is_email_verified')} (type: {type(user.get('is_email_verified'))})")
                    print(f"  - Profile public: {user.get('is_profile_public')} (type: {type(user.get('is_profile_public'))})")
            else:
                print(f"❌ Trending users failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ Trending users failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Trending users error: {e}")

def main():
    """Main test function"""
    print("🧪 Testing Fixes for Blockchain 403 and User Discovery Type Casting")
    print("=" * 70)
    
    # Test authentication
    token = test_authentication()
    if not token:
        print("❌ Cannot proceed without authentication token")
        sys.exit(1)
    
    # Test blockchain endpoints
    test_blockchain_endpoints(token)
    
    # Test social endpoints
    test_social_endpoints(token)
    
    print("\n" + "=" * 70)
    print("🎯 Fix Verification Complete!")
    print("\n💡 Next steps:")
    print("1. Run the Flutter app: flutter run")
    print("2. Test blockchain wallet access in the app")
    print("3. Test user discovery/trending features")
    print("4. Check Flutter console for any remaining errors")

if __name__ == "__main__":
    main()
