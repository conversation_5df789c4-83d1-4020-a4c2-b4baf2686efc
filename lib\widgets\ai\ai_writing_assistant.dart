import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/ai_writing_service.dart';
import '../../theme/app_theme.dart';

class AIWritingAssistant extends ConsumerStatefulWidget {
  final TextEditingController textController;
  final Function(String)? onTextInserted;

  const AIWritingAssistant({
    super.key,
    required this.textController,
    this.onTextInserted,
  });

  @override
  ConsumerState<AIWritingAssistant> createState() => _AIWritingAssistantState();
}

class _AIWritingAssistantState extends ConsumerState<AIWritingAssistant>
    with TickerProviderStateMixin {
  final AIWritingService _aiService = AIWritingService();
  
  List<WritingSuggestion> _suggestions = [];
  ReadabilityAnalysis? _readabilityAnalysis;
  bool _isAnalyzing = false;
  bool _isExpanded = false;
  
  late AnimationController _expandController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    
    _aiService.initialize();
    
    _expandController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );
    
    // Listen to text changes
    widget.textController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.textController.removeListener(_onTextChanged);
    _expandController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    // Debounce analysis
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && widget.textController.text.isNotEmpty) {
        _analyzeContent();
      }
    });
  }

  Future<void> _analyzeContent() async {
    if (_isAnalyzing) return;
    
    setState(() {
      _isAnalyzing = true;
    });

    try {
      final content = widget.textController.text;
      
      final suggestions = await _aiService.getWritingSuggestions(content);
      final readability = await _aiService.analyzeReadability(content);
      
      setState(() {
        _suggestions = suggestions;
        _readabilityAnalysis = readability;
        _isAnalyzing = false;
      });
    } catch (e) {
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _expandController.forward();
    } else {
      _expandController.reverse();
    }
  }

  Future<void> _completeText() async {
    final currentText = widget.textController.text;
    if (currentText.isEmpty) return;

    try {
      final completion = await _aiService.completeText(currentText);
      if (completion.isNotEmpty) {
        final newText = currentText + completion;
        widget.textController.text = newText;
        widget.onTextInserted?.call(completion);
      }
    } catch (e) {
      _showError('Failed to complete text: $e');
    }
  }

  Future<void> _improveText() async {
    final currentText = widget.textController.text;
    if (currentText.isEmpty) return;

    try {
      final improvedText = await _aiService.improveText(currentText);
      if (improvedText != currentText) {
        widget.textController.text = improvedText;
        widget.onTextInserted?.call('Text improved');
      }
    } catch (e) {
      _showError('Failed to improve text: $e');
    }
  }

  void _applySuggestion(WritingSuggestion suggestion) {
    // Insert suggestion at cursor position
    final text = widget.textController.text;
    final selection = widget.textController.selection;
    
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      suggestion.suggestion,
    );
    
    widget.textController.text = newText;
    widget.onTextInserted?.call(suggestion.suggestion);
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'AI Writing Assistant',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_isAnalyzing)
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  else
                    Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: Colors.white,
                    ),
                ],
              ),
            ),
          ),
          
          // Quick actions (always visible)
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.auto_fix_high,
                    label: 'Complete',
                    onTap: _completeText,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.edit,
                    label: 'Improve',
                    onTap: _improveText,
                  ),
                ),
              ],
            ),
          ),
          
          // Expandable content
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Column(
              children: [
                // Readability analysis
                if (_readabilityAnalysis != null) ...[
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.analytics,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Readability Analysis',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildAnalysisItem(
                                'Score',
                                '${_readabilityAnalysis!.score.toInt()}',
                                _getScoreColor(_readabilityAnalysis!.score),
                              ),
                            ),
                            Expanded(
                              child: _buildAnalysisItem(
                                'Level',
                                _readabilityAnalysis!.level,
                                AppTheme.primaryColor,
                              ),
                            ),
                            Expanded(
                              child: _buildAnalysisItem(
                                'Read Time',
                                '${_readabilityAnalysis!.estimatedReadingTime}m',
                                Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                
                // Writing suggestions
                if (_suggestions.isNotEmpty) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Suggestions',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._suggestions.map((suggestion) => _buildSuggestionCard(suggestion)),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 16, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildSuggestionCard(WritingSuggestion suggestion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _applySuggestion(suggestion),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[200]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getSuggestionIcon(suggestion.type),
                    size: 16,
                    color: _getSuggestionColor(suggestion.type),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      suggestion.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                suggestion.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  IconData _getSuggestionIcon(SuggestionType type) {
    switch (type) {
      case SuggestionType.grammar:
        return Icons.spellcheck;
      case SuggestionType.style:
        return Icons.style;
      case SuggestionType.structure:
        return Icons.format_list_bulleted;
      case SuggestionType.engagement:
        return Icons.favorite;
      case SuggestionType.expansion:
        return Icons.expand_more;
      case SuggestionType.seo:
        return Icons.search;
    }
  }

  Color _getSuggestionColor(SuggestionType type) {
    switch (type) {
      case SuggestionType.grammar:
        return Colors.red;
      case SuggestionType.style:
        return Colors.blue;
      case SuggestionType.structure:
        return Colors.green;
      case SuggestionType.engagement:
        return Colors.pink;
      case SuggestionType.expansion:
        return Colors.orange;
      case SuggestionType.seo:
        return Colors.purple;
    }
  }
}
