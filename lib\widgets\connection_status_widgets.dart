import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/offline_provider.dart';
import '../services/connectivity_service.dart';
import '../models/app_error.dart';
import '../theme/app_theme.dart';

/// Connection status banner that appears at the top of screens
class ConnectionStatusBanner extends ConsumerWidget {
  final bool showWhenOnline;
  final EdgeInsets? margin;

  const ConnectionStatusBanner({
    super.key,
    this.showWhenOnline = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityStatus = ref.watch(connectivityStatusProvider);

    return connectivityStatus.when(
      data: (status) {
        if (status == ConnectionStatus.online && !showWhenOnline) {
          return const SizedBox.shrink();
        }

        return _buildStatusBanner(context, status);
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildStatusBanner(BuildContext context, ConnectionStatus status) {
    final theme = Theme.of(context);

    Color backgroundColor;
    Color textColor;
    IconData icon;
    String message;

    switch (status) {
      case ConnectionStatus.online:
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = Icons.wifi;
        message = 'Connected';
        break;
      case ConnectionStatus.slow:
        backgroundColor = Colors.orange;
        textColor = Colors.white;
        icon = Icons.signal_wifi_bad;
        message = 'Slow connection detected';
        break;
      case ConnectionStatus.limited:
        backgroundColor = Colors.red;
        textColor = Colors.white;
        icon = Icons.signal_wifi_connected_no_internet_4;
        message = 'Limited connectivity - using cached content';
        break;
      case ConnectionStatus.offline:
        backgroundColor = Colors.grey;
        textColor = Colors.white;
        icon = Icons.wifi_off;
        message = 'No internet connection - showing cached content';
        break;
    }

    return Container(
      width: double.infinity,
      margin: margin ?? const EdgeInsets.all(0),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: textColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: textColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (status != ConnectionStatus.online)
            Consumer(
              builder: (context, ref, child) => IconButton(
                icon: Icon(Icons.refresh, color: textColor, size: 20),
                onPressed: () {
                  ref.read(connectivityServiceProvider).forceCheck();
                },
                tooltip: 'Check connection',
              ),
            ),
        ],
      ),
    );
  }
}

/// Small connection status indicator for app bars
class ConnectionStatusIndicator extends ConsumerWidget {
  final double size;
  final bool showText;

  const ConnectionStatusIndicator({
    super.key,
    this.size = 20,
    this.showText = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityStatus = ref.watch(connectivityStatusProvider);
    final theme = Theme.of(context);

    return connectivityStatus.when(
      data: (status) {
        if (status == ConnectionStatus.online) {
          return const SizedBox.shrink();
        }

        Color color;
        IconData icon;
        String text;

        switch (status) {
          case ConnectionStatus.slow:
            color = Colors.orange;
            icon = Icons.signal_wifi_bad;
            text = 'Slow';
            break;
          case ConnectionStatus.limited:
            color = Colors.red;
            icon = Icons.signal_wifi_connected_no_internet_4;
            text = 'Limited';
            break;
          case ConnectionStatus.offline:
            color = Colors.grey;
            icon = Icons.wifi_off;
            text = 'Offline';
            break;
          default:
            return const SizedBox.shrink();
        }

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: size),
            if (showText) ...[
              const SizedBox(width: 4),
              Text(
                text,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}

/// Error display widget with retry functionality
class ErrorDisplayWidget extends ConsumerWidget {
  final AppError error;
  final VoidCallback? onRetry;
  final bool showDetails;
  final EdgeInsets? padding;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.showDetails = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: _getErrorColor().withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getErrorIcon(),
              size: 40,
              color: _getErrorColor(),
            ),
          ),

          const SizedBox(height: 24),

          // Error title
          Text(
            _getErrorTitle(),
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // User-friendly message
          Text(
            error.userMessage,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          if (error.suggestion != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Colors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      error.suggestion!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 24),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (error.isRetryable == true && onRetry != null) ...[
                ElevatedButton.icon(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],
              OutlinedButton.icon(
                onPressed: () {
                  // Check connection
                  ref.read(connectivityServiceProvider).forceCheck();
                },
                icon: const Icon(Icons.network_check),
                label: const Text('Check Connection'),
              ),
            ],
          ),

          if (showDetails && error.details != null) ...[
            const SizedBox(height: 24),
            ExpansionTile(
              title: const Text('Technical Details'),
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    error.details!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Color _getErrorColor() {
    switch (error.type) {
      case AppErrorType.network:
        return Colors.orange;
      case AppErrorType.server:
        return Colors.red;
      case AppErrorType.authentication:
        return Colors.purple;
      case AppErrorType.authorization:
        return Colors.deepOrange;
      case AppErrorType.validation:
        return Colors.amber;
      case AppErrorType.notFound:
        return Colors.grey;
      case AppErrorType.maintenance:
        return Colors.blue;
      case AppErrorType.featureDisabled:
        return Colors.indigo;
      case AppErrorType.storage:
        return Colors.brown;
      case AppErrorType.unknown:
        return Colors.grey;
    }
  }

  IconData _getErrorIcon() {
    switch (error.type) {
      case AppErrorType.network:
        return Icons.wifi_off;
      case AppErrorType.server:
        return Icons.error;
      case AppErrorType.authentication:
        return Icons.login;
      case AppErrorType.authorization:
        return Icons.lock;
      case AppErrorType.validation:
        return Icons.error_outline;
      case AppErrorType.notFound:
        return Icons.search_off;
      case AppErrorType.maintenance:
        return Icons.build;
      case AppErrorType.featureDisabled:
        return Icons.block;
      case AppErrorType.storage:
        return Icons.storage;
      case AppErrorType.unknown:
        return Icons.help_outline;
    }
  }

  String _getErrorTitle() {
    switch (error.type) {
      case AppErrorType.network:
        return 'Connection Problem';
      case AppErrorType.server:
        return 'Server Error';
      case AppErrorType.authentication:
        return 'Sign In Required';
      case AppErrorType.authorization:
        return 'Access Denied';
      case AppErrorType.validation:
        return 'Invalid Data';
      case AppErrorType.notFound:
        return 'Not Found';
      case AppErrorType.maintenance:
        return 'System Maintenance';
      case AppErrorType.featureDisabled:
        return 'Feature Unavailable';
      case AppErrorType.storage:
        return 'Storage Error';
      case AppErrorType.unknown:
        return 'Something Went Wrong';
    }
  }
}

/// Offline indicator that shows when using cached content
class OfflineIndicator extends ConsumerWidget {
  final Widget child;
  final bool showBanner;

  const OfflineIndicator({
    super.key,
    required this.child,
    this.showBanner = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isOnline = ref.watch(isOnlineProvider);

    return Column(
      children: [
        if (showBanner && !isOnline) const ConnectionStatusBanner(),
        Expanded(child: child),
      ],
    );
  }
}

/// Retry button widget
class RetryButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String? text;
  final bool isLoading;

  const RetryButton({
    super.key,
    required this.onPressed,
    this.text,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: isLoading ? null : onPressed,
      icon: isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.refresh),
      label: Text(text ?? 'Try Again'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }
}
