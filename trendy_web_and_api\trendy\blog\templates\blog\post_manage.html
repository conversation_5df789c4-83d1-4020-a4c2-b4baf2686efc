{% extends 'blog/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{% if post %}Edit Post{% else %}Create New Post{% endif %} - Trendy Blog{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">{% if post %}Edit Post{% else %}Create New Post{% endif %}</h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-8">
                                {{ form.title|as_crispy_field }}
                                {{ form.content|as_crispy_field }}
                                {{ form.category|as_crispy_field }}
                                {{ form.tags_input|as_crispy_field }}
                                {{ form.status|as_crispy_field }}
                                {{ form.is_featured|as_crispy_field }}
                                {{ form.reference|as_crispy_field }}
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Media</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Images -->
                                        <div class="mb-4">
                                            <h6>Images</h6>
                                            {{ form.images|as_crispy_field }}
                                            {% if post and post.images.exists %}
                                            <div class="mt-3">
                                                <h6>Current Images</h6>
                                                <div class="row">
                                                    {% for image in post.images.all %}
                                                    <div class="col-6 mb-2">
                                                        <div class="position-relative">
                                                            <img src="{{ image.image.url }}" alt="{{ image.caption|default:'Post image' }}" class="img-fluid rounded">
                                                            <div class="position-absolute top-0 end-0 p-2">
                                                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteImage({{ image.id }})">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- Videos -->
                                        <div class="mb-4">
                                            <h6>Videos</h6>
                                            {{ form.videos|as_crispy_field }}
                                            {% if post and post.videos.exists %}
                                            <div class="mt-3">
                                                <h6>Current Videos</h6>
                                                {% for video in post.videos.all %}
                                                <div class="mb-3">
                                                    <div class="position-relative">
                                                        <video class="img-fluid rounded" controls poster="{% if video.thumbnail %}{{ video.thumbnail.url }}{% endif %}">
                                                            <source src="{{ video.video.url }}" type="video/mp4">
                                                        </video>
                                                        <div class="position-absolute top-0 end-0 p-2">
                                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteVideo({{ video.id }})">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Save Post
                                            </button>
                                            <a href="{% if post %}{% url 'post-detail' post.slug %}{% else %}{% url 'home' %}{% endif %}" class="btn btn-secondary">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </a>
                                            {% if post %}
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                                <i class="fas fa-trash me-2"></i>Delete Post
                                            </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if post %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this post? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'post-delete' post.slug %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% block extra_js %}
<script>
function clearImage() {
    document.getElementById('clear_image').value = '1';
    document.querySelector('input[name="image"]').value = '';
}

function clearVideo() {
    document.getElementById('clear_video').value = '1';
    document.querySelector('input[name="video"]').value = '';
}

// Initialize select2 for tags
$(document).ready(function() {
    $('#id_tags').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select tags',
        allowClear: true
    });
});
</script>
{% endblock %}
{% endblock %} 