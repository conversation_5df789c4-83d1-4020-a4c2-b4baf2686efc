"""
Blockchain API views for Trendy app
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from decimal import Decimal
import logging

from .models import (
    BlockchainNetwork, SmartContract, UserWalletAddress,
    BlockchainTransaction, TokenBalance, NFTAsset,
    StakingPool, UserStake, MarketplaceListing
)
from .services import BlockchainService, RewardService, NFTService, StakingService

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_wallet(request):
    """Get user's blockchain wallet information"""
    try:
        blockchain_service = BlockchainService()
        wallet = blockchain_service.get_user_wallet(request.user)
        
        if not wallet:
            return Response({
                'success': False,
                'message': 'Wallet not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get token balances
        token_balances = TokenBalance.objects.filter(
            user=request.user,
            network=blockchain_service.network
        )
        
        balances_data = []
        for balance in token_balances:
            balances_data.append({
                'contract_name': balance.contract.name,
                'contract_address': balance.contract.address,
                'balance': str(balance.balance),
                'staked_balance': str(balance.staked_balance),
                'total_balance': str(balance.total_balance)
            })
        
        return Response({
            'success': True,
            'wallet': {
                'address': wallet.address,
                'network': wallet.network.name,
                'isPrimary': wallet.is_primary,
                'isActive': wallet.is_active,
                'createdAt': wallet.created_at.isoformat(),
                'balances': balances_data
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting user wallet: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error retrieving wallet information'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_wallet(request):
    """Create a new blockchain wallet for user"""
    try:
        blockchain_service = BlockchainService()
        
        # Check if user already has a wallet
        existing_wallet = UserWalletAddress.objects.filter(
            user=request.user,
            network=blockchain_service.network
        ).first()
        
        if existing_wallet:
            return Response({
                'success': False,
                'message': 'Wallet already exists'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        wallet = blockchain_service.create_user_wallet(request.user)
        
        if wallet:
            return Response({
                'success': True,
                'message': 'Wallet created successfully',
                'wallet': {
                    'address': wallet.address,
                    'network': wallet.network.name,
                    'isPrimary': wallet.is_primary,
                    'isActive': wallet.is_active,
                    'createdAt': wallet.created_at.isoformat(),
                    'balances': []  # Add empty balances for now
                }
            })
        else:
            return Response({
                'success': False,
                'message': 'Failed to create wallet'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error creating wallet: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error creating wallet'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_activation_code(request):
    """Send activation code to user for wallet activation"""
    try:
        blockchain_service = BlockchainService()
        activation_code, message = blockchain_service.send_activation_code(request.user)

        if activation_code:
            return Response({
                'success': True,
                'message': message,
                'activation_code': activation_code  # In production, don't return this - send via email
            })
        else:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error sending activation code: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error sending activation code'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def activate_wallet(request):
    """Activate user's blockchain wallet with activation code"""
    try:
        activation_code = request.data.get('activation_code')

        if not activation_code:
            return Response({
                'success': False,
                'message': 'Activation code is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        blockchain_service = BlockchainService()
        success, message = blockchain_service.activate_user_wallet(request.user, activation_code)

        if success:
            # Get the activated wallet info
            wallet = UserWalletAddress.objects.filter(
                user=request.user,
                network=blockchain_service.network,
                is_active=True
            ).first()

            return Response({
                'success': True,
                'message': message,
                'wallet': {
                    'address': wallet.address,
                    'network': wallet.network.name,
                    'is_primary': wallet.is_primary,
                    'is_active': wallet.is_active,
                    'activated_at': wallet.activated_at,
                    'created_at': wallet.created_at
                } if wallet else None
            })
        else:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"Error activating wallet: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error activating wallet'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_nfts(request):
    """Get user's NFT collection"""
    try:
        nfts = NFTAsset.objects.filter(user=request.user).order_by('-minted_at')
        
        nfts_data = []
        for nft in nfts:
            nfts_data.append({
                'id': nft.id,
                'token_id': nft.token_id,
                'name': nft.name,
                'description': nft.description,
                'image_url': nft.image_url,
                'rarity': nft.rarity,
                'rarity_display': nft.get_rarity_display(),
                'contract_address': nft.contract.address,
                'network': nft.network.name,
                'minted_at': nft.minted_at,
                'attributes': nft.attributes
            })
        
        return Response({
            'success': True,
            'nfts': nfts_data,
            'total_count': len(nfts_data)
        })
        
    except Exception as e:
        logger.error(f"Error getting user NFTs: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error retrieving NFT collection'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mint_achievement_nft(request):
    """Mint achievement NFT for user (admin only)"""
    try:
        # Check if user is admin/staff
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        data = request.data
        required_fields = ['user_id', 'name', 'rarity', 'image_url']
        
        for field in required_fields:
            if field not in data:
                return Response({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        target_user = get_object_or_404(User, id=data['user_id'])
        
        blockchain_service = BlockchainService()
        nft_service = NFTService(blockchain_service)
        
        achievement_data = {
            'name': data['name'],
            'description': data.get('description', ''),
            'rarity': int(data['rarity']),
            'image_url': data['image_url'],
            'attributes': data.get('attributes', {})
        }
        
        success, nft_asset, tx_hash = nft_service.mint_achievement_nft(
            user=target_user,
            achievement_data=achievement_data
        )
        
        if success:
            return Response({
                'success': True,
                'message': 'NFT minted successfully',
                'nft': {
                    'id': nft_asset.id,
                    'token_id': nft_asset.token_id,
                    'name': nft_asset.name,
                    'rarity': nft_asset.rarity,
                    'tx_hash': tx_hash
                }
            })
        else:
            return Response({
                'success': False,
                'message': 'Failed to mint NFT'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error minting NFT: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error minting NFT'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def reward_tokens(request):
    """Reward tokens to user (admin only)"""
    try:
        # Check if user is admin/staff
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        data = request.data
        required_fields = ['user_id', 'amount', 'description']
        
        for field in required_fields:
            if field not in data:
                return Response({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        target_user = get_object_or_404(User, id=data['user_id'])
        
        blockchain_service = BlockchainService()
        
        success, tx_hash = blockchain_service.mint_reward_tokens(
            user=target_user,
            amount=Decimal(str(data['amount'])),
            description=data['description']
        )
        
        if success:
            return Response({
                'success': True,
                'message': f'Rewarded {data["amount"]} tokens successfully',
                'tx_hash': tx_hash
            })
        else:
            return Response({
                'success': False,
                'message': 'Failed to reward tokens'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        logger.error(f"Error rewarding tokens: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error rewarding tokens'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_staking_pools(request):
    """Get available staking pools"""
    try:
        pools = StakingPool.objects.filter(is_active=True)
        
        pools_data = []
        for pool in pools:
            pools_data.append({
                'id': pool.id,
                'name': pool.name,
                'description': pool.description,
                'apy_percentage': str(pool.apy_percentage),
                'minimum_stake': str(pool.minimum_stake),
                'maximum_stake': str(pool.maximum_stake) if pool.maximum_stake else None,
                'total_staked': str(pool.total_staked),
                'active_stakers': pool.active_stakers,
                'start_date': pool.start_date,
                'end_date': pool.end_date
            })
        
        return Response({
            'success': True,
            'pools': pools_data
        })
        
    except Exception as e:
        logger.error(f"Error getting staking pools: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error retrieving staking pools'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def stake_tokens(request):
    """Stake tokens in a pool"""
    try:
        data = request.data
        required_fields = ['pool_id', 'amount']
        
        for field in required_fields:
            if field not in data:
                return Response({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        blockchain_service = BlockchainService()
        staking_service = StakingService(blockchain_service)
        
        success, result, user_stake = staking_service.stake_tokens(
            user=request.user,
            pool_id=data['pool_id'],
            amount=Decimal(str(data['amount']))
        )
        
        if success:
            return Response({
                'success': True,
                'message': f'Successfully staked {data["amount"]} tokens',
                'tx_hash': result,
                'stake_id': user_stake.id if user_stake else None
            })
        else:
            return Response({
                'success': False,
                'message': result
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Error staking tokens: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error staking tokens'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_stakes(request):
    """Get user's staking positions"""
    try:
        stakes = UserStake.objects.filter(user=request.user, is_active=True)
        
        stakes_data = []
        for stake in stakes:
            pending_rewards = stake.calculate_pending_rewards()
            stakes_data.append({
                'id': stake.id,
                'pool_name': stake.pool.name,
                'amount_staked': str(stake.amount_staked),
                'rewards_earned': str(stake.rewards_earned),
                'pending_rewards': str(pending_rewards),
                'apy_percentage': str(stake.pool.apy_percentage),
                'staked_at': stake.staked_at
            })
        
        return Response({
            'success': True,
            'stakes': stakes_data
        })
        
    except Exception as e:
        logger.error(f"Error getting user stakes: {str(e)}")
        return Response({
            'success': False,
            'message': 'Error retrieving staking positions'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
