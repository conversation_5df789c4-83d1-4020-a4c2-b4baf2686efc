// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interactive_block.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$InteractiveBlockImpl _$$InteractiveBlockImplFromJson(
        Map<String, dynamic> json) =>
    _$InteractiveBlockImpl(
      id: (json['id'] as num).toInt(),
      blockType: json['block_type'] as String,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      position: (json['position'] as num?)?.toInt() ?? 0,
      isActive: json['is_active'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      poll: json['poll'] == null
          ? null
          : Poll.fromJson(json['poll'] as Map<String, dynamic>),
      quiz: json['quiz'] == null
          ? null
          : Quiz.fromJson(json['quiz'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$InteractiveBlockImplToJson(
        _$InteractiveBlockImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'block_type': instance.blockType,
      'title': instance.title,
      'description': instance.description,
      'position': instance.position,
      'is_active': instance.isActive,
      'metadata': instance.metadata,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'poll': instance.poll,
      'quiz': instance.quiz,
    };
