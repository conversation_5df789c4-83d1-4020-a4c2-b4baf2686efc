import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/screens/enhanced_auth_screen.dart';
import 'package:trendy/screens/post_detail_screen.dart';
import 'package:trendy/screens/profile_screen.dart';
import 'package:trendy/screens/community_screen.dart';
import 'package:trendy/screens/user_profile_screen.dart';
import 'screens/gamification_screen.dart';
import 'screens/rewards_screen.dart';
import 'screens/referral_screen.dart';
import 'screens/store_screen.dart';
import 'screens/premium_upgrade_screen.dart';
import 'screens/regional_settings_screen.dart';
import 'screens/create_post_screen.dart';
import 'screens/maintenance_screen.dart';
import 'theme/app_theme.dart';
import 'providers/theme_provider.dart' as theme_provider;
import 'providers/maintenance_provider.dart';
import 'providers/offline_provider.dart';
import 'services/app_initialization_service.dart';
import 'services/connectivity_service.dart';
import 'services/offline_storage_service.dart';
import 'services/error_reporting_service.dart';
import 'screens/splash_screen.dart';
import 'widgets/main_navigation.dart';

// main.dart
void main() {
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    // Update theme when system brightness changes
    final brightness =
        WidgetsBinding.instance.platformDispatcher.platformBrightness;
    ref
        .read(theme_provider.themeProvider.notifier)
        .updateSystemBrightness(brightness);
  }

  @override
  Widget build(BuildContext context) {
    final themeState = ref.watch(theme_provider.themeProvider);

    return MaterialApp(
      title: 'Trendy',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: _getFlutterThemeMode(themeState.themeMode),
      debugShowCheckedModeBanner: false,
      home: const SplashScreen(),
      routes: {
        '/post': (context) => PostDetailScreen(
              pk: ModalRoute.of(context)!.settings.arguments as String,
            ),
        '/profile': (context) => const ProfileScreen(),
        '/community': (context) => const CommunityScreen(),
        '/user-profile': (context) => UserProfileScreen(
              username: ModalRoute.of(context)!.settings.arguments as String,
            ),
        '/auth': (context) => const EnhancedAuthScreen(),
        '/gamification': (context) => const GamificationScreen(),
        '/rewards': (context) => const RewardsScreen(),
        '/referral': (context) => const ReferralScreen(),
        '/store': (context) => const StoreScreen(),
        '/premium': (context) => const PremiumUpgradeScreen(),
        '/regional-settings': (context) => const RegionalSettingsScreen(),
        '/create-post': (context) => const CreatePostScreen(),
      },
    );
  }

  ThemeMode _getFlutterThemeMode(theme_provider.ThemeMode themeMode) {
    switch (themeMode) {
      case theme_provider.ThemeMode.light:
        return ThemeMode.light;
      case theme_provider.ThemeMode.dark:
        return ThemeMode.dark;
      case theme_provider.ThemeMode.system:
        return ThemeMode.system;
    }
  }
}

class AppNavigator extends ConsumerWidget {
  const AppNavigator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMaintenanceActive = ref.watch(maintenanceStatusProvider);

    // Show maintenance screen if system is under maintenance
    if (isMaintenanceActive) {
      return const MaintenanceScreen();
    }

    // Show the main navigation with all features
    return const MainNavigation();
  }
}
