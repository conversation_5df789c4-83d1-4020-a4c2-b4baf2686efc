import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/models/country.dart';
import 'package:trendy/providers/regional_provider.dart';

class CountrySelector extends ConsumerStatefulWidget {
  final Country? selectedCountry;
  final Function(Country?) onCountrySelected;
  final bool showGlobalOption;
  final String? hintText;
  final bool isCompact;

  const CountrySelector({
    Key? key,
    this.selectedCountry,
    required this.onCountrySelected,
    this.showGlobalOption = true,
    this.hintText,
    this.isCompact = false,
  }) : super(key: key);

  @override
  ConsumerState<CountrySelector> createState() => _CountrySelectorState();
}

class _CountrySelectorState extends ConsumerState<CountrySelector> {
  bool _isExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final countriesAsync = ref.watch(filteredCountriesProvider);
    final searchQuery = ref.watch(countrySearchProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Selected country display / trigger
        InkWell(
          onTap: () => setState(() => _isExpanded = !_isExpanded),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: widget.isCompact ? 12 : 16,
              vertical: widget.isCompact ? 8 : 12,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              children: [
                if (widget.selectedCountry != null) ...[
                  Text(
                    widget.selectedCountry?.flagEmoji ?? '🌍',
                    style: TextStyle(fontSize: widget.isCompact ? 16 : 20),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.selectedCountry?.displayName ??
                          widget.selectedCountry?.name ??
                          'Unknown',
                      style: TextStyle(
                        fontSize: widget.isCompact ? 14 : 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.public,
                    color: Colors.grey.shade600,
                    size: widget.isCompact ? 16 : 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.hintText ?? 'Select Country',
                      style: TextStyle(
                        fontSize: widget.isCompact ? 14 : 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
                Icon(
                  _isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),

        // Dropdown content
        if (_isExpanded) ...[
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Search field
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search countries...',
                      prefixIcon: const Icon(Icons.search, size: 20),
                      suffixIcon: searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear, size: 20),
                              onPressed: () {
                                _searchController.clear();
                                ref.read(countrySearchProvider.notifier).state =
                                    '';
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    onChanged: (value) {
                      ref.read(countrySearchProvider.notifier).state = value;
                    },
                  ),
                ),

                // Global option
                if (widget.showGlobalOption) ...[
                  _buildCountryOption(
                    flag: '🌍',
                    name: 'Global Content',
                    subtitle: 'Show posts from all countries',
                    onTap: () => _selectCountry(null),
                    isSelected: widget.selectedCountry == null,
                  ),
                  const Divider(height: 1),
                ],

                // Countries list
                Container(
                  constraints: const BoxConstraints(maxHeight: 300),
                  child: countriesAsync.when(
                    data: (countries) {
                      if (countries.isEmpty) {
                        return const Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            'No countries found',
                            style: TextStyle(color: Colors.grey),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }

                      return ListView.separated(
                        shrinkWrap: true,
                        itemCount: countries.length,
                        separatorBuilder: (context, index) =>
                            const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final country = countries[index];
                          return _buildCountryOption(
                            flag: country.flagEmoji ?? '🏳️',
                            name: country.displayName ?? country.name,
                            subtitle: country.region?.name,
                            onTap: () => _selectCountry(country),
                            isSelected:
                                widget.selectedCountry?.id == country.id,
                          );
                        },
                      );
                    },
                    loading: () => const Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(child: CircularProgressIndicator()),
                    ),
                    error: (error, stack) => Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        'Error loading countries: $error',
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCountryOption({
    required String flag,
    required String name,
    String? subtitle,
    required VoidCallback onTap,
    required bool isSelected,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: isSelected ? Colors.blue.shade50 : null,
        child: Row(
          children: [
            Text(flag, style: const TextStyle(fontSize: 20)),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected ? Colors.blue.shade700 : null,
                    ),
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Colors.blue.shade700,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  void _selectCountry(Country? country) {
    widget.onCountrySelected(country);
    setState(() => _isExpanded = false);
    _searchController.clear();
    ref.read(countrySearchProvider.notifier).state = '';
  }
}
