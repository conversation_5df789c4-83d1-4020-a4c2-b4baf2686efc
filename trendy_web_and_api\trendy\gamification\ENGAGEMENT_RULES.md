# Engagement Rules & Unfair Practice Prevention

This document outlines the comprehensive system implemented to prevent unfair practices in earning points through engagement activities.

## Overview

The system prevents users from gaming the point system by implementing strict rules for:
- Reading posts/articles
- Liking content
- Commenting on content
- General engagement activities

## Key Features

### 1. Duplicate Prevention
- **Reading**: Users can only earn points once per post, regardless of how many times they read it
- **Liking**: Users can only earn points once per like action on the same content
- **Commenting**: Users can only earn points once per comment on the same content
- **General Rule**: All engagement activities are tracked to prevent duplicate rewards

### 2. Quality Requirements
- **Minimum Reading Time**: Users must spend at least 10 seconds reading a post
- **Minimum Scroll Percentage**: Users must scroll at least 30% of the content
- **Engagement Validation**: All activities are validated before awarding points

### 3. Rate Limiting
- **Like Cooldown**: 2 seconds between like actions
- **Comment Cooldown**: 30 seconds between comment actions
- **Actions Per Minute**: Maximum 30 actions per minute
- **Daily Limits**: 
  - 50 posts read per day
  - 20 comments per day
  - 100 likes per day

### 4. Fraud Detection
- **Rapid Reading Detection**: Flags users who read too quickly
- **Excessive Activity Detection**: Monitors for bot-like behavior
- **Suspicious Activity Logging**: All suspicious activities are logged for admin review
- **Auto-flagging**: High-risk activities automatically flag users

## Models

### EngagementSettings
Global configuration for engagement rules:
```python
max_posts_read_per_day = 50
max_comments_per_day = 20
max_likes_per_day = 100
min_reading_time_seconds = 10
min_scroll_percentage = 30.0
like_cooldown = 2  # seconds
comment_cooldown = 30  # seconds
enable_fraud_detection = True
```

### PostReadingHistory
Tracks reading history per user per post:
- Prevents duplicate reading rewards
- Records reading quality metrics
- Tracks time spent and scroll percentage

### EngagementHistory
Tracks all engagement activities:
- Prevents duplicate engagement rewards
- Records engagement type and target
- Maintains activity timeline

### UserEngagementTracker
Monitors daily activity limits:
- Tracks daily counters
- Implements rate limiting
- Manages fraud flags

### SuspiciousActivityLog
Logs suspicious activities:
- Records activity type and severity
- Provides admin review interface
- Tracks resolution status

## Usage Examples

### Reading Activity
```python
from gamification.services import GamificationService

# Award points for reading (with validation)
success, message = GamificationService.update_reading_activity(
    user=user,
    post_id=123,
    time_spent=15,  # seconds
    scroll_percentage=45.0  # percentage
)

if success:
    print("Points awarded for reading")
else:
    print(f"Points not awarded: {message}")
```

### Engagement Activity
```python
# Award points for liking (with validation)
success, message = GamificationService.update_engagement_activity(
    user=user,
    activity_type='like',
    object_id=123,
    target_type='post'
)

# Award points for commenting (with validation)
success, message = GamificationService.update_engagement_activity(
    user=user,
    activity_type='comment',
    object_id=456,
    target_type='comment'
)
```

## Admin Interface

All models are registered in Django Admin for easy management:

### EngagementSettings
- Configure global engagement rules
- Adjust fraud detection settings
- Modify point values and limits

### PostReadingHistory
- View user reading patterns
- Monitor reading quality metrics
- Identify potential abuse

### EngagementHistory
- Track all user engagements
- Validate engagement authenticity
- Review engagement patterns

### UserEngagementTracker
- Monitor daily activity limits
- Review flagged users
- Manage user restrictions

### SuspiciousActivityLog
- Review suspicious activities
- Investigate potential fraud
- Resolve flagged activities

## Testing

Use the management command to test the system:

```bash
python manage.py test_engagement_rules --username=testuser --post-id=1
```

This command tests:
1. First-time reading (should succeed)
2. Duplicate reading (should fail)
3. Rapid reading (should fail)
4. Insufficient scroll (should fail)
5. First-time like (should succeed)
6. Duplicate like (should fail)
7. First-time comment (should succeed)
8. Duplicate comment (should fail)
9. Rate limiting (should enforce cooldowns)

## Integration

The system is integrated into:

### Blog Views
- Like functionality validates before awarding points
- Comment functionality prevents duplicate rewards
- Reading tracking includes quality metrics

### Frontend Integration
- Reading sessions track time and scroll depth
- Engagement actions provide feedback on success/failure
- Rate limiting prevents rapid-fire actions

## Security Features

1. **Database Constraints**: Unique constraints prevent duplicate records
2. **Transaction Safety**: All operations use database transactions
3. **Input Validation**: All inputs are validated before processing
4. **Audit Trail**: Complete audit trail of all activities
5. **Admin Controls**: Comprehensive admin interface for monitoring

## Configuration

Administrators can configure the system through:
1. Django Admin interface
2. EngagementSettings model
3. Environment variables (if needed)
4. Management commands for bulk operations

## Monitoring

The system provides comprehensive monitoring through:
1. Real-time activity tracking
2. Suspicious activity alerts
3. Daily/weekly reports
4. User behavior analytics
5. Fraud detection metrics

This system ensures fair play while maintaining a positive user experience for legitimate users.
