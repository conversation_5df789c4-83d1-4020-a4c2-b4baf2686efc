from django.core.management.base import BaseCommand
from django.conf import settings
from blockchain.models import BlockchainNetwork, SmartContract
from decimal import Decimal


class Command(BaseCommand):
    help = 'Set up blockchain networks and basic contracts for development'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing blockchain data',
        )

    def handle(self, *args, **options):
        self.stdout.write('🔗 Setting up blockchain networks...')
        
        if options['reset']:
            self.stdout.write('🗑️  Resetting existing blockchain data...')
            BlockchainNetwork.objects.all().delete()
            SmartContract.objects.all().delete()
        
        # Create blockchain networks
        self.create_networks()
        
        # Create basic smart contracts
        self.create_basic_contracts()
        
        self.stdout.write(
            self.style.SUCCESS('✅ Successfully set up blockchain infrastructure!')
        )

    def create_networks(self):
        """Create blockchain networks from settings"""
        self.stdout.write('🌐 Creating blockchain networks...')
        
        for network_name, config in settings.BLOCKCHAIN_NETWORKS.items():
            network, created = BlockchainNetwork.objects.get_or_create(
                name=network_name,
                defaults={
                    'chain_id': config['chain_id'],
                    'rpc_url': config['rpc_url'],
                    'explorer_url': config['explorer_url'],
                    'native_token': config['native_token'],
                    'gas_price_gwei': config['gas_price_gwei'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'   ✅ Created: {network.get_name_display()}')
            else:
                self.stdout.write(f'   ℹ️  Exists: {network.get_name_display()}')

    def create_basic_contracts(self):
        """Create basic smart contracts for development"""
        self.stdout.write('📄 Creating basic smart contracts...')
        
        # Mock TRD Token ABI (simplified)
        token_abi = [
            {
                "inputs": [],
                "name": "name",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "symbol", 
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "totalSupply",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            }
        ]
        
        # Mock NFT ABI (simplified)
        nft_abi = [
            {
                "inputs": [],
                "name": "name",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "symbol",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view", 
                "type": "function"
            }
        ]
        
        # Create contracts for each network
        for network in BlockchainNetwork.objects.filter(is_active=True):
            # TRD Token Contract
            token_contract, created = SmartContract.objects.get_or_create(
                network=network,
                contract_type='token',
                name='Trendy Token',
                defaults={
                    'address': '0x1111111111111111111111111111111111111111',  # Mock address
                    'abi': token_abi,
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'   ✅ Created TRD Token on {network.get_name_display()}')
            
            # Achievement NFT Contract
            nft_contract, created = SmartContract.objects.get_or_create(
                network=network,
                contract_type='nft',
                name='Trendy Achievements',
                defaults={
                    'address': '0x2222222222222222222222222222222222222222',  # Mock address
                    'abi': nft_abi,
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'   ✅ Created Achievement NFTs on {network.get_name_display()}')
        
        self.stdout.write('💡 Note: These are mock contracts for development.')
        self.stdout.write('💡 Replace addresses with real deployments for production.')
