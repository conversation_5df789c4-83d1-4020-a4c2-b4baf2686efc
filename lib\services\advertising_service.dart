import 'package:dio/dio.dart';
import '../models/advertising_models.dart';
import '../services/platform_storage_service.dart';

class AdvertisingService {
  final Dio _dio;

  AdvertisingService(this._dio);

  // Get ad settings
  Future<AdSettings> getAdSettings() async {
    try {
      final response = await _dio.get('/api/v1/advertising/settings/');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['settings'] != null) {
          return AdSettings.fromJson(data['settings']);
        } else {
          throw Exception('Invalid ad settings response format');
        }
      } else {
        throw Exception('Failed to load ad settings');
      }
    } catch (e) {
      print('Error getting ad settings: $e');
      rethrow;
    }
  }

  // Get available ad placements
  Future<List<AdPlacement>> getAdPlacements() async {
    try {
      final response = await _dio.get('/api/v1/advertising/placements/');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['placements'] != null) {
          final List<dynamic> placements = data['placements'];
          return placements.map((json) => AdPlacement.fromJson(json)).toList();
        } else {
          throw Exception('Invalid ad placements response format');
        }
      } else {
        throw Exception('Failed to load ad placements');
      }
    } catch (e) {
      print('Error getting ad placements: $e');
      rethrow;
    }
  }

  // Get sponsored content
  Future<List<SponsoredContent>> getSponsoredContent({
    String? contentType,
    int page = 1,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        if (contentType != null) 'content_type': contentType,
      };

      final response = await _dio.get(
        '/api/v1/advertising/sponsored-content/',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['results'] ?? response.data;
        return data.map((json) => SponsoredContent.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load sponsored content');
      }
    } catch (e) {
      print('Error getting sponsored content: $e');
      rethrow;
    }
  }

  // Start a rewarded ad session
  Future<RewardedAdSession> startRewardedAdSession(String placementId) async {
    try {
      final response = await _dio.post(
        '/api/v1/advertising/rewarded-ads/start/',
        data: {'placement_id': placementId},
      );

      if (response.statusCode == 201) {
        return RewardedAdSession.fromJson(response.data);
      } else {
        throw Exception('Failed to start rewarded ad session');
      }
    } catch (e) {
      print('Error starting rewarded ad session: $e');
      rethrow;
    }
  }

  // Complete a rewarded ad session
  Future<RewardedAdSession> completeRewardedAdSession(String sessionId) async {
    try {
      final response = await _dio.post(
        '/api/v1/advertising/rewarded-ads/$sessionId/complete/',
      );

      if (response.statusCode == 200) {
        return RewardedAdSession.fromJson(response.data);
      } else {
        throw Exception('Failed to complete rewarded ad session');
      }
    } catch (e) {
      print('Error completing rewarded ad session: $e');
      rethrow;
    }
  }

  // Record ad impression
  Future<void> recordAdImpression({
    required String placementId,
    required String adNetworkId,
    String? sessionId,
    bool wasClicked = false,
  }) async {
    try {
      await _dio.post(
        '/api/v1/advertising/impressions/',
        data: {
          'placement_id': placementId,
          'ad_network_id': adNetworkId,
          'session_id': sessionId,
          'was_clicked': wasClicked,
        },
      );
    } catch (e) {
      print('Error recording ad impression: $e');
      // Don't rethrow - impression tracking shouldn't break the app
    }
  }

  // Get user's ad history
  Future<List<RewardedAdSession>> getUserAdHistory({int page = 1}) async {
    try {
      final response = await _dio.get(
        '/api/v1/advertising/user-history/',
        queryParameters: {'page': page},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['results'] ?? response.data;
        return data.map((json) => RewardedAdSession.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load ad history');
      }
    } catch (e) {
      print('Error getting user ad history: $e');
      rethrow;
    }
  }

  // Check if user can watch ads (daily limits, etc.)
  Future<Map<String, dynamic>> checkAdAvailability() async {
    try {
      final response = await _dio.get('/api/v1/advertising/availability/');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to check ad availability');
      }
    } catch (e) {
      print('Error checking ad availability: $e');
      rethrow;
    }
  }

  // Get ad analytics for user
  Future<Map<String, dynamic>> getUserAdAnalytics() async {
    try {
      final response = await _dio.get('/api/v1/advertising/user-analytics/');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to load ad analytics');
      }
    } catch (e) {
      print('Error getting ad analytics: $e');
      rethrow;
    }
  }

  // Click on sponsored content
  Future<Map<String, dynamic>?> clickSponsoredContent(String contentId) async {
    try {
      print(
          '🔍 AdvertisingService: Recording click for content ID: $contentId');

      final response = await _dio
          .post('/api/v1/advertising/sponsored-content/$contentId/click/');

      print(
          '🔍 AdvertisingService: Click response status: ${response.statusCode}');
      print('🔍 AdvertisingService: Click response data: ${response.data}');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception(
            'Failed to record sponsored content click: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error recording sponsored content click: $e');
      if (e.toString().contains('DioException')) {
        print('❌ Network error details: ${e.toString()}');
      }
      // Don't rethrow - click tracking shouldn't break the app
      return null;
    }
  }

  // Record impression for sponsored content
  Future<Map<String, dynamic>?> recordSponsoredContentImpression(
      String contentId) async {
    try {
      final response = await _dio
          .post('/api/v1/advertising/sponsored-content/$contentId/impression/');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to record sponsored content impression');
      }
    } catch (e) {
      print('Error recording sponsored content impression: $e');
      // Don't rethrow - impression tracking shouldn't break the app
      return null;
    }
  }

  // Local storage for ad session management
  Future<void> saveAdSession(
      String sessionId, Map<String, dynamic> data) async {
    try {
      await PlatformStorageService.setSecureData(
          'ad_session_$sessionId', data.toString());
    } catch (e) {
      print('Error saving ad session: $e');
    }
  }

  Future<String?> getAdSession(String sessionId) async {
    try {
      return await PlatformStorageService.getSecureData(
          'ad_session_$sessionId');
    } catch (e) {
      print('Error getting ad session: $e');
      return null;
    }
  }

  Future<void> clearAdSession(String sessionId) async {
    try {
      await PlatformStorageService.deleteSecureData('ad_session_$sessionId');
    } catch (e) {
      print('Error clearing ad session: $e');
    }
  }

  // Check if ads should be shown based on user preferences and settings
  Future<bool> shouldShowAds() async {
    try {
      final settings = await getAdSettings();

      // Check if ads are globally enabled
      if (!settings.adsEnabled) {
        return false;
      }

      // Check ad availability (daily limits, etc.)
      final availability = await checkAdAvailability();
      return availability['can_watch_ads'] ?? false;
    } catch (e) {
      print('Error checking if should show ads: $e');
      return false;
    }
  }

  // Get daily ad points earned
  Future<int> getDailyAdPoints() async {
    try {
      final analytics = await getUserAdAnalytics();
      return analytics['daily_points_earned'] ?? 0;
    } catch (e) {
      print('Error getting daily ad points: $e');
      return 0;
    }
  }

  // Get total ad points earned
  Future<int> getTotalAdPoints() async {
    try {
      final analytics = await getUserAdAnalytics();
      return analytics['total_points_earned'] ?? 0;
    } catch (e) {
      print('Error getting total ad points: $e');
      return 0;
    }
  }
}
