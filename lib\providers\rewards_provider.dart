import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../models/reward_models.dart';

// Rewards state
class RewardsState {
  final List<PayPalReward> availableRewards;
  final List<UserPayPalReward> userRewards;
  final bool isLoading;
  final String? error;
  final UserPayPalProfile? paypalProfile;

  const RewardsState({
    this.availableRewards = const [],
    this.userRewards = const [],
    this.isLoading = false,
    this.error,
    this.paypalProfile,
  });

  RewardsState copyWith({
    List<PayPalReward>? availableRewards,
    List<UserPayPalReward>? userRewards,
    bool? isLoading,
    String? error,
    UserPayPalProfile? paypalProfile,
  }) {
    return RewardsState(
      availableRewards: availableRewards ?? this.availableRewards,
      userRewards: userRewards ?? this.userRewards,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      paypalProfile: paypalProfile ?? this.paypalProfile,
    );
  }
}

// Rewards notifier
class RewardsNotifier extends StateNotifier<RewardsState> {
  final ApiService _apiService;

  RewardsNotifier(this._apiService) : super(const RewardsState());

  // Load available PayPal rewards
  Future<void> loadAvailableRewards() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the actual backend API
      final response = await _apiService.getPayPalRewards();

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final availableRewardsData = data['available_rewards'] as List? ?? [];

        final rewards = availableRewardsData.map((json) {
          return PayPalReward(
            id: json['id']?.toString() ?? '',
            name: json['name']?.toString() ?? '',
            description: json['description']?.toString() ?? '',
            amount: double.tryParse(json['usd_amount']?.toString() ?? '0') ?? 0.0,
            pointsRequired: json['points_required'] ?? 0,
            tier: _determineTier(json['points_required'] ?? 0),
            isActive: json['is_active'] ?? true,
            isLimitedTime: json['is_limited_time'] ?? false,
            expiresAt: json['expires_at'] != null ? DateTime.tryParse(json['expires_at']) : null,
            maxClaims: json['max_claims'] ?? 0,
            currentClaims: json['current_claims'] ?? 0,
          );
        }).toList();

        state = state.copyWith(
          availableRewards: rewards,
          isLoading: false,
        );
      } else {
        // Fallback to empty list if API fails
        state = state.copyWith(
          availableRewards: [],
          isLoading: false,
          error: response['message'] ?? 'Failed to load rewards from server',
        );
      }
    } catch (e) {
      print('Error loading rewards: $e');
      state = state.copyWith(
        isLoading: false,
        availableRewards: [],
        error: 'Failed to load rewards: ${e.toString()}',
      );
    }
  }

  // Helper method to determine tier based on points required
  String _determineTier(int pointsRequired) {
    if (pointsRequired <= 1000) return 'starter';
    if (pointsRequired <= 3000) return 'engagement';
    if (pointsRequired <= 7000) return 'achievement';
    return 'elite';
  }

  // Load user's PayPal rewards
  Future<void> loadUserRewards() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getUserPayPalRewards();

      if (response['success'] == true) {
        final rewardsData = response['rewards'] as List? ?? [];

        final userRewards = rewardsData.map((json) {
          return UserPayPalReward(
            id: json['id']?.toString() ?? '',
            userId: json['user_id']?.toString() ?? '',
            rewardId: json['reward_id']?.toString() ?? '',
            reward: PayPalReward(
              id: json['reward_id']?.toString() ?? '',
              name: json['reward_name']?.toString() ?? '',
              description: json['reward_description']?.toString() ?? '',
              amount: double.tryParse(json['usd_amount']?.toString() ?? '0') ?? 0.0,
              pointsRequired: json['points_required'] ?? 0,
              tier: _determineTier(json['points_required'] ?? 0),
              isActive: true,
            ),
            amount: double.tryParse(json['usd_amount']?.toString() ?? '0') ?? 0.0,
            paypalEmail: json['paypal_email']?.toString() ?? '',
            status: json['status']?.toString() ?? 'pending',
            claimedAt: json['claim_date'] != null ? DateTime.tryParse(json['claim_date']) : DateTime.now(),
            completedAt: json['processed_at'] != null ? DateTime.tryParse(json['processed_at']) : null,
          );
        }).toList();

        state = state.copyWith(
          userRewards: userRewards,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          userRewards: [],
          isLoading: false,
          error: response['message'] ?? 'Failed to load user rewards',
        );
      }
    } catch (e) {
      print('Error loading user rewards: $e');
      state = state.copyWith(
        isLoading: false,
        userRewards: [],
        error: 'Failed to load user rewards: ${e.toString()}',
      );
    }
  }

  // Load PayPal profile
  Future<void> loadPayPalProfile() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the actual backend API to get existing profile
      final response = await _apiService.getPayPalProfile();

      if (response['success'] == true && response['profile'] != null) {
        final profileData = response['profile'];

        final profile = UserPayPalProfile(
          id: profileData['id']?.toString() ?? '',
          userId: 'current_user', // This would come from auth context
          paypalEmail: profileData['paypal_email']?.toString() ?? '',
          isVerified: profileData['is_verified'] ?? false,
          verifiedAt: profileData['verified_at'] != null
              ? DateTime.tryParse(profileData['verified_at'])
              : null,
          totalEarnings: 0.0, // This would be calculated from user rewards
          totalRewardsClaimed: 0, // This would be calculated from user rewards
          isActive: true,
        );

        state = state.copyWith(
          paypalProfile: profile,
          isLoading: false,
        );
      } else {
        // No profile exists - user needs to set one up
        state = state.copyWith(
          paypalProfile: null,
          isLoading: false,
        );
      }
    } catch (e) {
      print('Error loading PayPal profile: $e');
      state = state.copyWith(
        paypalProfile: null,
        isLoading: false,
        error: 'Failed to load PayPal profile: ${e.toString()}',
      );
    }
  }

  // Claim a PayPal reward
  Future<bool> claimReward(String rewardId, String paypalEmail) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      print('🔄 Claiming PayPal reward: $rewardId for $paypalEmail');

      // Call the API service and handle the response properly
      final response = await _apiService.claimPayPalReward(rewardId, paypalEmail);
      print('✅ API response received: $response');

      // Check if the response indicates success
      bool isSuccess = false;
      String message = 'Reward claimed successfully';

      // The API service always returns Map<String, dynamic>
      if (response.containsKey('success')) {
        isSuccess = response['success'] == true;
        message = response['message']?.toString() ?? message;
      } else if (response.containsKey('status')) {
        isSuccess = response['status'] == 'success';
        message = response['message']?.toString() ?? message;
      } else {
        // If we get any response without error, consider it success
        isSuccess = true;
      }

      if (isSuccess) {
        // Create a mock user reward for successful claim
        final reward = state.availableRewards.firstWhere(
          (r) => r.id == rewardId,
          orElse: () => PayPalReward(
            id: rewardId,
            name: 'PayPal Reward',
            description: 'Reward claimed',
            amount: 10.0,
            pointsRequired: 1000,
            tier: 'starter',
            isActive: true,
          ),
        );

        final userReward = UserPayPalReward(
          id: 'claim_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'current_user',
          rewardId: rewardId,
          reward: reward,
          amount: reward.amount,
          status: 'pending',
          paypalEmail: paypalEmail,
          claimedAt: DateTime.now(),
        );

        // Add to user rewards list
        final updatedUserRewards = [...state.userRewards, userReward];

        state = state.copyWith(
          userRewards: updatedUserRewards,
          isLoading: false,
        );

        print('✅ Reward claimed successfully!');
        return true;
      } else {
        throw Exception(message);
      }
    } catch (e) {
      print('❌ Error claiming reward: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to claim reward. Please try again.',
      );
      return false;
    }
  }

  // Setup PayPal profile
  Future<bool> setupPayPalProfile(String paypalEmail) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      print('🔄 Setting up PayPal profile for: $paypalEmail');

      // Validate email format
      if (!_isValidEmail(paypalEmail)) {
        state = state.copyWith(
          isLoading: false,
          error: 'Please enter a valid PayPal email address.',
        );
        return false;
      }

      // Call the actual backend API
      final response = await _apiService.setupPayPalProfile(paypalEmail);

      if (response['success'] == true) {
        // Create profile from response data
        final profile = UserPayPalProfile(
          id: 'paypal_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'current_user',
          paypalEmail: paypalEmail,
          isVerified: response['verification_required'] != true, // If verification required, not verified yet
          verifiedAt: response['verification_required'] != true ? DateTime.now() : null,
          totalEarnings: 0.0,
          totalRewardsClaimed: 0,
          isActive: true,
        );

        state = state.copyWith(
          paypalProfile: profile,
          isLoading: false,
        );

        print('✅ PayPal profile setup successful!');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['error'] ?? 'Failed to setup PayPal profile',
        );
        return false;
      }
    } catch (e) {
      print('❌ Error setting up PayPal profile: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to setup PayPal profile. Please check your email and try again.',
      );
      return false;
    }
  }

  // Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  // Get rewards by tier
  List<PayPalReward> getRewardsByTier(String tier) {
    return state.availableRewards
        .where((reward) => reward.tier == tier)
        .toList();
  }

  // Check if user can claim a reward
  bool canClaimReward(PayPalReward reward, int userPoints) {
    return userPoints >= reward.pointsRequired && 
           state.paypalProfile != null &&
           state.paypalProfile!.isVerified;
  }

  // Get user's total earnings from rewards
  double getTotalEarnings() {
    return state.userRewards
        .where((reward) => reward.status == 'completed')
        .fold(0.0, (sum, reward) => sum + reward.amount);
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final rewardsProvider = StateNotifierProvider<RewardsNotifier, RewardsState>((ref) {
  return RewardsNotifier(ApiService());
});

// Convenience providers
final availableRewardsProvider = Provider<List<PayPalReward>>((ref) {
  return ref.watch(rewardsProvider).availableRewards;
});

final userRewardsProvider = Provider<List<UserPayPalReward>>((ref) {
  return ref.watch(rewardsProvider).userRewards;
});

final paypalProfileProvider = Provider<UserPayPalProfile?>((ref) {
  return ref.watch(rewardsProvider).paypalProfile;
});

final totalEarningsProvider = Provider<double>((ref) {
  final notifier = ref.read(rewardsProvider.notifier);
  return notifier.getTotalEarnings();
});

// Tier-specific providers
final starterRewardsProvider = Provider<List<PayPalReward>>((ref) {
  final notifier = ref.read(rewardsProvider.notifier);
  return notifier.getRewardsByTier('starter');
});

final engagementRewardsProvider = Provider<List<PayPalReward>>((ref) {
  final notifier = ref.read(rewardsProvider.notifier);
  return notifier.getRewardsByTier('engagement');
});

final achievementRewardsProvider = Provider<List<PayPalReward>>((ref) {
  final notifier = ref.read(rewardsProvider.notifier);
  return notifier.getRewardsByTier('achievement');
});

final eliteRewardsProvider = Provider<List<PayPalReward>>((ref) {
  final notifier = ref.read(rewardsProvider.notifier);
  return notifier.getRewardsByTier('elite');
});
