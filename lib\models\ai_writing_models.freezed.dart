// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_writing_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AIWritingPreferences _$AIWritingPreferencesFromJson(Map<String, dynamic> json) {
  return _AIWritingPreferences.fromJson(json);
}

/// @nodoc
mixin _$AIWritingPreferences {
  String get preferredTone => throw _privateConstructorUsedError;
  String get preferredStyle => throw _privateConstructorUsedError;
  String get targetAudience => throw _privateConstructorUsedError;
  bool get enableGrammarSuggestions => throw _privateConstructorUsedError;
  bool get enableSeoSuggestions => throw _privateConstructorUsedError;
  bool get enableContentGeneration => throw _privateConstructorUsedError;
  bool get enableReadabilityAnalysis => throw _privateConstructorUsedError;
  bool get enableAutoComplete => throw _privateConstructorUsedError;
  int get preferredWordCount => throw _privateConstructorUsedError;
  bool get includeReferences => throw _privateConstructorUsedError;
  bool get includeImagesSuggestions => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AIWritingPreferencesCopyWith<AIWritingPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIWritingPreferencesCopyWith<$Res> {
  factory $AIWritingPreferencesCopyWith(AIWritingPreferences value,
          $Res Function(AIWritingPreferences) then) =
      _$AIWritingPreferencesCopyWithImpl<$Res, AIWritingPreferences>;
  @useResult
  $Res call(
      {String preferredTone,
      String preferredStyle,
      String targetAudience,
      bool enableGrammarSuggestions,
      bool enableSeoSuggestions,
      bool enableContentGeneration,
      bool enableReadabilityAnalysis,
      bool enableAutoComplete,
      int preferredWordCount,
      bool includeReferences,
      bool includeImagesSuggestions});
}

/// @nodoc
class _$AIWritingPreferencesCopyWithImpl<$Res,
        $Val extends AIWritingPreferences>
    implements $AIWritingPreferencesCopyWith<$Res> {
  _$AIWritingPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? preferredTone = null,
    Object? preferredStyle = null,
    Object? targetAudience = null,
    Object? enableGrammarSuggestions = null,
    Object? enableSeoSuggestions = null,
    Object? enableContentGeneration = null,
    Object? enableReadabilityAnalysis = null,
    Object? enableAutoComplete = null,
    Object? preferredWordCount = null,
    Object? includeReferences = null,
    Object? includeImagesSuggestions = null,
  }) {
    return _then(_value.copyWith(
      preferredTone: null == preferredTone
          ? _value.preferredTone
          : preferredTone // ignore: cast_nullable_to_non_nullable
              as String,
      preferredStyle: null == preferredStyle
          ? _value.preferredStyle
          : preferredStyle // ignore: cast_nullable_to_non_nullable
              as String,
      targetAudience: null == targetAudience
          ? _value.targetAudience
          : targetAudience // ignore: cast_nullable_to_non_nullable
              as String,
      enableGrammarSuggestions: null == enableGrammarSuggestions
          ? _value.enableGrammarSuggestions
          : enableGrammarSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      enableSeoSuggestions: null == enableSeoSuggestions
          ? _value.enableSeoSuggestions
          : enableSeoSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      enableContentGeneration: null == enableContentGeneration
          ? _value.enableContentGeneration
          : enableContentGeneration // ignore: cast_nullable_to_non_nullable
              as bool,
      enableReadabilityAnalysis: null == enableReadabilityAnalysis
          ? _value.enableReadabilityAnalysis
          : enableReadabilityAnalysis // ignore: cast_nullable_to_non_nullable
              as bool,
      enableAutoComplete: null == enableAutoComplete
          ? _value.enableAutoComplete
          : enableAutoComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredWordCount: null == preferredWordCount
          ? _value.preferredWordCount
          : preferredWordCount // ignore: cast_nullable_to_non_nullable
              as int,
      includeReferences: null == includeReferences
          ? _value.includeReferences
          : includeReferences // ignore: cast_nullable_to_non_nullable
              as bool,
      includeImagesSuggestions: null == includeImagesSuggestions
          ? _value.includeImagesSuggestions
          : includeImagesSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AIWritingPreferencesImplCopyWith<$Res>
    implements $AIWritingPreferencesCopyWith<$Res> {
  factory _$$AIWritingPreferencesImplCopyWith(_$AIWritingPreferencesImpl value,
          $Res Function(_$AIWritingPreferencesImpl) then) =
      __$$AIWritingPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String preferredTone,
      String preferredStyle,
      String targetAudience,
      bool enableGrammarSuggestions,
      bool enableSeoSuggestions,
      bool enableContentGeneration,
      bool enableReadabilityAnalysis,
      bool enableAutoComplete,
      int preferredWordCount,
      bool includeReferences,
      bool includeImagesSuggestions});
}

/// @nodoc
class __$$AIWritingPreferencesImplCopyWithImpl<$Res>
    extends _$AIWritingPreferencesCopyWithImpl<$Res, _$AIWritingPreferencesImpl>
    implements _$$AIWritingPreferencesImplCopyWith<$Res> {
  __$$AIWritingPreferencesImplCopyWithImpl(_$AIWritingPreferencesImpl _value,
      $Res Function(_$AIWritingPreferencesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? preferredTone = null,
    Object? preferredStyle = null,
    Object? targetAudience = null,
    Object? enableGrammarSuggestions = null,
    Object? enableSeoSuggestions = null,
    Object? enableContentGeneration = null,
    Object? enableReadabilityAnalysis = null,
    Object? enableAutoComplete = null,
    Object? preferredWordCount = null,
    Object? includeReferences = null,
    Object? includeImagesSuggestions = null,
  }) {
    return _then(_$AIWritingPreferencesImpl(
      preferredTone: null == preferredTone
          ? _value.preferredTone
          : preferredTone // ignore: cast_nullable_to_non_nullable
              as String,
      preferredStyle: null == preferredStyle
          ? _value.preferredStyle
          : preferredStyle // ignore: cast_nullable_to_non_nullable
              as String,
      targetAudience: null == targetAudience
          ? _value.targetAudience
          : targetAudience // ignore: cast_nullable_to_non_nullable
              as String,
      enableGrammarSuggestions: null == enableGrammarSuggestions
          ? _value.enableGrammarSuggestions
          : enableGrammarSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      enableSeoSuggestions: null == enableSeoSuggestions
          ? _value.enableSeoSuggestions
          : enableSeoSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
      enableContentGeneration: null == enableContentGeneration
          ? _value.enableContentGeneration
          : enableContentGeneration // ignore: cast_nullable_to_non_nullable
              as bool,
      enableReadabilityAnalysis: null == enableReadabilityAnalysis
          ? _value.enableReadabilityAnalysis
          : enableReadabilityAnalysis // ignore: cast_nullable_to_non_nullable
              as bool,
      enableAutoComplete: null == enableAutoComplete
          ? _value.enableAutoComplete
          : enableAutoComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredWordCount: null == preferredWordCount
          ? _value.preferredWordCount
          : preferredWordCount // ignore: cast_nullable_to_non_nullable
              as int,
      includeReferences: null == includeReferences
          ? _value.includeReferences
          : includeReferences // ignore: cast_nullable_to_non_nullable
              as bool,
      includeImagesSuggestions: null == includeImagesSuggestions
          ? _value.includeImagesSuggestions
          : includeImagesSuggestions // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AIWritingPreferencesImpl implements _AIWritingPreferences {
  const _$AIWritingPreferencesImpl(
      {this.preferredTone = 'professional',
      this.preferredStyle = 'blog',
      this.targetAudience = 'general audience',
      this.enableGrammarSuggestions = true,
      this.enableSeoSuggestions = true,
      this.enableContentGeneration = true,
      this.enableReadabilityAnalysis = true,
      this.enableAutoComplete = true,
      this.preferredWordCount = 800,
      this.includeReferences = true,
      this.includeImagesSuggestions = true});

  factory _$AIWritingPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIWritingPreferencesImplFromJson(json);

  @override
  @JsonKey()
  final String preferredTone;
  @override
  @JsonKey()
  final String preferredStyle;
  @override
  @JsonKey()
  final String targetAudience;
  @override
  @JsonKey()
  final bool enableGrammarSuggestions;
  @override
  @JsonKey()
  final bool enableSeoSuggestions;
  @override
  @JsonKey()
  final bool enableContentGeneration;
  @override
  @JsonKey()
  final bool enableReadabilityAnalysis;
  @override
  @JsonKey()
  final bool enableAutoComplete;
  @override
  @JsonKey()
  final int preferredWordCount;
  @override
  @JsonKey()
  final bool includeReferences;
  @override
  @JsonKey()
  final bool includeImagesSuggestions;

  @override
  String toString() {
    return 'AIWritingPreferences(preferredTone: $preferredTone, preferredStyle: $preferredStyle, targetAudience: $targetAudience, enableGrammarSuggestions: $enableGrammarSuggestions, enableSeoSuggestions: $enableSeoSuggestions, enableContentGeneration: $enableContentGeneration, enableReadabilityAnalysis: $enableReadabilityAnalysis, enableAutoComplete: $enableAutoComplete, preferredWordCount: $preferredWordCount, includeReferences: $includeReferences, includeImagesSuggestions: $includeImagesSuggestions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIWritingPreferencesImpl &&
            (identical(other.preferredTone, preferredTone) ||
                other.preferredTone == preferredTone) &&
            (identical(other.preferredStyle, preferredStyle) ||
                other.preferredStyle == preferredStyle) &&
            (identical(other.targetAudience, targetAudience) ||
                other.targetAudience == targetAudience) &&
            (identical(
                    other.enableGrammarSuggestions, enableGrammarSuggestions) ||
                other.enableGrammarSuggestions == enableGrammarSuggestions) &&
            (identical(other.enableSeoSuggestions, enableSeoSuggestions) ||
                other.enableSeoSuggestions == enableSeoSuggestions) &&
            (identical(
                    other.enableContentGeneration, enableContentGeneration) ||
                other.enableContentGeneration == enableContentGeneration) &&
            (identical(other.enableReadabilityAnalysis,
                    enableReadabilityAnalysis) ||
                other.enableReadabilityAnalysis == enableReadabilityAnalysis) &&
            (identical(other.enableAutoComplete, enableAutoComplete) ||
                other.enableAutoComplete == enableAutoComplete) &&
            (identical(other.preferredWordCount, preferredWordCount) ||
                other.preferredWordCount == preferredWordCount) &&
            (identical(other.includeReferences, includeReferences) ||
                other.includeReferences == includeReferences) &&
            (identical(
                    other.includeImagesSuggestions, includeImagesSuggestions) ||
                other.includeImagesSuggestions == includeImagesSuggestions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      preferredTone,
      preferredStyle,
      targetAudience,
      enableGrammarSuggestions,
      enableSeoSuggestions,
      enableContentGeneration,
      enableReadabilityAnalysis,
      enableAutoComplete,
      preferredWordCount,
      includeReferences,
      includeImagesSuggestions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AIWritingPreferencesImplCopyWith<_$AIWritingPreferencesImpl>
      get copyWith =>
          __$$AIWritingPreferencesImplCopyWithImpl<_$AIWritingPreferencesImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIWritingPreferencesImplToJson(
      this,
    );
  }
}

abstract class _AIWritingPreferences implements AIWritingPreferences {
  const factory _AIWritingPreferences(
      {final String preferredTone,
      final String preferredStyle,
      final String targetAudience,
      final bool enableGrammarSuggestions,
      final bool enableSeoSuggestions,
      final bool enableContentGeneration,
      final bool enableReadabilityAnalysis,
      final bool enableAutoComplete,
      final int preferredWordCount,
      final bool includeReferences,
      final bool includeImagesSuggestions}) = _$AIWritingPreferencesImpl;

  factory _AIWritingPreferences.fromJson(Map<String, dynamic> json) =
      _$AIWritingPreferencesImpl.fromJson;

  @override
  String get preferredTone;
  @override
  String get preferredStyle;
  @override
  String get targetAudience;
  @override
  bool get enableGrammarSuggestions;
  @override
  bool get enableSeoSuggestions;
  @override
  bool get enableContentGeneration;
  @override
  bool get enableReadabilityAnalysis;
  @override
  bool get enableAutoComplete;
  @override
  int get preferredWordCount;
  @override
  bool get includeReferences;
  @override
  bool get includeImagesSuggestions;
  @override
  @JsonKey(ignore: true)
  _$$AIWritingPreferencesImplCopyWith<_$AIWritingPreferencesImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ContentIdea _$ContentIdeaFromJson(Map<String, dynamic> json) {
  return _ContentIdea.fromJson(json);
}

/// @nodoc
mixin _$ContentIdea {
  String get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentIdeaCopyWith<ContentIdea> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentIdeaCopyWith<$Res> {
  factory $ContentIdeaCopyWith(
          ContentIdea value, $Res Function(ContentIdea) then) =
      _$ContentIdeaCopyWithImpl<$Res, ContentIdea>;
  @useResult
  $Res call({String title, String? description, List<String>? tags});
}

/// @nodoc
class _$ContentIdeaCopyWithImpl<$Res, $Val extends ContentIdea>
    implements $ContentIdeaCopyWith<$Res> {
  _$ContentIdeaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = freezed,
    Object? tags = freezed,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentIdeaImplCopyWith<$Res>
    implements $ContentIdeaCopyWith<$Res> {
  factory _$$ContentIdeaImplCopyWith(
          _$ContentIdeaImpl value, $Res Function(_$ContentIdeaImpl) then) =
      __$$ContentIdeaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, String? description, List<String>? tags});
}

/// @nodoc
class __$$ContentIdeaImplCopyWithImpl<$Res>
    extends _$ContentIdeaCopyWithImpl<$Res, _$ContentIdeaImpl>
    implements _$$ContentIdeaImplCopyWith<$Res> {
  __$$ContentIdeaImplCopyWithImpl(
      _$ContentIdeaImpl _value, $Res Function(_$ContentIdeaImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? description = freezed,
    Object? tags = freezed,
  }) {
    return _then(_$ContentIdeaImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentIdeaImpl implements _ContentIdea {
  const _$ContentIdeaImpl(
      {required this.title, this.description, final List<String>? tags})
      : _tags = tags;

  factory _$ContentIdeaImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentIdeaImplFromJson(json);

  @override
  final String title;
  @override
  final String? description;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ContentIdea(title: $title, description: $description, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentIdeaImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, description,
      const DeepCollectionEquality().hash(_tags));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentIdeaImplCopyWith<_$ContentIdeaImpl> get copyWith =>
      __$$ContentIdeaImplCopyWithImpl<_$ContentIdeaImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentIdeaImplToJson(
      this,
    );
  }
}

abstract class _ContentIdea implements ContentIdea {
  const factory _ContentIdea(
      {required final String title,
      final String? description,
      final List<String>? tags}) = _$ContentIdeaImpl;

  factory _ContentIdea.fromJson(Map<String, dynamic> json) =
      _$ContentIdeaImpl.fromJson;

  @override
  String get title;
  @override
  String? get description;
  @override
  List<String>? get tags;
  @override
  @JsonKey(ignore: true)
  _$$ContentIdeaImplCopyWith<_$ContentIdeaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContentOutline _$ContentOutlineFromJson(Map<String, dynamic> json) {
  return _ContentOutline.fromJson(json);
}

/// @nodoc
mixin _$ContentOutline {
  List<String> get introduction => throw _privateConstructorUsedError;
  List<OutlineSection> get mainSections => throw _privateConstructorUsedError;
  List<String> get conclusion => throw _privateConstructorUsedError;
  int get estimatedWordCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentOutlineCopyWith<ContentOutline> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentOutlineCopyWith<$Res> {
  factory $ContentOutlineCopyWith(
          ContentOutline value, $Res Function(ContentOutline) then) =
      _$ContentOutlineCopyWithImpl<$Res, ContentOutline>;
  @useResult
  $Res call(
      {List<String> introduction,
      List<OutlineSection> mainSections,
      List<String> conclusion,
      int estimatedWordCount});
}

/// @nodoc
class _$ContentOutlineCopyWithImpl<$Res, $Val extends ContentOutline>
    implements $ContentOutlineCopyWith<$Res> {
  _$ContentOutlineCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? introduction = null,
    Object? mainSections = null,
    Object? conclusion = null,
    Object? estimatedWordCount = null,
  }) {
    return _then(_value.copyWith(
      introduction: null == introduction
          ? _value.introduction
          : introduction // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mainSections: null == mainSections
          ? _value.mainSections
          : mainSections // ignore: cast_nullable_to_non_nullable
              as List<OutlineSection>,
      conclusion: null == conclusion
          ? _value.conclusion
          : conclusion // ignore: cast_nullable_to_non_nullable
              as List<String>,
      estimatedWordCount: null == estimatedWordCount
          ? _value.estimatedWordCount
          : estimatedWordCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentOutlineImplCopyWith<$Res>
    implements $ContentOutlineCopyWith<$Res> {
  factory _$$ContentOutlineImplCopyWith(_$ContentOutlineImpl value,
          $Res Function(_$ContentOutlineImpl) then) =
      __$$ContentOutlineImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> introduction,
      List<OutlineSection> mainSections,
      List<String> conclusion,
      int estimatedWordCount});
}

/// @nodoc
class __$$ContentOutlineImplCopyWithImpl<$Res>
    extends _$ContentOutlineCopyWithImpl<$Res, _$ContentOutlineImpl>
    implements _$$ContentOutlineImplCopyWith<$Res> {
  __$$ContentOutlineImplCopyWithImpl(
      _$ContentOutlineImpl _value, $Res Function(_$ContentOutlineImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? introduction = null,
    Object? mainSections = null,
    Object? conclusion = null,
    Object? estimatedWordCount = null,
  }) {
    return _then(_$ContentOutlineImpl(
      introduction: null == introduction
          ? _value._introduction
          : introduction // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mainSections: null == mainSections
          ? _value._mainSections
          : mainSections // ignore: cast_nullable_to_non_nullable
              as List<OutlineSection>,
      conclusion: null == conclusion
          ? _value._conclusion
          : conclusion // ignore: cast_nullable_to_non_nullable
              as List<String>,
      estimatedWordCount: null == estimatedWordCount
          ? _value.estimatedWordCount
          : estimatedWordCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentOutlineImpl implements _ContentOutline {
  const _$ContentOutlineImpl(
      {required final List<String> introduction,
      required final List<OutlineSection> mainSections,
      required final List<String> conclusion,
      required this.estimatedWordCount})
      : _introduction = introduction,
        _mainSections = mainSections,
        _conclusion = conclusion;

  factory _$ContentOutlineImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentOutlineImplFromJson(json);

  final List<String> _introduction;
  @override
  List<String> get introduction {
    if (_introduction is EqualUnmodifiableListView) return _introduction;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_introduction);
  }

  final List<OutlineSection> _mainSections;
  @override
  List<OutlineSection> get mainSections {
    if (_mainSections is EqualUnmodifiableListView) return _mainSections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mainSections);
  }

  final List<String> _conclusion;
  @override
  List<String> get conclusion {
    if (_conclusion is EqualUnmodifiableListView) return _conclusion;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_conclusion);
  }

  @override
  final int estimatedWordCount;

  @override
  String toString() {
    return 'ContentOutline(introduction: $introduction, mainSections: $mainSections, conclusion: $conclusion, estimatedWordCount: $estimatedWordCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentOutlineImpl &&
            const DeepCollectionEquality()
                .equals(other._introduction, _introduction) &&
            const DeepCollectionEquality()
                .equals(other._mainSections, _mainSections) &&
            const DeepCollectionEquality()
                .equals(other._conclusion, _conclusion) &&
            (identical(other.estimatedWordCount, estimatedWordCount) ||
                other.estimatedWordCount == estimatedWordCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_introduction),
      const DeepCollectionEquality().hash(_mainSections),
      const DeepCollectionEquality().hash(_conclusion),
      estimatedWordCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentOutlineImplCopyWith<_$ContentOutlineImpl> get copyWith =>
      __$$ContentOutlineImplCopyWithImpl<_$ContentOutlineImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentOutlineImplToJson(
      this,
    );
  }
}

abstract class _ContentOutline implements ContentOutline {
  const factory _ContentOutline(
      {required final List<String> introduction,
      required final List<OutlineSection> mainSections,
      required final List<String> conclusion,
      required final int estimatedWordCount}) = _$ContentOutlineImpl;

  factory _ContentOutline.fromJson(Map<String, dynamic> json) =
      _$ContentOutlineImpl.fromJson;

  @override
  List<String> get introduction;
  @override
  List<OutlineSection> get mainSections;
  @override
  List<String> get conclusion;
  @override
  int get estimatedWordCount;
  @override
  @JsonKey(ignore: true)
  _$$ContentOutlineImplCopyWith<_$ContentOutlineImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OutlineSection _$OutlineSectionFromJson(Map<String, dynamic> json) {
  return _OutlineSection.fromJson(json);
}

/// @nodoc
mixin _$OutlineSection {
  String get title => throw _privateConstructorUsedError;
  List<String> get points => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OutlineSectionCopyWith<OutlineSection> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutlineSectionCopyWith<$Res> {
  factory $OutlineSectionCopyWith(
          OutlineSection value, $Res Function(OutlineSection) then) =
      _$OutlineSectionCopyWithImpl<$Res, OutlineSection>;
  @useResult
  $Res call({String title, List<String> points});
}

/// @nodoc
class _$OutlineSectionCopyWithImpl<$Res, $Val extends OutlineSection>
    implements $OutlineSectionCopyWith<$Res> {
  _$OutlineSectionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? points = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      points: null == points
          ? _value.points
          : points // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OutlineSectionImplCopyWith<$Res>
    implements $OutlineSectionCopyWith<$Res> {
  factory _$$OutlineSectionImplCopyWith(_$OutlineSectionImpl value,
          $Res Function(_$OutlineSectionImpl) then) =
      __$$OutlineSectionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title, List<String> points});
}

/// @nodoc
class __$$OutlineSectionImplCopyWithImpl<$Res>
    extends _$OutlineSectionCopyWithImpl<$Res, _$OutlineSectionImpl>
    implements _$$OutlineSectionImplCopyWith<$Res> {
  __$$OutlineSectionImplCopyWithImpl(
      _$OutlineSectionImpl _value, $Res Function(_$OutlineSectionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? points = null,
  }) {
    return _then(_$OutlineSectionImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      points: null == points
          ? _value._points
          : points // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OutlineSectionImpl implements _OutlineSection {
  const _$OutlineSectionImpl(
      {required this.title, required final List<String> points})
      : _points = points;

  factory _$OutlineSectionImpl.fromJson(Map<String, dynamic> json) =>
      _$$OutlineSectionImplFromJson(json);

  @override
  final String title;
  final List<String> _points;
  @override
  List<String> get points {
    if (_points is EqualUnmodifiableListView) return _points;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_points);
  }

  @override
  String toString() {
    return 'OutlineSection(title: $title, points: $points)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OutlineSectionImpl &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality().equals(other._points, _points));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, title, const DeepCollectionEquality().hash(_points));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OutlineSectionImplCopyWith<_$OutlineSectionImpl> get copyWith =>
      __$$OutlineSectionImplCopyWithImpl<_$OutlineSectionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OutlineSectionImplToJson(
      this,
    );
  }
}

abstract class _OutlineSection implements OutlineSection {
  const factory _OutlineSection(
      {required final String title,
      required final List<String> points}) = _$OutlineSectionImpl;

  factory _OutlineSection.fromJson(Map<String, dynamic> json) =
      _$OutlineSectionImpl.fromJson;

  @override
  String get title;
  @override
  List<String> get points;
  @override
  @JsonKey(ignore: true)
  _$$OutlineSectionImplCopyWith<_$OutlineSectionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GrammarImprovement _$GrammarImprovementFromJson(Map<String, dynamic> json) {
  return _GrammarImprovement.fromJson(json);
}

/// @nodoc
mixin _$GrammarImprovement {
  String get improvedText => throw _privateConstructorUsedError;
  List<TextChange> get changes => throw _privateConstructorUsedError;
  double get readabilityScore => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrammarImprovementCopyWith<GrammarImprovement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrammarImprovementCopyWith<$Res> {
  factory $GrammarImprovementCopyWith(
          GrammarImprovement value, $Res Function(GrammarImprovement) then) =
      _$GrammarImprovementCopyWithImpl<$Res, GrammarImprovement>;
  @useResult
  $Res call(
      {String improvedText, List<TextChange> changes, double readabilityScore});
}

/// @nodoc
class _$GrammarImprovementCopyWithImpl<$Res, $Val extends GrammarImprovement>
    implements $GrammarImprovementCopyWith<$Res> {
  _$GrammarImprovementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? improvedText = null,
    Object? changes = null,
    Object? readabilityScore = null,
  }) {
    return _then(_value.copyWith(
      improvedText: null == improvedText
          ? _value.improvedText
          : improvedText // ignore: cast_nullable_to_non_nullable
              as String,
      changes: null == changes
          ? _value.changes
          : changes // ignore: cast_nullable_to_non_nullable
              as List<TextChange>,
      readabilityScore: null == readabilityScore
          ? _value.readabilityScore
          : readabilityScore // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GrammarImprovementImplCopyWith<$Res>
    implements $GrammarImprovementCopyWith<$Res> {
  factory _$$GrammarImprovementImplCopyWith(_$GrammarImprovementImpl value,
          $Res Function(_$GrammarImprovementImpl) then) =
      __$$GrammarImprovementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String improvedText, List<TextChange> changes, double readabilityScore});
}

/// @nodoc
class __$$GrammarImprovementImplCopyWithImpl<$Res>
    extends _$GrammarImprovementCopyWithImpl<$Res, _$GrammarImprovementImpl>
    implements _$$GrammarImprovementImplCopyWith<$Res> {
  __$$GrammarImprovementImplCopyWithImpl(_$GrammarImprovementImpl _value,
      $Res Function(_$GrammarImprovementImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? improvedText = null,
    Object? changes = null,
    Object? readabilityScore = null,
  }) {
    return _then(_$GrammarImprovementImpl(
      improvedText: null == improvedText
          ? _value.improvedText
          : improvedText // ignore: cast_nullable_to_non_nullable
              as String,
      changes: null == changes
          ? _value._changes
          : changes // ignore: cast_nullable_to_non_nullable
              as List<TextChange>,
      readabilityScore: null == readabilityScore
          ? _value.readabilityScore
          : readabilityScore // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GrammarImprovementImpl implements _GrammarImprovement {
  const _$GrammarImprovementImpl(
      {required this.improvedText,
      required final List<TextChange> changes,
      required this.readabilityScore})
      : _changes = changes;

  factory _$GrammarImprovementImpl.fromJson(Map<String, dynamic> json) =>
      _$$GrammarImprovementImplFromJson(json);

  @override
  final String improvedText;
  final List<TextChange> _changes;
  @override
  List<TextChange> get changes {
    if (_changes is EqualUnmodifiableListView) return _changes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_changes);
  }

  @override
  final double readabilityScore;

  @override
  String toString() {
    return 'GrammarImprovement(improvedText: $improvedText, changes: $changes, readabilityScore: $readabilityScore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GrammarImprovementImpl &&
            (identical(other.improvedText, improvedText) ||
                other.improvedText == improvedText) &&
            const DeepCollectionEquality().equals(other._changes, _changes) &&
            (identical(other.readabilityScore, readabilityScore) ||
                other.readabilityScore == readabilityScore));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, improvedText,
      const DeepCollectionEquality().hash(_changes), readabilityScore);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GrammarImprovementImplCopyWith<_$GrammarImprovementImpl> get copyWith =>
      __$$GrammarImprovementImplCopyWithImpl<_$GrammarImprovementImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GrammarImprovementImplToJson(
      this,
    );
  }
}

abstract class _GrammarImprovement implements GrammarImprovement {
  const factory _GrammarImprovement(
      {required final String improvedText,
      required final List<TextChange> changes,
      required final double readabilityScore}) = _$GrammarImprovementImpl;

  factory _GrammarImprovement.fromJson(Map<String, dynamic> json) =
      _$GrammarImprovementImpl.fromJson;

  @override
  String get improvedText;
  @override
  List<TextChange> get changes;
  @override
  double get readabilityScore;
  @override
  @JsonKey(ignore: true)
  _$$GrammarImprovementImplCopyWith<_$GrammarImprovementImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TextChange _$TextChangeFromJson(Map<String, dynamic> json) {
  return _TextChange.fromJson(json);
}

/// @nodoc
mixin _$TextChange {
  String get original => throw _privateConstructorUsedError;
  String get improved => throw _privateConstructorUsedError;
  String get reason => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TextChangeCopyWith<TextChange> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TextChangeCopyWith<$Res> {
  factory $TextChangeCopyWith(
          TextChange value, $Res Function(TextChange) then) =
      _$TextChangeCopyWithImpl<$Res, TextChange>;
  @useResult
  $Res call({String original, String improved, String reason});
}

/// @nodoc
class _$TextChangeCopyWithImpl<$Res, $Val extends TextChange>
    implements $TextChangeCopyWith<$Res> {
  _$TextChangeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? original = null,
    Object? improved = null,
    Object? reason = null,
  }) {
    return _then(_value.copyWith(
      original: null == original
          ? _value.original
          : original // ignore: cast_nullable_to_non_nullable
              as String,
      improved: null == improved
          ? _value.improved
          : improved // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TextChangeImplCopyWith<$Res>
    implements $TextChangeCopyWith<$Res> {
  factory _$$TextChangeImplCopyWith(
          _$TextChangeImpl value, $Res Function(_$TextChangeImpl) then) =
      __$$TextChangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String original, String improved, String reason});
}

/// @nodoc
class __$$TextChangeImplCopyWithImpl<$Res>
    extends _$TextChangeCopyWithImpl<$Res, _$TextChangeImpl>
    implements _$$TextChangeImplCopyWith<$Res> {
  __$$TextChangeImplCopyWithImpl(
      _$TextChangeImpl _value, $Res Function(_$TextChangeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? original = null,
    Object? improved = null,
    Object? reason = null,
  }) {
    return _then(_$TextChangeImpl(
      original: null == original
          ? _value.original
          : original // ignore: cast_nullable_to_non_nullable
              as String,
      improved: null == improved
          ? _value.improved
          : improved // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TextChangeImpl implements _TextChange {
  const _$TextChangeImpl(
      {required this.original, required this.improved, required this.reason});

  factory _$TextChangeImpl.fromJson(Map<String, dynamic> json) =>
      _$$TextChangeImplFromJson(json);

  @override
  final String original;
  @override
  final String improved;
  @override
  final String reason;

  @override
  String toString() {
    return 'TextChange(original: $original, improved: $improved, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TextChangeImpl &&
            (identical(other.original, original) ||
                other.original == original) &&
            (identical(other.improved, improved) ||
                other.improved == improved) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, original, improved, reason);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TextChangeImplCopyWith<_$TextChangeImpl> get copyWith =>
      __$$TextChangeImplCopyWithImpl<_$TextChangeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TextChangeImplToJson(
      this,
    );
  }
}

abstract class _TextChange implements TextChange {
  const factory _TextChange(
      {required final String original,
      required final String improved,
      required final String reason}) = _$TextChangeImpl;

  factory _TextChange.fromJson(Map<String, dynamic> json) =
      _$TextChangeImpl.fromJson;

  @override
  String get original;
  @override
  String get improved;
  @override
  String get reason;
  @override
  @JsonKey(ignore: true)
  _$$TextChangeImplCopyWith<_$TextChangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SEOSuggestions _$SEOSuggestionsFromJson(Map<String, dynamic> json) {
  return _SEOSuggestions.fromJson(json);
}

/// @nodoc
mixin _$SEOSuggestions {
  List<String> get titleSuggestions => throw _privateConstructorUsedError;
  String get metaDescription => throw _privateConstructorUsedError;
  List<String> get keywords => throw _privateConstructorUsedError;
  List<String> get contentSuggestions => throw _privateConstructorUsedError;
  List<String> get readabilityIssues => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SEOSuggestionsCopyWith<SEOSuggestions> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SEOSuggestionsCopyWith<$Res> {
  factory $SEOSuggestionsCopyWith(
          SEOSuggestions value, $Res Function(SEOSuggestions) then) =
      _$SEOSuggestionsCopyWithImpl<$Res, SEOSuggestions>;
  @useResult
  $Res call(
      {List<String> titleSuggestions,
      String metaDescription,
      List<String> keywords,
      List<String> contentSuggestions,
      List<String> readabilityIssues});
}

/// @nodoc
class _$SEOSuggestionsCopyWithImpl<$Res, $Val extends SEOSuggestions>
    implements $SEOSuggestionsCopyWith<$Res> {
  _$SEOSuggestionsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? titleSuggestions = null,
    Object? metaDescription = null,
    Object? keywords = null,
    Object? contentSuggestions = null,
    Object? readabilityIssues = null,
  }) {
    return _then(_value.copyWith(
      titleSuggestions: null == titleSuggestions
          ? _value.titleSuggestions
          : titleSuggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      metaDescription: null == metaDescription
          ? _value.metaDescription
          : metaDescription // ignore: cast_nullable_to_non_nullable
              as String,
      keywords: null == keywords
          ? _value.keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as List<String>,
      contentSuggestions: null == contentSuggestions
          ? _value.contentSuggestions
          : contentSuggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      readabilityIssues: null == readabilityIssues
          ? _value.readabilityIssues
          : readabilityIssues // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SEOSuggestionsImplCopyWith<$Res>
    implements $SEOSuggestionsCopyWith<$Res> {
  factory _$$SEOSuggestionsImplCopyWith(_$SEOSuggestionsImpl value,
          $Res Function(_$SEOSuggestionsImpl) then) =
      __$$SEOSuggestionsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> titleSuggestions,
      String metaDescription,
      List<String> keywords,
      List<String> contentSuggestions,
      List<String> readabilityIssues});
}

/// @nodoc
class __$$SEOSuggestionsImplCopyWithImpl<$Res>
    extends _$SEOSuggestionsCopyWithImpl<$Res, _$SEOSuggestionsImpl>
    implements _$$SEOSuggestionsImplCopyWith<$Res> {
  __$$SEOSuggestionsImplCopyWithImpl(
      _$SEOSuggestionsImpl _value, $Res Function(_$SEOSuggestionsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? titleSuggestions = null,
    Object? metaDescription = null,
    Object? keywords = null,
    Object? contentSuggestions = null,
    Object? readabilityIssues = null,
  }) {
    return _then(_$SEOSuggestionsImpl(
      titleSuggestions: null == titleSuggestions
          ? _value._titleSuggestions
          : titleSuggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      metaDescription: null == metaDescription
          ? _value.metaDescription
          : metaDescription // ignore: cast_nullable_to_non_nullable
              as String,
      keywords: null == keywords
          ? _value._keywords
          : keywords // ignore: cast_nullable_to_non_nullable
              as List<String>,
      contentSuggestions: null == contentSuggestions
          ? _value._contentSuggestions
          : contentSuggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      readabilityIssues: null == readabilityIssues
          ? _value._readabilityIssues
          : readabilityIssues // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SEOSuggestionsImpl implements _SEOSuggestions {
  const _$SEOSuggestionsImpl(
      {required final List<String> titleSuggestions,
      required this.metaDescription,
      required final List<String> keywords,
      required final List<String> contentSuggestions,
      required final List<String> readabilityIssues})
      : _titleSuggestions = titleSuggestions,
        _keywords = keywords,
        _contentSuggestions = contentSuggestions,
        _readabilityIssues = readabilityIssues;

  factory _$SEOSuggestionsImpl.fromJson(Map<String, dynamic> json) =>
      _$$SEOSuggestionsImplFromJson(json);

  final List<String> _titleSuggestions;
  @override
  List<String> get titleSuggestions {
    if (_titleSuggestions is EqualUnmodifiableListView)
      return _titleSuggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_titleSuggestions);
  }

  @override
  final String metaDescription;
  final List<String> _keywords;
  @override
  List<String> get keywords {
    if (_keywords is EqualUnmodifiableListView) return _keywords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_keywords);
  }

  final List<String> _contentSuggestions;
  @override
  List<String> get contentSuggestions {
    if (_contentSuggestions is EqualUnmodifiableListView)
      return _contentSuggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contentSuggestions);
  }

  final List<String> _readabilityIssues;
  @override
  List<String> get readabilityIssues {
    if (_readabilityIssues is EqualUnmodifiableListView)
      return _readabilityIssues;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_readabilityIssues);
  }

  @override
  String toString() {
    return 'SEOSuggestions(titleSuggestions: $titleSuggestions, metaDescription: $metaDescription, keywords: $keywords, contentSuggestions: $contentSuggestions, readabilityIssues: $readabilityIssues)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SEOSuggestionsImpl &&
            const DeepCollectionEquality()
                .equals(other._titleSuggestions, _titleSuggestions) &&
            (identical(other.metaDescription, metaDescription) ||
                other.metaDescription == metaDescription) &&
            const DeepCollectionEquality().equals(other._keywords, _keywords) &&
            const DeepCollectionEquality()
                .equals(other._contentSuggestions, _contentSuggestions) &&
            const DeepCollectionEquality()
                .equals(other._readabilityIssues, _readabilityIssues));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_titleSuggestions),
      metaDescription,
      const DeepCollectionEquality().hash(_keywords),
      const DeepCollectionEquality().hash(_contentSuggestions),
      const DeepCollectionEquality().hash(_readabilityIssues));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SEOSuggestionsImplCopyWith<_$SEOSuggestionsImpl> get copyWith =>
      __$$SEOSuggestionsImplCopyWithImpl<_$SEOSuggestionsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SEOSuggestionsImplToJson(
      this,
    );
  }
}

abstract class _SEOSuggestions implements SEOSuggestions {
  const factory _SEOSuggestions(
      {required final List<String> titleSuggestions,
      required final String metaDescription,
      required final List<String> keywords,
      required final List<String> contentSuggestions,
      required final List<String> readabilityIssues}) = _$SEOSuggestionsImpl;

  factory _SEOSuggestions.fromJson(Map<String, dynamic> json) =
      _$SEOSuggestionsImpl.fromJson;

  @override
  List<String> get titleSuggestions;
  @override
  String get metaDescription;
  @override
  List<String> get keywords;
  @override
  List<String> get contentSuggestions;
  @override
  List<String> get readabilityIssues;
  @override
  @JsonKey(ignore: true)
  _$$SEOSuggestionsImplCopyWith<_$SEOSuggestionsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReadabilityAnalysis _$ReadabilityAnalysisFromJson(Map<String, dynamic> json) {
  return _ReadabilityAnalysis.fromJson(json);
}

/// @nodoc
mixin _$ReadabilityAnalysis {
  int get wordCount => throw _privateConstructorUsedError;
  int get sentenceCount => throw _privateConstructorUsedError;
  int get paragraphCount => throw _privateConstructorUsedError;
  double get avgWordsPerSentence => throw _privateConstructorUsedError;
  double get avgSentencesPerParagraph => throw _privateConstructorUsedError;
  double get readabilityScore => throw _privateConstructorUsedError;
  String get readingLevel => throw _privateConstructorUsedError;
  int get estimatedReadingTime => throw _privateConstructorUsedError;
  List<String> get suggestions => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReadabilityAnalysisCopyWith<ReadabilityAnalysis> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReadabilityAnalysisCopyWith<$Res> {
  factory $ReadabilityAnalysisCopyWith(
          ReadabilityAnalysis value, $Res Function(ReadabilityAnalysis) then) =
      _$ReadabilityAnalysisCopyWithImpl<$Res, ReadabilityAnalysis>;
  @useResult
  $Res call(
      {int wordCount,
      int sentenceCount,
      int paragraphCount,
      double avgWordsPerSentence,
      double avgSentencesPerParagraph,
      double readabilityScore,
      String readingLevel,
      int estimatedReadingTime,
      List<String> suggestions});
}

/// @nodoc
class _$ReadabilityAnalysisCopyWithImpl<$Res, $Val extends ReadabilityAnalysis>
    implements $ReadabilityAnalysisCopyWith<$Res> {
  _$ReadabilityAnalysisCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wordCount = null,
    Object? sentenceCount = null,
    Object? paragraphCount = null,
    Object? avgWordsPerSentence = null,
    Object? avgSentencesPerParagraph = null,
    Object? readabilityScore = null,
    Object? readingLevel = null,
    Object? estimatedReadingTime = null,
    Object? suggestions = null,
  }) {
    return _then(_value.copyWith(
      wordCount: null == wordCount
          ? _value.wordCount
          : wordCount // ignore: cast_nullable_to_non_nullable
              as int,
      sentenceCount: null == sentenceCount
          ? _value.sentenceCount
          : sentenceCount // ignore: cast_nullable_to_non_nullable
              as int,
      paragraphCount: null == paragraphCount
          ? _value.paragraphCount
          : paragraphCount // ignore: cast_nullable_to_non_nullable
              as int,
      avgWordsPerSentence: null == avgWordsPerSentence
          ? _value.avgWordsPerSentence
          : avgWordsPerSentence // ignore: cast_nullable_to_non_nullable
              as double,
      avgSentencesPerParagraph: null == avgSentencesPerParagraph
          ? _value.avgSentencesPerParagraph
          : avgSentencesPerParagraph // ignore: cast_nullable_to_non_nullable
              as double,
      readabilityScore: null == readabilityScore
          ? _value.readabilityScore
          : readabilityScore // ignore: cast_nullable_to_non_nullable
              as double,
      readingLevel: null == readingLevel
          ? _value.readingLevel
          : readingLevel // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedReadingTime: null == estimatedReadingTime
          ? _value.estimatedReadingTime
          : estimatedReadingTime // ignore: cast_nullable_to_non_nullable
              as int,
      suggestions: null == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReadabilityAnalysisImplCopyWith<$Res>
    implements $ReadabilityAnalysisCopyWith<$Res> {
  factory _$$ReadabilityAnalysisImplCopyWith(_$ReadabilityAnalysisImpl value,
          $Res Function(_$ReadabilityAnalysisImpl) then) =
      __$$ReadabilityAnalysisImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int wordCount,
      int sentenceCount,
      int paragraphCount,
      double avgWordsPerSentence,
      double avgSentencesPerParagraph,
      double readabilityScore,
      String readingLevel,
      int estimatedReadingTime,
      List<String> suggestions});
}

/// @nodoc
class __$$ReadabilityAnalysisImplCopyWithImpl<$Res>
    extends _$ReadabilityAnalysisCopyWithImpl<$Res, _$ReadabilityAnalysisImpl>
    implements _$$ReadabilityAnalysisImplCopyWith<$Res> {
  __$$ReadabilityAnalysisImplCopyWithImpl(_$ReadabilityAnalysisImpl _value,
      $Res Function(_$ReadabilityAnalysisImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wordCount = null,
    Object? sentenceCount = null,
    Object? paragraphCount = null,
    Object? avgWordsPerSentence = null,
    Object? avgSentencesPerParagraph = null,
    Object? readabilityScore = null,
    Object? readingLevel = null,
    Object? estimatedReadingTime = null,
    Object? suggestions = null,
  }) {
    return _then(_$ReadabilityAnalysisImpl(
      wordCount: null == wordCount
          ? _value.wordCount
          : wordCount // ignore: cast_nullable_to_non_nullable
              as int,
      sentenceCount: null == sentenceCount
          ? _value.sentenceCount
          : sentenceCount // ignore: cast_nullable_to_non_nullable
              as int,
      paragraphCount: null == paragraphCount
          ? _value.paragraphCount
          : paragraphCount // ignore: cast_nullable_to_non_nullable
              as int,
      avgWordsPerSentence: null == avgWordsPerSentence
          ? _value.avgWordsPerSentence
          : avgWordsPerSentence // ignore: cast_nullable_to_non_nullable
              as double,
      avgSentencesPerParagraph: null == avgSentencesPerParagraph
          ? _value.avgSentencesPerParagraph
          : avgSentencesPerParagraph // ignore: cast_nullable_to_non_nullable
              as double,
      readabilityScore: null == readabilityScore
          ? _value.readabilityScore
          : readabilityScore // ignore: cast_nullable_to_non_nullable
              as double,
      readingLevel: null == readingLevel
          ? _value.readingLevel
          : readingLevel // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedReadingTime: null == estimatedReadingTime
          ? _value.estimatedReadingTime
          : estimatedReadingTime // ignore: cast_nullable_to_non_nullable
              as int,
      suggestions: null == suggestions
          ? _value._suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReadabilityAnalysisImpl implements _ReadabilityAnalysis {
  const _$ReadabilityAnalysisImpl(
      {required this.wordCount,
      required this.sentenceCount,
      required this.paragraphCount,
      required this.avgWordsPerSentence,
      required this.avgSentencesPerParagraph,
      required this.readabilityScore,
      required this.readingLevel,
      required this.estimatedReadingTime,
      required final List<String> suggestions})
      : _suggestions = suggestions;

  factory _$ReadabilityAnalysisImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReadabilityAnalysisImplFromJson(json);

  @override
  final int wordCount;
  @override
  final int sentenceCount;
  @override
  final int paragraphCount;
  @override
  final double avgWordsPerSentence;
  @override
  final double avgSentencesPerParagraph;
  @override
  final double readabilityScore;
  @override
  final String readingLevel;
  @override
  final int estimatedReadingTime;
  final List<String> _suggestions;
  @override
  List<String> get suggestions {
    if (_suggestions is EqualUnmodifiableListView) return _suggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestions);
  }

  @override
  String toString() {
    return 'ReadabilityAnalysis(wordCount: $wordCount, sentenceCount: $sentenceCount, paragraphCount: $paragraphCount, avgWordsPerSentence: $avgWordsPerSentence, avgSentencesPerParagraph: $avgSentencesPerParagraph, readabilityScore: $readabilityScore, readingLevel: $readingLevel, estimatedReadingTime: $estimatedReadingTime, suggestions: $suggestions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReadabilityAnalysisImpl &&
            (identical(other.wordCount, wordCount) ||
                other.wordCount == wordCount) &&
            (identical(other.sentenceCount, sentenceCount) ||
                other.sentenceCount == sentenceCount) &&
            (identical(other.paragraphCount, paragraphCount) ||
                other.paragraphCount == paragraphCount) &&
            (identical(other.avgWordsPerSentence, avgWordsPerSentence) ||
                other.avgWordsPerSentence == avgWordsPerSentence) &&
            (identical(
                    other.avgSentencesPerParagraph, avgSentencesPerParagraph) ||
                other.avgSentencesPerParagraph == avgSentencesPerParagraph) &&
            (identical(other.readabilityScore, readabilityScore) ||
                other.readabilityScore == readabilityScore) &&
            (identical(other.readingLevel, readingLevel) ||
                other.readingLevel == readingLevel) &&
            (identical(other.estimatedReadingTime, estimatedReadingTime) ||
                other.estimatedReadingTime == estimatedReadingTime) &&
            const DeepCollectionEquality()
                .equals(other._suggestions, _suggestions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      wordCount,
      sentenceCount,
      paragraphCount,
      avgWordsPerSentence,
      avgSentencesPerParagraph,
      readabilityScore,
      readingLevel,
      estimatedReadingTime,
      const DeepCollectionEquality().hash(_suggestions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReadabilityAnalysisImplCopyWith<_$ReadabilityAnalysisImpl> get copyWith =>
      __$$ReadabilityAnalysisImplCopyWithImpl<_$ReadabilityAnalysisImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReadabilityAnalysisImplToJson(
      this,
    );
  }
}

abstract class _ReadabilityAnalysis implements ReadabilityAnalysis {
  const factory _ReadabilityAnalysis(
      {required final int wordCount,
      required final int sentenceCount,
      required final int paragraphCount,
      required final double avgWordsPerSentence,
      required final double avgSentencesPerParagraph,
      required final double readabilityScore,
      required final String readingLevel,
      required final int estimatedReadingTime,
      required final List<String> suggestions}) = _$ReadabilityAnalysisImpl;

  factory _ReadabilityAnalysis.fromJson(Map<String, dynamic> json) =
      _$ReadabilityAnalysisImpl.fromJson;

  @override
  int get wordCount;
  @override
  int get sentenceCount;
  @override
  int get paragraphCount;
  @override
  double get avgWordsPerSentence;
  @override
  double get avgSentencesPerParagraph;
  @override
  double get readabilityScore;
  @override
  String get readingLevel;
  @override
  int get estimatedReadingTime;
  @override
  List<String> get suggestions;
  @override
  @JsonKey(ignore: true)
  _$$ReadabilityAnalysisImplCopyWith<_$ReadabilityAnalysisImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIWritingSession _$AIWritingSessionFromJson(Map<String, dynamic> json) {
  return _AIWritingSession.fromJson(json);
}

/// @nodoc
mixin _$AIWritingSession {
  String get id => throw _privateConstructorUsedError;
  String? get postId => throw _privateConstructorUsedError;
  Map<String, dynamic> get sessionData => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  int get suggestionsGenerated => throw _privateConstructorUsedError;
  int get suggestionsAccepted => throw _privateConstructorUsedError;
  int get wordsGenerated => throw _privateConstructorUsedError;
  int get timeSavedMinutes => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AIWritingSessionCopyWith<AIWritingSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIWritingSessionCopyWith<$Res> {
  factory $AIWritingSessionCopyWith(
          AIWritingSession value, $Res Function(AIWritingSession) then) =
      _$AIWritingSessionCopyWithImpl<$Res, AIWritingSession>;
  @useResult
  $Res call(
      {String id,
      String? postId,
      Map<String, dynamic> sessionData,
      String status,
      int suggestionsGenerated,
      int suggestionsAccepted,
      int wordsGenerated,
      int timeSavedMinutes,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$AIWritingSessionCopyWithImpl<$Res, $Val extends AIWritingSession>
    implements $AIWritingSessionCopyWith<$Res> {
  _$AIWritingSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postId = freezed,
    Object? sessionData = null,
    Object? status = null,
    Object? suggestionsGenerated = null,
    Object? suggestionsAccepted = null,
    Object? wordsGenerated = null,
    Object? timeSavedMinutes = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      postId: freezed == postId
          ? _value.postId
          : postId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionData: null == sessionData
          ? _value.sessionData
          : sessionData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      suggestionsGenerated: null == suggestionsGenerated
          ? _value.suggestionsGenerated
          : suggestionsGenerated // ignore: cast_nullable_to_non_nullable
              as int,
      suggestionsAccepted: null == suggestionsAccepted
          ? _value.suggestionsAccepted
          : suggestionsAccepted // ignore: cast_nullable_to_non_nullable
              as int,
      wordsGenerated: null == wordsGenerated
          ? _value.wordsGenerated
          : wordsGenerated // ignore: cast_nullable_to_non_nullable
              as int,
      timeSavedMinutes: null == timeSavedMinutes
          ? _value.timeSavedMinutes
          : timeSavedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AIWritingSessionImplCopyWith<$Res>
    implements $AIWritingSessionCopyWith<$Res> {
  factory _$$AIWritingSessionImplCopyWith(_$AIWritingSessionImpl value,
          $Res Function(_$AIWritingSessionImpl) then) =
      __$$AIWritingSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? postId,
      Map<String, dynamic> sessionData,
      String status,
      int suggestionsGenerated,
      int suggestionsAccepted,
      int wordsGenerated,
      int timeSavedMinutes,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$AIWritingSessionImplCopyWithImpl<$Res>
    extends _$AIWritingSessionCopyWithImpl<$Res, _$AIWritingSessionImpl>
    implements _$$AIWritingSessionImplCopyWith<$Res> {
  __$$AIWritingSessionImplCopyWithImpl(_$AIWritingSessionImpl _value,
      $Res Function(_$AIWritingSessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postId = freezed,
    Object? sessionData = null,
    Object? status = null,
    Object? suggestionsGenerated = null,
    Object? suggestionsAccepted = null,
    Object? wordsGenerated = null,
    Object? timeSavedMinutes = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$AIWritingSessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      postId: freezed == postId
          ? _value.postId
          : postId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionData: null == sessionData
          ? _value._sessionData
          : sessionData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      suggestionsGenerated: null == suggestionsGenerated
          ? _value.suggestionsGenerated
          : suggestionsGenerated // ignore: cast_nullable_to_non_nullable
              as int,
      suggestionsAccepted: null == suggestionsAccepted
          ? _value.suggestionsAccepted
          : suggestionsAccepted // ignore: cast_nullable_to_non_nullable
              as int,
      wordsGenerated: null == wordsGenerated
          ? _value.wordsGenerated
          : wordsGenerated // ignore: cast_nullable_to_non_nullable
              as int,
      timeSavedMinutes: null == timeSavedMinutes
          ? _value.timeSavedMinutes
          : timeSavedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AIWritingSessionImpl implements _AIWritingSession {
  const _$AIWritingSessionImpl(
      {required this.id,
      this.postId,
      required final Map<String, dynamic> sessionData,
      this.status = 'active',
      this.suggestionsGenerated = 0,
      this.suggestionsAccepted = 0,
      this.wordsGenerated = 0,
      this.timeSavedMinutes = 0,
      required this.createdAt,
      required this.updatedAt})
      : _sessionData = sessionData;

  factory _$AIWritingSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIWritingSessionImplFromJson(json);

  @override
  final String id;
  @override
  final String? postId;
  final Map<String, dynamic> _sessionData;
  @override
  Map<String, dynamic> get sessionData {
    if (_sessionData is EqualUnmodifiableMapView) return _sessionData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_sessionData);
  }

  @override
  @JsonKey()
  final String status;
  @override
  @JsonKey()
  final int suggestionsGenerated;
  @override
  @JsonKey()
  final int suggestionsAccepted;
  @override
  @JsonKey()
  final int wordsGenerated;
  @override
  @JsonKey()
  final int timeSavedMinutes;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'AIWritingSession(id: $id, postId: $postId, sessionData: $sessionData, status: $status, suggestionsGenerated: $suggestionsGenerated, suggestionsAccepted: $suggestionsAccepted, wordsGenerated: $wordsGenerated, timeSavedMinutes: $timeSavedMinutes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIWritingSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.postId, postId) || other.postId == postId) &&
            const DeepCollectionEquality()
                .equals(other._sessionData, _sessionData) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.suggestionsGenerated, suggestionsGenerated) ||
                other.suggestionsGenerated == suggestionsGenerated) &&
            (identical(other.suggestionsAccepted, suggestionsAccepted) ||
                other.suggestionsAccepted == suggestionsAccepted) &&
            (identical(other.wordsGenerated, wordsGenerated) ||
                other.wordsGenerated == wordsGenerated) &&
            (identical(other.timeSavedMinutes, timeSavedMinutes) ||
                other.timeSavedMinutes == timeSavedMinutes) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      postId,
      const DeepCollectionEquality().hash(_sessionData),
      status,
      suggestionsGenerated,
      suggestionsAccepted,
      wordsGenerated,
      timeSavedMinutes,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AIWritingSessionImplCopyWith<_$AIWritingSessionImpl> get copyWith =>
      __$$AIWritingSessionImplCopyWithImpl<_$AIWritingSessionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIWritingSessionImplToJson(
      this,
    );
  }
}

abstract class _AIWritingSession implements AIWritingSession {
  const factory _AIWritingSession(
      {required final String id,
      final String? postId,
      required final Map<String, dynamic> sessionData,
      final String status,
      final int suggestionsGenerated,
      final int suggestionsAccepted,
      final int wordsGenerated,
      final int timeSavedMinutes,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$AIWritingSessionImpl;

  factory _AIWritingSession.fromJson(Map<String, dynamic> json) =
      _$AIWritingSessionImpl.fromJson;

  @override
  String get id;
  @override
  String? get postId;
  @override
  Map<String, dynamic> get sessionData;
  @override
  String get status;
  @override
  int get suggestionsGenerated;
  @override
  int get suggestionsAccepted;
  @override
  int get wordsGenerated;
  @override
  int get timeSavedMinutes;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$AIWritingSessionImplCopyWith<_$AIWritingSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContentSuggestion _$ContentSuggestionFromJson(Map<String, dynamic> json) {
  return _ContentSuggestion.fromJson(json);
}

/// @nodoc
mixin _$ContentSuggestion {
  String get id => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  String get suggestionType => throw _privateConstructorUsedError;
  String get originalText => throw _privateConstructorUsedError;
  String get suggestedText => throw _privateConstructorUsedError;
  String get explanation => throw _privateConstructorUsedError;
  double get confidenceScore => throw _privateConstructorUsedError;
  int? get startPosition => throw _privateConstructorUsedError;
  int? get endPosition => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String? get userFeedback => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentSuggestionCopyWith<ContentSuggestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentSuggestionCopyWith<$Res> {
  factory $ContentSuggestionCopyWith(
          ContentSuggestion value, $Res Function(ContentSuggestion) then) =
      _$ContentSuggestionCopyWithImpl<$Res, ContentSuggestion>;
  @useResult
  $Res call(
      {String id,
      String sessionId,
      String suggestionType,
      String originalText,
      String suggestedText,
      String explanation,
      double confidenceScore,
      int? startPosition,
      int? endPosition,
      String status,
      String? userFeedback,
      DateTime createdAt});
}

/// @nodoc
class _$ContentSuggestionCopyWithImpl<$Res, $Val extends ContentSuggestion>
    implements $ContentSuggestionCopyWith<$Res> {
  _$ContentSuggestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sessionId = null,
    Object? suggestionType = null,
    Object? originalText = null,
    Object? suggestedText = null,
    Object? explanation = null,
    Object? confidenceScore = null,
    Object? startPosition = freezed,
    Object? endPosition = freezed,
    Object? status = null,
    Object? userFeedback = freezed,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      suggestionType: null == suggestionType
          ? _value.suggestionType
          : suggestionType // ignore: cast_nullable_to_non_nullable
              as String,
      originalText: null == originalText
          ? _value.originalText
          : originalText // ignore: cast_nullable_to_non_nullable
              as String,
      suggestedText: null == suggestedText
          ? _value.suggestedText
          : suggestedText // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: null == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String,
      confidenceScore: null == confidenceScore
          ? _value.confidenceScore
          : confidenceScore // ignore: cast_nullable_to_non_nullable
              as double,
      startPosition: freezed == startPosition
          ? _value.startPosition
          : startPosition // ignore: cast_nullable_to_non_nullable
              as int?,
      endPosition: freezed == endPosition
          ? _value.endPosition
          : endPosition // ignore: cast_nullable_to_non_nullable
              as int?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      userFeedback: freezed == userFeedback
          ? _value.userFeedback
          : userFeedback // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentSuggestionImplCopyWith<$Res>
    implements $ContentSuggestionCopyWith<$Res> {
  factory _$$ContentSuggestionImplCopyWith(_$ContentSuggestionImpl value,
          $Res Function(_$ContentSuggestionImpl) then) =
      __$$ContentSuggestionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String sessionId,
      String suggestionType,
      String originalText,
      String suggestedText,
      String explanation,
      double confidenceScore,
      int? startPosition,
      int? endPosition,
      String status,
      String? userFeedback,
      DateTime createdAt});
}

/// @nodoc
class __$$ContentSuggestionImplCopyWithImpl<$Res>
    extends _$ContentSuggestionCopyWithImpl<$Res, _$ContentSuggestionImpl>
    implements _$$ContentSuggestionImplCopyWith<$Res> {
  __$$ContentSuggestionImplCopyWithImpl(_$ContentSuggestionImpl _value,
      $Res Function(_$ContentSuggestionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sessionId = null,
    Object? suggestionType = null,
    Object? originalText = null,
    Object? suggestedText = null,
    Object? explanation = null,
    Object? confidenceScore = null,
    Object? startPosition = freezed,
    Object? endPosition = freezed,
    Object? status = null,
    Object? userFeedback = freezed,
    Object? createdAt = null,
  }) {
    return _then(_$ContentSuggestionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      suggestionType: null == suggestionType
          ? _value.suggestionType
          : suggestionType // ignore: cast_nullable_to_non_nullable
              as String,
      originalText: null == originalText
          ? _value.originalText
          : originalText // ignore: cast_nullable_to_non_nullable
              as String,
      suggestedText: null == suggestedText
          ? _value.suggestedText
          : suggestedText // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: null == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String,
      confidenceScore: null == confidenceScore
          ? _value.confidenceScore
          : confidenceScore // ignore: cast_nullable_to_non_nullable
              as double,
      startPosition: freezed == startPosition
          ? _value.startPosition
          : startPosition // ignore: cast_nullable_to_non_nullable
              as int?,
      endPosition: freezed == endPosition
          ? _value.endPosition
          : endPosition // ignore: cast_nullable_to_non_nullable
              as int?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      userFeedback: freezed == userFeedback
          ? _value.userFeedback
          : userFeedback // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentSuggestionImpl implements _ContentSuggestion {
  const _$ContentSuggestionImpl(
      {required this.id,
      required this.sessionId,
      required this.suggestionType,
      required this.originalText,
      required this.suggestedText,
      required this.explanation,
      required this.confidenceScore,
      this.startPosition,
      this.endPosition,
      this.status = 'pending',
      this.userFeedback,
      required this.createdAt});

  factory _$ContentSuggestionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentSuggestionImplFromJson(json);

  @override
  final String id;
  @override
  final String sessionId;
  @override
  final String suggestionType;
  @override
  final String originalText;
  @override
  final String suggestedText;
  @override
  final String explanation;
  @override
  final double confidenceScore;
  @override
  final int? startPosition;
  @override
  final int? endPosition;
  @override
  @JsonKey()
  final String status;
  @override
  final String? userFeedback;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'ContentSuggestion(id: $id, sessionId: $sessionId, suggestionType: $suggestionType, originalText: $originalText, suggestedText: $suggestedText, explanation: $explanation, confidenceScore: $confidenceScore, startPosition: $startPosition, endPosition: $endPosition, status: $status, userFeedback: $userFeedback, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentSuggestionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.suggestionType, suggestionType) ||
                other.suggestionType == suggestionType) &&
            (identical(other.originalText, originalText) ||
                other.originalText == originalText) &&
            (identical(other.suggestedText, suggestedText) ||
                other.suggestedText == suggestedText) &&
            (identical(other.explanation, explanation) ||
                other.explanation == explanation) &&
            (identical(other.confidenceScore, confidenceScore) ||
                other.confidenceScore == confidenceScore) &&
            (identical(other.startPosition, startPosition) ||
                other.startPosition == startPosition) &&
            (identical(other.endPosition, endPosition) ||
                other.endPosition == endPosition) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.userFeedback, userFeedback) ||
                other.userFeedback == userFeedback) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      sessionId,
      suggestionType,
      originalText,
      suggestedText,
      explanation,
      confidenceScore,
      startPosition,
      endPosition,
      status,
      userFeedback,
      createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentSuggestionImplCopyWith<_$ContentSuggestionImpl> get copyWith =>
      __$$ContentSuggestionImplCopyWithImpl<_$ContentSuggestionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentSuggestionImplToJson(
      this,
    );
  }
}

abstract class _ContentSuggestion implements ContentSuggestion {
  const factory _ContentSuggestion(
      {required final String id,
      required final String sessionId,
      required final String suggestionType,
      required final String originalText,
      required final String suggestedText,
      required final String explanation,
      required final double confidenceScore,
      final int? startPosition,
      final int? endPosition,
      final String status,
      final String? userFeedback,
      required final DateTime createdAt}) = _$ContentSuggestionImpl;

  factory _ContentSuggestion.fromJson(Map<String, dynamic> json) =
      _$ContentSuggestionImpl.fromJson;

  @override
  String get id;
  @override
  String get sessionId;
  @override
  String get suggestionType;
  @override
  String get originalText;
  @override
  String get suggestedText;
  @override
  String get explanation;
  @override
  double get confidenceScore;
  @override
  int? get startPosition;
  @override
  int? get endPosition;
  @override
  String get status;
  @override
  String? get userFeedback;
  @override
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$ContentSuggestionImplCopyWith<_$ContentSuggestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContentTemplate _$ContentTemplateFromJson(Map<String, dynamic> json) {
  return _ContentTemplate.fromJson(json);
}

/// @nodoc
mixin _$ContentTemplate {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get templateContent => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  int get estimatedWordCount => throw _privateConstructorUsedError;
  String get difficultyLevel => throw _privateConstructorUsedError;
  bool get isPublic => throw _privateConstructorUsedError;
  int get usageCount => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentTemplateCopyWith<ContentTemplate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentTemplateCopyWith<$Res> {
  factory $ContentTemplateCopyWith(
          ContentTemplate value, $Res Function(ContentTemplate) then) =
      _$ContentTemplateCopyWithImpl<$Res, ContentTemplate>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      String templateContent,
      String category,
      int estimatedWordCount,
      String difficultyLevel,
      bool isPublic,
      int usageCount,
      DateTime createdAt});
}

/// @nodoc
class _$ContentTemplateCopyWithImpl<$Res, $Val extends ContentTemplate>
    implements $ContentTemplateCopyWith<$Res> {
  _$ContentTemplateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? templateContent = null,
    Object? category = null,
    Object? estimatedWordCount = null,
    Object? difficultyLevel = null,
    Object? isPublic = null,
    Object? usageCount = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      templateContent: null == templateContent
          ? _value.templateContent
          : templateContent // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedWordCount: null == estimatedWordCount
          ? _value.estimatedWordCount
          : estimatedWordCount // ignore: cast_nullable_to_non_nullable
              as int,
      difficultyLevel: null == difficultyLevel
          ? _value.difficultyLevel
          : difficultyLevel // ignore: cast_nullable_to_non_nullable
              as String,
      isPublic: null == isPublic
          ? _value.isPublic
          : isPublic // ignore: cast_nullable_to_non_nullable
              as bool,
      usageCount: null == usageCount
          ? _value.usageCount
          : usageCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentTemplateImplCopyWith<$Res>
    implements $ContentTemplateCopyWith<$Res> {
  factory _$$ContentTemplateImplCopyWith(_$ContentTemplateImpl value,
          $Res Function(_$ContentTemplateImpl) then) =
      __$$ContentTemplateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      String templateContent,
      String category,
      int estimatedWordCount,
      String difficultyLevel,
      bool isPublic,
      int usageCount,
      DateTime createdAt});
}

/// @nodoc
class __$$ContentTemplateImplCopyWithImpl<$Res>
    extends _$ContentTemplateCopyWithImpl<$Res, _$ContentTemplateImpl>
    implements _$$ContentTemplateImplCopyWith<$Res> {
  __$$ContentTemplateImplCopyWithImpl(
      _$ContentTemplateImpl _value, $Res Function(_$ContentTemplateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? templateContent = null,
    Object? category = null,
    Object? estimatedWordCount = null,
    Object? difficultyLevel = null,
    Object? isPublic = null,
    Object? usageCount = null,
    Object? createdAt = null,
  }) {
    return _then(_$ContentTemplateImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      templateContent: null == templateContent
          ? _value.templateContent
          : templateContent // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedWordCount: null == estimatedWordCount
          ? _value.estimatedWordCount
          : estimatedWordCount // ignore: cast_nullable_to_non_nullable
              as int,
      difficultyLevel: null == difficultyLevel
          ? _value.difficultyLevel
          : difficultyLevel // ignore: cast_nullable_to_non_nullable
              as String,
      isPublic: null == isPublic
          ? _value.isPublic
          : isPublic // ignore: cast_nullable_to_non_nullable
              as bool,
      usageCount: null == usageCount
          ? _value.usageCount
          : usageCount // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentTemplateImpl implements _ContentTemplate {
  const _$ContentTemplateImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.templateContent,
      required this.category,
      required this.estimatedWordCount,
      required this.difficultyLevel,
      required this.isPublic,
      required this.usageCount,
      required this.createdAt});

  factory _$ContentTemplateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentTemplateImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String templateContent;
  @override
  final String category;
  @override
  final int estimatedWordCount;
  @override
  final String difficultyLevel;
  @override
  final bool isPublic;
  @override
  final int usageCount;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'ContentTemplate(id: $id, name: $name, description: $description, templateContent: $templateContent, category: $category, estimatedWordCount: $estimatedWordCount, difficultyLevel: $difficultyLevel, isPublic: $isPublic, usageCount: $usageCount, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentTemplateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.templateContent, templateContent) ||
                other.templateContent == templateContent) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.estimatedWordCount, estimatedWordCount) ||
                other.estimatedWordCount == estimatedWordCount) &&
            (identical(other.difficultyLevel, difficultyLevel) ||
                other.difficultyLevel == difficultyLevel) &&
            (identical(other.isPublic, isPublic) ||
                other.isPublic == isPublic) &&
            (identical(other.usageCount, usageCount) ||
                other.usageCount == usageCount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      templateContent,
      category,
      estimatedWordCount,
      difficultyLevel,
      isPublic,
      usageCount,
      createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentTemplateImplCopyWith<_$ContentTemplateImpl> get copyWith =>
      __$$ContentTemplateImplCopyWithImpl<_$ContentTemplateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentTemplateImplToJson(
      this,
    );
  }
}

abstract class _ContentTemplate implements ContentTemplate {
  const factory _ContentTemplate(
      {required final String id,
      required final String name,
      required final String description,
      required final String templateContent,
      required final String category,
      required final int estimatedWordCount,
      required final String difficultyLevel,
      required final bool isPublic,
      required final int usageCount,
      required final DateTime createdAt}) = _$ContentTemplateImpl;

  factory _ContentTemplate.fromJson(Map<String, dynamic> json) =
      _$ContentTemplateImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String get templateContent;
  @override
  String get category;
  @override
  int get estimatedWordCount;
  @override
  String get difficultyLevel;
  @override
  bool get isPublic;
  @override
  int get usageCount;
  @override
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$ContentTemplateImplCopyWith<_$ContentTemplateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AIUsageAnalytics _$AIUsageAnalyticsFromJson(Map<String, dynamic> json) {
  return _AIUsageAnalytics.fromJson(json);
}

/// @nodoc
mixin _$AIUsageAnalytics {
  String get featureUsed => throw _privateConstructorUsedError;
  int get usageCount => throw _privateConstructorUsedError;
  int get totalTimeSavedMinutes => throw _privateConstructorUsedError;
  int get totalWordsGenerated => throw _privateConstructorUsedError;
  int get year => throw _privateConstructorUsedError;
  int get month => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AIUsageAnalyticsCopyWith<AIUsageAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AIUsageAnalyticsCopyWith<$Res> {
  factory $AIUsageAnalyticsCopyWith(
          AIUsageAnalytics value, $Res Function(AIUsageAnalytics) then) =
      _$AIUsageAnalyticsCopyWithImpl<$Res, AIUsageAnalytics>;
  @useResult
  $Res call(
      {String featureUsed,
      int usageCount,
      int totalTimeSavedMinutes,
      int totalWordsGenerated,
      int year,
      int month});
}

/// @nodoc
class _$AIUsageAnalyticsCopyWithImpl<$Res, $Val extends AIUsageAnalytics>
    implements $AIUsageAnalyticsCopyWith<$Res> {
  _$AIUsageAnalyticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? featureUsed = null,
    Object? usageCount = null,
    Object? totalTimeSavedMinutes = null,
    Object? totalWordsGenerated = null,
    Object? year = null,
    Object? month = null,
  }) {
    return _then(_value.copyWith(
      featureUsed: null == featureUsed
          ? _value.featureUsed
          : featureUsed // ignore: cast_nullable_to_non_nullable
              as String,
      usageCount: null == usageCount
          ? _value.usageCount
          : usageCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalTimeSavedMinutes: null == totalTimeSavedMinutes
          ? _value.totalTimeSavedMinutes
          : totalTimeSavedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      totalWordsGenerated: null == totalWordsGenerated
          ? _value.totalWordsGenerated
          : totalWordsGenerated // ignore: cast_nullable_to_non_nullable
              as int,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: null == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AIUsageAnalyticsImplCopyWith<$Res>
    implements $AIUsageAnalyticsCopyWith<$Res> {
  factory _$$AIUsageAnalyticsImplCopyWith(_$AIUsageAnalyticsImpl value,
          $Res Function(_$AIUsageAnalyticsImpl) then) =
      __$$AIUsageAnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String featureUsed,
      int usageCount,
      int totalTimeSavedMinutes,
      int totalWordsGenerated,
      int year,
      int month});
}

/// @nodoc
class __$$AIUsageAnalyticsImplCopyWithImpl<$Res>
    extends _$AIUsageAnalyticsCopyWithImpl<$Res, _$AIUsageAnalyticsImpl>
    implements _$$AIUsageAnalyticsImplCopyWith<$Res> {
  __$$AIUsageAnalyticsImplCopyWithImpl(_$AIUsageAnalyticsImpl _value,
      $Res Function(_$AIUsageAnalyticsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? featureUsed = null,
    Object? usageCount = null,
    Object? totalTimeSavedMinutes = null,
    Object? totalWordsGenerated = null,
    Object? year = null,
    Object? month = null,
  }) {
    return _then(_$AIUsageAnalyticsImpl(
      featureUsed: null == featureUsed
          ? _value.featureUsed
          : featureUsed // ignore: cast_nullable_to_non_nullable
              as String,
      usageCount: null == usageCount
          ? _value.usageCount
          : usageCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalTimeSavedMinutes: null == totalTimeSavedMinutes
          ? _value.totalTimeSavedMinutes
          : totalTimeSavedMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      totalWordsGenerated: null == totalWordsGenerated
          ? _value.totalWordsGenerated
          : totalWordsGenerated // ignore: cast_nullable_to_non_nullable
              as int,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: null == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AIUsageAnalyticsImpl implements _AIUsageAnalytics {
  const _$AIUsageAnalyticsImpl(
      {required this.featureUsed,
      required this.usageCount,
      required this.totalTimeSavedMinutes,
      required this.totalWordsGenerated,
      required this.year,
      required this.month});

  factory _$AIUsageAnalyticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AIUsageAnalyticsImplFromJson(json);

  @override
  final String featureUsed;
  @override
  final int usageCount;
  @override
  final int totalTimeSavedMinutes;
  @override
  final int totalWordsGenerated;
  @override
  final int year;
  @override
  final int month;

  @override
  String toString() {
    return 'AIUsageAnalytics(featureUsed: $featureUsed, usageCount: $usageCount, totalTimeSavedMinutes: $totalTimeSavedMinutes, totalWordsGenerated: $totalWordsGenerated, year: $year, month: $month)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AIUsageAnalyticsImpl &&
            (identical(other.featureUsed, featureUsed) ||
                other.featureUsed == featureUsed) &&
            (identical(other.usageCount, usageCount) ||
                other.usageCount == usageCount) &&
            (identical(other.totalTimeSavedMinutes, totalTimeSavedMinutes) ||
                other.totalTimeSavedMinutes == totalTimeSavedMinutes) &&
            (identical(other.totalWordsGenerated, totalWordsGenerated) ||
                other.totalWordsGenerated == totalWordsGenerated) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.month, month) || other.month == month));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, featureUsed, usageCount,
      totalTimeSavedMinutes, totalWordsGenerated, year, month);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AIUsageAnalyticsImplCopyWith<_$AIUsageAnalyticsImpl> get copyWith =>
      __$$AIUsageAnalyticsImplCopyWithImpl<_$AIUsageAnalyticsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AIUsageAnalyticsImplToJson(
      this,
    );
  }
}

abstract class _AIUsageAnalytics implements AIUsageAnalytics {
  const factory _AIUsageAnalytics(
      {required final String featureUsed,
      required final int usageCount,
      required final int totalTimeSavedMinutes,
      required final int totalWordsGenerated,
      required final int year,
      required final int month}) = _$AIUsageAnalyticsImpl;

  factory _AIUsageAnalytics.fromJson(Map<String, dynamic> json) =
      _$AIUsageAnalyticsImpl.fromJson;

  @override
  String get featureUsed;
  @override
  int get usageCount;
  @override
  int get totalTimeSavedMinutes;
  @override
  int get totalWordsGenerated;
  @override
  int get year;
  @override
  int get month;
  @override
  @JsonKey(ignore: true)
  _$$AIUsageAnalyticsImplCopyWith<_$AIUsageAnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContentIdeaRequest _$ContentIdeaRequestFromJson(Map<String, dynamic> json) {
  return _ContentIdeaRequest.fromJson(json);
}

/// @nodoc
mixin _$ContentIdeaRequest {
  String get topic => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentIdeaRequestCopyWith<ContentIdeaRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentIdeaRequestCopyWith<$Res> {
  factory $ContentIdeaRequestCopyWith(
          ContentIdeaRequest value, $Res Function(ContentIdeaRequest) then) =
      _$ContentIdeaRequestCopyWithImpl<$Res, ContentIdeaRequest>;
  @useResult
  $Res call({String topic, int count});
}

/// @nodoc
class _$ContentIdeaRequestCopyWithImpl<$Res, $Val extends ContentIdeaRequest>
    implements $ContentIdeaRequestCopyWith<$Res> {
  _$ContentIdeaRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? topic = null,
    Object? count = null,
  }) {
    return _then(_value.copyWith(
      topic: null == topic
          ? _value.topic
          : topic // ignore: cast_nullable_to_non_nullable
              as String,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentIdeaRequestImplCopyWith<$Res>
    implements $ContentIdeaRequestCopyWith<$Res> {
  factory _$$ContentIdeaRequestImplCopyWith(_$ContentIdeaRequestImpl value,
          $Res Function(_$ContentIdeaRequestImpl) then) =
      __$$ContentIdeaRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String topic, int count});
}

/// @nodoc
class __$$ContentIdeaRequestImplCopyWithImpl<$Res>
    extends _$ContentIdeaRequestCopyWithImpl<$Res, _$ContentIdeaRequestImpl>
    implements _$$ContentIdeaRequestImplCopyWith<$Res> {
  __$$ContentIdeaRequestImplCopyWithImpl(_$ContentIdeaRequestImpl _value,
      $Res Function(_$ContentIdeaRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? topic = null,
    Object? count = null,
  }) {
    return _then(_$ContentIdeaRequestImpl(
      topic: null == topic
          ? _value.topic
          : topic // ignore: cast_nullable_to_non_nullable
              as String,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentIdeaRequestImpl implements _ContentIdeaRequest {
  const _$ContentIdeaRequestImpl({required this.topic, this.count = 5});

  factory _$ContentIdeaRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentIdeaRequestImplFromJson(json);

  @override
  final String topic;
  @override
  @JsonKey()
  final int count;

  @override
  String toString() {
    return 'ContentIdeaRequest(topic: $topic, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentIdeaRequestImpl &&
            (identical(other.topic, topic) || other.topic == topic) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, topic, count);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentIdeaRequestImplCopyWith<_$ContentIdeaRequestImpl> get copyWith =>
      __$$ContentIdeaRequestImplCopyWithImpl<_$ContentIdeaRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentIdeaRequestImplToJson(
      this,
    );
  }
}

abstract class _ContentIdeaRequest implements ContentIdeaRequest {
  const factory _ContentIdeaRequest(
      {required final String topic,
      final int count}) = _$ContentIdeaRequestImpl;

  factory _ContentIdeaRequest.fromJson(Map<String, dynamic> json) =
      _$ContentIdeaRequestImpl.fromJson;

  @override
  String get topic;
  @override
  int get count;
  @override
  @JsonKey(ignore: true)
  _$$ContentIdeaRequestImplCopyWith<_$ContentIdeaRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ContentOutlineRequest _$ContentOutlineRequestFromJson(
    Map<String, dynamic> json) {
  return _ContentOutlineRequest.fromJson(json);
}

/// @nodoc
mixin _$ContentOutlineRequest {
  String get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentOutlineRequestCopyWith<ContentOutlineRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentOutlineRequestCopyWith<$Res> {
  factory $ContentOutlineRequestCopyWith(ContentOutlineRequest value,
          $Res Function(ContentOutlineRequest) then) =
      _$ContentOutlineRequestCopyWithImpl<$Res, ContentOutlineRequest>;
  @useResult
  $Res call({String title});
}

/// @nodoc
class _$ContentOutlineRequestCopyWithImpl<$Res,
        $Val extends ContentOutlineRequest>
    implements $ContentOutlineRequestCopyWith<$Res> {
  _$ContentOutlineRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
  }) {
    return _then(_value.copyWith(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentOutlineRequestImplCopyWith<$Res>
    implements $ContentOutlineRequestCopyWith<$Res> {
  factory _$$ContentOutlineRequestImplCopyWith(
          _$ContentOutlineRequestImpl value,
          $Res Function(_$ContentOutlineRequestImpl) then) =
      __$$ContentOutlineRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String title});
}

/// @nodoc
class __$$ContentOutlineRequestImplCopyWithImpl<$Res>
    extends _$ContentOutlineRequestCopyWithImpl<$Res,
        _$ContentOutlineRequestImpl>
    implements _$$ContentOutlineRequestImplCopyWith<$Res> {
  __$$ContentOutlineRequestImplCopyWithImpl(_$ContentOutlineRequestImpl _value,
      $Res Function(_$ContentOutlineRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
  }) {
    return _then(_$ContentOutlineRequestImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentOutlineRequestImpl implements _ContentOutlineRequest {
  const _$ContentOutlineRequestImpl({required this.title});

  factory _$ContentOutlineRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentOutlineRequestImplFromJson(json);

  @override
  final String title;

  @override
  String toString() {
    return 'ContentOutlineRequest(title: $title)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentOutlineRequestImpl &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentOutlineRequestImplCopyWith<_$ContentOutlineRequestImpl>
      get copyWith => __$$ContentOutlineRequestImplCopyWithImpl<
          _$ContentOutlineRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentOutlineRequestImplToJson(
      this,
    );
  }
}

abstract class _ContentOutlineRequest implements ContentOutlineRequest {
  const factory _ContentOutlineRequest({required final String title}) =
      _$ContentOutlineRequestImpl;

  factory _ContentOutlineRequest.fromJson(Map<String, dynamic> json) =
      _$ContentOutlineRequestImpl.fromJson;

  @override
  String get title;
  @override
  @JsonKey(ignore: true)
  _$$ContentOutlineRequestImplCopyWith<_$ContentOutlineRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

GrammarImprovementRequest _$GrammarImprovementRequestFromJson(
    Map<String, dynamic> json) {
  return _GrammarImprovementRequest.fromJson(json);
}

/// @nodoc
mixin _$GrammarImprovementRequest {
  String get text => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrammarImprovementRequestCopyWith<GrammarImprovementRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrammarImprovementRequestCopyWith<$Res> {
  factory $GrammarImprovementRequestCopyWith(GrammarImprovementRequest value,
          $Res Function(GrammarImprovementRequest) then) =
      _$GrammarImprovementRequestCopyWithImpl<$Res, GrammarImprovementRequest>;
  @useResult
  $Res call({String text});
}

/// @nodoc
class _$GrammarImprovementRequestCopyWithImpl<$Res,
        $Val extends GrammarImprovementRequest>
    implements $GrammarImprovementRequestCopyWith<$Res> {
  _$GrammarImprovementRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
  }) {
    return _then(_value.copyWith(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GrammarImprovementRequestImplCopyWith<$Res>
    implements $GrammarImprovementRequestCopyWith<$Res> {
  factory _$$GrammarImprovementRequestImplCopyWith(
          _$GrammarImprovementRequestImpl value,
          $Res Function(_$GrammarImprovementRequestImpl) then) =
      __$$GrammarImprovementRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String text});
}

/// @nodoc
class __$$GrammarImprovementRequestImplCopyWithImpl<$Res>
    extends _$GrammarImprovementRequestCopyWithImpl<$Res,
        _$GrammarImprovementRequestImpl>
    implements _$$GrammarImprovementRequestImplCopyWith<$Res> {
  __$$GrammarImprovementRequestImplCopyWithImpl(
      _$GrammarImprovementRequestImpl _value,
      $Res Function(_$GrammarImprovementRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
  }) {
    return _then(_$GrammarImprovementRequestImpl(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GrammarImprovementRequestImpl implements _GrammarImprovementRequest {
  const _$GrammarImprovementRequestImpl({required this.text});

  factory _$GrammarImprovementRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$GrammarImprovementRequestImplFromJson(json);

  @override
  final String text;

  @override
  String toString() {
    return 'GrammarImprovementRequest(text: $text)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GrammarImprovementRequestImpl &&
            (identical(other.text, text) || other.text == text));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, text);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GrammarImprovementRequestImplCopyWith<_$GrammarImprovementRequestImpl>
      get copyWith => __$$GrammarImprovementRequestImplCopyWithImpl<
          _$GrammarImprovementRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GrammarImprovementRequestImplToJson(
      this,
    );
  }
}

abstract class _GrammarImprovementRequest implements GrammarImprovementRequest {
  const factory _GrammarImprovementRequest({required final String text}) =
      _$GrammarImprovementRequestImpl;

  factory _GrammarImprovementRequest.fromJson(Map<String, dynamic> json) =
      _$GrammarImprovementRequestImpl.fromJson;

  @override
  String get text;
  @override
  @JsonKey(ignore: true)
  _$$GrammarImprovementRequestImplCopyWith<_$GrammarImprovementRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SEOSuggestionsRequest _$SEOSuggestionsRequestFromJson(
    Map<String, dynamic> json) {
  return _SEOSuggestionsRequest.fromJson(json);
}

/// @nodoc
mixin _$SEOSuggestionsRequest {
  String get content => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SEOSuggestionsRequestCopyWith<SEOSuggestionsRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SEOSuggestionsRequestCopyWith<$Res> {
  factory $SEOSuggestionsRequestCopyWith(SEOSuggestionsRequest value,
          $Res Function(SEOSuggestionsRequest) then) =
      _$SEOSuggestionsRequestCopyWithImpl<$Res, SEOSuggestionsRequest>;
  @useResult
  $Res call({String content, String? title});
}

/// @nodoc
class _$SEOSuggestionsRequestCopyWithImpl<$Res,
        $Val extends SEOSuggestionsRequest>
    implements $SEOSuggestionsRequestCopyWith<$Res> {
  _$SEOSuggestionsRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? title = freezed,
  }) {
    return _then(_value.copyWith(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SEOSuggestionsRequestImplCopyWith<$Res>
    implements $SEOSuggestionsRequestCopyWith<$Res> {
  factory _$$SEOSuggestionsRequestImplCopyWith(
          _$SEOSuggestionsRequestImpl value,
          $Res Function(_$SEOSuggestionsRequestImpl) then) =
      __$$SEOSuggestionsRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String content, String? title});
}

/// @nodoc
class __$$SEOSuggestionsRequestImplCopyWithImpl<$Res>
    extends _$SEOSuggestionsRequestCopyWithImpl<$Res,
        _$SEOSuggestionsRequestImpl>
    implements _$$SEOSuggestionsRequestImplCopyWith<$Res> {
  __$$SEOSuggestionsRequestImplCopyWithImpl(_$SEOSuggestionsRequestImpl _value,
      $Res Function(_$SEOSuggestionsRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
    Object? title = freezed,
  }) {
    return _then(_$SEOSuggestionsRequestImpl(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SEOSuggestionsRequestImpl implements _SEOSuggestionsRequest {
  const _$SEOSuggestionsRequestImpl({required this.content, this.title});

  factory _$SEOSuggestionsRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SEOSuggestionsRequestImplFromJson(json);

  @override
  final String content;
  @override
  final String? title;

  @override
  String toString() {
    return 'SEOSuggestionsRequest(content: $content, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SEOSuggestionsRequestImpl &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, content, title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SEOSuggestionsRequestImplCopyWith<_$SEOSuggestionsRequestImpl>
      get copyWith => __$$SEOSuggestionsRequestImplCopyWithImpl<
          _$SEOSuggestionsRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SEOSuggestionsRequestImplToJson(
      this,
    );
  }
}

abstract class _SEOSuggestionsRequest implements SEOSuggestionsRequest {
  const factory _SEOSuggestionsRequest(
      {required final String content,
      final String? title}) = _$SEOSuggestionsRequestImpl;

  factory _SEOSuggestionsRequest.fromJson(Map<String, dynamic> json) =
      _$SEOSuggestionsRequestImpl.fromJson;

  @override
  String get content;
  @override
  String? get title;
  @override
  @JsonKey(ignore: true)
  _$$SEOSuggestionsRequestImplCopyWith<_$SEOSuggestionsRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TextCompletionRequest _$TextCompletionRequestFromJson(
    Map<String, dynamic> json) {
  return _TextCompletionRequest.fromJson(json);
}

/// @nodoc
mixin _$TextCompletionRequest {
  String get partialText => throw _privateConstructorUsedError;
  String? get context => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TextCompletionRequestCopyWith<TextCompletionRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TextCompletionRequestCopyWith<$Res> {
  factory $TextCompletionRequestCopyWith(TextCompletionRequest value,
          $Res Function(TextCompletionRequest) then) =
      _$TextCompletionRequestCopyWithImpl<$Res, TextCompletionRequest>;
  @useResult
  $Res call({String partialText, String? context});
}

/// @nodoc
class _$TextCompletionRequestCopyWithImpl<$Res,
        $Val extends TextCompletionRequest>
    implements $TextCompletionRequestCopyWith<$Res> {
  _$TextCompletionRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partialText = null,
    Object? context = freezed,
  }) {
    return _then(_value.copyWith(
      partialText: null == partialText
          ? _value.partialText
          : partialText // ignore: cast_nullable_to_non_nullable
              as String,
      context: freezed == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TextCompletionRequestImplCopyWith<$Res>
    implements $TextCompletionRequestCopyWith<$Res> {
  factory _$$TextCompletionRequestImplCopyWith(
          _$TextCompletionRequestImpl value,
          $Res Function(_$TextCompletionRequestImpl) then) =
      __$$TextCompletionRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String partialText, String? context});
}

/// @nodoc
class __$$TextCompletionRequestImplCopyWithImpl<$Res>
    extends _$TextCompletionRequestCopyWithImpl<$Res,
        _$TextCompletionRequestImpl>
    implements _$$TextCompletionRequestImplCopyWith<$Res> {
  __$$TextCompletionRequestImplCopyWithImpl(_$TextCompletionRequestImpl _value,
      $Res Function(_$TextCompletionRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partialText = null,
    Object? context = freezed,
  }) {
    return _then(_$TextCompletionRequestImpl(
      partialText: null == partialText
          ? _value.partialText
          : partialText // ignore: cast_nullable_to_non_nullable
              as String,
      context: freezed == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TextCompletionRequestImpl implements _TextCompletionRequest {
  const _$TextCompletionRequestImpl({required this.partialText, this.context});

  factory _$TextCompletionRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$TextCompletionRequestImplFromJson(json);

  @override
  final String partialText;
  @override
  final String? context;

  @override
  String toString() {
    return 'TextCompletionRequest(partialText: $partialText, context: $context)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TextCompletionRequestImpl &&
            (identical(other.partialText, partialText) ||
                other.partialText == partialText) &&
            (identical(other.context, context) || other.context == context));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, partialText, context);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TextCompletionRequestImplCopyWith<_$TextCompletionRequestImpl>
      get copyWith => __$$TextCompletionRequestImplCopyWithImpl<
          _$TextCompletionRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TextCompletionRequestImplToJson(
      this,
    );
  }
}

abstract class _TextCompletionRequest implements TextCompletionRequest {
  const factory _TextCompletionRequest(
      {required final String partialText,
      final String? context}) = _$TextCompletionRequestImpl;

  factory _TextCompletionRequest.fromJson(Map<String, dynamic> json) =
      _$TextCompletionRequestImpl.fromJson;

  @override
  String get partialText;
  @override
  String? get context;
  @override
  @JsonKey(ignore: true)
  _$$TextCompletionRequestImplCopyWith<_$TextCompletionRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ReadabilityAnalysisRequest _$ReadabilityAnalysisRequestFromJson(
    Map<String, dynamic> json) {
  return _ReadabilityAnalysisRequest.fromJson(json);
}

/// @nodoc
mixin _$ReadabilityAnalysisRequest {
  String get content => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReadabilityAnalysisRequestCopyWith<ReadabilityAnalysisRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReadabilityAnalysisRequestCopyWith<$Res> {
  factory $ReadabilityAnalysisRequestCopyWith(ReadabilityAnalysisRequest value,
          $Res Function(ReadabilityAnalysisRequest) then) =
      _$ReadabilityAnalysisRequestCopyWithImpl<$Res,
          ReadabilityAnalysisRequest>;
  @useResult
  $Res call({String content});
}

/// @nodoc
class _$ReadabilityAnalysisRequestCopyWithImpl<$Res,
        $Val extends ReadabilityAnalysisRequest>
    implements $ReadabilityAnalysisRequestCopyWith<$Res> {
  _$ReadabilityAnalysisRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
  }) {
    return _then(_value.copyWith(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReadabilityAnalysisRequestImplCopyWith<$Res>
    implements $ReadabilityAnalysisRequestCopyWith<$Res> {
  factory _$$ReadabilityAnalysisRequestImplCopyWith(
          _$ReadabilityAnalysisRequestImpl value,
          $Res Function(_$ReadabilityAnalysisRequestImpl) then) =
      __$$ReadabilityAnalysisRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String content});
}

/// @nodoc
class __$$ReadabilityAnalysisRequestImplCopyWithImpl<$Res>
    extends _$ReadabilityAnalysisRequestCopyWithImpl<$Res,
        _$ReadabilityAnalysisRequestImpl>
    implements _$$ReadabilityAnalysisRequestImplCopyWith<$Res> {
  __$$ReadabilityAnalysisRequestImplCopyWithImpl(
      _$ReadabilityAnalysisRequestImpl _value,
      $Res Function(_$ReadabilityAnalysisRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? content = null,
  }) {
    return _then(_$ReadabilityAnalysisRequestImpl(
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReadabilityAnalysisRequestImpl implements _ReadabilityAnalysisRequest {
  const _$ReadabilityAnalysisRequestImpl({required this.content});

  factory _$ReadabilityAnalysisRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ReadabilityAnalysisRequestImplFromJson(json);

  @override
  final String content;

  @override
  String toString() {
    return 'ReadabilityAnalysisRequest(content: $content)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReadabilityAnalysisRequestImpl &&
            (identical(other.content, content) || other.content == content));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, content);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReadabilityAnalysisRequestImplCopyWith<_$ReadabilityAnalysisRequestImpl>
      get copyWith => __$$ReadabilityAnalysisRequestImplCopyWithImpl<
          _$ReadabilityAnalysisRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReadabilityAnalysisRequestImplToJson(
      this,
    );
  }
}

abstract class _ReadabilityAnalysisRequest
    implements ReadabilityAnalysisRequest {
  const factory _ReadabilityAnalysisRequest({required final String content}) =
      _$ReadabilityAnalysisRequestImpl;

  factory _ReadabilityAnalysisRequest.fromJson(Map<String, dynamic> json) =
      _$ReadabilityAnalysisRequestImpl.fromJson;

  @override
  String get content;
  @override
  @JsonKey(ignore: true)
  _$$ReadabilityAnalysisRequestImplCopyWith<_$ReadabilityAnalysisRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
