import 'package:flutter_tts/flutter_tts.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter/foundation.dart';

/// Service for handling text-to-speech and speech-to-text functionality
class VoiceService {
  static final VoiceService _instance = VoiceService._internal();
  factory VoiceService() => _instance;
  VoiceService._internal();

  late FlutterTts _flutterTts;
  late stt.SpeechToText _speech;
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isSpeaking = false;

  /// Initialize the voice service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _flutterTts = FlutterTts();
      _speech = stt.SpeechToText();

      // Initialize TTS
      await _initializeTts();

      // Initialize STT
      await _initializeStt();

      _isInitialized = true;
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing voice service: $e');
      }
    }
  }

  /// Initialize Text-to-Speech
  Future<void> _initializeTts() async {
    try {
      await _flutterTts.setLanguage('en-US');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(1.0);
      await _flutterTts.setPitch(1.0);

      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
      });

      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
      });

      _flutterTts.setErrorHandler((msg) {
        _isSpeaking = false;
        if (kDebugMode) {
          print('TTS Error: $msg');
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing TTS: $e');
      }
    }
  }

  /// Initialize Speech-to-Text
  Future<void> _initializeStt() async {
    try {
      bool available = await _speech.initialize(
        onStatus: (status) {
          if (kDebugMode) {
            print('STT Status: $status');
          }
          _isListening = status == 'listening';
        },
        onError: (error) {
          if (kDebugMode) {
            print('STT Error: $error');
          }
          _isListening = false;
        },
      );

      if (!available) {
        if (kDebugMode) {
          print('Speech recognition not available');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing STT: $e');
      }
    }
  }

  /// Speak the given text
  Future<void> speak(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (_isSpeaking) {
        await stop();
      }
      await _flutterTts.speak(text);
    } catch (e) {
      if (kDebugMode) {
        print('Error speaking: $e');
      }
    }
  }

  /// Stop speaking
  Future<void> stop() async {
    if (!_isInitialized) return;

    try {
      await _flutterTts.stop();
      _isSpeaking = false;
    } catch (e) {
      if (kDebugMode) {
        print('Error stopping TTS: $e');
      }
    }
  }

  /// Start listening for speech
  Future<void> startListening({
    required Function(String) onResult,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (_isListening) {
        await stopListening();
      }

      bool available = await _speech.initialize();
      if (!available) {
        onError?.call('Speech recognition not available');
        return;
      }

      await _speech.listen(
        onResult: (result) {
          onResult(result.recognizedWords);
        },
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: 'en_US',
        onSoundLevelChange: (level) {
          // Handle sound level changes if needed
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error starting listening: $e');
      }
      onError?.call('Error starting speech recognition: $e');
    }
  }

  /// Stop listening for speech
  Future<void> stopListening() async {
    if (!_isInitialized) return;

    try {
      await _speech.stop();
      _isListening = false;
    } catch (e) {
      if (kDebugMode) {
        print('Error stopping listening: $e');
      }
    }
  }

  /// Check if currently speaking
  bool get isSpeaking => _isSpeaking;

  /// Check if currently listening
  bool get isListening => _isListening;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Get available languages for TTS
  Future<List<String>> getLanguages() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final languages = await _flutterTts.getLanguages;
      return List<String>.from(languages ?? []);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting languages: $e');
      }
      return [];
    }
  }

  /// Set TTS language
  Future<void> setLanguage(String language) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _flutterTts.setLanguage(language);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting language: $e');
      }
    }
  }

  /// Set TTS speech rate (0.0 to 1.0)
  Future<void> setSpeechRate(double rate) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _flutterTts.setSpeechRate(rate.clamp(0.0, 1.0));
    } catch (e) {
      if (kDebugMode) {
        print('Error setting speech rate: $e');
      }
    }
  }

  /// Set TTS volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _flutterTts.setVolume(volume.clamp(0.0, 1.0));
    } catch (e) {
      if (kDebugMode) {
        print('Error setting volume: $e');
      }
    }
  }

  /// Set TTS pitch (0.5 to 2.0)
  Future<void> setPitch(double pitch) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await _flutterTts.setPitch(pitch.clamp(0.5, 2.0));
    } catch (e) {
      if (kDebugMode) {
        print('Error setting pitch: $e');
      }
    }
  }

  /// Dispose of resources
  void dispose() {
    try {
      _flutterTts.stop();
      _speech.stop();
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing voice service: $e');
      }
    }
  }
}
