import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/gamification.dart';
import '../../services/gamification_service.dart';
import '../../theme/app_theme.dart';

class UserLevelWidget extends ConsumerStatefulWidget {
  final bool showDetails;
  final VoidCallback? onTap;

  const UserLevelWidget({
    super.key,
    this.showDetails = true,
    this.onTap,
  });

  @override
  ConsumerState<UserLevelWidget> createState() => _UserLevelWidgetState();
}

class _UserLevelWidgetState extends ConsumerState<UserLevelWidget>
    with TickerProviderStateMixin {
  final GamificationService _gamificationService = GamificationService();
  
  UserLevel? _userLevel;
  bool _isLoading = true;
  String? _error;
  
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _loadUserLevel();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _loadUserLevel() async {
    try {
      if (!mounted) return;

      setState(() {
        _isLoading = true;
        _error = null;
      });

      final userLevel = await _gamificationService.getUserLevel();

      if (!mounted) return;

      setState(() {
        _userLevel = userLevel;
        _isLoading = false;
      });

      // Animate progress bar
      if (mounted) {
        _progressController.forward();

        // Start pulse animation for high-level users
        if (userLevel.currentLevel >= 10) {
          _pulseController.repeat(reverse: true);
        }
      }

    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_userLevel == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _userLevel!.currentLevel >= 10 ? _pulseAnimation.value : 1.0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                gradient: _getLevelGradient(_userLevel!.currentLevel),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: _getLevelColor(_userLevel!.currentLevel).withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    // Level header
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              '${_userLevel!.currentLevel}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _gamificationService.getLevelTitle(_userLevel!.currentLevel),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                '${_gamificationService.formatPoints(_userLevel!.totalPoints)} points',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (widget.showDetails)
                          Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white.withOpacity(0.7),
                            size: 16,
                          ),
                      ],
                    ),
                    
                    if (widget.showDetails) ...[
                      const SizedBox(height: 16),
                      
                      // Progress to next level
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Level ${_userLevel!.currentLevel + 1}',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                '${_userLevel!.pointsToNextLevel} points to go',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          
                          // Progress bar
                          Container(
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: AnimatedBuilder(
                              animation: _progressAnimation,
                              builder: (context, child) {
                                // Ensure widthFactor is always between 0.0 and 1.0
                                final progressPercentage = (_userLevel?.levelProgressPercentage ?? 0.0).clamp(0.0, 100.0);
                                final animationValue = _progressAnimation.value.clamp(0.0, 1.0);
                                final widthFactor = ((progressPercentage / 100) * animationValue).clamp(0.0, 1.0);

                                return FractionallySizedBox(
                                  alignment: Alignment.centerLeft,
                                  widthFactor: widthFactor,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(4),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.white.withOpacity(0.5),
                                          blurRadius: 4,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Streaks
                      Row(
                        children: [
                          _buildStreakItem(
                            'Reading',
                            _userLevel!.readingStreak,
                            Icons.book,
                          ),
                          const SizedBox(width: 16),
                          _buildStreakItem(
                            'Writing',
                            _userLevel!.writingStreak,
                            Icons.edit,
                          ),
                          const SizedBox(width: 16),
                          _buildStreakItem(
                            'Engagement',
                            _userLevel!.engagementStreak,
                            Icons.favorite,
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStreakItem(String label, int streak, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '$streak',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (streak > 0)
                  Text(
                    _gamificationService.getStreakEmoji(streak),
                    style: const TextStyle(fontSize: 12),
                  ),
              ],
            ),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Row(
        children: [
          CircularProgressIndicator(),
          SizedBox(width: 16),
          Text('Loading level...'),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Failed to load level data',
              style: TextStyle(color: Colors.red[600]),
            ),
          ),
          IconButton(
            onPressed: _loadUserLevel,
            icon: Icon(Icons.refresh, color: Colors.red[600]),
          ),
        ],
      ),
    );
  }

  Color _getLevelColor(int level) {
    if (level >= 50) return const Color(0xFFFFD700); // Gold
    if (level >= 40) return const Color(0xFFC0C0C0); // Silver
    if (level >= 30) return const Color(0xFFCD7F32); // Bronze
    if (level >= 20) return const Color(0xFF9C27B0); // Purple
    if (level >= 10) return const Color(0xFF2196F3); // Blue
    if (level >= 5) return const Color(0xFF4CAF50); // Green
    return const Color(0xFF9E9E9E); // Grey
  }

  LinearGradient _getLevelGradient(int level) {
    final color = _getLevelColor(level);
    return LinearGradient(
      colors: [
        color,
        color.withOpacity(0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}
