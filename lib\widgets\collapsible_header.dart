import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class CollapsibleHeader extends StatefulWidget {
  final Animation<double> animation;
  final String searchQuery;
  final ValueChanged<String> onSearchChanged;
  final VoidCallback? onSearchClear;
  final Widget? gamificationWidget;
  final Animation<double>? gamificationAnimation;

  const CollapsibleHeader({
    Key? key,
    required this.animation,
    required this.searchQuery,
    required this.onSearchChanged,
    this.onSearchClear,
    this.gamificationWidget,
    this.gamificationAnimation,
  }) : super(key: key);

  @override
  State<CollapsibleHeader> createState() => _CollapsibleHeaderState();
}

class _CollapsibleHeaderState extends State<CollapsibleHeader> {
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.searchQuery);
  }

  @override
  void didUpdateWidget(CollapsibleHeader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchQuery != oldWidget.searchQuery) {
      _searchController.text = widget.searchQuery;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search Bar
        AnimatedBuilder(
          animation: widget.animation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, -60 * widget.animation.value),
              child: Opacity(
                opacity: 1 - widget.animation.value,
                child: widget.animation.value == 1.0
                    ? const SizedBox.shrink()
                    : Container(
                  margin: const EdgeInsets.fromLTRB(16, 8, 16, 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 12,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search posts, topics, authors...',
                      hintStyle: const TextStyle(
                        color: AppTheme.textTertiary,
                        fontSize: 15,
                      ),
                      prefixIcon: Container(
                        padding: const EdgeInsets.all(12),
                        child: const Icon(
                          Icons.search_rounded,
                          color: AppTheme.primaryColor,
                          size: 22,
                        ),
                      ),
                      suffixIcon: widget.searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(
                                Icons.clear_rounded,
                                color: AppTheme.textTertiary,
                                size: 20,
                              ),
                              onPressed: widget.onSearchClear,
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                    ),
                    onChanged: widget.onSearchChanged,
                  ),
                ),
              ),
            );
          },
        ),

        // Gamification Widget
        if (widget.gamificationWidget != null && widget.gamificationAnimation != null)
          AnimatedBuilder(
            animation: widget.gamificationAnimation!,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, -80 * widget.gamificationAnimation!.value),
                child: Opacity(
                  opacity: 1 - widget.gamificationAnimation!.value,
                  child: widget.gamificationAnimation!.value == 1.0
                      ? const SizedBox.shrink()
                      : Padding(
                          padding: const EdgeInsets.fromLTRB(16, 4, 16, 4),
                          child: widget.gamificationWidget!,
                        ),
                ),
              );
            },
          ),
      ],
    );
  }
}

class CollapsibleSearchFAB extends StatelessWidget {
  final Animation<double> animation;
  final VoidCallback onPressed;
  final String tooltip;

  const CollapsibleSearchFAB({
    Key? key,
    required this.animation,
    required this.onPressed,
    this.tooltip = 'Show Search',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return AnimatedScale(
          scale: animation.value,
          duration: const Duration(milliseconds: 200),
          child: animation.value > 0.5
              ? FloatingActionButton(
                  onPressed: onPressed,
                  backgroundColor: AppTheme.primaryColor,
                  tooltip: tooltip,
                  heroTag: "search_fab", // Unique hero tag to avoid conflicts
                  child: const Icon(
                    Icons.search,
                    color: Colors.white,
                  ),
                )
              : const SizedBox.shrink(),
        );
      },
    );
  }
}

class ScrollDirectionListener extends StatefulWidget {
  final Widget child;
  final ScrollController scrollController;
  final ValueChanged<bool> onScrollDirectionChanged;
  final double threshold;

  const ScrollDirectionListener({
    Key? key,
    required this.child,
    required this.scrollController,
    required this.onScrollDirectionChanged,
    this.threshold = 50.0,
  }) : super(key: key);

  @override
  State<ScrollDirectionListener> createState() => _ScrollDirectionListenerState();
}

class _ScrollDirectionListenerState extends State<ScrollDirectionListener> {
  double _lastScrollOffset = 0.0;
  bool _isScrollingDown = false;

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    final currentScrollOffset = widget.scrollController.position.pixels;
    final scrollDelta = currentScrollOffset - _lastScrollOffset;

    if (currentScrollOffset <= 0) {
      // At the top, always show header
      if (_isScrollingDown) {
        setState(() {
          _isScrollingDown = false;
        });
        widget.onScrollDirectionChanged(false);
      }
    } else if (scrollDelta > widget.threshold && !_isScrollingDown) {
      // Scrolling down significantly
      setState(() {
        _isScrollingDown = true;
      });
      widget.onScrollDirectionChanged(true);
    } else if (scrollDelta < -widget.threshold && _isScrollingDown) {
      // Scrolling up significantly
      setState(() {
        _isScrollingDown = false;
      });
      widget.onScrollDirectionChanged(false);
    }

    _lastScrollOffset = currentScrollOffset;
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

class SmoothCollapsibleHeader extends StatefulWidget {
  final Widget child;
  final Widget header;
  final ScrollController scrollController;
  final Duration animationDuration;
  final Curve animationCurve;
  final double scrollThreshold;

  const SmoothCollapsibleHeader({
    Key? key,
    required this.child,
    required this.header,
    required this.scrollController,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.scrollThreshold = 50.0,
  }) : super(key: key);

  @override
  State<SmoothCollapsibleHeader> createState() => _SmoothCollapsibleHeaderState();
}

class _SmoothCollapsibleHeaderState extends State<SmoothCollapsibleHeader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isHeaderVisible = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onScrollDirectionChanged(bool isScrollingDown) {
    if (isScrollingDown && _isHeaderVisible) {
      _hideHeader();
    } else if (!isScrollingDown && !_isHeaderVisible) {
      _showHeader();
    }
  }

  void _showHeader() {
    setState(() {
      _isHeaderVisible = true;
    });
    _animationController.reverse();
  }

  void _hideHeader() {
    setState(() {
      _isHeaderVisible = false;
    });
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return ScrollDirectionListener(
      scrollController: widget.scrollController,
      onScrollDirectionChanged: _onScrollDirectionChanged,
      threshold: widget.scrollThreshold,
      child: Column(
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, -100 * _animation.value),
                child: Opacity(
                  opacity: 1 - _animation.value,
                  child: _animation.value == 1.0 
                      ? const SizedBox.shrink() 
                      : widget.header,
                ),
              );
            },
          ),
          Expanded(child: widget.child),
        ],
      ),
    );
  }
}
