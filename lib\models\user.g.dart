// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: (json['id'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String? ?? '',
      firstName: json['first_name'] as String? ?? '',
      lastName: json['last_name'] as String? ?? '',
      bio: json['bio'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      phoneNumber: json['phone_number'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      location: json['location'] as String?,
      website: json['website'] as String?,
      twitterUrl: json['twitter_url'] as String?,
      linkedinUrl: json['linkedin_url'] as String?,
      githubUrl: json['github_url'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      receiveEmailNotifications:
          json['receive_email_notifications'] as bool? ?? true,
      receivePushNotifications:
          json['receive_push_notifications'] as bool? ?? true,
      isProfilePublic: json['is_profile_public'] as bool? ?? true,
      isStaff: json['is_staff'] as bool? ?? false,
      isSuperuser: json['is_superuser'] as bool? ?? false,
      dateJoined: json['date_joined'] as String?,
      updatedAt: json['updated_at'] as String?,
      preferredCountry: json['preferred_country'] == null
          ? null
          : Country.fromJson(json['preferred_country'] as Map<String, dynamic>),
      detectedCountry: json['detected_country'] == null
          ? null
          : Country.fromJson(json['detected_country'] as Map<String, dynamic>),
      showGlobalContent: json['show_global_content'] as bool? ?? true,
      autoDetectLocation: json['auto_detect_location'] as bool? ?? true,
      role: json['role'] as String? ?? 'regular_user',
      isContentCreator: json['is_content_creator'] as bool? ?? false,
      isRegularUser: json['is_regular_user'] as bool? ?? true,
      canCreateContent: json['can_create_content'] as bool? ?? false,
      canModerateContent: json['can_moderate_content'] as bool? ?? false,
      groups: (json['groups'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const <String>[],
      permissions: (json['permissions'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as bool),
          ) ??
          const <String, bool>{},
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'bio': instance.bio,
      'avatar_url': instance.avatarUrl,
      'phone_number': instance.phoneNumber,
      'date_of_birth': instance.dateOfBirth,
      'location': instance.location,
      'website': instance.website,
      'twitter_url': instance.twitterUrl,
      'linkedin_url': instance.linkedinUrl,
      'github_url': instance.githubUrl,
      'is_email_verified': instance.isEmailVerified,
      'receive_email_notifications': instance.receiveEmailNotifications,
      'receive_push_notifications': instance.receivePushNotifications,
      'is_profile_public': instance.isProfilePublic,
      'is_staff': instance.isStaff,
      'is_superuser': instance.isSuperuser,
      'date_joined': instance.dateJoined,
      'updated_at': instance.updatedAt,
      'preferred_country': instance.preferredCountry,
      'detected_country': instance.detectedCountry,
      'show_global_content': instance.showGlobalContent,
      'auto_detect_location': instance.autoDetectLocation,
      'role': instance.role,
      'is_content_creator': instance.isContentCreator,
      'is_regular_user': instance.isRegularUser,
      'can_create_content': instance.canCreateContent,
      'can_moderate_content': instance.canModerateContent,
      'groups': instance.groups,
      'permissions': instance.permissions,
    };

_$UserProfileImpl _$$UserProfileImplFromJson(Map<String, dynamic> json) =>
    _$UserProfileImpl(
      id: (json['id'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String? ?? '',
      firstName: json['first_name'] as String? ?? '',
      lastName: json['last_name'] as String? ?? '',
      bio: json['bio'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      avatar: json['avatar'] as String?,
      location: json['location'] as String?,
      website: json['website'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      isProfilePublic: json['is_profile_public'] as bool? ?? true,
      dateJoined: json['date_joined'] as String?,
      followersCount: (json['followers_count'] as num?)?.toInt() ?? 0,
      followingCount: (json['following_count'] as num?)?.toInt() ?? 0,
      postsCount: (json['posts_count'] as num?)?.toInt() ?? 0,
      isFollowing: json['is_following'] as bool? ?? false,
    );

Map<String, dynamic> _$$UserProfileImplToJson(_$UserProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'bio': instance.bio,
      'avatar_url': instance.avatarUrl,
      'avatar': instance.avatar,
      'location': instance.location,
      'website': instance.website,
      'is_email_verified': instance.isEmailVerified,
      'is_profile_public': instance.isProfilePublic,
      'date_joined': instance.dateJoined,
      'followers_count': instance.followersCount,
      'following_count': instance.followingCount,
      'posts_count': instance.postsCount,
      'is_following': instance.isFollowing,
    };
