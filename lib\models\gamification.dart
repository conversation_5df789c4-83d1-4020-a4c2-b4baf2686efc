import 'package:freezed_annotation/freezed_annotation.dart';

part 'gamification.freezed.dart';
part 'gamification.g.dart';

@freezed
class Badge with _$Badge {
  const factory Badge({
    required int id,
    required String name,
    required String description,
    @J<PERSON><PERSON><PERSON>(name: 'badge_type') required String badgeType,
    @Json<PERSON>ey(name: 'type_display') required String typeDisplay,
    required String rarity,
    @J<PERSON><PERSON><PERSON>(name: 'rarity_display') required String rarityDisplay,
    required String icon,
    required String color,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'image_url') String? imageUrl,
    @Json<PERSON>ey(name: 'points_reward') required int pointsReward,
    @Json<PERSON>ey(name: 'is_secret') @Default(false) bool isSecret,
    @Default({}) Map<String, dynamic> requirements,
  }) = _Badge;

  factory Badge.fromJson(Map<String, dynamic> json) => _$Badge<PERSON>rom<PERSON>son(json);
}

@freezed
class UserBadge with _$UserBadge {
  const factory UserBadge({
    required int id,
    required Badge badge,
    @<PERSON><PERSON>K<PERSON>(name: 'earned_at') required DateTime earnedAt,
    @JsonKey(name: 'progress_data')
    @Default({})
    Map<String, dynamic> progressData,
  }) = _UserBadge;

  factory UserBadge.fromJson(Map<String, dynamic> json) =>
      _$UserBadgeFromJson(json);
}

@freezed
class Challenge with _$Challenge {
  const factory Challenge({
    required int id,
    required String title,
    required String description,
    @JsonKey(name: 'challenge_type') required String challengeType,
    @JsonKey(name: 'type_display') required String typeDisplay,
    required String difficulty,
    @JsonKey(name: 'difficulty_display') required String difficultyDisplay,
    @Default({}) Map<String, dynamic> requirements,
    @JsonKey(name: 'points_reward') required int pointsReward,
    @JsonKey(name: 'badge_reward') Badge? badgeReward,
    @JsonKey(name: 'start_date') required DateTime startDate,
    @JsonKey(name: 'end_date') required DateTime endDate,
    @JsonKey(name: 'duration_days') required int durationDays,
    @JsonKey(name: 'max_participants') int? maxParticipants,
    @JsonKey(name: 'is_ongoing') @Default(false) bool isOngoing,
    @JsonKey(name: 'participant_count') @Default(0) int participantCount,
    @JsonKey(name: 'user_participation')
    ChallengeParticipation? userParticipation,
    @JsonKey(name: 'is_featured') @Default(false) bool isFeatured,
  }) = _Challenge;

  factory Challenge.fromJson(Map<String, dynamic> json) =>
      _$ChallengeFromJson(json);
}

@freezed
class ChallengeParticipation with _$ChallengeParticipation {
  const factory ChallengeParticipation({
    required int id,
    @JsonKey(name: 'challenge_title') String? challengeTitle,
    @Default({}) Map<String, dynamic> progress,
    @JsonKey(name: 'completion_percentage')
    @Default(0.0)
    double completionPercentage,
    @JsonKey(name: 'is_completed') @Default(false) bool isCompleted,
    @JsonKey(name: 'completed_at') DateTime? completedAt,
    @JsonKey(name: 'joined_at') required DateTime joinedAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _ChallengeParticipation;

  factory ChallengeParticipation.fromJson(Map<String, dynamic> json) =>
      _$ChallengeParticipationFromJson(json);
}

@freezed
class UserLevel with _$UserLevel {
  const factory UserLevel({
    @JsonKey(name: 'total_points') @Default(0) int totalPoints,
    @JsonKey(name: 'current_level') @Default(1) int currentLevel,
    @JsonKey(name: 'points_to_next_level') @Default(100) int pointsToNextLevel,
    @JsonKey(name: 'level_progress_percentage')
    @Default(0.0)
    double levelProgressPercentage,
    @JsonKey(name: 'next_level_points') @Default(100) int nextLevelPoints,
    @JsonKey(name: 'reading_streak') @Default(0) int readingStreak,
    @JsonKey(name: 'writing_streak') @Default(0) int writingStreak,
    @JsonKey(name: 'engagement_streak') @Default(0) int engagementStreak,
    @JsonKey(name: 'total_posts_read') @Default(0) int totalPostsRead,
    @JsonKey(name: 'total_posts_written') @Default(0) int totalPostsWritten,
    @JsonKey(name: 'total_comments_made') @Default(0) int totalCommentsMade,
    @JsonKey(name: 'total_likes_given') @Default(0) int totalLikesGiven,
    @JsonKey(name: 'total_voice_comments') @Default(0) int totalVoiceComments,
  }) = _UserLevel;

  factory UserLevel.fromJson(Map<String, dynamic> json) =>
      _$UserLevelFromJson(json);
}

@freezed
class PointTransaction with _$PointTransaction {
  const factory PointTransaction({
    required int id,
    @JsonKey(name: 'transaction_type') required String transactionType,
    @JsonKey(name: 'type_display') required String typeDisplay,
    required int points,
    required String description,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _PointTransaction;

  factory PointTransaction.fromJson(Map<String, dynamic> json) =>
      _$PointTransactionFromJson(json);
}

@freezed
class LeaderboardEntry with _$LeaderboardEntry {
  const factory LeaderboardEntry({
    @JsonKey(name: 'user_id') required int userId,
    required String username,
    @JsonKey(name: 'total_points') required int totalPoints,
    @JsonKey(name: 'current_level') required int currentLevel,
    required int rank,
    @JsonKey(name: 'badge_count') required int badgeCount,
  }) = _LeaderboardEntry;

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardEntryFromJson(json);
}

@freezed
class GamificationUserProfile with _$GamificationUserProfile {
  const factory GamificationUserProfile({
    required int id,
    required String username,
    required String email,
    @JsonKey(name: 'date_joined') required DateTime dateJoined,
    UserLevel? level,
    @JsonKey(name: 'earned_badges') @Default([]) List<UserBadge> earnedBadges,
    @JsonKey(name: 'recent_transactions')
    @Default([])
    List<PointTransaction> recentTransactions,
    @JsonKey(name: 'active_challenges')
    @Default([])
    List<ChallengeParticipation> activeChallenges,
  }) = _GamificationUserProfile;

  factory GamificationUserProfile.fromJson(Map<String, dynamic> json) =>
      _$GamificationUserProfileFromJson(json);
}

// Enums for better type safety
enum BadgeType {
  reading,
  writing,
  engagement,
  community,
  special,
  milestone,
}

enum BadgeRarity {
  common,
  uncommon,
  rare,
  epic,
  legendary,
}

enum ChallengeType {
  reading,
  writing,
  engagement,
  community,
}

enum ChallengeDifficulty {
  easy,
  medium,
  hard,
  expert,
}

extension BadgeTypeExtension on BadgeType {
  String get value {
    switch (this) {
      case BadgeType.reading:
        return 'reading';
      case BadgeType.writing:
        return 'writing';
      case BadgeType.engagement:
        return 'engagement';
      case BadgeType.community:
        return 'community';
      case BadgeType.special:
        return 'special';
      case BadgeType.milestone:
        return 'milestone';
    }
  }

  String get displayName {
    switch (this) {
      case BadgeType.reading:
        return 'Reading Achievement';
      case BadgeType.writing:
        return 'Writing Achievement';
      case BadgeType.engagement:
        return 'Engagement Achievement';
      case BadgeType.community:
        return 'Community Achievement';
      case BadgeType.special:
        return 'Special Achievement';
      case BadgeType.milestone:
        return 'Milestone Achievement';
    }
  }
}

extension BadgeRarityExtension on BadgeRarity {
  String get value {
    switch (this) {
      case BadgeRarity.common:
        return 'common';
      case BadgeRarity.uncommon:
        return 'uncommon';
      case BadgeRarity.rare:
        return 'rare';
      case BadgeRarity.epic:
        return 'epic';
      case BadgeRarity.legendary:
        return 'legendary';
    }
  }

  String get displayName {
    switch (this) {
      case BadgeRarity.common:
        return 'Common';
      case BadgeRarity.uncommon:
        return 'Uncommon';
      case BadgeRarity.rare:
        return 'Rare';
      case BadgeRarity.epic:
        return 'Epic';
      case BadgeRarity.legendary:
        return 'Legendary';
    }
  }
}

extension ChallengeTypeExtension on ChallengeType {
  String get value {
    switch (this) {
      case ChallengeType.reading:
        return 'reading';
      case ChallengeType.writing:
        return 'writing';
      case ChallengeType.engagement:
        return 'engagement';
      case ChallengeType.community:
        return 'community';
    }
  }

  String get displayName {
    switch (this) {
      case ChallengeType.reading:
        return 'Reading Challenge';
      case ChallengeType.writing:
        return 'Writing Challenge';
      case ChallengeType.engagement:
        return 'Engagement Challenge';
      case ChallengeType.community:
        return 'Community Challenge';
    }
  }
}

extension ChallengeDifficultyExtension on ChallengeDifficulty {
  String get value {
    switch (this) {
      case ChallengeDifficulty.easy:
        return 'easy';
      case ChallengeDifficulty.medium:
        return 'medium';
      case ChallengeDifficulty.hard:
        return 'hard';
      case ChallengeDifficulty.expert:
        return 'expert';
    }
  }

  String get displayName {
    switch (this) {
      case ChallengeDifficulty.easy:
        return 'Easy';
      case ChallengeDifficulty.medium:
        return 'Medium';
      case ChallengeDifficulty.hard:
        return 'Hard';
      case ChallengeDifficulty.expert:
        return 'Expert';
    }
  }
}
