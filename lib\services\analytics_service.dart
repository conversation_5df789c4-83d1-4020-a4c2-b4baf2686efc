import 'dart:async';
import 'dart:math';
import 'package:dio/dio.dart';
import '../config/api_config.dart';
import '../models/reading_session.dart';
import '../models/content_analytics.dart';

class AnalyticsService {
  late final Dio _dio;
  Timer? _progressTimer;
  String? _currentSessionId;
  DateTime? _sessionStart;
  int _currentPostId = 0;
  double _currentProgress = 0.0;
  double _maxScrollDepth = 0.0;

  AnalyticsService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      sendTimeout: ApiConfig.sendTimeout,
    ));
  }

  // Start a reading session
  Future<String?> startReadingSession(int postId, {String deviceType = 'mobile'}) async {
    try {
      final response = await _dio.post(
        '/api/v1/analytics/reading-sessions/start/',
        data: {
          'post_id': postId,
          'device_type': deviceType,
        },
      );

      if (response.statusCode == 201) {
        _currentSessionId = response.data['session_id'].toString();
        _sessionStart = DateTime.now();
        _currentPostId = postId;
        _currentProgress = 0.0;
        _maxScrollDepth = 0.0;
        
        // Start automatic progress tracking
        _startProgressTracking();
        
        return _currentSessionId;
      }
    } catch (e) {
      print('Error starting reading session: $e');
    }
    return null;
  }

  // Update reading progress
  Future<void> updateProgress(double progressPercentage, double scrollDepth) async {
    if (_currentSessionId == null) return;

    try {
      _currentProgress = progressPercentage;
      _maxScrollDepth = max(_maxScrollDepth, scrollDepth);

      await _dio.post(
        '/api/v1/analytics/reading-sessions/update/',
        data: {
          'session_id': _currentSessionId,
          'progress_percentage': progressPercentage,
          'scroll_depth': scrollDepth,
        },
      );
    } catch (e) {
      print('Error updating reading progress: $e');
    }
  }

  // End reading session
  Future<void> endReadingSession() async {
    if (_currentSessionId == null) return;

    try {
      _stopProgressTracking();
      
      await _dio.post(
        '/api/v1/analytics/reading-sessions/end/',
        data: {
          'session_id': _currentSessionId,
        },
      );

      _currentSessionId = null;
      _sessionStart = null;
      _currentPostId = 0;
      _currentProgress = 0.0;
      _maxScrollDepth = 0.0;
    } catch (e) {
      print('Error ending reading session: $e');
    }
  }

  // Get content analytics for a post
  Future<ContentAnalytics?> getContentAnalytics(int postId) async {
    try {
      final response = await _dio.get('/api/v1/analytics/content/$postId/');
      
      if (response.statusCode == 200) {
        return ContentAnalytics.fromJson(response.data);
      }
    } catch (e) {
      print('Error fetching content analytics: $e');
    }
    return null;
  }

  // Get user reading statistics
  Future<Map<String, dynamic>?> getUserReadingStats() async {
    try {
      final response = await _dio.get('/api/v1/analytics/user/stats/');
      
      if (response.statusCode == 200) {
        return response.data;
      }
    } catch (e) {
      print('Error fetching user reading stats: $e');
    }
    return null;
  }

  // Calculate estimated reading time based on content
  static int calculateReadingTime(String content, {int wordsPerMinute = 200}) {
    final wordCount = content.split(RegExp(r'\s+')).length;
    final minutes = (wordCount / wordsPerMinute).ceil();
    return minutes * 60; // Return in seconds
  }

  // Calculate content complexity score
  static double calculateComplexityScore(String content) {
    final words = content.split(RegExp(r'\s+'));
    final sentences = content.split(RegExp(r'[.!?]+'));
    
    if (words.isEmpty || sentences.isEmpty) return 0.0;
    
    final avgWordsPerSentence = words.length / sentences.length;
    final avgWordLength = words.map((w) => w.length).reduce((a, b) => a + b) / words.length;
    
    // Simple complexity calculation (0-100 scale)
    final complexityScore = (avgWordsPerSentence * 2) + (avgWordLength * 5);
    return min(100.0, complexityScore);
  }

  // Start automatic progress tracking
  void _startProgressTracking() {
    _progressTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_currentSessionId != null) {
        // This would be called by the UI to update progress
        // The actual progress calculation should be done by the reading widget
      }
    });
  }

  // Stop progress tracking
  void _stopProgressTracking() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  // Calculate reading speed in words per minute
  static double calculateReadingSpeed(int wordsRead, Duration timeSpent) {
    if (timeSpent.inSeconds == 0) return 0.0;
    final minutes = timeSpent.inSeconds / 60.0;
    return wordsRead / minutes;
  }

  // Get reading difficulty level
  static String getReadingDifficultyLevel(double complexityScore) {
    if (complexityScore >= 80) return 'Very Difficult';
    if (complexityScore >= 60) return 'Difficult';
    if (complexityScore >= 40) return 'Moderate';
    if (complexityScore >= 20) return 'Easy';
    return 'Very Easy';
  }

  // Format reading time for display
  static String formatReadingTime(int seconds) {
    if (seconds < 60) {
      return '< 1 min read';
    } else if (seconds < 3600) {
      final minutes = (seconds / 60).round();
      return '$minutes min read';
    } else {
      final hours = (seconds / 3600).floor();
      final minutes = ((seconds % 3600) / 60).round();
      return '${hours}h ${minutes}m read';
    }
  }

  // Dispose resources
  void dispose() {
    _stopProgressTracking();
    endReadingSession();
  }
}
