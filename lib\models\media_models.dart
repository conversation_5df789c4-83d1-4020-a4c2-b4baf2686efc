/// Media optimization and gallery models for Trendy Blog Platform
/// Supports progressive loading, full-screen viewing, and media scrolling

import 'dart:ui' show Size;
import '../config/api_config.dart';

class OptimizedMedia {
  final int id;
  final String type;
  final String title;
  final String? caption;
  final String? description;
  final int order;
  final DateTime createdAt;
  final bool isOptimized;
  final ResponsiveMediaData? responsive;
  final Map<String, String>? optimizedUrls;
  final Map<String, dynamic>? metadata;
  final String? src;
  final String? preview;

  OptimizedMedia({
    required this.id,
    required this.type,
    required this.title,
    this.caption,
    this.description,
    required this.order,
    required this.createdAt,
    required this.isOptimized,
    this.responsive,
    this.optimizedUrls,
    this.metadata,
    this.src,
    this.preview,
  });

  factory OptimizedMedia.fromJson(Map<String, dynamic> json) {
    return OptimizedMedia(
      id: json['id'],
      type: json['type'],
      title: json['title'] ?? '',
      caption: json['caption'],
      description: json['description'],
      order: json['order'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      isOptimized: json['is_optimized'] ?? false,
      responsive: json['responsive'] != null 
          ? ResponsiveMediaData.fromJson(json['responsive'])
          : null,
      optimizedUrls: json['optimized_urls'] != null
          ? Map<String, String>.from(json['optimized_urls'])
          : null,
      metadata: json['metadata'],
      src: json['src'],
      preview: json['preview'],
    );
  }

  /// Get the best quality URL for the given size preference
  String getBestUrl(String sizePreference) {
    String? url;
    if (isOptimized && optimizedUrls != null) {
      url = optimizedUrls![sizePreference] ??
            optimizedUrls!['large'] ??
            optimizedUrls!['medium'] ??
            src;
    } else {
      url = src ?? preview;
    }
    return ApiConfig.getMediaUrl(url);
  }

  /// Get thumbnail URL for gallery preview
  String get thumbnailUrl {
    String? url;
    if (isOptimized && optimizedUrls != null) {
      url = optimizedUrls!['small'] ?? optimizedUrls!['thumbnail'] ?? src;
    } else {
      url = src ?? preview;
    }
    return ApiConfig.getMediaUrl(url);
  }

  /// Get full-size URL for full-screen viewing
  String get fullSizeUrl {
    String? url;
    if (isOptimized && optimizedUrls != null) {
      url = optimizedUrls!['large'] ?? optimizedUrls!['original'] ?? src;
    } else {
      url = src;
    }
    return ApiConfig.getMediaUrl(url);
  }
}

class ResponsiveMediaData {
  final String src;
  final String? srcset;
  final String? placeholder;
  final String? sizes;
  final String loading;

  ResponsiveMediaData({
    required this.src,
    this.srcset,
    this.placeholder,
    this.sizes,
    this.loading = 'lazy',
  });

  factory ResponsiveMediaData.fromJson(Map<String, dynamic> json) {
    return ResponsiveMediaData(
      src: json['src'] ?? '',
      srcset: json['srcset'],
      placeholder: json['placeholder'],
      sizes: json['sizes'],
      loading: json['loading'] ?? 'lazy',
    );
  }
}

class MediaGalleryItem {
  final int id;
  final int index;
  final String type;
  final String title;
  final String? caption;
  final String? description;
  final String? thumbnail;
  final String? medium;
  final String? large;
  final String? original;
  final String? src;
  final ResponsiveMediaData? responsive;
  final Map<String, dynamic>? metadata;
  final String? videoUrl;
  final String? poster;
  final Map<String, dynamic>? videoMetadata;

  MediaGalleryItem({
    required this.id,
    required this.index,
    required this.type,
    required this.title,
    this.caption,
    this.description,
    this.thumbnail,
    this.medium,
    this.large,
    this.original,
    this.src,
    this.responsive,
    this.metadata,
    this.videoUrl,
    this.poster,
    this.videoMetadata,
  });

  factory MediaGalleryItem.fromJson(Map<String, dynamic> json) {
    return MediaGalleryItem(
      id: json['id'],
      index: json['index'],
      type: json['type'],
      title: json['title'] ?? '',
      caption: json['caption'],
      description: json['description'],
      thumbnail: json['thumbnail'],
      medium: json['medium'],
      large: json['large'],
      original: json['original'],
      src: json['src'],
      responsive: json['responsive'] != null 
          ? ResponsiveMediaData.fromJson(json['responsive'])
          : null,
      metadata: json['metadata'],
      videoUrl: json['video_url'],
      poster: json['poster'],
      videoMetadata: json['video_metadata'],
    );
  }

  /// Get the best URL for the current viewing context
  String getBestUrl({String quality = 'large'}) {
    String? url;
    switch (quality) {
      case 'thumbnail':
        url = thumbnail ?? medium ?? large ?? src;
        break;
      case 'medium':
        url = medium ?? large ?? original ?? src;
        break;
      case 'large':
        url = large ?? original ?? medium ?? src;
        break;
      case 'original':
        url = original ?? large ?? medium ?? src;
        break;
      default:
        url = large ?? medium ?? src;
        break;
    }
    return ApiConfig.getMediaUrl(url);
  }

  /// Get video URL if this is a video item
  String? get videoSource => type == 'video' ? ApiConfig.getMediaUrl(videoUrl) : null;

  /// Get poster/thumbnail for video
  String? get videoPoster => type == 'video' ? ApiConfig.getMediaUrl(poster ?? thumbnail) : null;

  /// Check if this is an image
  bool get isImage => type == 'image';

  /// Check if this is a video
  bool get isVideo => type == 'video';

  /// Get aspect ratio from metadata
  double get aspectRatio {
    if (metadata != null && metadata!['aspect_ratio'] != null) {
      return (metadata!['aspect_ratio'] as num).toDouble();
    }
    return 16 / 9; // Default aspect ratio
  }

  /// Get original dimensions
  Size? get originalSize {
    if (metadata != null && 
        metadata!['original_width'] != null && 
        metadata!['original_height'] != null) {
      return Size(
        (metadata!['original_width'] as num).toDouble(),
        (metadata!['original_height'] as num).toDouble(),
      );
    }
    return null;
  }
}

class MediaGalleryData {
  final int postId;
  final String postTitle;
  final int totalItems;
  final List<MediaGalleryItem> galleryItems;
  final MediaNavigationConfig navigation;

  MediaGalleryData({
    required this.postId,
    required this.postTitle,
    required this.totalItems,
    required this.galleryItems,
    required this.navigation,
  });

  factory MediaGalleryData.fromJson(Map<String, dynamic> json) {
    return MediaGalleryData(
      postId: json['post_id'],
      postTitle: json['post_title'] ?? '',
      totalItems: json['total_items'] ?? 0,
      galleryItems: (json['gallery_items'] as List<dynamic>?)
          ?.map((item) => MediaGalleryItem.fromJson(item))
          .toList() ?? [],
      navigation: MediaNavigationConfig.fromJson(json['navigation'] ?? {}),
    );
  }

  /// Check if gallery has multiple items
  bool get hasMultipleItems => totalItems > 1;

  /// Check if gallery has any items
  bool get hasItems => totalItems > 0;

  /// Get item by index safely
  MediaGalleryItem? getItemAt(int index) {
    if (index >= 0 && index < galleryItems.length) {
      return galleryItems[index];
    }
    return null;
  }

  /// Get next item index (with looping if enabled)
  int getNextIndex(int currentIndex) {
    if (navigation.loopGallery) {
      return (currentIndex + 1) % galleryItems.length;
    }
    return (currentIndex + 1).clamp(0, galleryItems.length - 1);
  }

  /// Get previous item index (with looping if enabled)
  int getPreviousIndex(int currentIndex) {
    if (navigation.loopGallery) {
      return (currentIndex - 1 + galleryItems.length) % galleryItems.length;
    }
    return (currentIndex - 1).clamp(0, galleryItems.length - 1);
  }
}

class MediaNavigationConfig {
  final bool enableSwipe;
  final bool enableKeyboard;
  final bool enableZoom;
  final bool autoPlayVideos;
  final bool loopGallery;

  MediaNavigationConfig({
    this.enableSwipe = true,
    this.enableKeyboard = true,
    this.enableZoom = true,
    this.autoPlayVideos = false,
    this.loopGallery = true,
  });

  factory MediaNavigationConfig.fromJson(Map<String, dynamic> json) {
    return MediaNavigationConfig(
      enableSwipe: json['enable_swipe'] ?? true,
      enableKeyboard: json['enable_keyboard'] ?? true,
      enableZoom: json['enable_zoom'] ?? true,
      autoPlayVideos: json['auto_play_videos'] ?? false,
      loopGallery: json['loop_gallery'] ?? true,
    );
  }
}

class ProgressiveLoadingData {
  final String placeholder;
  final String lowQuality;
  final String mediumQuality;
  final String highQuality;
  final String loadingStrategy;

  ProgressiveLoadingData({
    required this.placeholder,
    required this.lowQuality,
    required this.mediumQuality,
    required this.highQuality,
    this.loadingStrategy = 'progressive',
  });

  factory ProgressiveLoadingData.fromJson(Map<String, dynamic> json) {
    final data = json['progressive_data'] ?? {};
    return ProgressiveLoadingData(
      placeholder: data['placeholder'] ?? '',
      lowQuality: data['low_quality'] ?? '',
      mediumQuality: data['medium_quality'] ?? '',
      highQuality: data['high_quality'] ?? '',
      loadingStrategy: data['loading_strategy'] ?? 'progressive',
    );
  }

  /// Get URL for the specified quality level
  String getUrlForQuality(ImageQuality quality) {
    switch (quality) {
      case ImageQuality.placeholder:
        return placeholder;
      case ImageQuality.low:
        return lowQuality;
      case ImageQuality.medium:
        return mediumQuality;
      case ImageQuality.high:
        return highQuality;
    }
  }
}

enum ImageQuality {
  placeholder,
  low,
  medium,
  high,
}


