#!/usr/bin/env python3
"""
Comprehensive API Health Check Script
Tests all endpoints and features to ensure everything is working correctly.
"""

import os
import sys
import django
import requests
import json
import time
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from django.contrib.auth import get_user_model
from blog.models import Post, Category
from gamification.models import Badge, Challenge

User = get_user_model()

class HealthChecker:
    def __init__(self, base_url='http://127.0.0.1:8000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'errors': []
        }
    
    def log(self, message, level='INFO'):
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def test_endpoint(self, endpoint, method='GET', data=None, expected_status=200, description=None):
        """Test a single API endpoint"""
        self.results['total_tests'] += 1
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method == 'GET':
                response = self.session.get(url)
            elif method == 'POST':
                response = self.session.post(url, json=data)
            elif method == 'PUT':
                response = self.session.put(url, json=data)
            elif method == 'DELETE':
                response = self.session.delete(url)
            
            if response.status_code == expected_status:
                self.results['passed_tests'] += 1
                self.log(f"✅ {description or endpoint} - Status: {response.status_code}")
                return True, response
            else:
                self.results['failed_tests'] += 1
                error_msg = f"❌ {description or endpoint} - Expected: {expected_status}, Got: {response.status_code}"
                self.log(error_msg, 'ERROR')
                self.results['errors'].append(error_msg)
                return False, response
                
        except Exception as e:
            self.results['failed_tests'] += 1
            error_msg = f"❌ {description or endpoint} - Exception: {str(e)}"
            self.log(error_msg, 'ERROR')
            self.results['errors'].append(error_msg)
            return False, None
    
    def test_blog_endpoints(self):
        """Test blog-related endpoints"""
        self.log("Testing Blog Endpoints...", 'INFO')
        
        # Test posts list
        self.test_endpoint('/api/v1/posts/', description='Posts List')
        
        # Test post detail (assuming post with ID 1 exists)
        self.test_endpoint('/api/v1/posts/1/', description='Post Detail')
        
        # Test categories
        self.test_endpoint('/api/v1/categories/', description='Categories List')
        
        # Test comments for a post
        self.test_endpoint('/api/v1/posts/welcome-to-trendy-blog/comments/', description='Post Comments')
    
    def test_analytics_endpoints(self):
        """Test analytics endpoints"""
        self.log("Testing Analytics Endpoints...", 'INFO')
        
        # Test user stats (requires auth, expect 403 for unauthenticated)
        self.test_endpoint('/api/v1/analytics/user/stats/',
                          expected_status=403, description='User Stats (Unauthorized)')
    
    def test_interactive_endpoints(self):
        """Test interactive content endpoints"""
        self.log("Testing Interactive Content Endpoints...", 'INFO')
        
        # Test interactive blocks for a post
        self.test_endpoint('/api/v1/interactive/posts/1/blocks/', 
                          description='Interactive Blocks')
    
    def test_voice_endpoints(self):
        """Test voice features endpoints"""
        self.log("Testing Voice Features Endpoints...", 'INFO')
        
        # Test voice comments for a post
        self.test_endpoint('/api/v1/voice/posts/1/voice-comments/', 
                          description='Voice Comments')
    
    def test_gamification_endpoints(self):
        """Test gamification endpoints"""
        self.log("Testing Gamification Endpoints...", 'INFO')
        
        # Test badges
        self.test_endpoint('/api/v1/gamification/badges/', description='Badges List')
        
        # Test challenges
        self.test_endpoint('/api/v1/gamification/challenges/', description='Challenges List')
        
        # Test leaderboard
        self.test_endpoint('/api/v1/gamification/leaderboard/', description='Leaderboard')
        
        # Test user level (requires auth, expect 403 for unauthenticated)
        self.test_endpoint('/api/v1/gamification/user/level/',
                          expected_status=403, description='User Level (Unauthorized)')
    
    def test_social_endpoints(self):
        """Test social features endpoints"""
        self.log("Testing Social Features Endpoints...", 'INFO')
        
        # Test search
        self.test_endpoint('/api/v1/social/search/?q=test', description='Search')
        
        # Test recommendations
        self.test_endpoint('/api/v1/social/recommendations/', description='Recommendations')
        
        # Test search suggestions
        self.test_endpoint('/api/v1/social/search/suggestions/?q=te', 
                          description='Search Suggestions')
        
        # Test user profile (requires auth, expect 403 for unauthenticated)
        self.test_endpoint('/api/v1/social/profile/',
                          expected_status=403, description='User Profile (Unauthorized)')
    
    def test_performance(self):
        """Test API performance"""
        self.log("Testing API Performance...", 'INFO')
        
        endpoints = [
            '/api/v1/posts/',
            '/api/v1/categories/',
            '/api/v1/gamification/badges/',
            '/api/v1/gamification/challenges/',
            '/api/v1/social/recommendations/'
        ]
        
        for endpoint in endpoints:
            start_time = time.time()
            success, response = self.test_endpoint(endpoint, description=f'Performance: {endpoint}')
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if success:
                if response_time < 1000:  # Less than 1 second
                    self.log(f"⚡ {endpoint} - Response time: {response_time:.2f}ms (Excellent)")
                elif response_time < 3000:  # Less than 3 seconds
                    self.log(f"⚠️  {endpoint} - Response time: {response_time:.2f}ms (Acceptable)")
                else:
                    self.log(f"🐌 {endpoint} - Response time: {response_time:.2f}ms (Slow)", 'WARNING')
    
    def test_data_integrity(self):
        """Test data integrity"""
        self.log("Testing Data Integrity...", 'INFO')
        
        # Check if we have test data
        posts_count = Post.objects.count()
        categories_count = Category.objects.count()
        badges_count = Badge.objects.count()
        challenges_count = Challenge.objects.count()
        
        self.log(f"📊 Database Stats:")
        self.log(f"   Posts: {posts_count}")
        self.log(f"   Categories: {categories_count}")
        self.log(f"   Badges: {badges_count}")
        self.log(f"   Challenges: {challenges_count}")
        
        if posts_count == 0:
            self.log("⚠️  No posts found in database", 'WARNING')
        if categories_count == 0:
            self.log("⚠️  No categories found in database", 'WARNING')
        if badges_count == 0:
            self.log("⚠️  No badges found in database", 'WARNING')
        if challenges_count == 0:
            self.log("⚠️  No challenges found in database", 'WARNING')
    
    def run_all_tests(self):
        """Run all health checks"""
        self.log("🚀 Starting Comprehensive API Health Check", 'INFO')
        self.log("=" * 60)
        
        start_time = time.time()
        
        # Test all endpoint categories
        self.test_blog_endpoints()
        self.test_analytics_endpoints()
        self.test_interactive_endpoints()
        self.test_voice_endpoints()
        self.test_gamification_endpoints()
        self.test_social_endpoints()
        
        # Performance and data integrity tests
        self.test_performance()
        self.test_data_integrity()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Print summary
        self.log("=" * 60)
        self.log("🏁 Health Check Summary", 'INFO')
        self.log(f"Total Tests: {self.results['total_tests']}")
        self.log(f"Passed: {self.results['passed_tests']}")
        self.log(f"Failed: {self.results['failed_tests']}")
        self.log(f"Success Rate: {(self.results['passed_tests'] / self.results['total_tests'] * 100):.1f}%")
        self.log(f"Total Time: {total_time:.2f} seconds")
        
        if self.results['failed_tests'] > 0:
            self.log("❌ Some tests failed. Check the errors above.", 'ERROR')
            return False
        else:
            self.log("✅ All tests passed! API is healthy.", 'INFO')
            return True

if __name__ == '__main__':
    checker = HealthChecker()
    success = checker.run_all_tests()
    sys.exit(0 if success else 1)
