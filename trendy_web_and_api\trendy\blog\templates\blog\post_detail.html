{% extends 'blog/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ post.title }} - Trendy Blog{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <article class="post-content">
                <!-- Post Header -->
                <header class="post-header mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center gap-2">
                            <a href="{% url 'home' %}?category={{ post.category.slug }}" 
                               class="badge bg-gradient text-decoration-none">{{ post.category.name }}</a>
                            <span class="text-muted">{{ post.created_at|date:"F j, Y" }}</span>
                        </div>
                        {% if user.is_authenticated and user == post.author or user.is_staff %}
                        <div class="dropdown">
                            <button class="btn btn-link text-dark p-0" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="{% url 'post-edit' post.slug %}">
                                        <i class="fas fa-edit me-2"></i>Edit Post
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#" 
                                       data-bs-toggle="modal" data-bs-target="#deletePostModal">
                                        <i class="fas fa-trash me-2"></i>Delete Post
                                    </a>
                                </li>
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    <h1 class="display-4 fw-bold mb-3">{{ post.title }}</h1>
                    <div class="d-flex align-items-center gap-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar me-2">
                                <img src="{{ post.author.profile.image.url }}" 
                                     alt="{{ post.author.username }}" 
                                     class="rounded-circle"
                                     width="40"
                                     height="40">
                            </div>
                            <div>
                                <div class="fw-medium">{{ post.author.get_full_name|default:post.author.username }}</div>
                                <small class="text-muted">{{ post.read_time }} min read</small>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <form method="post" action="{% url 'post-like' post.slug %}" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-like {% if user in post.likes.all %}liked{% endif %}">
                                    <i class="fas fa-heart"></i>
                                    <span>{{ post.like_count }}</span>
                                </button>
                            </form>
                            <button class="btn btn-like" onclick="sharePost()">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                </header>

                <!-- Post Content -->
                <div class="post-body">
                    {{ post.content|safe }}
                </div>

                <!-- Post Images -->
                {% if post.media_items.exists %}
                <div class="post-images mt-4">
                    <div class="row g-3">
                        {% for media in post.media_items.all %}
                        {% if media.media_type == 'image' %}
                        <div class="col-md-6">
                            <div class="image-container">
                                <img src="{% if media.image %}{{ media.image.url }}{% else %}{{ media.image_url }}{% endif %}"
                                     alt="{{ media.caption|default:post.title }}"
                                     class="img-fluid rounded-3">
                                {% if media.caption %}
                                <div class="image-caption text-center mt-2 text-muted">
                                    {{ media.caption }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Post Videos -->
                {% if post.videos.exists %}
                <div class="post-videos mt-4">
                    <div class="row g-3">
                        {% for video in post.videos.all %}
                        <div class="col-md-6">
                            <div class="video-container">
                                <video controls class="w-100 rounded-3">
                                    <source src="{{ video.video.url }}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                                {% if video.title or video.description %}
                                <div class="video-info mt-2">
                                    {% if video.title %}
                                    <h5 class="mb-1">{{ video.title }}</h5>
                                    {% endif %}
                                    {% if video.description %}
                                    <p class="text-muted mb-0">{{ video.description }}</p>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Post Tags -->
                {% if post.tags.exists %}
                <div class="post-tags mt-4">
                    {% for tag in post.tags.all %}
                    <a href="{% url 'home' %}?tag={{ tag.slug }}" class="tag-pill">
                        #{{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
                {% endif %}
            </article>

            <!-- Comments Section -->
            <section class="comments-section mt-5">
                <h3 class="fw-bold mb-4">Comments</h3>
                {% if user.is_authenticated %}
                <form method="post" action="{% url 'post-comment' post.slug %}" class="comment-form mb-4">
                    {% csrf_token %}
                    {{ comment_form|crispy }}
                    <button type="submit" class="btn btn-gradient">
                        Post Comment
                    </button>
                </form>
                {% else %}
                <div class="auth-prompt text-center py-4">
                    <p class="text-muted mb-2">Please <a href="{% url 'login' %}" class="auth-link">login</a> to join the conversation</p>
                </div>
                {% endif %}

                <!-- Comments List -->
                <div class="comments-list">
                    {% include "blog/comments.html" with comments=comments %}
                </div>
            </section>
        </div>

        <!-- Sidebar -->
        <aside class="col-lg-4">
            <!-- Related Posts -->
            {% if related_posts %}
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="fw-bold mb-3">Related Posts</h5>
                    <div class="related-posts">
                        {% for related_post in related_posts %}
                        <a href="{% url 'post-detail' related_post.slug %}" class="related-post-item">
                            {% if related_post.media_items.exists %}
                            {% with first_media=related_post.media_items.first %}
                            <img src="{% if first_media.image %}{{ first_media.image.url }}{% else %}{{ first_media.image_url }}{% endif %}"
                                 alt="{{ related_post.title }}"
                                 class="related-post-image">
                            {% endwith %}
                            {% endif %}
                            <div class="related-post-content">
                                <h6 class="mb-1">{{ related_post.title }}</h6>
                                <small class="text-muted">{{ related_post.created_at|date:"M j, Y" }}</small>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Newsletter -->
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="fw-bold mb-3">Subscribe to Newsletter</h5>
                    <p class="text-muted mb-3">Get the latest posts delivered right to your inbox.</p>
                    <form method="post" action="{% url 'newsletter-subscribe' %}" class="newsletter-form">
                        {% csrf_token %}
                        <div class="input-group">
                            <input type="email" name="email" class="form-control" placeholder="Enter your email" required>
                            <button type="submit" class="btn btn-gradient">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </aside>
    </div>
</div>

<!-- Delete Post Modal -->
<div class="modal fade" id="deletePostModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Post</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this post? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'post-delete' post.slug %}" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .post-content {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .video-container {
        position: relative;
        width: 100%;
        background: #000;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .video-container video {
        display: block;
        width: 100%;
        max-height: 400px;
        object-fit: contain;
    }

    .video-info {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
    }

    .image-container {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
    }

    .image-container img {
        width: 100%;
        height: auto;
        display: block;
    }

    .image-caption {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .tag-pill {
        display: inline-block;
        padding: 0.375rem 1rem;
        background: rgba(79, 70, 229, 0.1);
        color: var(--primary-color);
        border-radius: 2rem;
        text-decoration: none;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        margin: 0.25rem;
    }

    .tag-pill:hover {
        background: var(--gradient);
        color: white;
    }

    .btn-like {
        background: none;
        border: none;
        color: #6c757d;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-like:hover, .btn-like.liked {
        color: #dc3545;
        background: rgba(220, 53, 69, 0.1);
    }

    .related-post-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem 0;
        text-decoration: none;
        color: var(--text-color);
        border-bottom: 1px solid #e5e7eb;
    }

    .related-post-item:last-child {
        border-bottom: none;
    }

    .related-post-image {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border-radius: 0.5rem;
    }

    .related-post-content h6 {
        margin: 0;
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .auth-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .auth-link:hover {
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .post-content {
            padding: 1.5rem;
        }

        .video-container video {
            max-height: 300px;
        }
    }
</style>

<script>
    // Track view count when page loads
    document.addEventListener('DOMContentLoaded', function() {
        fetch('/api/v1/posts/{{ post.id }}/increment_view/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        }).then(response => {
            if (response.ok) {
                console.log('View count updated');
            }
        }).catch(error => {
            console.log('Error updating view count:', error);
        });
    });

    function sharePost() {
        if (navigator.share) {
            navigator.share({
                title: '{{ post.title }}',
                text: '{{ post.content|striptags|truncatewords:30 }}',
                url: window.location.href
            });
        } else {
            // Fallback for browsers that don't support Web Share API
            const shareUrl = window.location.href;
            navigator.clipboard.writeText(shareUrl).then(() => {
                alert('Link copied to clipboard!');
            });
        }
    }
</script>
{% endblock %}