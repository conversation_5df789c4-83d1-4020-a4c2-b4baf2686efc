#!/usr/bin/env python3
"""
Comprehensive App Store Compliance Test for Trendy App
"""

import os
import re
import json

def test_compliance_files():
    """Test if all compliance files are present"""
    print("🛡️ Testing App Store Compliance")
    print("=" * 50)
    
    print("1. 📁 Testing compliance file structure...")
    
    required_files = [
        'lib/services/compliance_service.dart',
        'lib/services/content_moderation_service.dart',
        'legal_documents/PRIVACY_POLICY.md',
        'APP_STORE_COMPLIANCE_GUIDE.md',
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - Missing")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} compliance files")
        return False
    
    print(f"\n✅ All {len(required_files)} compliance files found")
    return True


def test_payment_compliance():
    """Test payment system compliance"""
    print("\n2. 💳 Testing payment compliance...")
    
    # Check if in_app_purchase is in pubspec.yaml
    try:
        with open('pubspec.yaml', 'r') as f:
            pubspec_content = f.read()
            
        if 'in_app_purchase:' in pubspec_content:
            print("   ✅ in_app_purchase dependency found")
        else:
            print("   ❌ in_app_purchase dependency missing")
            
        # Check compliance service
        with open('lib/services/compliance_service.dart', 'r') as f:
            compliance_content = f.read()
            
        payment_features = [
            'purchaseDigitalContent',
            'purchasePhysicalGood',
            'InAppPurchase',
            'showPaymentDisclaimer',
        ]
        
        found_features = []
        for feature in payment_features:
            if feature in compliance_content:
                found_features.append(feature)
        
        print(f"   ✅ Payment compliance features: {len(found_features)}/{len(payment_features)}")
        
        if 'PayPal' in compliance_content and 'physical' in compliance_content.lower():
            print("   ✅ PayPal restricted to physical goods only")
        else:
            print("   ⚠️  PayPal usage needs verification")
            
    except Exception as e:
        print(f"   ❌ Error checking payment compliance: {e}")


def test_content_moderation():
    """Test content moderation system"""
    print("\n3. 🛡️ Testing content moderation...")
    
    try:
        with open('lib/services/content_moderation_service.dart', 'r') as f:
            moderation_content = f.read()
            
        required_features = [
            'reportContent',
            'blockUser',
            'moderateContent',
            'showReportDialog',
            'getCommunityGuidelines',
        ]
        
        found_features = []
        for feature in required_features:
            if feature in moderation_content:
                found_features.append(feature)
        
        print(f"   ✅ Moderation features: {len(found_features)}/{len(required_features)}")
        
        # Check for report categories
        if 'reportCategories' in moderation_content:
            categories = re.findall(r"'([^']+)'", moderation_content)
            report_categories = [cat for cat in categories if any(word in cat.lower() 
                               for word in ['inappropriate', 'spam', 'harassment', 'hate'])]
            print(f"   ✅ Report categories found: {len(report_categories)}")
        
        # Check for content filtering
        if 'prohibitedWords' in moderation_content:
            print("   ✅ Content filtering system implemented")
        else:
            print("   ⚠️  Content filtering needs implementation")
            
    except Exception as e:
        print(f"   ❌ Error checking content moderation: {e}")


def test_privacy_compliance():
    """Test privacy and legal compliance"""
    print("\n4. 📋 Testing privacy compliance...")
    
    try:
        with open('legal_documents/PRIVACY_POLICY.md', 'r') as f:
            privacy_content = f.read()
            
        required_sections = [
            'INFORMATION WE COLLECT',
            'HOW WE USE YOUR INFORMATION',
            'INFORMATION SHARING',
            'DATA SECURITY',
            'YOUR RIGHTS',
            'CHILDREN\'S PRIVACY',
            'BLOCKCHAIN',
            'GDPR',
            'CCPA',
        ]
        
        found_sections = []
        for section in required_sections:
            if section.upper() in privacy_content.upper():
                found_sections.append(section)
        
        print(f"   ✅ Privacy policy sections: {len(found_sections)}/{len(required_sections)}")
        
        # Check for specific compliance mentions
        compliance_checks = [
            ('Age restriction', '13 years'),
            ('GDPR compliance', 'GDPR'),
            ('CCPA compliance', 'CCPA'),
            ('Blockchain disclosure', 'blockchain'),
            ('Data deletion', 'delete'),
        ]
        
        for check_name, check_term in compliance_checks:
            if check_term.lower() in privacy_content.lower():
                print(f"   ✅ {check_name}")
            else:
                print(f"   ⚠️  {check_name} needs attention")
                
    except Exception as e:
        print(f"   ❌ Error checking privacy compliance: {e}")


def test_age_rating_compliance():
    """Test age rating and content appropriateness"""
    print("\n5. 👶 Testing age rating compliance...")
    
    # Check for age verification in compliance service
    try:
        with open('lib/services/compliance_service.dart', 'r') as f:
            compliance_content = f.read()
            
        if 'verifyAge' in compliance_content:
            print("   ✅ Age verification system implemented")
        else:
            print("   ❌ Age verification missing")
            
        if '13 years' in compliance_content:
            print("   ✅ Correct age restriction (13+)")
        else:
            print("   ⚠️  Age restriction needs clarification")
            
        # Check for parental consent mention
        if 'parental' in compliance_content.lower():
            print("   ✅ Parental consent mentioned")
        else:
            print("   ⚠️  Parental consent should be mentioned")
            
    except Exception as e:
        print(f"   ❌ Error checking age compliance: {e}")


def test_blockchain_compliance():
    """Test blockchain-specific compliance"""
    print("\n6. ⛓️ Testing blockchain compliance...")
    
    try:
        with open('lib/services/compliance_service.dart', 'r') as f:
            compliance_content = f.read()
            
        blockchain_disclaimers = [
            'utility tokens',
            'not intended as investment',
            'no guaranteed monetary value',
            'subject to local regulations',
            'tax compliance',
        ]
        
        found_disclaimers = []
        for disclaimer in blockchain_disclaimers:
            if disclaimer.lower() in compliance_content.lower():
                found_disclaimers.append(disclaimer)
        
        print(f"   ✅ Blockchain disclaimers: {len(found_disclaimers)}/{len(blockchain_disclaimers)}")
        
        if 'showBlockchainDisclaimer' in compliance_content:
            print("   ✅ Blockchain disclaimer dialog implemented")
        else:
            print("   ❌ Blockchain disclaimer dialog missing")
            
    except Exception as e:
        print(f"   ❌ Error checking blockchain compliance: {e}")


def analyze_risk_factors():
    """Analyze potential risk factors for app store rejection"""
    print("\n7. ⚠️ Analyzing risk factors...")
    
    risk_factors = []
    
    # Check for high-risk keywords in app description/content
    high_risk_terms = [
        'investment', 'profit guaranteed', 'get rich', 'easy money',
        'cryptocurrency investment', 'trading', 'financial advice'
    ]
    
    # Check pubspec.yaml for app description
    try:
        with open('pubspec.yaml', 'r') as f:
            pubspec_content = f.read().lower()
            
        for term in high_risk_terms:
            if term in pubspec_content:
                risk_factors.append(f"High-risk term in app description: '{term}'")
                
    except Exception:
        pass
    
    # Check for potential payment violations
    try:
        # Search for PayPal usage in non-compliance contexts
        for root, dirs, files in os.walk('lib'):
            for file in files:
                if file.endswith('.dart'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read()
                            if 'PayPal' in content and 'premium' in content.lower():
                                if 'physical' not in content.lower():
                                    risk_factors.append(f"Potential payment violation in {file_path}")
                    except Exception:
                        continue
                        
    except Exception:
        pass
    
    if risk_factors:
        print("   ⚠️ Risk factors found:")
        for risk in risk_factors:
            print(f"      • {risk}")
    else:
        print("   ✅ No major risk factors detected")
    
    return risk_factors


def generate_compliance_report():
    """Generate final compliance report"""
    print("\n" + "=" * 60)
    print("📊 COMPLIANCE REPORT SUMMARY")
    print("=" * 60)
    
    # Run all tests
    files_ok = test_compliance_files()
    test_payment_compliance()
    test_content_moderation()
    test_privacy_compliance()
    test_age_rating_compliance()
    test_blockchain_compliance()
    risk_factors = analyze_risk_factors()
    
    print("\n🎯 FINAL ASSESSMENT:")
    
    if files_ok and len(risk_factors) == 0:
        print("✅ HIGH COMPLIANCE CONFIDENCE")
        print("   Your app has strong compliance implementation")
        print("   Low risk of app store rejection")
    elif files_ok and len(risk_factors) <= 2:
        print("🟡 MODERATE COMPLIANCE CONFIDENCE")
        print("   Most compliance features implemented")
        print("   Address risk factors before submission")
    else:
        print("🔴 COMPLIANCE NEEDS ATTENTION")
        print("   Critical compliance issues need fixing")
        print("   High risk of app store rejection")
    
    print("\n📋 NEXT STEPS:")
    print("1. ✅ Review and fix any identified issues")
    print("2. ✅ Test all compliance features thoroughly")
    print("3. ✅ Update legal documents with your details")
    print("4. ✅ Set up proper payment systems")
    print("5. ✅ Test content moderation workflows")
    print("6. ✅ Prepare app store metadata carefully")
    
    print("\n🚀 SUBMISSION READINESS:")
    if files_ok and len(risk_factors) == 0:
        print("   🟢 READY FOR SUBMISSION")
    elif files_ok:
        print("   🟡 NEEDS MINOR FIXES")
    else:
        print("   🔴 NEEDS MAJOR WORK")
    
    print("\n💡 REMEMBER:")
    print("   • Test on real devices before submission")
    print("   • Have legal documents reviewed by counsel")
    print("   • Monitor app store policy changes")
    print("   • Prepare for potential review delays")
    print("   • Have customer support ready for launch")


def main():
    """Main compliance test function"""
    print("🛡️ TRENDY APP - APP STORE COMPLIANCE TEST")
    print("=" * 60)
    
    try:
        generate_compliance_report()
        
        print("\n" + "=" * 60)
        print("🎊 Compliance testing complete!")
        print("📖 Review the full compliance guide: APP_STORE_COMPLIANCE_GUIDE.md")
        print("⚖️ Consult legal counsel for final review")
        print("🚀 Good luck with your app store submission!")
        
    except Exception as e:
        print(f"\n❌ Compliance test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
