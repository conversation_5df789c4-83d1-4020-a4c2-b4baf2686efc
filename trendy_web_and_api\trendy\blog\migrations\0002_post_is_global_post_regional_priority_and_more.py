# Generated by Django 5.0.4 on 2025-07-29 11:02

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("blog", "0001_initial"),
        ("regional", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="post",
            name="is_global",
            field=models.BooleanField(
                default=False,
                help_text="If True, this post is visible to all users regardless of location",
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="regional_priority",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Priority for regional content (higher = more prominent in region)",
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="target_countries",
            field=models.ManyToManyField(
                blank=True,
                help_text="Countries where this post should be visible. Leave empty for global posts.",
                related_name="targeted_posts",
                to="regional.country",
            ),
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["is_global", "status"], name="blog_post_is_glob_ee992a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="post",
            index=models.Index(
                fields=["regional_priority", "-created_at"],
                name="blog_post_regiona_06a2bc_idx",
            ),
        ),
    ]
