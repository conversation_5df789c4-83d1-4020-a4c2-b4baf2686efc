import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/post.dart';
import '../models/category.dart';
import '../models/user.dart';
import '../models/paginated_response.dart';

class OfflineStorageService {
  static final OfflineStorageService _instance = OfflineStorageService._internal();
  factory OfflineStorageService() => _instance;
  OfflineStorageService._internal();

  SharedPreferences? _prefs;
  Directory? _cacheDir;
  bool _isInitialized = false;

  // Cache keys
  static const String _postsKey = 'cached_posts';
  static const String _categoriesKey = 'cached_categories';
  static const String _userKey = 'cached_user';
  static const String _lastSyncKey = 'last_sync_time';
  static const String _offlineModeKey = 'offline_mode_enabled';

  /// Initialize the storage service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _cacheDir = await getApplicationCacheDirectory();
      _isInitialized = true;
      print('📦 Offline storage initialized');
    } catch (e) {
      print('❌ Failed to initialize offline storage: $e');
    }
  }

  /// Check if storage is initialized
  bool get isInitialized => _isInitialized;

  /// Enable/disable offline mode
  Future<void> setOfflineModeEnabled(bool enabled) async {
    await _prefs?.setBool(_offlineModeKey, enabled);
  }

  /// Check if offline mode is enabled
  bool get isOfflineModeEnabled => _prefs?.getBool(_offlineModeKey) ?? true;

  /// Get last sync time
  DateTime? get lastSyncTime {
    final timestamp = _prefs?.getInt(_lastSyncKey);
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  /// Update last sync time
  Future<void> updateLastSyncTime() async {
    await _prefs?.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// Cache posts data
  Future<void> cachePosts(PaginatedResponse<Post> posts, {String? category, String? search}) async {
    if (!_isInitialized) return;

    try {
      final cacheKey = _buildPostsCacheKey(category: category, search: search);
      final jsonData = {
        'data': posts.toJson((post) => post.toJson()),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'category': category,
        'search': search,
      };

      await _prefs?.setString(cacheKey, jsonEncode(jsonData));
      print('📦 Cached ${posts.results.length} posts for key: $cacheKey');
    } catch (e) {
      print('❌ Failed to cache posts: $e');
    }
  }

  /// Get cached posts
  Future<PaginatedResponse<Post>?> getCachedPosts({String? category, String? search}) async {
    if (!_isInitialized) return null;

    try {
      final cacheKey = _buildPostsCacheKey(category: category, search: search);
      final cachedData = _prefs?.getString(cacheKey);
      
      if (cachedData == null) return null;

      final jsonData = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(jsonData['timestamp']);
      
      // Check if cache is still valid (24 hours)
      if (DateTime.now().difference(timestamp).inHours > 24) {
        await _prefs?.remove(cacheKey);
        return null;
      }

      final paginatedData = jsonData['data'];
      return PaginatedResponse.fromJson(
        paginatedData,
        (json) => Post.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      print('❌ Failed to get cached posts: $e');
      return null;
    }
  }

  /// Cache categories
  Future<void> cacheCategories(PaginatedResponse<Category> categories) async {
    if (!_isInitialized) return;

    try {
      final jsonData = {
        'data': categories.toJson((category) => category.toJson()),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await _prefs?.setString(_categoriesKey, jsonEncode(jsonData));
      print('📦 Cached ${categories.results.length} categories');
    } catch (e) {
      print('❌ Failed to cache categories: $e');
    }
  }

  /// Get cached categories
  Future<PaginatedResponse<Category>?> getCachedCategories() async {
    if (!_isInitialized) return null;

    try {
      final cachedData = _prefs?.getString(_categoriesKey);
      if (cachedData == null) return null;

      final jsonData = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(jsonData['timestamp']);
      
      // Check if cache is still valid (1 week)
      if (DateTime.now().difference(timestamp).inDays > 7) {
        await _prefs?.remove(_categoriesKey);
        return null;
      }

      final paginatedData = jsonData['data'];
      return PaginatedResponse.fromJson(
        paginatedData,
        (json) => Category.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      print('❌ Failed to get cached categories: $e');
      return null;
    }
  }

  /// Cache user data
  Future<void> cacheUser(User user) async {
    if (!_isInitialized) return;

    try {
      final jsonData = {
        'data': user.toJson(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await _prefs?.setString(_userKey, jsonEncode(jsonData));
      print('📦 Cached user data for: ${user.username}');
    } catch (e) {
      print('❌ Failed to cache user: $e');
    }
  }

  /// Get cached user
  Future<User?> getCachedUser() async {
    if (!_isInitialized) return null;

    try {
      final cachedData = _prefs?.getString(_userKey);
      if (cachedData == null) return null;

      final jsonData = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(jsonData['timestamp']);
      
      // Check if cache is still valid (1 hour)
      if (DateTime.now().difference(timestamp).inHours > 1) {
        await _prefs?.remove(_userKey);
        return null;
      }

      return User.fromJson(jsonData['data']);
    } catch (e) {
      print('❌ Failed to get cached user: $e');
      return null;
    }
  }

  /// Cache image file
  Future<String?> cacheImage(String url) async {
    if (!_isInitialized || _cacheDir == null) return null;

    try {
      final uri = Uri.parse(url);
      final filename = uri.pathSegments.last;
      final file = File('${_cacheDir!.path}/images/$filename');
      
      if (await file.exists()) {
        return file.path;
      }

      // Create directory if it doesn't exist
      await file.parent.create(recursive: true);
      
      // Download and cache the image
      final httpClient = HttpClient();
      final request = await httpClient.getUrl(uri);
      final response = await request.close();
      
      if (response.statusCode == 200) {
        await response.pipe(file.openWrite());
        print('📦 Cached image: $filename');
        return file.path;
      }
    } catch (e) {
      print('❌ Failed to cache image: $e');
    }
    
    return null;
  }

  /// Get cached image path
  String? getCachedImagePath(String url) {
    if (!_isInitialized || _cacheDir == null) return null;

    try {
      final uri = Uri.parse(url);
      final filename = uri.pathSegments.last;
      final file = File('${_cacheDir!.path}/images/$filename');
      
      if (file.existsSync()) {
        return file.path;
      }
    } catch (e) {
      print('❌ Failed to get cached image path: $e');
    }
    
    return null;
  }

  /// Clear all cached data
  Future<void> clearAllCache() async {
    if (!_isInitialized) return;

    try {
      // Clear SharedPreferences cache
      await _prefs?.remove(_postsKey);
      await _prefs?.remove(_categoriesKey);
      await _prefs?.remove(_userKey);
      
      // Clear all posts cache keys
      final keys = _prefs?.getKeys() ?? <String>{};
      for (final key in keys) {
        if (key.startsWith('cached_posts_')) {
          await _prefs?.remove(key);
        }
      }

      // Clear image cache directory
      if (_cacheDir != null) {
        final imagesDir = Directory('${_cacheDir!.path}/images');
        if (await imagesDir.exists()) {
          await imagesDir.delete(recursive: true);
        }
      }

      print('📦 Cleared all cached data');
    } catch (e) {
      print('❌ Failed to clear cache: $e');
    }
  }

  /// Get cache size in bytes
  Future<int> getCacheSize() async {
    if (!_isInitialized || _cacheDir == null) return 0;

    try {
      int totalSize = 0;
      
      // Calculate SharedPreferences size (approximate)
      final keys = _prefs?.getKeys() ?? <String>{};
      for (final key in keys) {
        if (key.startsWith('cached_')) {
          final value = _prefs?.getString(key) ?? '';
          totalSize += value.length;
        }
      }

      // Calculate image cache size
      final imagesDir = Directory('${_cacheDir!.path}/images');
      if (await imagesDir.exists()) {
        await for (final entity in imagesDir.list(recursive: true)) {
          if (entity is File) {
            totalSize += await entity.length();
          }
        }
      }

      return totalSize;
    } catch (e) {
      print('❌ Failed to calculate cache size: $e');
      return 0;
    }
  }

  /// Get human-readable cache size
  Future<String> getCacheSizeFormatted() async {
    final sizeInBytes = await getCacheSize();
    
    if (sizeInBytes < 1024) {
      return '$sizeInBytes B';
    } else if (sizeInBytes < 1024 * 1024) {
      return '${(sizeInBytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Check if we have cached data available
  Future<bool> hasCachedData() async {
    if (!_isInitialized) return false;

    final cachedPosts = await getCachedPosts();
    final cachedCategories = await getCachedCategories();
    
    return cachedPosts != null || cachedCategories != null;
  }

  /// Build cache key for posts with filters
  String _buildPostsCacheKey({String? category, String? search}) {
    String key = 'cached_posts';
    if (category != null) key += '_cat_$category';
    if (search != null) key += '_search_${search.hashCode}';
    return key;
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    final size = await getCacheSize();
    final sizeFormatted = await getCacheSizeFormatted();
    final hasCached = await hasCachedData();
    
    return {
      'size_bytes': size,
      'size_formatted': sizeFormatted,
      'has_cached_data': hasCached,
      'last_sync': lastSyncTime?.toIso8601String(),
      'offline_mode_enabled': isOfflineModeEnabled,
    };
  }
}
