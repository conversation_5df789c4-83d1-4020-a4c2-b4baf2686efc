// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

WalletOverview _$WalletOverviewFromJson(Map<String, dynamic> json) {
  return _WalletOverview.fromJson(json);
}

/// @nodoc
mixin _$WalletOverview {
  WalletInfo get wallet => throw _privateConstructorUsedError;
  @JsonKey(name: 'recent_transactions')
  List<WalletTransaction> get recentTransactions =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'pending_deposits')
  int get pendingDeposits => throw _privateConstructorUsedError;
  @JsonKey(name: 'pending_withdrawals')
  int get pendingWithdrawals => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletOverviewCopyWith<WalletOverview> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletOverviewCopyWith<$Res> {
  factory $WalletOverviewCopyWith(
          WalletOverview value, $Res Function(WalletOverview) then) =
      _$WalletOverviewCopyWithImpl<$Res, WalletOverview>;
  @useResult
  $Res call(
      {WalletInfo wallet,
      @JsonKey(name: 'recent_transactions')
      List<WalletTransaction> recentTransactions,
      @JsonKey(name: 'pending_deposits') int pendingDeposits,
      @JsonKey(name: 'pending_withdrawals') int pendingWithdrawals});

  $WalletInfoCopyWith<$Res> get wallet;
}

/// @nodoc
class _$WalletOverviewCopyWithImpl<$Res, $Val extends WalletOverview>
    implements $WalletOverviewCopyWith<$Res> {
  _$WalletOverviewCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wallet = null,
    Object? recentTransactions = null,
    Object? pendingDeposits = null,
    Object? pendingWithdrawals = null,
  }) {
    return _then(_value.copyWith(
      wallet: null == wallet
          ? _value.wallet
          : wallet // ignore: cast_nullable_to_non_nullable
              as WalletInfo,
      recentTransactions: null == recentTransactions
          ? _value.recentTransactions
          : recentTransactions // ignore: cast_nullable_to_non_nullable
              as List<WalletTransaction>,
      pendingDeposits: null == pendingDeposits
          ? _value.pendingDeposits
          : pendingDeposits // ignore: cast_nullable_to_non_nullable
              as int,
      pendingWithdrawals: null == pendingWithdrawals
          ? _value.pendingWithdrawals
          : pendingWithdrawals // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WalletInfoCopyWith<$Res> get wallet {
    return $WalletInfoCopyWith<$Res>(_value.wallet, (value) {
      return _then(_value.copyWith(wallet: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WalletOverviewImplCopyWith<$Res>
    implements $WalletOverviewCopyWith<$Res> {
  factory _$$WalletOverviewImplCopyWith(_$WalletOverviewImpl value,
          $Res Function(_$WalletOverviewImpl) then) =
      __$$WalletOverviewImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {WalletInfo wallet,
      @JsonKey(name: 'recent_transactions')
      List<WalletTransaction> recentTransactions,
      @JsonKey(name: 'pending_deposits') int pendingDeposits,
      @JsonKey(name: 'pending_withdrawals') int pendingWithdrawals});

  @override
  $WalletInfoCopyWith<$Res> get wallet;
}

/// @nodoc
class __$$WalletOverviewImplCopyWithImpl<$Res>
    extends _$WalletOverviewCopyWithImpl<$Res, _$WalletOverviewImpl>
    implements _$$WalletOverviewImplCopyWith<$Res> {
  __$$WalletOverviewImplCopyWithImpl(
      _$WalletOverviewImpl _value, $Res Function(_$WalletOverviewImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wallet = null,
    Object? recentTransactions = null,
    Object? pendingDeposits = null,
    Object? pendingWithdrawals = null,
  }) {
    return _then(_$WalletOverviewImpl(
      wallet: null == wallet
          ? _value.wallet
          : wallet // ignore: cast_nullable_to_non_nullable
              as WalletInfo,
      recentTransactions: null == recentTransactions
          ? _value._recentTransactions
          : recentTransactions // ignore: cast_nullable_to_non_nullable
              as List<WalletTransaction>,
      pendingDeposits: null == pendingDeposits
          ? _value.pendingDeposits
          : pendingDeposits // ignore: cast_nullable_to_non_nullable
              as int,
      pendingWithdrawals: null == pendingWithdrawals
          ? _value.pendingWithdrawals
          : pendingWithdrawals // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletOverviewImpl implements _WalletOverview {
  const _$WalletOverviewImpl(
      {required this.wallet,
      @JsonKey(name: 'recent_transactions')
      final List<WalletTransaction> recentTransactions = const [],
      @JsonKey(name: 'pending_deposits') this.pendingDeposits = 0,
      @JsonKey(name: 'pending_withdrawals') this.pendingWithdrawals = 0})
      : _recentTransactions = recentTransactions;

  factory _$WalletOverviewImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletOverviewImplFromJson(json);

  @override
  final WalletInfo wallet;
  final List<WalletTransaction> _recentTransactions;
  @override
  @JsonKey(name: 'recent_transactions')
  List<WalletTransaction> get recentTransactions {
    if (_recentTransactions is EqualUnmodifiableListView)
      return _recentTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentTransactions);
  }

  @override
  @JsonKey(name: 'pending_deposits')
  final int pendingDeposits;
  @override
  @JsonKey(name: 'pending_withdrawals')
  final int pendingWithdrawals;

  @override
  String toString() {
    return 'WalletOverview(wallet: $wallet, recentTransactions: $recentTransactions, pendingDeposits: $pendingDeposits, pendingWithdrawals: $pendingWithdrawals)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletOverviewImpl &&
            (identical(other.wallet, wallet) || other.wallet == wallet) &&
            const DeepCollectionEquality()
                .equals(other._recentTransactions, _recentTransactions) &&
            (identical(other.pendingDeposits, pendingDeposits) ||
                other.pendingDeposits == pendingDeposits) &&
            (identical(other.pendingWithdrawals, pendingWithdrawals) ||
                other.pendingWithdrawals == pendingWithdrawals));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      wallet,
      const DeepCollectionEquality().hash(_recentTransactions),
      pendingDeposits,
      pendingWithdrawals);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletOverviewImplCopyWith<_$WalletOverviewImpl> get copyWith =>
      __$$WalletOverviewImplCopyWithImpl<_$WalletOverviewImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletOverviewImplToJson(
      this,
    );
  }
}

abstract class _WalletOverview implements WalletOverview {
  const factory _WalletOverview(
          {required final WalletInfo wallet,
          @JsonKey(name: 'recent_transactions')
          final List<WalletTransaction> recentTransactions,
          @JsonKey(name: 'pending_deposits') final int pendingDeposits,
          @JsonKey(name: 'pending_withdrawals') final int pendingWithdrawals}) =
      _$WalletOverviewImpl;

  factory _WalletOverview.fromJson(Map<String, dynamic> json) =
      _$WalletOverviewImpl.fromJson;

  @override
  WalletInfo get wallet;
  @override
  @JsonKey(name: 'recent_transactions')
  List<WalletTransaction> get recentTransactions;
  @override
  @JsonKey(name: 'pending_deposits')
  int get pendingDeposits;
  @override
  @JsonKey(name: 'pending_withdrawals')
  int get pendingWithdrawals;
  @override
  @JsonKey(ignore: true)
  _$$WalletOverviewImplCopyWith<_$WalletOverviewImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WalletInfo _$WalletInfoFromJson(Map<String, dynamic> json) {
  return _WalletInfo.fromJson(json);
}

/// @nodoc
mixin _$WalletInfo {
  double get balance => throw _privateConstructorUsedError;
  @JsonKey(name: 'formatted_balance')
  String get formattedBalance => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_verified')
  bool get isVerified => throw _privateConstructorUsedError;
  @JsonKey(name: 'daily_spent')
  double get dailySpent => throw _privateConstructorUsedError;
  @JsonKey(name: 'monthly_spent')
  double get monthlySpent => throw _privateConstructorUsedError;
  @JsonKey(name: 'daily_limit')
  double get dailyLimit => throw _privateConstructorUsedError;
  @JsonKey(name: 'monthly_limit')
  double get monthlyLimit => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_transactions')
  int get totalTransactions => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_credits')
  int get totalCredits => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_debits')
  int get totalDebits => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_deposited')
  double get totalDeposited => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_spent')
  double get totalSpent => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletInfoCopyWith<WalletInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletInfoCopyWith<$Res> {
  factory $WalletInfoCopyWith(
          WalletInfo value, $Res Function(WalletInfo) then) =
      _$WalletInfoCopyWithImpl<$Res, WalletInfo>;
  @useResult
  $Res call(
      {double balance,
      @JsonKey(name: 'formatted_balance') String formattedBalance,
      @JsonKey(name: 'is_active') bool isActive,
      @JsonKey(name: 'is_verified') bool isVerified,
      @JsonKey(name: 'daily_spent') double dailySpent,
      @JsonKey(name: 'monthly_spent') double monthlySpent,
      @JsonKey(name: 'daily_limit') double dailyLimit,
      @JsonKey(name: 'monthly_limit') double monthlyLimit,
      @JsonKey(name: 'total_transactions') int totalTransactions,
      @JsonKey(name: 'total_credits') int totalCredits,
      @JsonKey(name: 'total_debits') int totalDebits,
      @JsonKey(name: 'total_deposited') double totalDeposited,
      @JsonKey(name: 'total_spent') double totalSpent});
}

/// @nodoc
class _$WalletInfoCopyWithImpl<$Res, $Val extends WalletInfo>
    implements $WalletInfoCopyWith<$Res> {
  _$WalletInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? balance = null,
    Object? formattedBalance = null,
    Object? isActive = null,
    Object? isVerified = null,
    Object? dailySpent = null,
    Object? monthlySpent = null,
    Object? dailyLimit = null,
    Object? monthlyLimit = null,
    Object? totalTransactions = null,
    Object? totalCredits = null,
    Object? totalDebits = null,
    Object? totalDeposited = null,
    Object? totalSpent = null,
  }) {
    return _then(_value.copyWith(
      balance: null == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as double,
      formattedBalance: null == formattedBalance
          ? _value.formattedBalance
          : formattedBalance // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      dailySpent: null == dailySpent
          ? _value.dailySpent
          : dailySpent // ignore: cast_nullable_to_non_nullable
              as double,
      monthlySpent: null == monthlySpent
          ? _value.monthlySpent
          : monthlySpent // ignore: cast_nullable_to_non_nullable
              as double,
      dailyLimit: null == dailyLimit
          ? _value.dailyLimit
          : dailyLimit // ignore: cast_nullable_to_non_nullable
              as double,
      monthlyLimit: null == monthlyLimit
          ? _value.monthlyLimit
          : monthlyLimit // ignore: cast_nullable_to_non_nullable
              as double,
      totalTransactions: null == totalTransactions
          ? _value.totalTransactions
          : totalTransactions // ignore: cast_nullable_to_non_nullable
              as int,
      totalCredits: null == totalCredits
          ? _value.totalCredits
          : totalCredits // ignore: cast_nullable_to_non_nullable
              as int,
      totalDebits: null == totalDebits
          ? _value.totalDebits
          : totalDebits // ignore: cast_nullable_to_non_nullable
              as int,
      totalDeposited: null == totalDeposited
          ? _value.totalDeposited
          : totalDeposited // ignore: cast_nullable_to_non_nullable
              as double,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WalletInfoImplCopyWith<$Res>
    implements $WalletInfoCopyWith<$Res> {
  factory _$$WalletInfoImplCopyWith(
          _$WalletInfoImpl value, $Res Function(_$WalletInfoImpl) then) =
      __$$WalletInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double balance,
      @JsonKey(name: 'formatted_balance') String formattedBalance,
      @JsonKey(name: 'is_active') bool isActive,
      @JsonKey(name: 'is_verified') bool isVerified,
      @JsonKey(name: 'daily_spent') double dailySpent,
      @JsonKey(name: 'monthly_spent') double monthlySpent,
      @JsonKey(name: 'daily_limit') double dailyLimit,
      @JsonKey(name: 'monthly_limit') double monthlyLimit,
      @JsonKey(name: 'total_transactions') int totalTransactions,
      @JsonKey(name: 'total_credits') int totalCredits,
      @JsonKey(name: 'total_debits') int totalDebits,
      @JsonKey(name: 'total_deposited') double totalDeposited,
      @JsonKey(name: 'total_spent') double totalSpent});
}

/// @nodoc
class __$$WalletInfoImplCopyWithImpl<$Res>
    extends _$WalletInfoCopyWithImpl<$Res, _$WalletInfoImpl>
    implements _$$WalletInfoImplCopyWith<$Res> {
  __$$WalletInfoImplCopyWithImpl(
      _$WalletInfoImpl _value, $Res Function(_$WalletInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? balance = null,
    Object? formattedBalance = null,
    Object? isActive = null,
    Object? isVerified = null,
    Object? dailySpent = null,
    Object? monthlySpent = null,
    Object? dailyLimit = null,
    Object? monthlyLimit = null,
    Object? totalTransactions = null,
    Object? totalCredits = null,
    Object? totalDebits = null,
    Object? totalDeposited = null,
    Object? totalSpent = null,
  }) {
    return _then(_$WalletInfoImpl(
      balance: null == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as double,
      formattedBalance: null == formattedBalance
          ? _value.formattedBalance
          : formattedBalance // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      dailySpent: null == dailySpent
          ? _value.dailySpent
          : dailySpent // ignore: cast_nullable_to_non_nullable
              as double,
      monthlySpent: null == monthlySpent
          ? _value.monthlySpent
          : monthlySpent // ignore: cast_nullable_to_non_nullable
              as double,
      dailyLimit: null == dailyLimit
          ? _value.dailyLimit
          : dailyLimit // ignore: cast_nullable_to_non_nullable
              as double,
      monthlyLimit: null == monthlyLimit
          ? _value.monthlyLimit
          : monthlyLimit // ignore: cast_nullable_to_non_nullable
              as double,
      totalTransactions: null == totalTransactions
          ? _value.totalTransactions
          : totalTransactions // ignore: cast_nullable_to_non_nullable
              as int,
      totalCredits: null == totalCredits
          ? _value.totalCredits
          : totalCredits // ignore: cast_nullable_to_non_nullable
              as int,
      totalDebits: null == totalDebits
          ? _value.totalDebits
          : totalDebits // ignore: cast_nullable_to_non_nullable
              as int,
      totalDeposited: null == totalDeposited
          ? _value.totalDeposited
          : totalDeposited // ignore: cast_nullable_to_non_nullable
              as double,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletInfoImpl implements _WalletInfo {
  const _$WalletInfoImpl(
      {this.balance = 0.0,
      @JsonKey(name: 'formatted_balance') this.formattedBalance = '\$0.00',
      @JsonKey(name: 'is_active') this.isActive = true,
      @JsonKey(name: 'is_verified') this.isVerified = false,
      @JsonKey(name: 'daily_spent') this.dailySpent = 0.0,
      @JsonKey(name: 'monthly_spent') this.monthlySpent = 0.0,
      @JsonKey(name: 'daily_limit') this.dailyLimit = 100.0,
      @JsonKey(name: 'monthly_limit') this.monthlyLimit = 1000.0,
      @JsonKey(name: 'total_transactions') this.totalTransactions = 0,
      @JsonKey(name: 'total_credits') this.totalCredits = 0,
      @JsonKey(name: 'total_debits') this.totalDebits = 0,
      @JsonKey(name: 'total_deposited') this.totalDeposited = 0.0,
      @JsonKey(name: 'total_spent') this.totalSpent = 0.0});

  factory _$WalletInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletInfoImplFromJson(json);

  @override
  @JsonKey()
  final double balance;
  @override
  @JsonKey(name: 'formatted_balance')
  final String formattedBalance;
  @override
  @JsonKey(name: 'is_active')
  final bool isActive;
  @override
  @JsonKey(name: 'is_verified')
  final bool isVerified;
  @override
  @JsonKey(name: 'daily_spent')
  final double dailySpent;
  @override
  @JsonKey(name: 'monthly_spent')
  final double monthlySpent;
  @override
  @JsonKey(name: 'daily_limit')
  final double dailyLimit;
  @override
  @JsonKey(name: 'monthly_limit')
  final double monthlyLimit;
  @override
  @JsonKey(name: 'total_transactions')
  final int totalTransactions;
  @override
  @JsonKey(name: 'total_credits')
  final int totalCredits;
  @override
  @JsonKey(name: 'total_debits')
  final int totalDebits;
  @override
  @JsonKey(name: 'total_deposited')
  final double totalDeposited;
  @override
  @JsonKey(name: 'total_spent')
  final double totalSpent;

  @override
  String toString() {
    return 'WalletInfo(balance: $balance, formattedBalance: $formattedBalance, isActive: $isActive, isVerified: $isVerified, dailySpent: $dailySpent, monthlySpent: $monthlySpent, dailyLimit: $dailyLimit, monthlyLimit: $monthlyLimit, totalTransactions: $totalTransactions, totalCredits: $totalCredits, totalDebits: $totalDebits, totalDeposited: $totalDeposited, totalSpent: $totalSpent)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletInfoImpl &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.formattedBalance, formattedBalance) ||
                other.formattedBalance == formattedBalance) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.dailySpent, dailySpent) ||
                other.dailySpent == dailySpent) &&
            (identical(other.monthlySpent, monthlySpent) ||
                other.monthlySpent == monthlySpent) &&
            (identical(other.dailyLimit, dailyLimit) ||
                other.dailyLimit == dailyLimit) &&
            (identical(other.monthlyLimit, monthlyLimit) ||
                other.monthlyLimit == monthlyLimit) &&
            (identical(other.totalTransactions, totalTransactions) ||
                other.totalTransactions == totalTransactions) &&
            (identical(other.totalCredits, totalCredits) ||
                other.totalCredits == totalCredits) &&
            (identical(other.totalDebits, totalDebits) ||
                other.totalDebits == totalDebits) &&
            (identical(other.totalDeposited, totalDeposited) ||
                other.totalDeposited == totalDeposited) &&
            (identical(other.totalSpent, totalSpent) ||
                other.totalSpent == totalSpent));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      balance,
      formattedBalance,
      isActive,
      isVerified,
      dailySpent,
      monthlySpent,
      dailyLimit,
      monthlyLimit,
      totalTransactions,
      totalCredits,
      totalDebits,
      totalDeposited,
      totalSpent);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletInfoImplCopyWith<_$WalletInfoImpl> get copyWith =>
      __$$WalletInfoImplCopyWithImpl<_$WalletInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletInfoImplToJson(
      this,
    );
  }
}

abstract class _WalletInfo implements WalletInfo {
  const factory _WalletInfo(
          {final double balance,
          @JsonKey(name: 'formatted_balance') final String formattedBalance,
          @JsonKey(name: 'is_active') final bool isActive,
          @JsonKey(name: 'is_verified') final bool isVerified,
          @JsonKey(name: 'daily_spent') final double dailySpent,
          @JsonKey(name: 'monthly_spent') final double monthlySpent,
          @JsonKey(name: 'daily_limit') final double dailyLimit,
          @JsonKey(name: 'monthly_limit') final double monthlyLimit,
          @JsonKey(name: 'total_transactions') final int totalTransactions,
          @JsonKey(name: 'total_credits') final int totalCredits,
          @JsonKey(name: 'total_debits') final int totalDebits,
          @JsonKey(name: 'total_deposited') final double totalDeposited,
          @JsonKey(name: 'total_spent') final double totalSpent}) =
      _$WalletInfoImpl;

  factory _WalletInfo.fromJson(Map<String, dynamic> json) =
      _$WalletInfoImpl.fromJson;

  @override
  double get balance;
  @override
  @JsonKey(name: 'formatted_balance')
  String get formattedBalance;
  @override
  @JsonKey(name: 'is_active')
  bool get isActive;
  @override
  @JsonKey(name: 'is_verified')
  bool get isVerified;
  @override
  @JsonKey(name: 'daily_spent')
  double get dailySpent;
  @override
  @JsonKey(name: 'monthly_spent')
  double get monthlySpent;
  @override
  @JsonKey(name: 'daily_limit')
  double get dailyLimit;
  @override
  @JsonKey(name: 'monthly_limit')
  double get monthlyLimit;
  @override
  @JsonKey(name: 'total_transactions')
  int get totalTransactions;
  @override
  @JsonKey(name: 'total_credits')
  int get totalCredits;
  @override
  @JsonKey(name: 'total_debits')
  int get totalDebits;
  @override
  @JsonKey(name: 'total_deposited')
  double get totalDeposited;
  @override
  @JsonKey(name: 'total_spent')
  double get totalSpent;
  @override
  @JsonKey(ignore: true)
  _$$WalletInfoImplCopyWith<_$WalletInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WalletTransaction _$WalletTransactionFromJson(Map<String, dynamic> json) {
  return _WalletTransaction.fromJson(json);
}

/// @nodoc
mixin _$WalletTransaction {
  String get id => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get purpose => throw _privateConstructorUsedError;
  String get amount => throw _privateConstructorUsedError;
  String get formattedAmount => throw _privateConstructorUsedError;
  String get balanceAfter => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String? get completedAt => throw _privateConstructorUsedError;
  String? get paymentMethod => throw _privateConstructorUsedError;
  String? get referenceId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletTransactionCopyWith<WalletTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletTransactionCopyWith<$Res> {
  factory $WalletTransactionCopyWith(
          WalletTransaction value, $Res Function(WalletTransaction) then) =
      _$WalletTransactionCopyWithImpl<$Res, WalletTransaction>;
  @useResult
  $Res call(
      {String id,
      String type,
      String purpose,
      String amount,
      String formattedAmount,
      String balanceAfter,
      String status,
      String description,
      String createdAt,
      String? completedAt,
      String? paymentMethod,
      String? referenceId});
}

/// @nodoc
class _$WalletTransactionCopyWithImpl<$Res, $Val extends WalletTransaction>
    implements $WalletTransactionCopyWith<$Res> {
  _$WalletTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? purpose = null,
    Object? amount = null,
    Object? formattedAmount = null,
    Object? balanceAfter = null,
    Object? status = null,
    Object? description = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? paymentMethod = freezed,
    Object? referenceId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      purpose: null == purpose
          ? _value.purpose
          : purpose // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String,
      formattedAmount: null == formattedAmount
          ? _value.formattedAmount
          : formattedAmount // ignore: cast_nullable_to_non_nullable
              as String,
      balanceAfter: null == balanceAfter
          ? _value.balanceAfter
          : balanceAfter // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceId: freezed == referenceId
          ? _value.referenceId
          : referenceId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WalletTransactionImplCopyWith<$Res>
    implements $WalletTransactionCopyWith<$Res> {
  factory _$$WalletTransactionImplCopyWith(_$WalletTransactionImpl value,
          $Res Function(_$WalletTransactionImpl) then) =
      __$$WalletTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String type,
      String purpose,
      String amount,
      String formattedAmount,
      String balanceAfter,
      String status,
      String description,
      String createdAt,
      String? completedAt,
      String? paymentMethod,
      String? referenceId});
}

/// @nodoc
class __$$WalletTransactionImplCopyWithImpl<$Res>
    extends _$WalletTransactionCopyWithImpl<$Res, _$WalletTransactionImpl>
    implements _$$WalletTransactionImplCopyWith<$Res> {
  __$$WalletTransactionImplCopyWithImpl(_$WalletTransactionImpl _value,
      $Res Function(_$WalletTransactionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? purpose = null,
    Object? amount = null,
    Object? formattedAmount = null,
    Object? balanceAfter = null,
    Object? status = null,
    Object? description = null,
    Object? createdAt = null,
    Object? completedAt = freezed,
    Object? paymentMethod = freezed,
    Object? referenceId = freezed,
  }) {
    return _then(_$WalletTransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      purpose: null == purpose
          ? _value.purpose
          : purpose // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String,
      formattedAmount: null == formattedAmount
          ? _value.formattedAmount
          : formattedAmount // ignore: cast_nullable_to_non_nullable
              as String,
      balanceAfter: null == balanceAfter
          ? _value.balanceAfter
          : balanceAfter // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceId: freezed == referenceId
          ? _value.referenceId
          : referenceId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletTransactionImpl implements _WalletTransaction {
  const _$WalletTransactionImpl(
      {this.id = '',
      this.type = '',
      this.purpose = '',
      this.amount = '0.00',
      this.formattedAmount = '0.00',
      this.balanceAfter = '0.00',
      this.status = 'pending',
      this.description = '',
      this.createdAt = '',
      this.completedAt,
      this.paymentMethod,
      this.referenceId});

  factory _$WalletTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletTransactionImplFromJson(json);

  @override
  @JsonKey()
  final String id;
  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final String purpose;
  @override
  @JsonKey()
  final String amount;
  @override
  @JsonKey()
  final String formattedAmount;
  @override
  @JsonKey()
  final String balanceAfter;
  @override
  @JsonKey()
  final String status;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String createdAt;
  @override
  final String? completedAt;
  @override
  final String? paymentMethod;
  @override
  final String? referenceId;

  @override
  String toString() {
    return 'WalletTransaction(id: $id, type: $type, purpose: $purpose, amount: $amount, formattedAmount: $formattedAmount, balanceAfter: $balanceAfter, status: $status, description: $description, createdAt: $createdAt, completedAt: $completedAt, paymentMethod: $paymentMethod, referenceId: $referenceId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.purpose, purpose) || other.purpose == purpose) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.formattedAmount, formattedAmount) ||
                other.formattedAmount == formattedAmount) &&
            (identical(other.balanceAfter, balanceAfter) ||
                other.balanceAfter == balanceAfter) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.referenceId, referenceId) ||
                other.referenceId == referenceId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      type,
      purpose,
      amount,
      formattedAmount,
      balanceAfter,
      status,
      description,
      createdAt,
      completedAt,
      paymentMethod,
      referenceId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletTransactionImplCopyWith<_$WalletTransactionImpl> get copyWith =>
      __$$WalletTransactionImplCopyWithImpl<_$WalletTransactionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletTransactionImplToJson(
      this,
    );
  }
}

abstract class _WalletTransaction implements WalletTransaction {
  const factory _WalletTransaction(
      {final String id,
      final String type,
      final String purpose,
      final String amount,
      final String formattedAmount,
      final String balanceAfter,
      final String status,
      final String description,
      final String createdAt,
      final String? completedAt,
      final String? paymentMethod,
      final String? referenceId}) = _$WalletTransactionImpl;

  factory _WalletTransaction.fromJson(Map<String, dynamic> json) =
      _$WalletTransactionImpl.fromJson;

  @override
  String get id;
  @override
  String get type;
  @override
  String get purpose;
  @override
  String get amount;
  @override
  String get formattedAmount;
  @override
  String get balanceAfter;
  @override
  String get status;
  @override
  String get description;
  @override
  String get createdAt;
  @override
  String? get completedAt;
  @override
  String? get paymentMethod;
  @override
  String? get referenceId;
  @override
  @JsonKey(ignore: true)
  _$$WalletTransactionImplCopyWith<_$WalletTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WalletDepositRequest _$WalletDepositRequestFromJson(Map<String, dynamic> json) {
  return _WalletDepositRequest.fromJson(json);
}

/// @nodoc
mixin _$WalletDepositRequest {
  String get depositRequestId => throw _privateConstructorUsedError;
  Map<String, dynamic> get paymentData => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletDepositRequestCopyWith<WalletDepositRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletDepositRequestCopyWith<$Res> {
  factory $WalletDepositRequestCopyWith(WalletDepositRequest value,
          $Res Function(WalletDepositRequest) then) =
      _$WalletDepositRequestCopyWithImpl<$Res, WalletDepositRequest>;
  @useResult
  $Res call(
      {String depositRequestId,
      Map<String, dynamic> paymentData,
      String message});
}

/// @nodoc
class _$WalletDepositRequestCopyWithImpl<$Res,
        $Val extends WalletDepositRequest>
    implements $WalletDepositRequestCopyWith<$Res> {
  _$WalletDepositRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? depositRequestId = null,
    Object? paymentData = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      depositRequestId: null == depositRequestId
          ? _value.depositRequestId
          : depositRequestId // ignore: cast_nullable_to_non_nullable
              as String,
      paymentData: null == paymentData
          ? _value.paymentData
          : paymentData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WalletDepositRequestImplCopyWith<$Res>
    implements $WalletDepositRequestCopyWith<$Res> {
  factory _$$WalletDepositRequestImplCopyWith(_$WalletDepositRequestImpl value,
          $Res Function(_$WalletDepositRequestImpl) then) =
      __$$WalletDepositRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String depositRequestId,
      Map<String, dynamic> paymentData,
      String message});
}

/// @nodoc
class __$$WalletDepositRequestImplCopyWithImpl<$Res>
    extends _$WalletDepositRequestCopyWithImpl<$Res, _$WalletDepositRequestImpl>
    implements _$$WalletDepositRequestImplCopyWith<$Res> {
  __$$WalletDepositRequestImplCopyWithImpl(_$WalletDepositRequestImpl _value,
      $Res Function(_$WalletDepositRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? depositRequestId = null,
    Object? paymentData = null,
    Object? message = null,
  }) {
    return _then(_$WalletDepositRequestImpl(
      depositRequestId: null == depositRequestId
          ? _value.depositRequestId
          : depositRequestId // ignore: cast_nullable_to_non_nullable
              as String,
      paymentData: null == paymentData
          ? _value._paymentData
          : paymentData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletDepositRequestImpl implements _WalletDepositRequest {
  const _$WalletDepositRequestImpl(
      {this.depositRequestId = '',
      final Map<String, dynamic> paymentData = const {},
      this.message = ''})
      : _paymentData = paymentData;

  factory _$WalletDepositRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletDepositRequestImplFromJson(json);

  @override
  @JsonKey()
  final String depositRequestId;
  final Map<String, dynamic> _paymentData;
  @override
  @JsonKey()
  Map<String, dynamic> get paymentData {
    if (_paymentData is EqualUnmodifiableMapView) return _paymentData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_paymentData);
  }

  @override
  @JsonKey()
  final String message;

  @override
  String toString() {
    return 'WalletDepositRequest(depositRequestId: $depositRequestId, paymentData: $paymentData, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletDepositRequestImpl &&
            (identical(other.depositRequestId, depositRequestId) ||
                other.depositRequestId == depositRequestId) &&
            const DeepCollectionEquality()
                .equals(other._paymentData, _paymentData) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, depositRequestId,
      const DeepCollectionEquality().hash(_paymentData), message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletDepositRequestImplCopyWith<_$WalletDepositRequestImpl>
      get copyWith =>
          __$$WalletDepositRequestImplCopyWithImpl<_$WalletDepositRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletDepositRequestImplToJson(
      this,
    );
  }
}

abstract class _WalletDepositRequest implements WalletDepositRequest {
  const factory _WalletDepositRequest(
      {final String depositRequestId,
      final Map<String, dynamic> paymentData,
      final String message}) = _$WalletDepositRequestImpl;

  factory _WalletDepositRequest.fromJson(Map<String, dynamic> json) =
      _$WalletDepositRequestImpl.fromJson;

  @override
  String get depositRequestId;
  @override
  Map<String, dynamic> get paymentData;
  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$WalletDepositRequestImplCopyWith<_$WalletDepositRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WalletWithdrawalRequest _$WalletWithdrawalRequestFromJson(
    Map<String, dynamic> json) {
  return _WalletWithdrawalRequest.fromJson(json);
}

/// @nodoc
mixin _$WalletWithdrawalRequest {
  String get withdrawalRequestId => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletWithdrawalRequestCopyWith<WalletWithdrawalRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletWithdrawalRequestCopyWith<$Res> {
  factory $WalletWithdrawalRequestCopyWith(WalletWithdrawalRequest value,
          $Res Function(WalletWithdrawalRequest) then) =
      _$WalletWithdrawalRequestCopyWithImpl<$Res, WalletWithdrawalRequest>;
  @useResult
  $Res call({String withdrawalRequestId, String message});
}

/// @nodoc
class _$WalletWithdrawalRequestCopyWithImpl<$Res,
        $Val extends WalletWithdrawalRequest>
    implements $WalletWithdrawalRequestCopyWith<$Res> {
  _$WalletWithdrawalRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? withdrawalRequestId = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      withdrawalRequestId: null == withdrawalRequestId
          ? _value.withdrawalRequestId
          : withdrawalRequestId // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WalletWithdrawalRequestImplCopyWith<$Res>
    implements $WalletWithdrawalRequestCopyWith<$Res> {
  factory _$$WalletWithdrawalRequestImplCopyWith(
          _$WalletWithdrawalRequestImpl value,
          $Res Function(_$WalletWithdrawalRequestImpl) then) =
      __$$WalletWithdrawalRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String withdrawalRequestId, String message});
}

/// @nodoc
class __$$WalletWithdrawalRequestImplCopyWithImpl<$Res>
    extends _$WalletWithdrawalRequestCopyWithImpl<$Res,
        _$WalletWithdrawalRequestImpl>
    implements _$$WalletWithdrawalRequestImplCopyWith<$Res> {
  __$$WalletWithdrawalRequestImplCopyWithImpl(
      _$WalletWithdrawalRequestImpl _value,
      $Res Function(_$WalletWithdrawalRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? withdrawalRequestId = null,
    Object? message = null,
  }) {
    return _then(_$WalletWithdrawalRequestImpl(
      withdrawalRequestId: null == withdrawalRequestId
          ? _value.withdrawalRequestId
          : withdrawalRequestId // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletWithdrawalRequestImpl implements _WalletWithdrawalRequest {
  const _$WalletWithdrawalRequestImpl(
      {this.withdrawalRequestId = '', this.message = ''});

  factory _$WalletWithdrawalRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletWithdrawalRequestImplFromJson(json);

  @override
  @JsonKey()
  final String withdrawalRequestId;
  @override
  @JsonKey()
  final String message;

  @override
  String toString() {
    return 'WalletWithdrawalRequest(withdrawalRequestId: $withdrawalRequestId, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletWithdrawalRequestImpl &&
            (identical(other.withdrawalRequestId, withdrawalRequestId) ||
                other.withdrawalRequestId == withdrawalRequestId) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, withdrawalRequestId, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletWithdrawalRequestImplCopyWith<_$WalletWithdrawalRequestImpl>
      get copyWith => __$$WalletWithdrawalRequestImplCopyWithImpl<
          _$WalletWithdrawalRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletWithdrawalRequestImplToJson(
      this,
    );
  }
}

abstract class _WalletWithdrawalRequest implements WalletWithdrawalRequest {
  const factory _WalletWithdrawalRequest(
      {final String withdrawalRequestId,
      final String message}) = _$WalletWithdrawalRequestImpl;

  factory _WalletWithdrawalRequest.fromJson(Map<String, dynamic> json) =
      _$WalletWithdrawalRequestImpl.fromJson;

  @override
  String get withdrawalRequestId;
  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$WalletWithdrawalRequestImplCopyWith<_$WalletWithdrawalRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WalletSettings _$WalletSettingsFromJson(Map<String, dynamic> json) {
  return _WalletSettings.fromJson(json);
}

/// @nodoc
mixin _$WalletSettings {
  String get minimumDeposit => throw _privateConstructorUsedError;
  String get maximumDeposit => throw _privateConstructorUsedError;
  String get minimumWithdrawal => throw _privateConstructorUsedError;
  String get maximumWithdrawal => throw _privateConstructorUsedError;
  String get withdrawalFeePercentage => throw _privateConstructorUsedError;
  String get withdrawalFeeFixed => throw _privateConstructorUsedError;
  String get depositFeePercentage => throw _privateConstructorUsedError;
  bool get walletsEnabled => throw _privateConstructorUsedError;
  bool get depositsEnabled => throw _privateConstructorUsedError;
  bool get withdrawalsEnabled => throw _privateConstructorUsedError;
  bool get requireVerification => throw _privateConstructorUsedError;
  String get dailyWithdrawalLimit => throw _privateConstructorUsedError;
  String get monthlyWithdrawalLimit => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletSettingsCopyWith<WalletSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletSettingsCopyWith<$Res> {
  factory $WalletSettingsCopyWith(
          WalletSettings value, $Res Function(WalletSettings) then) =
      _$WalletSettingsCopyWithImpl<$Res, WalletSettings>;
  @useResult
  $Res call(
      {String minimumDeposit,
      String maximumDeposit,
      String minimumWithdrawal,
      String maximumWithdrawal,
      String withdrawalFeePercentage,
      String withdrawalFeeFixed,
      String depositFeePercentage,
      bool walletsEnabled,
      bool depositsEnabled,
      bool withdrawalsEnabled,
      bool requireVerification,
      String dailyWithdrawalLimit,
      String monthlyWithdrawalLimit});
}

/// @nodoc
class _$WalletSettingsCopyWithImpl<$Res, $Val extends WalletSettings>
    implements $WalletSettingsCopyWith<$Res> {
  _$WalletSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimumDeposit = null,
    Object? maximumDeposit = null,
    Object? minimumWithdrawal = null,
    Object? maximumWithdrawal = null,
    Object? withdrawalFeePercentage = null,
    Object? withdrawalFeeFixed = null,
    Object? depositFeePercentage = null,
    Object? walletsEnabled = null,
    Object? depositsEnabled = null,
    Object? withdrawalsEnabled = null,
    Object? requireVerification = null,
    Object? dailyWithdrawalLimit = null,
    Object? monthlyWithdrawalLimit = null,
  }) {
    return _then(_value.copyWith(
      minimumDeposit: null == minimumDeposit
          ? _value.minimumDeposit
          : minimumDeposit // ignore: cast_nullable_to_non_nullable
              as String,
      maximumDeposit: null == maximumDeposit
          ? _value.maximumDeposit
          : maximumDeposit // ignore: cast_nullable_to_non_nullable
              as String,
      minimumWithdrawal: null == minimumWithdrawal
          ? _value.minimumWithdrawal
          : minimumWithdrawal // ignore: cast_nullable_to_non_nullable
              as String,
      maximumWithdrawal: null == maximumWithdrawal
          ? _value.maximumWithdrawal
          : maximumWithdrawal // ignore: cast_nullable_to_non_nullable
              as String,
      withdrawalFeePercentage: null == withdrawalFeePercentage
          ? _value.withdrawalFeePercentage
          : withdrawalFeePercentage // ignore: cast_nullable_to_non_nullable
              as String,
      withdrawalFeeFixed: null == withdrawalFeeFixed
          ? _value.withdrawalFeeFixed
          : withdrawalFeeFixed // ignore: cast_nullable_to_non_nullable
              as String,
      depositFeePercentage: null == depositFeePercentage
          ? _value.depositFeePercentage
          : depositFeePercentage // ignore: cast_nullable_to_non_nullable
              as String,
      walletsEnabled: null == walletsEnabled
          ? _value.walletsEnabled
          : walletsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      depositsEnabled: null == depositsEnabled
          ? _value.depositsEnabled
          : depositsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      withdrawalsEnabled: null == withdrawalsEnabled
          ? _value.withdrawalsEnabled
          : withdrawalsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      requireVerification: null == requireVerification
          ? _value.requireVerification
          : requireVerification // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyWithdrawalLimit: null == dailyWithdrawalLimit
          ? _value.dailyWithdrawalLimit
          : dailyWithdrawalLimit // ignore: cast_nullable_to_non_nullable
              as String,
      monthlyWithdrawalLimit: null == monthlyWithdrawalLimit
          ? _value.monthlyWithdrawalLimit
          : monthlyWithdrawalLimit // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WalletSettingsImplCopyWith<$Res>
    implements $WalletSettingsCopyWith<$Res> {
  factory _$$WalletSettingsImplCopyWith(_$WalletSettingsImpl value,
          $Res Function(_$WalletSettingsImpl) then) =
      __$$WalletSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String minimumDeposit,
      String maximumDeposit,
      String minimumWithdrawal,
      String maximumWithdrawal,
      String withdrawalFeePercentage,
      String withdrawalFeeFixed,
      String depositFeePercentage,
      bool walletsEnabled,
      bool depositsEnabled,
      bool withdrawalsEnabled,
      bool requireVerification,
      String dailyWithdrawalLimit,
      String monthlyWithdrawalLimit});
}

/// @nodoc
class __$$WalletSettingsImplCopyWithImpl<$Res>
    extends _$WalletSettingsCopyWithImpl<$Res, _$WalletSettingsImpl>
    implements _$$WalletSettingsImplCopyWith<$Res> {
  __$$WalletSettingsImplCopyWithImpl(
      _$WalletSettingsImpl _value, $Res Function(_$WalletSettingsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minimumDeposit = null,
    Object? maximumDeposit = null,
    Object? minimumWithdrawal = null,
    Object? maximumWithdrawal = null,
    Object? withdrawalFeePercentage = null,
    Object? withdrawalFeeFixed = null,
    Object? depositFeePercentage = null,
    Object? walletsEnabled = null,
    Object? depositsEnabled = null,
    Object? withdrawalsEnabled = null,
    Object? requireVerification = null,
    Object? dailyWithdrawalLimit = null,
    Object? monthlyWithdrawalLimit = null,
  }) {
    return _then(_$WalletSettingsImpl(
      minimumDeposit: null == minimumDeposit
          ? _value.minimumDeposit
          : minimumDeposit // ignore: cast_nullable_to_non_nullable
              as String,
      maximumDeposit: null == maximumDeposit
          ? _value.maximumDeposit
          : maximumDeposit // ignore: cast_nullable_to_non_nullable
              as String,
      minimumWithdrawal: null == minimumWithdrawal
          ? _value.minimumWithdrawal
          : minimumWithdrawal // ignore: cast_nullable_to_non_nullable
              as String,
      maximumWithdrawal: null == maximumWithdrawal
          ? _value.maximumWithdrawal
          : maximumWithdrawal // ignore: cast_nullable_to_non_nullable
              as String,
      withdrawalFeePercentage: null == withdrawalFeePercentage
          ? _value.withdrawalFeePercentage
          : withdrawalFeePercentage // ignore: cast_nullable_to_non_nullable
              as String,
      withdrawalFeeFixed: null == withdrawalFeeFixed
          ? _value.withdrawalFeeFixed
          : withdrawalFeeFixed // ignore: cast_nullable_to_non_nullable
              as String,
      depositFeePercentage: null == depositFeePercentage
          ? _value.depositFeePercentage
          : depositFeePercentage // ignore: cast_nullable_to_non_nullable
              as String,
      walletsEnabled: null == walletsEnabled
          ? _value.walletsEnabled
          : walletsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      depositsEnabled: null == depositsEnabled
          ? _value.depositsEnabled
          : depositsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      withdrawalsEnabled: null == withdrawalsEnabled
          ? _value.withdrawalsEnabled
          : withdrawalsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      requireVerification: null == requireVerification
          ? _value.requireVerification
          : requireVerification // ignore: cast_nullable_to_non_nullable
              as bool,
      dailyWithdrawalLimit: null == dailyWithdrawalLimit
          ? _value.dailyWithdrawalLimit
          : dailyWithdrawalLimit // ignore: cast_nullable_to_non_nullable
              as String,
      monthlyWithdrawalLimit: null == monthlyWithdrawalLimit
          ? _value.monthlyWithdrawalLimit
          : monthlyWithdrawalLimit // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletSettingsImpl implements _WalletSettings {
  const _$WalletSettingsImpl(
      {this.minimumDeposit = '5.00',
      this.maximumDeposit = '500.00',
      this.minimumWithdrawal = '10.00',
      this.maximumWithdrawal = '1000.00',
      this.withdrawalFeePercentage = '0.00',
      this.withdrawalFeeFixed = '0.00',
      this.depositFeePercentage = '0.00',
      this.walletsEnabled = true,
      this.depositsEnabled = true,
      this.withdrawalsEnabled = true,
      this.requireVerification = false,
      this.dailyWithdrawalLimit = '100.00',
      this.monthlyWithdrawalLimit = '1000.00'});

  factory _$WalletSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletSettingsImplFromJson(json);

  @override
  @JsonKey()
  final String minimumDeposit;
  @override
  @JsonKey()
  final String maximumDeposit;
  @override
  @JsonKey()
  final String minimumWithdrawal;
  @override
  @JsonKey()
  final String maximumWithdrawal;
  @override
  @JsonKey()
  final String withdrawalFeePercentage;
  @override
  @JsonKey()
  final String withdrawalFeeFixed;
  @override
  @JsonKey()
  final String depositFeePercentage;
  @override
  @JsonKey()
  final bool walletsEnabled;
  @override
  @JsonKey()
  final bool depositsEnabled;
  @override
  @JsonKey()
  final bool withdrawalsEnabled;
  @override
  @JsonKey()
  final bool requireVerification;
  @override
  @JsonKey()
  final String dailyWithdrawalLimit;
  @override
  @JsonKey()
  final String monthlyWithdrawalLimit;

  @override
  String toString() {
    return 'WalletSettings(minimumDeposit: $minimumDeposit, maximumDeposit: $maximumDeposit, minimumWithdrawal: $minimumWithdrawal, maximumWithdrawal: $maximumWithdrawal, withdrawalFeePercentage: $withdrawalFeePercentage, withdrawalFeeFixed: $withdrawalFeeFixed, depositFeePercentage: $depositFeePercentage, walletsEnabled: $walletsEnabled, depositsEnabled: $depositsEnabled, withdrawalsEnabled: $withdrawalsEnabled, requireVerification: $requireVerification, dailyWithdrawalLimit: $dailyWithdrawalLimit, monthlyWithdrawalLimit: $monthlyWithdrawalLimit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletSettingsImpl &&
            (identical(other.minimumDeposit, minimumDeposit) ||
                other.minimumDeposit == minimumDeposit) &&
            (identical(other.maximumDeposit, maximumDeposit) ||
                other.maximumDeposit == maximumDeposit) &&
            (identical(other.minimumWithdrawal, minimumWithdrawal) ||
                other.minimumWithdrawal == minimumWithdrawal) &&
            (identical(other.maximumWithdrawal, maximumWithdrawal) ||
                other.maximumWithdrawal == maximumWithdrawal) &&
            (identical(
                    other.withdrawalFeePercentage, withdrawalFeePercentage) ||
                other.withdrawalFeePercentage == withdrawalFeePercentage) &&
            (identical(other.withdrawalFeeFixed, withdrawalFeeFixed) ||
                other.withdrawalFeeFixed == withdrawalFeeFixed) &&
            (identical(other.depositFeePercentage, depositFeePercentage) ||
                other.depositFeePercentage == depositFeePercentage) &&
            (identical(other.walletsEnabled, walletsEnabled) ||
                other.walletsEnabled == walletsEnabled) &&
            (identical(other.depositsEnabled, depositsEnabled) ||
                other.depositsEnabled == depositsEnabled) &&
            (identical(other.withdrawalsEnabled, withdrawalsEnabled) ||
                other.withdrawalsEnabled == withdrawalsEnabled) &&
            (identical(other.requireVerification, requireVerification) ||
                other.requireVerification == requireVerification) &&
            (identical(other.dailyWithdrawalLimit, dailyWithdrawalLimit) ||
                other.dailyWithdrawalLimit == dailyWithdrawalLimit) &&
            (identical(other.monthlyWithdrawalLimit, monthlyWithdrawalLimit) ||
                other.monthlyWithdrawalLimit == monthlyWithdrawalLimit));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      minimumDeposit,
      maximumDeposit,
      minimumWithdrawal,
      maximumWithdrawal,
      withdrawalFeePercentage,
      withdrawalFeeFixed,
      depositFeePercentage,
      walletsEnabled,
      depositsEnabled,
      withdrawalsEnabled,
      requireVerification,
      dailyWithdrawalLimit,
      monthlyWithdrawalLimit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletSettingsImplCopyWith<_$WalletSettingsImpl> get copyWith =>
      __$$WalletSettingsImplCopyWithImpl<_$WalletSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletSettingsImplToJson(
      this,
    );
  }
}

abstract class _WalletSettings implements WalletSettings {
  const factory _WalletSettings(
      {final String minimumDeposit,
      final String maximumDeposit,
      final String minimumWithdrawal,
      final String maximumWithdrawal,
      final String withdrawalFeePercentage,
      final String withdrawalFeeFixed,
      final String depositFeePercentage,
      final bool walletsEnabled,
      final bool depositsEnabled,
      final bool withdrawalsEnabled,
      final bool requireVerification,
      final String dailyWithdrawalLimit,
      final String monthlyWithdrawalLimit}) = _$WalletSettingsImpl;

  factory _WalletSettings.fromJson(Map<String, dynamic> json) =
      _$WalletSettingsImpl.fromJson;

  @override
  String get minimumDeposit;
  @override
  String get maximumDeposit;
  @override
  String get minimumWithdrawal;
  @override
  String get maximumWithdrawal;
  @override
  String get withdrawalFeePercentage;
  @override
  String get withdrawalFeeFixed;
  @override
  String get depositFeePercentage;
  @override
  bool get walletsEnabled;
  @override
  bool get depositsEnabled;
  @override
  bool get withdrawalsEnabled;
  @override
  bool get requireVerification;
  @override
  String get dailyWithdrawalLimit;
  @override
  String get monthlyWithdrawalLimit;
  @override
  @JsonKey(ignore: true)
  _$$WalletSettingsImplCopyWith<_$WalletSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WalletSpendResult _$WalletSpendResultFromJson(Map<String, dynamic> json) {
  return _WalletSpendResult.fromJson(json);
}

/// @nodoc
mixin _$WalletSpendResult {
  String get transactionId => throw _privateConstructorUsedError;
  String get newBalance => throw _privateConstructorUsedError;
  String get formattedBalance => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletSpendResultCopyWith<WalletSpendResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletSpendResultCopyWith<$Res> {
  factory $WalletSpendResultCopyWith(
          WalletSpendResult value, $Res Function(WalletSpendResult) then) =
      _$WalletSpendResultCopyWithImpl<$Res, WalletSpendResult>;
  @useResult
  $Res call(
      {String transactionId,
      String newBalance,
      String formattedBalance,
      String message});
}

/// @nodoc
class _$WalletSpendResultCopyWithImpl<$Res, $Val extends WalletSpendResult>
    implements $WalletSpendResultCopyWith<$Res> {
  _$WalletSpendResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
    Object? newBalance = null,
    Object? formattedBalance = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      newBalance: null == newBalance
          ? _value.newBalance
          : newBalance // ignore: cast_nullable_to_non_nullable
              as String,
      formattedBalance: null == formattedBalance
          ? _value.formattedBalance
          : formattedBalance // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WalletSpendResultImplCopyWith<$Res>
    implements $WalletSpendResultCopyWith<$Res> {
  factory _$$WalletSpendResultImplCopyWith(_$WalletSpendResultImpl value,
          $Res Function(_$WalletSpendResultImpl) then) =
      __$$WalletSpendResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String transactionId,
      String newBalance,
      String formattedBalance,
      String message});
}

/// @nodoc
class __$$WalletSpendResultImplCopyWithImpl<$Res>
    extends _$WalletSpendResultCopyWithImpl<$Res, _$WalletSpendResultImpl>
    implements _$$WalletSpendResultImplCopyWith<$Res> {
  __$$WalletSpendResultImplCopyWithImpl(_$WalletSpendResultImpl _value,
      $Res Function(_$WalletSpendResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
    Object? newBalance = null,
    Object? formattedBalance = null,
    Object? message = null,
  }) {
    return _then(_$WalletSpendResultImpl(
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      newBalance: null == newBalance
          ? _value.newBalance
          : newBalance // ignore: cast_nullable_to_non_nullable
              as String,
      formattedBalance: null == formattedBalance
          ? _value.formattedBalance
          : formattedBalance // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletSpendResultImpl implements _WalletSpendResult {
  const _$WalletSpendResultImpl(
      {this.transactionId = '',
      this.newBalance = '0.00',
      this.formattedBalance = '0.00',
      this.message = ''});

  factory _$WalletSpendResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletSpendResultImplFromJson(json);

  @override
  @JsonKey()
  final String transactionId;
  @override
  @JsonKey()
  final String newBalance;
  @override
  @JsonKey()
  final String formattedBalance;
  @override
  @JsonKey()
  final String message;

  @override
  String toString() {
    return 'WalletSpendResult(transactionId: $transactionId, newBalance: $newBalance, formattedBalance: $formattedBalance, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletSpendResultImpl &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.newBalance, newBalance) ||
                other.newBalance == newBalance) &&
            (identical(other.formattedBalance, formattedBalance) ||
                other.formattedBalance == formattedBalance) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, transactionId, newBalance, formattedBalance, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletSpendResultImplCopyWith<_$WalletSpendResultImpl> get copyWith =>
      __$$WalletSpendResultImplCopyWithImpl<_$WalletSpendResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletSpendResultImplToJson(
      this,
    );
  }
}

abstract class _WalletSpendResult implements WalletSpendResult {
  const factory _WalletSpendResult(
      {final String transactionId,
      final String newBalance,
      final String formattedBalance,
      final String message}) = _$WalletSpendResultImpl;

  factory _WalletSpendResult.fromJson(Map<String, dynamic> json) =
      _$WalletSpendResultImpl.fromJson;

  @override
  String get transactionId;
  @override
  String get newBalance;
  @override
  String get formattedBalance;
  @override
  String get message;
  @override
  @JsonKey(ignore: true)
  _$$WalletSpendResultImplCopyWith<_$WalletSpendResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WalletState _$WalletStateFromJson(Map<String, dynamic> json) {
  return _WalletState.fromJson(json);
}

/// @nodoc
mixin _$WalletState {
  WalletOverview? get overview => throw _privateConstructorUsedError;
  WalletSettings? get settings => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  List<WalletTransaction> get transactions =>
      throw _privateConstructorUsedError;
  bool get hasMoreTransactions => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WalletStateCopyWith<WalletState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletStateCopyWith<$Res> {
  factory $WalletStateCopyWith(
          WalletState value, $Res Function(WalletState) then) =
      _$WalletStateCopyWithImpl<$Res, WalletState>;
  @useResult
  $Res call(
      {WalletOverview? overview,
      WalletSettings? settings,
      bool isLoading,
      String? error,
      List<WalletTransaction> transactions,
      bool hasMoreTransactions});

  $WalletOverviewCopyWith<$Res>? get overview;
  $WalletSettingsCopyWith<$Res>? get settings;
}

/// @nodoc
class _$WalletStateCopyWithImpl<$Res, $Val extends WalletState>
    implements $WalletStateCopyWith<$Res> {
  _$WalletStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? overview = freezed,
    Object? settings = freezed,
    Object? isLoading = null,
    Object? error = freezed,
    Object? transactions = null,
    Object? hasMoreTransactions = null,
  }) {
    return _then(_value.copyWith(
      overview: freezed == overview
          ? _value.overview
          : overview // ignore: cast_nullable_to_non_nullable
              as WalletOverview?,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as WalletSettings?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      transactions: null == transactions
          ? _value.transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<WalletTransaction>,
      hasMoreTransactions: null == hasMoreTransactions
          ? _value.hasMoreTransactions
          : hasMoreTransactions // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WalletOverviewCopyWith<$Res>? get overview {
    if (_value.overview == null) {
      return null;
    }

    return $WalletOverviewCopyWith<$Res>(_value.overview!, (value) {
      return _then(_value.copyWith(overview: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $WalletSettingsCopyWith<$Res>? get settings {
    if (_value.settings == null) {
      return null;
    }

    return $WalletSettingsCopyWith<$Res>(_value.settings!, (value) {
      return _then(_value.copyWith(settings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$WalletStateImplCopyWith<$Res>
    implements $WalletStateCopyWith<$Res> {
  factory _$$WalletStateImplCopyWith(
          _$WalletStateImpl value, $Res Function(_$WalletStateImpl) then) =
      __$$WalletStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {WalletOverview? overview,
      WalletSettings? settings,
      bool isLoading,
      String? error,
      List<WalletTransaction> transactions,
      bool hasMoreTransactions});

  @override
  $WalletOverviewCopyWith<$Res>? get overview;
  @override
  $WalletSettingsCopyWith<$Res>? get settings;
}

/// @nodoc
class __$$WalletStateImplCopyWithImpl<$Res>
    extends _$WalletStateCopyWithImpl<$Res, _$WalletStateImpl>
    implements _$$WalletStateImplCopyWith<$Res> {
  __$$WalletStateImplCopyWithImpl(
      _$WalletStateImpl _value, $Res Function(_$WalletStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? overview = freezed,
    Object? settings = freezed,
    Object? isLoading = null,
    Object? error = freezed,
    Object? transactions = null,
    Object? hasMoreTransactions = null,
  }) {
    return _then(_$WalletStateImpl(
      overview: freezed == overview
          ? _value.overview
          : overview // ignore: cast_nullable_to_non_nullable
              as WalletOverview?,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as WalletSettings?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      transactions: null == transactions
          ? _value._transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<WalletTransaction>,
      hasMoreTransactions: null == hasMoreTransactions
          ? _value.hasMoreTransactions
          : hasMoreTransactions // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WalletStateImpl implements _WalletState {
  const _$WalletStateImpl(
      {this.overview,
      this.settings,
      this.isLoading = false,
      this.error,
      final List<WalletTransaction> transactions = const [],
      this.hasMoreTransactions = false})
      : _transactions = transactions;

  factory _$WalletStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$WalletStateImplFromJson(json);

  @override
  final WalletOverview? overview;
  @override
  final WalletSettings? settings;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;
  final List<WalletTransaction> _transactions;
  @override
  @JsonKey()
  List<WalletTransaction> get transactions {
    if (_transactions is EqualUnmodifiableListView) return _transactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions);
  }

  @override
  @JsonKey()
  final bool hasMoreTransactions;

  @override
  String toString() {
    return 'WalletState(overview: $overview, settings: $settings, isLoading: $isLoading, error: $error, transactions: $transactions, hasMoreTransactions: $hasMoreTransactions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WalletStateImpl &&
            (identical(other.overview, overview) ||
                other.overview == overview) &&
            (identical(other.settings, settings) ||
                other.settings == settings) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality()
                .equals(other._transactions, _transactions) &&
            (identical(other.hasMoreTransactions, hasMoreTransactions) ||
                other.hasMoreTransactions == hasMoreTransactions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      overview,
      settings,
      isLoading,
      error,
      const DeepCollectionEquality().hash(_transactions),
      hasMoreTransactions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$WalletStateImplCopyWith<_$WalletStateImpl> get copyWith =>
      __$$WalletStateImplCopyWithImpl<_$WalletStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WalletStateImplToJson(
      this,
    );
  }
}

abstract class _WalletState implements WalletState {
  const factory _WalletState(
      {final WalletOverview? overview,
      final WalletSettings? settings,
      final bool isLoading,
      final String? error,
      final List<WalletTransaction> transactions,
      final bool hasMoreTransactions}) = _$WalletStateImpl;

  factory _WalletState.fromJson(Map<String, dynamic> json) =
      _$WalletStateImpl.fromJson;

  @override
  WalletOverview? get overview;
  @override
  WalletSettings? get settings;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  List<WalletTransaction> get transactions;
  @override
  bool get hasMoreTransactions;
  @override
  @JsonKey(ignore: true)
  _$$WalletStateImplCopyWith<_$WalletStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
