from django.urls import path
from . import views

app_name = 'interactive'

urlpatterns = [
    # Interactive blocks
    path('posts/<int:post_id>/blocks/', views.get_post_interactive_blocks, name='post-blocks'),
    path('posts/<int:post_id>/blocks/create/', views.create_interactive_block, name='create-block'),
    path('blocks/<int:block_id>/', views.get_interactive_block, name='get-block'),
    path('blocks/<int:block_id>/delete/', views.delete_interactive_block, name='delete-block'),
    
    # Polls
    path('polls/<int:poll_id>/debug/', views.debug_poll_auth, name='debug-poll-auth'),
    path('polls/<int:poll_id>/vote/', views.vote_poll, name='vote-poll'),
    path('polls/<int:poll_id>/results/', views.get_poll_results, name='poll-results'),
    
    # Quizzes
    path('quizzes/<int:quiz_id>/submit/', views.submit_quiz, name='submit-quiz'),
    path('quizzes/<int:quiz_id>/attempts/', views.get_quiz_attempts, name='quiz-attempts'),
]
