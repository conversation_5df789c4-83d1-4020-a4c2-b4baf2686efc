import 'package:flutter/material.dart';
import 'dart:async';
import '../services/analytics_service.dart';
import '../models/content_analytics.dart';

class ReadingProgressTracker extends StatefulWidget {
  final int postId;
  final Widget child;
  final VoidCallback? onReadingStarted;
  final VoidCallback? onReadingCompleted;

  const ReadingProgressTracker({
    super.key,
    required this.postId,
    required this.child,
    this.onReadingStarted,
    this.onReadingCompleted,
  });

  @override
  State<ReadingProgressTracker> createState() => _ReadingProgressTrackerState();
}

class _ReadingProgressTrackerState extends State<ReadingProgressTracker> {
  final AnalyticsService _analyticsService = AnalyticsService();
  final ScrollController _scrollController = ScrollController();
  
  String? _sessionId;
  bool _hasStartedReading = false;
  bool _hasCompletedReading = false;
  double _currentProgress = 0.0;
  Timer? _progressUpdateTimer;
  ContentAnalytics? _contentAnalytics;

  @override
  void initState() {
    super.initState();
    _initializeTracking();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _progressUpdateTimer?.cancel();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _analyticsService.endReadingSession();
    _analyticsService.dispose();
    super.dispose();
  }

  Future<void> _initializeTracking() async {
    // Load content analytics
    _contentAnalytics = await _analyticsService.getContentAnalytics(widget.postId);
    
    // Start reading session after a short delay to ensure user is actually reading
    Timer(const Duration(seconds: 2), () {
      if (mounted && !_hasStartedReading) {
        _startReadingSession();
      }
    });
  }

  Future<void> _startReadingSession() async {
    if (_hasStartedReading) return;
    
    _sessionId = await _analyticsService.startReadingSession(
      widget.postId,
      deviceType: _getDeviceType(),
    );
    
    if (_sessionId != null) {
      _hasStartedReading = true;
      widget.onReadingStarted?.call();
      
      // Start periodic progress updates
      _progressUpdateTimer = Timer.periodic(
        const Duration(seconds: 5),
        (_) => _updateProgress(),
      );
    }
  }

  void _onScroll() {
    if (!_hasStartedReading || _scrollController.position.maxScrollExtent == 0) return;
    
    final scrollProgress = _scrollController.offset / _scrollController.position.maxScrollExtent;
    final progressPercentage = (scrollProgress * 100).clamp(0.0, 100.0);
    
    _currentProgress = progressPercentage;
    
    // Mark as completed if user has read 90% or more
    if (progressPercentage >= 90 && !_hasCompletedReading) {
      _hasCompletedReading = true;
      widget.onReadingCompleted?.call();
    }
  }

  Future<void> _updateProgress() async {
    if (_sessionId == null) return;
    
    final scrollDepth = _scrollController.hasClients 
        ? (_scrollController.offset / _scrollController.position.maxScrollExtent * 100).clamp(0.0, 100.0)
        : 0.0;
    
    await _analyticsService.updateProgress(_currentProgress, scrollDepth);
  }

  String _getDeviceType() {
    final data = MediaQuery.of(context);
    if (data.size.width < 600) return 'mobile';
    if (data.size.width < 1200) return 'tablet';
    return 'desktop';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Reading progress indicator
        if (_hasStartedReading) _buildProgressIndicator(),
        
        // Content with scroll tracking
        Expanded(
          child: Scrollbar(
            controller: _scrollController,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: widget.child,
            ),
          ),
        ),
        
        // Reading analytics summary
        if (_contentAnalytics != null) _buildAnalyticsSummary(),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      height: 4,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: LinearProgressIndicator(
        value: _currentProgress / 100,
        backgroundColor: Colors.grey[300],
        valueColor: AlwaysStoppedAnimation<Color>(
          _hasCompletedReading ? Colors.green : Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildAnalyticsSummary() {
    final analytics = _contentAnalytics!;
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reading Analytics',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsItem(
                  'Reading Time',
                  AnalyticsService.formatReadingTime(analytics.estimatedReadingTime),
                  Icons.schedule,
                ),
              ),
              Expanded(
                child: _buildAnalyticsItem(
                  'Words',
                  '${analytics.wordCount}',
                  Icons.text_fields,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildAnalyticsItem(
                  'Difficulty',
                  analytics.readingLevel,
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildAnalyticsItem(
                  'Progress',
                  '${_currentProgress.toInt()}%',
                  Icons.timeline,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
