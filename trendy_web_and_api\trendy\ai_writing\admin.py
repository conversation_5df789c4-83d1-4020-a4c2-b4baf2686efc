from django.contrib import admin
from django.utils.html import format_html
from .models import (
    AIConfiguration, AIWritingPreferences, ContentPrompt, AIWritingSession,
    ContentSuggestion, AIUsageAnalytics, ContentTemplate
)


@admin.register(AIConfiguration)
class AIConfigurationAdmin(admin.ModelAdmin):
    list_display = ['name', 'provider', 'model_name', 'status_display', 'is_default', 'total_requests', 'last_used']
    list_filter = ['provider', 'is_active', 'is_default', 'enable_content_generation']
    search_fields = ['name', 'model_name']
    readonly_fields = ['total_requests', 'total_tokens_used', 'last_used', 'created_at', 'updated_at']

    fieldsets = (
        ('Basic Configuration', {
            'fields': ('name', 'provider', 'model_name', 'is_active', 'is_default')
        }),
        ('API Settings', {
            'fields': ('api_key', 'api_base_url', 'api_version'),
            'description': 'Configure API connection settings'
        }),
        ('Model Parameters', {
            'fields': ('max_tokens', 'temperature', 'top_p', 'frequency_penalty', 'presence_penalty'),
            'description': 'Fine-tune model behavior'
        }),
        ('Rate Limiting', {
            'fields': ('requests_per_minute', 'requests_per_day'),
            'description': 'Control API usage limits'
        }),
        ('Feature Toggles', {
            'fields': (
                'enable_content_generation', 'enable_grammar_improvement',
                'enable_seo_suggestions', 'enable_text_completion', 'enable_readability_analysis'
            ),
            'description': 'Enable/disable specific AI features'
        }),
        ('Advanced Settings', {
            'fields': ('custom_headers',),
            'classes': ('collapse',),
            'description': 'Additional API headers in JSON format'
        }),
        ('Usage Statistics', {
            'fields': ('total_requests', 'total_tokens_used', 'last_used'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def status_display(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">🟢 Active</span>')
        else:
            return format_html('<span style="color: red;">🔴 Inactive</span>')
    status_display.short_description = 'Status'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()

    actions = ['make_default', 'activate_configs', 'deactivate_configs']

    def make_default(self, request, queryset):
        if queryset.count() != 1:
            self.message_user(request, "Please select exactly one configuration to make default.", level='error')
            return

        config = queryset.first()
        AIConfiguration.objects.filter(is_default=True).update(is_default=False)
        config.is_default = True
        config.save()
        self.message_user(request, f"'{config.name}' is now the default AI configuration.")
    make_default.short_description = "Set as default configuration"

    def activate_configs(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} AI configuration(s).")
    activate_configs.short_description = "Activate selected configurations"

    def deactivate_configs(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} AI configuration(s).")
    deactivate_configs.short_description = "Deactivate selected configurations"


@admin.register(AIWritingPreferences)
class AIWritingPreferencesAdmin(admin.ModelAdmin):
    list_display = ['user', 'preferred_tone', 'preferred_style', 'target_audience', 'updated_at']
    list_filter = ['preferred_tone', 'preferred_style', 'enable_grammar_suggestions', 'enable_seo_suggestions']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Writing Preferences', {
            'fields': ('preferred_tone', 'preferred_style', 'target_audience', 'preferred_word_count')
        }),
        ('Feature Settings', {
            'fields': (
                'enable_grammar_suggestions', 'enable_seo_suggestions', 
                'enable_content_generation', 'enable_readability_analysis', 
                'enable_auto_complete'
            )
        }),
        ('Content Preferences', {
            'fields': ('include_references', 'include_images_suggestions')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(ContentPrompt)
class ContentPromptAdmin(admin.ModelAdmin):
    list_display = ['name', 'prompt_type', 'category', 'is_active', 'usage_count', 'created_at']
    list_filter = ['prompt_type', 'is_active', 'category']
    search_fields = ['name', 'template']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Info', {
            'fields': ('name', 'prompt_type', 'category', 'is_active')
        }),
        ('Template', {
            'fields': ('template',),
            'description': 'Use placeholders: {topic}, {tone}, {audience}'
        }),
        ('Statistics', {
            'fields': ('usage_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(AIWritingSession)
class AIWritingSessionAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'post', 'status', 'suggestions_generated', 'suggestions_accepted', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__username', 'post__title']
    readonly_fields = ['id', 'created_at', 'updated_at', 'completed_at']
    
    fieldsets = (
        ('Session Info', {
            'fields': ('id', 'user', 'post', 'status')
        }),
        ('Analytics', {
            'fields': (
                'suggestions_generated', 'suggestions_accepted', 
                'words_generated', 'time_saved_minutes'
            )
        }),
        ('Session Data', {
            'fields': ('session_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'completed_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(ContentSuggestion)
class ContentSuggestionAdmin(admin.ModelAdmin):
    list_display = ['session', 'suggestion_type', 'status', 'confidence_score', 'created_at']
    list_filter = ['suggestion_type', 'status', 'created_at']
    search_fields = ['session__user__username', 'original_text', 'suggested_text']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Suggestion Info', {
            'fields': ('session', 'suggestion_type', 'status', 'confidence_score')
        }),
        ('Content', {
            'fields': ('original_text', 'suggested_text', 'explanation')
        }),
        ('Position', {
            'fields': ('start_position', 'end_position')
        }),
        ('Feedback', {
            'fields': ('user_feedback',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(AIUsageAnalytics)
class AIUsageAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'feature_used', 'usage_count', 'total_words_generated', 'year', 'month']
    list_filter = ['feature_used', 'year', 'month']
    search_fields = ['user__username', 'feature_used']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Analytics', {
            'fields': (
                'user', 'feature_used', 'usage_count', 
                'total_words_generated', 'total_time_saved_minutes'
            )
        }),
        ('Period', {
            'fields': ('year', 'month')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )


@admin.register(ContentTemplate)
class ContentTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'difficulty_level', 'estimated_word_count', 'usage_count', 'is_public']
    list_filter = ['difficulty_level', 'is_public', 'category']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Template Info', {
            'fields': ('name', 'description', 'category', 'is_public', 'created_by')
        }),
        ('Template Content', {
            'fields': ('template_content',),
            'description': 'Use placeholders: {title}, {topic}, {audience}'
        }),
        ('Metadata', {
            'fields': ('estimated_word_count', 'difficulty_level')
        }),
        ('Statistics', {
            'fields': ('usage_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
