"""
Regional content filtering URLs
"""

from django.urls import path
from . import views

app_name = 'regional'

urlpatterns = [
    # Public endpoints
    path('countries/', views.get_countries, name='countries'),
    path('regions/', views.get_regions, name='regions'),
    
    # User preferences
    path('preferences/', views.get_user_regional_preferences, name='user_preferences'),
    path('preferences/country/', views.set_preferred_country, name='set_preferred_country'),
    path('preferences/settings/', views.update_regional_settings, name='update_settings'),
    
    # Location detection
    path('detect-location/', views.detect_location, name='detect_location'),
    path('detect-location-enhanced/', views.detect_location_with_fallback, name='detect_location_enhanced'),
    path('location-history/', views.get_location_history, name='location_history'),
    
    # Statistics
    path('stats/', views.get_regional_stats, name='regional_stats'),
]
