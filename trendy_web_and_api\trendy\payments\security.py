import hashlib
import hmac
import json
import time
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Any, Optional, Tuple

from django.conf import settings
from django.core.cache import cache
from django.http import HttpRequest
from django.utils import timezone
from django.contrib.auth.models import User

from .models import PaymentTransaction


class PaymentSecurityService:
    """Service for payment security, validation, and fraud prevention"""
    
    # Security constants
    MAX_PAYMENT_AMOUNT = Decimal('10000.00')
    MIN_PAYMENT_AMOUNT = Decimal('0.01')
    MAX_FAILED_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    RATE_LIMIT_WINDOW_MINUTES = 60
    MAX_PAYMENTS_PER_HOUR = 10
    HIGH_VALUE_THRESHOLD = Decimal('1000.00')
    
    @classmethod
    def validate_payment_request(
        cls,
        user: User,
        amount: Decimal,
        payment_method: str,
        currency: str = 'USD',
        request: Optional[HttpRequest] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Comprehensive payment validation
        Returns: (is_valid, error_message, error_code)
        """
        
        # Basic amount validation
        if amount < cls.MIN_PAYMENT_AMOUNT:
            return False, f'Payment amount must be at least ${cls.MIN_PAYMENT_AMOUNT}', 'AMOUNT_TOO_LOW'
        
        if amount > cls.MAX_PAYMENT_AMOUNT:
            return False, f'Payment amount exceeds maximum limit of ${cls.MAX_PAYMENT_AMOUNT}', 'AMOUNT_TOO_HIGH'
        
        # Payment method validation
        if not cls._is_valid_payment_method(payment_method):
            return False, 'Invalid payment method', 'INVALID_PAYMENT_METHOD'
        
        # Currency validation
        if not cls._is_valid_currency(currency):
            return False, f'Unsupported currency: {currency}', 'INVALID_CURRENCY'
        
        # Check user lockout status
        is_locked, lockout_message = cls._check_user_lockout(user)
        if is_locked:
            return False, lockout_message, 'ACCOUNT_LOCKED'
        
        # Rate limiting check
        is_rate_limited, rate_limit_message = cls._check_rate_limit(user)
        if is_rate_limited:
            return False, rate_limit_message, 'RATE_LIMITED'
        
        # Fraud detection checks
        is_suspicious, fraud_message = cls._check_fraud_patterns(user, amount, request, metadata)
        if is_suspicious:
            return False, fraud_message, 'SUSPICIOUS_ACTIVITY'
        
        # High-value transaction check
        if amount >= cls.HIGH_VALUE_THRESHOLD:
            # In production, this might trigger additional verification
            pass
        
        return True, None, None
    
    @classmethod
    def generate_payment_token(cls, user_id: int, amount: Decimal, timestamp: str) -> str:
        """Generate secure payment token for verification"""
        secret_key = getattr(settings, 'PAYMENT_SECRET_KEY', settings.SECRET_KEY)
        data = f"{user_id}:{amount}:{timestamp}"
        return hmac.new(
            secret_key.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    @classmethod
    def verify_payment_token(cls, token: str, user_id: int, amount: Decimal, timestamp: str) -> bool:
        """Verify payment token"""
        expected_token = cls.generate_payment_token(user_id, amount, timestamp)
        return hmac.compare_digest(token, expected_token)
    
    @classmethod
    def record_failed_attempt(cls, user: User, reason: str = 'payment_failed') -> None:
        """Record failed payment attempt for fraud prevention"""
        cache_key = f"payment_failures:{user.id}"
        current_time = timezone.now()
        
        # Get existing failures
        failures = cache.get(cache_key, [])
        
        # Add new failure
        failures.append({
            'timestamp': current_time.isoformat(),
            'reason': reason
        })
        
        # Keep only recent failures (within lockout window)
        cutoff_time = current_time - timedelta(minutes=cls.LOCKOUT_DURATION_MINUTES)
        failures = [
            f for f in failures 
            if datetime.fromisoformat(f['timestamp'].replace('Z', '+00:00')) > cutoff_time
        ]
        
        # Store updated failures
        cache.set(cache_key, failures, timeout=cls.LOCKOUT_DURATION_MINUTES * 60)
    
    @classmethod
    def clear_failed_attempts(cls, user: User) -> None:
        """Clear failed attempts on successful payment"""
        cache_key = f"payment_failures:{user.id}"
        cache.delete(cache_key)
    
    @classmethod
    def get_client_ip(cls, request: HttpRequest) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'
    
    @classmethod
    def _is_valid_payment_method(cls, payment_method: str) -> bool:
        """Validate payment method"""
        valid_methods = ['paypal', 'store_points', 'wallet', 'in_app_purchase']
        return payment_method.lower() in valid_methods
    
    @classmethod
    def _is_valid_currency(cls, currency: str) -> bool:
        """Validate currency"""
        valid_currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD']
        return currency.upper() in valid_currencies
    
    @classmethod
    def _check_user_lockout(cls, user: User) -> Tuple[bool, Optional[str]]:
        """Check if user is locked out due to failed attempts"""
        cache_key = f"payment_failures:{user.id}"
        failures = cache.get(cache_key, [])
        
        if len(failures) >= cls.MAX_FAILED_ATTEMPTS:
            # Check if lockout period has expired
            latest_failure = max(failures, key=lambda x: x['timestamp'])
            failure_time = datetime.fromisoformat(latest_failure['timestamp'].replace('Z', '+00:00'))
            lockout_end = failure_time + timedelta(minutes=cls.LOCKOUT_DURATION_MINUTES)
            
            if timezone.now() < lockout_end:
                remaining_minutes = int((lockout_end - timezone.now()).total_seconds() / 60)
                return True, f'Too many failed payment attempts. Please try again in {remaining_minutes} minutes.'
            else:
                # Lockout expired, clear failures
                cls.clear_failed_attempts(user)
        
        return False, None
    
    @classmethod
    def _check_rate_limit(cls, user: User) -> Tuple[bool, Optional[str]]:
        """Check payment rate limiting"""
        cache_key = f"payment_rate_limit:{user.id}"
        current_time = timezone.now()
        
        # Get recent payment attempts
        attempts = cache.get(cache_key, [])
        
        # Filter attempts within the rate limit window
        cutoff_time = current_time - timedelta(minutes=cls.RATE_LIMIT_WINDOW_MINUTES)
        recent_attempts = [
            attempt for attempt in attempts
            if datetime.fromisoformat(attempt.replace('Z', '+00:00')) > cutoff_time
        ]
        
        if len(recent_attempts) >= cls.MAX_PAYMENTS_PER_HOUR:
            return True, f'Too many payment attempts. Maximum {cls.MAX_PAYMENTS_PER_HOUR} payments per hour allowed.'
        
        # Add current attempt
        recent_attempts.append(current_time.isoformat())
        cache.set(cache_key, recent_attempts, timeout=cls.RATE_LIMIT_WINDOW_MINUTES * 60)
        
        return False, None
    
    @classmethod
    def _check_fraud_patterns(
        cls,
        user: User,
        amount: Decimal,
        request: Optional[HttpRequest] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[str]]:
        """Check for suspicious fraud patterns"""
        
        # Check for rapid successive high-value payments
        recent_transactions = PaymentTransaction.objects.filter(
            user=user,
            created_at__gte=timezone.now() - timedelta(minutes=10),
            amount__gte=Decimal('100.00')
        ).count()
        
        if recent_transactions >= 3:
            return True, 'Suspicious payment pattern detected. Please contact support.'
        
        # Check for unusual payment amounts (round numbers might be suspicious)
        if amount >= Decimal('500.00') and amount % 100 == 0:
            # This is a simple heuristic - in production you'd have more sophisticated checks
            pass
        
        # IP-based checks (if request is available)
        if request:
            client_ip = cls.get_client_ip(request)
            
            # Check for multiple users from same IP making payments
            ip_cache_key = f"payment_ip:{client_ip}"
            ip_users = cache.get(ip_cache_key, set())
            
            if len(ip_users) > 5:  # More than 5 users from same IP
                return True, 'Suspicious activity detected from your network. Please contact support.'
            
            ip_users.add(user.id)
            cache.set(ip_cache_key, ip_users, timeout=3600)  # 1 hour
        
        return False, None
    
    @classmethod
    def log_security_event(cls, user: User, event_type: str, details: Dict[str, Any]) -> None:
        """Log security events for monitoring"""
        # In production, this would integrate with your logging/monitoring system
        import logging
        logger = logging.getLogger('payment_security')
        
        logger.warning(
            f"Payment security event: {event_type} for user {user.id}",
            extra={
                'user_id': user.id,
                'event_type': event_type,
                'details': details,
                'timestamp': timezone.now().isoformat()
            }
        )


class PaymentSecurityMiddleware:
    """Middleware for payment security checks"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Pre-process payment requests
        if self._is_payment_request(request):
            security_check = self._perform_security_checks(request)
            if not security_check['allowed']:
                from django.http import JsonResponse
                return JsonResponse({
                    'success': False,
                    'error': security_check['message'],
                    'error_code': security_check.get('code', 'SECURITY_CHECK_FAILED')
                }, status=403)
        
        response = self.get_response(request)
        return response
    
    def _is_payment_request(self, request) -> bool:
        """Check if this is a payment-related request"""
        payment_paths = [
            '/api/v1/payments/',
            '/api/v1/monetization/',
        ]
        return any(request.path.startswith(path) for path in payment_paths)
    
    def _perform_security_checks(self, request) -> Dict[str, Any]:
        """Perform basic security checks on payment requests"""
        # Basic rate limiting by IP
        client_ip = PaymentSecurityService.get_client_ip(request)
        ip_cache_key = f"request_rate_limit:{client_ip}"
        
        request_count = cache.get(ip_cache_key, 0)
        if request_count > 100:  # Max 100 requests per minute per IP
            return {
                'allowed': False,
                'message': 'Too many requests from your IP address',
                'code': 'IP_RATE_LIMITED'
            }
        
        cache.set(ip_cache_key, request_count + 1, timeout=60)
        
        return {'allowed': True}
