import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/voice_service.dart';
import '../../theme/app_theme.dart';

class TextToSpeechWidget extends ConsumerStatefulWidget {
  final String text;
  final String? title;

  const TextToSpeechWidget({super.key, required this.text, this.title});

  @override
  ConsumerState<TextToSpeechWidget> createState() => _TextToSpeechWidgetState();
}

class _TextToSpeechWidgetState extends ConsumerState<TextToSpeechWidget>
    with TickerProviderStateMixin {
  final VoiceService _voiceService = VoiceService();

  bool _isPlaying = false;
  bool _isInitialized = false;
  double _speechRate = 0.5;
  double _pitch = 1.0;
  bool _showSettings = false;

  late AnimationController _pulseController;
  late AnimationController _settingsController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _settingsAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _settingsController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _settingsAnimation = CurvedAnimation(
      parent: _settingsController,
      curve: Curves.easeInOut,
    );

    _initializeVoiceService();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _settingsController.dispose();
    super.dispose();
  }

  Future<void> _initializeVoiceService() async {
    try {
      await _voiceService.initialize();
      setState(() {
        _isInitialized = _voiceService.isInitialized;
      });
    } catch (e) {
      setState(() {
        _isInitialized = false;
      });
    }
  }

  Future<void> _togglePlayback() async {
    if (!_isInitialized) return;

    if (_isPlaying) {
      await _stopPlayback();
    } else {
      await _startPlayback();
    }
  }

  Future<void> _startPlayback() async {
    try {
      // Clean text for better speech
      final cleanText = _cleanTextForSpeech(widget.text);

      await _voiceService.speak(cleanText);

      setState(() {
        _isPlaying = true;
      });

      _pulseController.repeat(reverse: true);

      // Monitor speaking status
      _monitorSpeaking();
    } catch (e) {
      _showError('Failed to start text-to-speech: $e');
    }
  }

  Future<void> _stopPlayback() async {
    await _voiceService.stop();

    setState(() {
      _isPlaying = false;
    });

    _pulseController.stop();
    _pulseController.reset();
  }

  void _monitorSpeaking() {
    // Check if still speaking every second
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _isPlaying && !_voiceService.isSpeaking) {
        setState(() {
          _isPlaying = false;
        });
        _pulseController.stop();
        _pulseController.reset();
      } else if (mounted && _isPlaying) {
        _monitorSpeaking();
      }
    });
  }

  String _cleanTextForSpeech(String text) {
    // Remove HTML tags and clean up text for better speech
    return text
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(
          RegExp(r'\s+'),
          ' ',
        ) // Replace multiple spaces with single space
        .replaceAll(
          RegExp(r'[^\w\s.,!?;:]'),
          '',
        ) // Keep only alphanumeric and basic punctuation
        .trim();
  }

  void _toggleSettings() {
    setState(() {
      _showSettings = !_showSettings;
    });

    if (_showSettings) {
      _settingsController.forward();
    } else {
      _settingsController.reverse();
    }
  }

  Future<void> _updateSpeechRate(double rate) async {
    setState(() {
      _speechRate = rate;
    });
    await _voiceService.setSpeechRate(rate);
  }

  Future<void> _updatePitch(double pitch) async {
    setState(() {
      _pitch = pitch;
    });
    await _voiceService.setPitch(pitch);
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Main control
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Play/Stop button
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _isPlaying ? _pulseAnimation.value : 1.0,
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          onPressed: _togglePlayback,
                          icon: Icon(
                            _isPlaying ? Icons.stop : Icons.play_arrow,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(width: 16),

                // Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.record_voice_over,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Listen to Article',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _isPlaying ? 'Playing...' : 'Tap to listen',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      if (widget.title != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          widget.title!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),

                // Settings button
                IconButton(
                  onPressed: _toggleSettings,
                  icon: Icon(Icons.settings, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // Settings panel
          SizeTransition(
            sizeFactor: _settingsAnimation,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Voice Settings',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Speech rate
                  Row(
                    children: [
                      Icon(Icons.speed, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Speed',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${(_speechRate * 2).toStringAsFixed(1)}x',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                  Slider(
                    value: _speechRate,
                    min: 0.1,
                    max: 1.0,
                    divisions: 9,
                    activeColor: AppTheme.primaryColor,
                    onChanged: _updateSpeechRate,
                  ),

                  const SizedBox(height: 8),

                  // Pitch
                  Row(
                    children: [
                      Icon(Icons.tune, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Pitch',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        _pitch.toStringAsFixed(1),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                  Slider(
                    value: _pitch,
                    min: 0.5,
                    max: 2.0,
                    divisions: 15,
                    activeColor: AppTheme.primaryColor,
                    onChanged: _updatePitch,
                  ),

                  const SizedBox(height: 8),

                  // Quick presets
                  Row(
                    children: [
                      Expanded(
                        child: _buildPresetButton('Slow', () {
                          _updateSpeechRate(0.3);
                          _updatePitch(0.8);
                        }),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildPresetButton('Normal', () {
                          _updateSpeechRate(0.5);
                          _updatePitch(1.0);
                        }),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildPresetButton('Fast', () {
                          _updateSpeechRate(0.8);
                          _updatePitch(1.2);
                        }),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButton(String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppTheme.primaryColor,
          ),
        ),
      ),
    );
  }
}
