from django.db import transaction
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from datetime import timedelta
from decimal import Decimal
import uuid

from .models import (
    PremiumSubscription, PurchaseTransaction, RewardTierUnlock, 
    PointBoostPurchase, StreakProtection, VirtualItem, UserVirtualItem,
    ReferralProgram, MonetizationSettings
)
from gamification.services import GamificationService


class MonetizationService:
    """Service for handling all monetization features"""
    
    @staticmethod
    def get_user_premium_status(user):
        """Get user's premium subscription status"""
        try:
            subscription = PremiumSubscription.objects.get(user=user)
            return {
                'is_premium': subscription.is_active,
                'plan': subscription.plan,
                'status': subscription.status,
                'end_date': subscription.end_date,
                'days_remaining': subscription.days_remaining,
                'point_multiplier': float(subscription.point_multiplier),
                'daily_bonus': subscription.daily_streak_bonus,
            }
        except PremiumSubscription.DoesNotExist:
            return {
                'is_premium': False,
                'plan': None,
                'status': None,
                'end_date': None,
                'days_remaining': 0,
                'point_multiplier': 1.0,
                'daily_bonus': 5,
            }
    
    @staticmethod
    def subscribe_to_premium(user, plan='monthly', payment_method='wallet'):
        """Subscribe user to premium - supports wallet and external payments"""
        try:
            with transaction.atomic():
                settings_obj = MonetizationSettings.get_settings()

                if not settings_obj.premium_enabled:
                    return False, "Premium subscriptions are currently disabled"

                # Check if user already has active subscription
                existing_sub = PremiumSubscription.objects.filter(user=user).first()

                if existing_sub and existing_sub.status == 'active':
                    return False, "You already have an active premium subscription"

                # Calculate subscription period and price
                if plan == 'monthly':
                    price = settings_obj.premium_monthly_price
                    days = 30
                elif plan == 'quarterly':
                    price = settings_obj.premium_monthly_price * 3 * Decimal('0.85')  # 15% discount
                    days = 90
                elif plan == 'yearly':
                    price = settings_obj.premium_monthly_price * 12 * Decimal('0.7')  # 30% discount
                    days = 365
                else:
                    return False, "Invalid subscription plan"

                # Try wallet payment first if requested
                if payment_method == 'wallet':
                    from wallet.services import WalletService

                    success, result = WalletService.spend_from_wallet(
                        user=user,
                        amount=price,
                        purpose='purchase_subscription',
                        description=f'Premium subscription - {plan}'
                    )

                    if success:
                        # Payment successful from wallet - activate subscription immediately
                        wallet_transaction = result

                        # Create purchase transaction record
                        purchase = PurchaseTransaction.objects.create(
                            user=user,
                            transaction_type='premium_subscription',
                            item_name=f'Premium Subscription - {plan.title()}',
                            item_description=f'Premium membership with {settings_obj.premium_point_multiplier}x points and exclusive features',
                            amount=price,
                            payment_method='wallet',
                            status='completed',
                            completed_at=timezone.now(),
                            wallet_transaction_id=str(wallet_transaction.id)
                        )

                        # Create or update subscription as active
                        if existing_sub:
                            subscription = existing_sub
                            subscription.plan = plan
                            subscription.status = 'active'
                            subscription.start_date = timezone.now()
                            subscription.end_date = timezone.now() + timedelta(days=days)
                            subscription.monthly_price = settings_obj.premium_monthly_price
                            subscription.point_multiplier = settings_obj.premium_point_multiplier
                            subscription.daily_streak_bonus = settings_obj.premium_daily_bonus
                            subscription.total_paid = price
                            subscription.last_payment_date = timezone.now()
                            subscription.save()
                        else:
                            subscription = PremiumSubscription.objects.create(
                                user=user,
                                plan=plan,
                                status='active',
                                start_date=timezone.now(),
                                end_date=timezone.now() + timedelta(days=days),
                                monthly_price=settings_obj.premium_monthly_price,
                                total_paid=price,
                                point_multiplier=settings_obj.premium_point_multiplier,
                                daily_streak_bonus=settings_obj.premium_daily_bonus,
                                last_payment_date=timezone.now(),
                            )

                        return True, {
                            'message': f"Premium subscription activated! Paid from wallet.",
                            'subscription_id': subscription.id,
                            'transaction_id': purchase.id,
                            'amount': float(price),
                            'plan': plan,
                            'payment_method': 'wallet',
                            'status': 'active',
                            'wallet_payment': True
                        }
                    else:
                        # Insufficient wallet balance - return error with suggestion
                        return False, f"Insufficient wallet balance. {result}. Add money to wallet or use external payment."

                # External payment method (PayPal, etc.)
                # Create purchase transaction as pending
                purchase = PurchaseTransaction.objects.create(
                    user=user,
                    transaction_type='premium_subscription',
                    item_name=f'Premium Subscription - {plan.title()}',
                    item_description=f'Premium membership with {settings_obj.premium_point_multiplier}x points and exclusive features',
                    amount=price,
                    payment_method=payment_method,
                    status='pending',  # Will be updated when payment is confirmed
                )

                # Create or update subscription as pending
                if existing_sub:
                    # Update existing subscription
                    subscription = existing_sub
                    subscription.plan = plan
                    subscription.status = 'pending'
                    subscription.start_date = timezone.now()
                    subscription.end_date = timezone.now() + timedelta(days=days)
                    subscription.monthly_price = settings_obj.premium_monthly_price
                    subscription.point_multiplier = settings_obj.premium_point_multiplier
                    subscription.daily_streak_bonus = settings_obj.premium_daily_bonus
                    subscription.total_paid = Decimal('0.00')  # Will be updated when payment confirmed
                    subscription.save()
                else:
                    # Create new subscription
                    subscription = PremiumSubscription.objects.create(
                        user=user,
                        plan=plan,
                        status='pending',
                        start_date=timezone.now(),
                        end_date=timezone.now() + timedelta(days=days),
                        monthly_price=settings_obj.premium_monthly_price,
                        total_paid=Decimal('0.00'),  # Will be updated when payment confirmed
                        point_multiplier=settings_obj.premium_point_multiplier,
                        daily_streak_bonus=settings_obj.premium_daily_bonus,
                    )

                return True, {
                    'message': f"Premium subscription created. Please complete payment to activate.",
                    'subscription_id': subscription.id,
                    'transaction_id': purchase.id,
                    'amount': float(price),
                    'plan': plan,
                    'payment_required': True
                }

        except Exception as e:
            return False, f"Error creating subscription: {str(e)}"

    @staticmethod
    def activate_premium_from_payment(payment_transaction):
        """Activate premium subscription after successful payment"""
        try:
            from django.db import transaction
            with transaction.atomic():
                # Find the pending subscription for this user
                subscription = PremiumSubscription.objects.filter(
                    user=payment_transaction.user,
                    status='pending'
                ).first()

                if not subscription:
                    return False, "No pending subscription found"

                # Calculate end date based on plan
                if subscription.plan == 'monthly':
                    days = 30
                elif subscription.plan == 'quarterly':
                    days = 90
                elif subscription.plan == 'yearly':
                    days = 365
                else:
                    days = 30

                # Activate subscription
                now = timezone.now()
                subscription.status = 'active'
                subscription.start_date = now
                subscription.end_date = now + timedelta(days=days)
                subscription.last_payment_date = now
                subscription.next_payment_date = now + timedelta(days=days)
                subscription.total_paid = payment_transaction.amount
                subscription.save()

                # Update transaction
                payment_transaction.status = 'completed'
                payment_transaction.completed_at = now
                payment_transaction.save()

                # Send confirmation email
                MonetizationService.send_premium_confirmation(payment_transaction.user, subscription)

                return True, "Premium subscription activated successfully"

        except Exception as e:
            return False, f"Error activating subscription: {str(e)}"

    @staticmethod
    def activate_tier_unlock_from_payment(payment_transaction):
        """Activate tier unlock after successful payment"""
        try:
            from django.db import transaction
            with transaction.atomic():
                user = payment_transaction.user

                # Extract tier from transaction description or metadata
                tier = 'engagement'  # Default tier
                if 'achievement' in payment_transaction.description.lower():
                    tier = 'achievement'
                elif 'elite' in payment_transaction.description.lower():
                    tier = 'elite'

                # Unlock the tier
                success, message = MonetizationService.unlock_reward_tier(
                    user=user,
                    tier=tier,
                    payment_method='paypal'
                )

                return success, message

        except Exception as e:
            return False, f"Error activating tier unlock: {str(e)}"

    @staticmethod
    def activate_point_boost_from_payment(payment_transaction):
        """Activate point boost after successful payment"""
        try:
            from django.db import transaction
            with transaction.atomic():
                user = payment_transaction.user

                # Extract package info from transaction description
                package_name = 'quick_start'  # Default
                if 'power' in payment_transaction.description.lower():
                    package_name = 'power_boost'
                elif 'mega' in payment_transaction.description.lower():
                    package_name = 'mega_boost'
                elif 'ultimate' in payment_transaction.description.lower():
                    package_name = 'ultimate_boost'

                # Purchase the point boost
                success, message = MonetizationService.purchase_point_boost(
                    user=user,
                    package=package_name,
                    payment_method='paypal'
                )

                return success, message

        except Exception as e:
            return False, f"Error activating point boost: {str(e)}"

    @staticmethod
    def renew_subscription(subscription):
        """Renew an existing subscription"""
        try:
            with transaction.atomic():
                if subscription.status != 'active':
                    return False, "Can only renew active subscriptions"

                # Calculate renewal period
                if subscription.plan == 'monthly':
                    days = 30
                    price = subscription.monthly_price
                elif subscription.plan == 'quarterly':
                    days = 90
                    price = subscription.monthly_price * 3 * Decimal('0.85')
                elif subscription.plan == 'yearly':
                    days = 365
                    price = subscription.monthly_price * 12 * Decimal('0.7')
                else:
                    return False, "Invalid subscription plan"

                # Create renewal transaction
                purchase = PurchaseTransaction.objects.create(
                    user=subscription.user,
                    transaction_type='premium_subscription',
                    item_name=f'Premium Subscription Renewal - {subscription.plan.title()}',
                    item_description=f'Renewal of premium membership',
                    amount=price,
                    payment_method='paypal',
                    status='pending',
                )

                # This would trigger PayPal payment processing
                # For now, we'll mark as completed (in real app, wait for payment confirmation)
                purchase.status = 'completed'
                purchase.completed_at = timezone.now()
                purchase.save()

                # Extend subscription
                subscription.end_date = subscription.end_date + timedelta(days=days)
                subscription.next_payment_date = subscription.end_date
                subscription.last_payment_date = timezone.now()
                subscription.total_paid += price
                subscription.save()

                return True, f"Subscription renewed until {subscription.end_date.strftime('%Y-%m-%d')}"

        except Exception as e:
            return False, f"Error renewing subscription: {str(e)}"

    @staticmethod
    def check_expired_subscriptions():
        """Check and handle expired subscriptions - run as background job"""
        try:
            now = timezone.now()
            expired_subscriptions = PremiumSubscription.objects.filter(
                status='active',
                end_date__lt=now
            )

            updated_count = 0
            for subscription in expired_subscriptions:
                subscription.status = 'expired'
                subscription.save()
                updated_count += 1

                # Send expiration notification
                MonetizationService.send_expiration_notification(subscription)

            return True, f"Updated {updated_count} expired subscriptions"

        except Exception as e:
            return False, f"Error checking expired subscriptions: {str(e)}"

    @staticmethod
    def unlock_reward_tier(user, tier, payment_method='paypal'):
        """Unlock a reward tier for user"""
        try:
            with transaction.atomic():
                settings_obj = MonetizationSettings.get_settings()
                
                if not settings_obj.tier_unlocks_enabled:
                    return False, "Tier unlocks are currently disabled"
                
                # Check if already unlocked
                if RewardTierUnlock.objects.filter(user=user, tier=tier).exists():
                    return False, "Tier already unlocked"
                
                # Get tier price
                tier_prices = {
                    'starter': Decimal('0.00'),  # Free
                    'engagement': settings_obj.engagement_tier_price,
                    'achievement': settings_obj.achievement_tier_price,
                    'elite': settings_obj.elite_tier_price,
                }
                
                if tier not in tier_prices:
                    return False, "Invalid tier"
                
                price = tier_prices[tier]
                
                if price > 0:
                    # Create purchase transaction
                    purchase = PurchaseTransaction.objects.create(
                        user=user,
                        transaction_type='tier_unlock',
                        item_name=f'{tier.title()} Tier Unlock',
                        item_description=f'Unlock access to {tier} tier rewards',
                        amount=price,
                        payment_method=payment_method,
                        status='completed',
                        completed_at=timezone.now()
                    )
                else:
                    purchase = None
                
                # Create tier unlock
                RewardTierUnlock.objects.create(
                    user=user,
                    tier=tier,
                    unlock_price=price,
                    purchase_transaction=purchase
                )
                
                return True, f"Successfully unlocked {tier} tier!"
                
        except Exception as e:
            return False, f"Error unlocking tier: {str(e)}"
    
    @staticmethod
    def purchase_point_boost(user, package, payment_method='wallet'):
        """Purchase point boost package - supports wallet and external payments"""
        try:
            with transaction.atomic():
                # Package definitions
                packages = {
                    'quick_start': {'base': 200, 'bonus': 50, 'price': Decimal('1.99')},
                    'power_boost': {'base': 600, 'bonus': 200, 'price': Decimal('4.99')},
                    'mega_boost': {'base': 1500, 'bonus': 500, 'price': Decimal('9.99')},
                    'ultimate_boost': {'base': 3500, 'bonus': 1500, 'price': Decimal('19.99')},
                }

                if package not in packages:
                    return False, "Invalid package"

                pkg_data = packages[package]
                total_points = pkg_data['base'] + pkg_data['bonus']
                price = pkg_data['price']

                # Try wallet payment first if requested
                if payment_method == 'wallet':
                    from wallet.services import WalletService

                    success, result = WalletService.spend_from_wallet(
                        user=user,
                        amount=price,
                        purpose='purchase_points',
                        description=f'Point boost package: {package}'
                    )

                    if success:
                        # Payment successful from wallet
                        wallet_transaction = result

                        # Create purchase transaction record
                        purchase = PurchaseTransaction.objects.create(
                            user=user,
                            transaction_type='point_boost',
                            item_name=f'{package.replace("_", " ").title()} Package',
                            item_description=f'{pkg_data["base"]} + {pkg_data["bonus"]} bonus points',
                            amount=price,
                            payment_method='wallet',
                            status='completed',
                            completed_at=timezone.now(),
                            wallet_transaction_id=str(wallet_transaction.id)
                        )

                        # Create point boost record
                        boost = PointBoostPurchase.objects.create(
                            user=user,
                            package=package,
                            base_points=pkg_data['base'],
                            bonus_points=pkg_data['bonus'],
                            total_points=total_points,
                            purchase_transaction=purchase
                        )

                        # Award points immediately
                        GamificationService.award_points(
                            user=user,
                            points=total_points,
                            transaction_type='purchase',
                            description=f'Point boost package: {package}',
                            check_badges=True
                        )

                        # Mark points as awarded
                        boost.points_awarded = True
                        boost.awarded_at = timezone.now()
                        boost.save()

                        return True, f"Successfully purchased {total_points} points from wallet!"
                    else:
                        # Insufficient wallet balance
                        return False, f"Insufficient wallet balance. {result}. Add money to wallet or use external payment."

                # External payment - create pending transaction
                purchase = PurchaseTransaction.objects.create(
                    user=user,
                    transaction_type='point_boost',
                    item_name=f'{package.replace("_", " ").title()} Package',
                    item_description=f'{pkg_data["base"]} + {pkg_data["bonus"]} bonus points',
                    amount=price,
                    payment_method=payment_method,
                    status='pending'  # Will be completed when payment is confirmed
                )

                return True, {
                    'message': f"Point boost package created. Complete payment to receive {total_points} points.",
                    'transaction_id': purchase.id,
                    'package': package,
                    'total_points': total_points,
                    'amount': float(price),
                    'payment_required': True
                }

        except Exception as e:
            return False, f"Error purchasing point boost: {str(e)}"
    
    @staticmethod
    def purchase_streak_protection(user, protection_type, payment_method='stripe'):
        """Purchase streak protection"""
        try:
            with transaction.atomic():
                # Protection definitions
                protections = {
                    'shield': {'days': 1, 'price': Decimal('0.99')},
                    'insurance': {'days': 3, 'price': Decimal('2.99')},
                    'guardian': {'days': 7, 'price': Decimal('4.99')},
                }
                
                if protection_type not in protections:
                    return False, "Invalid protection type"
                
                prot_data = protections[protection_type]
                
                # Create purchase transaction
                purchase = PurchaseTransaction.objects.create(
                    user=user,
                    transaction_type='streak_protection',
                    item_name=f'Streak {protection_type.title()}',
                    item_description=f'Protect your streak for {prot_data["days"]} days',
                    amount=prot_data['price'],
                    payment_method=payment_method,
                    status='completed',
                    completed_at=timezone.now()
                )
                
                # Create streak protection
                StreakProtection.objects.create(
                    user=user,
                    protection_type=protection_type,
                    protection_days=prot_data['days'],
                    days_remaining=prot_data['days'],
                    purchase_transaction=purchase,
                    expires_at=timezone.now() + timedelta(days=30)  # Protection expires in 30 days if unused
                )
                
                return True, f"Successfully purchased {prot_data['days']}-day streak protection!"
                
        except Exception as e:
            return False, f"Error purchasing streak protection: {str(e)}"
    
    @staticmethod
    def get_user_unlocked_tiers(user):
        """Get list of tiers user has unlocked"""
        unlocked = RewardTierUnlock.objects.filter(user=user).values_list('tier', flat=True)
        
        # Starter tier is always unlocked
        tiers = set(['starter'])
        tiers.update(unlocked)
        
        return list(tiers)
    
    @staticmethod
    def apply_premium_multiplier(user, base_points):
        """Apply premium point multiplier if user has premium"""
        premium_status = MonetizationService.get_user_premium_status(user)
        
        if premium_status['is_premium']:
            multiplier = premium_status['point_multiplier']
            bonus_points = int(base_points * (multiplier - 1))
            return base_points + bonus_points, bonus_points
        
        return base_points, 0
    
    @staticmethod
    def process_referral_milestone(referrer, referee, milestone):
        """Process referral milestone rewards"""
        try:
            referral = ReferralProgram.objects.get(referrer=referrer, referee=referee)
            settings_obj = MonetizationSettings.get_settings()
            
            if milestone == 'level_5' and not referral.level_5_reward_given:
                # Award referral rewards
                GamificationService.award_points(
                    user=referrer,
                    points=settings_obj.referral_join_points,
                    transaction_type='referral',
                    description=f'Referral bonus: {referee.username} reached level 5'
                )
                
                # Mark as given
                referral.referee_reached_level_5 = True
                referral.level_5_reward_given = True
                referral.save()
                
                return True, f"Referral bonus awarded to {referrer.username}"
            
            elif milestone == 'premium' and not referral.premium_reward_given:
                # Award premium referral bonus
                GamificationService.award_points(
                    user=referrer,
                    points=int(settings_obj.referral_premium_reward * 100),  # Convert to points
                    transaction_type='referral',
                    description=f'Premium referral bonus: {referee.username} went premium'
                )
                
                # Mark as given
                referral.referee_went_premium = True
                referral.premium_reward_given = True
                referral.save()
                
                return True, f"Premium referral bonus awarded to {referrer.username}"
            
            return False, "Milestone already processed or invalid"
            
        except ReferralProgram.DoesNotExist:
            return False, "Referral relationship not found"
        except Exception as e:
            return False, f"Error processing referral: {str(e)}"
    
    @staticmethod
    def send_premium_confirmation(user, subscription):
        """Send premium subscription confirmation email"""
        send_mail(
            subject="Welcome to Trendy Premium! 🌟",
            message=f"""
Hi {user.username},

Welcome to Trendy Premium! Your subscription is now active.

Premium Benefits:
✅ 2x Point Multiplier - Earn points twice as fast!
✅ +15 Daily Streak Bonus (vs +5 for free users)
✅ Unlimited Voice Comments
✅ Access to Premium-Only Rewards
✅ Priority Support
✅ Exclusive Premium Badge

Your subscription details:
Plan: {subscription.plan.title()}
Status: {subscription.status.title()}
Valid until: {subscription.end_date.strftime('%Y-%m-%d')}

Start earning more points and unlock exclusive rewards!

Thanks for supporting Trendy!
            """,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=True,
        )

    @staticmethod
    def send_expiration_notification(subscription):
        """Send subscription expiration notification"""
        send_mail(
            subject="Your Trendy Premium has expired 😔",
            message=f"""
Hi {subscription.user.username},

Your Trendy Premium subscription has expired.

Subscription Details:
Plan: {subscription.plan.title()}
Expired on: {subscription.end_date.strftime('%Y-%m-%d')}

Don't worry! You can reactivate your premium benefits anytime:
✅ 2x Point Multiplier
✅ +15 Daily Streak Bonus
✅ Unlimited Voice Comments
✅ Access to Premium-Only Rewards

Reactivate your premium subscription to continue earning more points!

Thanks for being part of Trendy!
            """,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[subscription.user.email],
            fail_silently=True,
        )

    @staticmethod
    def get_monetization_stats():
        """Get monetization statistics for admin dashboard"""
        from django.db.models import Sum, Count
        
        # Premium subscriptions
        active_premium = PremiumSubscription.objects.filter(status='active').count()
        premium_revenue = PurchaseTransaction.objects.filter(
            transaction_type='premium_subscription',
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        # Other purchases
        boost_revenue = PurchaseTransaction.objects.filter(
            transaction_type='point_boost',
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        tier_revenue = PurchaseTransaction.objects.filter(
            transaction_type='tier_unlock',
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        total_revenue = premium_revenue + boost_revenue + tier_revenue
        
        return {
            'active_premium_users': active_premium,
            'premium_revenue': premium_revenue,
            'boost_revenue': boost_revenue,
            'tier_revenue': tier_revenue,
            'total_revenue': total_revenue,
            'total_transactions': PurchaseTransaction.objects.filter(status='completed').count(),
        }
