"""
API views for system maintenance and feature status
"""
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from .models import SystemMaintenance, FeatureToggle
import json


@api_view(['GET'])
@permission_classes([AllowAny])
def system_status(request):
    """Get current system maintenance and feature status"""
    try:
        # Check if maintenance is active
        maintenance_active = SystemMaintenance.is_maintenance_active()
        active_maintenance = SystemMaintenance.get_active_maintenance()
        
        # Get feature toggles
        feature_toggles = {}
        feature_status = {}
        
        for feature in FeatureToggle.objects.all():
            feature_toggles[feature.name] = {
                'name': feature.name,
                'display_name': feature.display_name,
                'description': feature.description,
                'feature_type': feature.feature_type,
                'is_enabled': feature.is_enabled,
                'is_global': feature.is_global,
                'enabled_for_admins': feature.enabled_for_admins,
                'enabled_for_staff': feature.enabled_for_staff,
                'enabled_for_users': feature.enabled_for_users,
                'disable_start': feature.disable_start.isoformat() if feature.disable_start else None,
                'disable_end': feature.disable_end.isoformat() if feature.disable_end else None,
                'disabled_message': feature.disabled_message,
            }
            
            # Check if feature is enabled for current user
            if request.user.is_authenticated:
                feature_status[feature.name] = feature.is_enabled_for_user(request.user)
            else:
                feature_status[feature.name] = feature.is_currently_enabled
        
        # Build response
        response_data = {
            'maintenance_active': maintenance_active,
            'active_maintenance': None,
            'feature_toggles': feature_toggles,
            'feature_status': feature_status,
            'last_checked': None,
        }
        
        if active_maintenance:
            response_data['active_maintenance'] = {
                'is_active': active_maintenance.is_active,
                'title': active_maintenance.title,
                'message': active_maintenance.public_message,
                'maintenance_type': active_maintenance.maintenance_type,
                'status': active_maintenance.status,
                'scheduled_start': active_maintenance.scheduled_start.isoformat() if active_maintenance.scheduled_start else None,
                'scheduled_end': active_maintenance.scheduled_end.isoformat() if active_maintenance.scheduled_end else None,
                'actual_start': active_maintenance.actual_start.isoformat() if active_maintenance.actual_start else None,
                'actual_end': active_maintenance.actual_end.isoformat() if active_maintenance.actual_end else None,
                'allow_admin_access': active_maintenance.allow_admin_access,
                'allow_staff_access': active_maintenance.allow_staff_access,
            }
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {'error': 'Failed to get system status', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def feature_status(request, feature_name):
    """Check if a specific feature is enabled"""
    try:
        enabled = FeatureToggle.is_feature_enabled(feature_name, request.user if request.user.is_authenticated else None)
        
        try:
            feature = FeatureToggle.objects.get(name=feature_name)
            return Response({
                'feature': feature_name,
                'enabled': enabled,
                'display_name': feature.display_name,
                'disabled_message': feature.disabled_message if not enabled else None,
            }, status=status.HTTP_200_OK)
        except FeatureToggle.DoesNotExist:
            # Feature doesn't exist, assume enabled
            return Response({
                'feature': feature_name,
                'enabled': True,
                'display_name': feature_name,
                'disabled_message': None,
            }, status=status.HTTP_200_OK)
            
    except Exception as e:
        return Response(
            {'error': 'Failed to check feature status', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """Simple health check endpoint"""
    try:
        # Check if maintenance is active
        if SystemMaintenance.is_maintenance_active():
            active_maintenance = SystemMaintenance.get_active_maintenance()
            return Response({
                'status': 'maintenance',
                'message': active_maintenance.public_message if active_maintenance else 'System is under maintenance',
                'maintenance_type': active_maintenance.maintenance_type if active_maintenance else 'maintenance',
                'scheduled_end': active_maintenance.scheduled_end.isoformat() if active_maintenance and active_maintenance.scheduled_end else None,
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        return Response({
            'status': 'healthy',
            'message': 'System is operational',
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'status': 'error',
            'message': 'Health check failed',
            'details': str(e),
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def features_list(request):
    """Get all feature toggles"""
    try:
        features = {}
        
        for feature in FeatureToggle.objects.all():
            features[feature.name] = {
                'name': feature.name,
                'display_name': feature.display_name,
                'description': feature.description,
                'feature_type': feature.feature_type,
                'enabled': feature.is_enabled_for_user(request.user) if request.user.is_authenticated else feature.is_currently_enabled,
                'disabled_message': feature.disabled_message,
            }
        
        return Response(features, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {'error': 'Failed to get features list', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@method_decorator(csrf_exempt, name='dispatch')
class MaintenanceStatusView(View):
    """Simple maintenance status check for non-authenticated requests"""
    
    def get(self, request):
        try:
            maintenance_active = SystemMaintenance.is_maintenance_active()
            
            if maintenance_active:
                active_maintenance = SystemMaintenance.get_active_maintenance()
                return JsonResponse({
                    'maintenance_active': True,
                    'title': active_maintenance.title if active_maintenance else 'System Maintenance',
                    'message': active_maintenance.public_message if active_maintenance else 'System is under maintenance',
                    'maintenance_type': active_maintenance.maintenance_type if active_maintenance else 'maintenance',
                    'scheduled_end': active_maintenance.scheduled_end.isoformat() if active_maintenance and active_maintenance.scheduled_end else None,
                }, status=503)
            
            return JsonResponse({
                'maintenance_active': False,
                'message': 'System is operational',
            })
            
        except Exception as e:
            return JsonResponse({
                'error': 'Failed to check maintenance status',
                'details': str(e),
            }, status=500)
