import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';
import '../screens/enhanced_auth_screen.dart';
import '../models/user.dart';

/// Authentication guard widget that protects routes requiring login
class AuthGuard extends ConsumerWidget {
  final Widget child;
  final String? redirectMessage;
  final bool showLoginPrompt;

  const AuthGuard({
    Key? key,
    required this.child,
    this.redirectMessage,
    this.showLoginPrompt = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(enhancedAuthProvider);

    // Show loading while checking authentication
    if (authState.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If user is authenticated, show the protected content
    if (authState.isAuthenticated) {
      return child;
    }

    // If not authenticated, show login prompt or redirect to login
    if (showLoginPrompt) {
      return _buildLoginPrompt(context);
    } else {
      // Directly show login screen
      return const EnhancedAuthScreen();
    }
  }

  Widget _buildLoginPrompt(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Login Required'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Lock icon
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.lock_outline,
                  size: 64,
                  color: Colors.blue,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Title
              const Text(
                'Login Required',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Message
              Text(
                redirectMessage ?? 
                'You need to be logged in to access this feature.\n\nPlease log in to continue.',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black54,
                  height: 1.5,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Login button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (context) => const EnhancedAuthScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Login',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Register option
              TextButton(
                onPressed: () {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const EnhancedAuthScreen(initialMode: AuthMode.register),
                    ),
                  );
                },
                child: const Text(
                  'Don\'t have an account? Sign up',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Quick authentication check widget for conditional rendering
class AuthCheck extends ConsumerWidget {
  final Widget authenticatedChild;
  final Widget unauthenticatedChild;

  const AuthCheck({
    Key? key,
    required this.authenticatedChild,
    required this.unauthenticatedChild,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(enhancedAuthProvider);

    if (authState.isAuthenticated) {
      return authenticatedChild;
    } else {
      return unauthenticatedChild;
    }
  }
}

/// Authentication-aware navigation helper
class AuthNavigation {
  static void pushWithAuth(
    BuildContext context,
    Widget destination, {
    String? loginMessage,
    WidgetRef? ref,
  }) {
    if (ref != null) {
      final authState = ref.read(enhancedAuthProvider);
      if (authState.isAuthenticated) {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => destination),
        );
      } else {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AuthGuard(
              redirectMessage: loginMessage,
              child: destination,
            ),
          ),
        );
      }
    } else {
      // Fallback: wrap in AuthGuard
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AuthGuard(
            redirectMessage: loginMessage,
            child: destination,
          ),
        ),
      );
    }
  }

  static void replaceWithAuth(
    BuildContext context,
    Widget destination, {
    String? loginMessage,
    WidgetRef? ref,
  }) {
    if (ref != null) {
      final authState = ref.read(enhancedAuthProvider);
      if (authState.isAuthenticated) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => destination),
        );
      } else {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => AuthGuard(
              redirectMessage: loginMessage,
              child: destination,
            ),
          ),
        );
      }
    } else {
      // Fallback: wrap in AuthGuard
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => AuthGuard(
            redirectMessage: loginMessage,
            child: destination,
          ),
        ),
      );
    }
  }
}
