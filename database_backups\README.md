# 🗄️ Database Backups

This folder contains SQLite database backups and data dumps for the Trendy app.

## **📁 File Types**

### **SQLite Backups (.sqlite3)**
- **Purpose**: Complete database backups
- **Size**: ~1-5 MB (depending on data)
- **Git**: Excluded (too large for Git)
- **Sharing**: Copy manually or via cloud storage

### **JSON Data Dumps (.json)**
- **Purpose**: Data in JSON format for version control
- **Size**: Smaller, text-based
- **Git**: Included (can be committed)
- **Sharing**: Via Git commits

## **🔄 Usage**

### **Create Backup**
```bash
# From project root
./backup_database.sh
```

### **Restore Backup**
```bash
# Restore latest
./restore_database.sh latest

# Restore specific backup
./restore_database.sh db_backup_20241223_143022.sqlite3
```

## **📋 File Naming Convention**

### **SQLite Backups**
- `db_backup_YYYYMMDD_HHMMSS.sqlite3` - Timestamped backups
- `latest_backup.sqlite3` - Always points to most recent backup
- `pre_restore_backup_YYYYMMDD_HHMMSS.sqlite3` - Auto-created before restore

### **JSON Dumps**
- `data_dump_YYYYMMDD_HHMMSS.json` - Django data in JSON format

## **🚀 Team Workflow**

### **Sharing Database Changes**
1. **Create backup**: `./backup_database.sh`
2. **Share SQLite file** via preferred method:
   - Cloud storage (Dropbox, Google Drive)
   - USB drive
   - Network share
   - Email (if small enough)
3. **Team member restores**: `./restore_database.sh latest`

### **Alternative: JSON Dumps**
1. **Commit JSON dump** to Git
2. **Team member pulls** and loads data via Django fixtures

## **💡 Tips**

### **Regular Backups**
- Create backups before major changes
- Name important backups descriptively
- Keep multiple versions for safety

### **File Management**
- Clean up old backups periodically
- Keep important milestone backups
- Use cloud storage for automatic sync

## **⚠️ Important Notes**

- **SQLite files are binary** - can't be merged in Git
- **Stop Django server** before restoring database
- **Always backup current database** before restoring
- **Don't run Django on multiple machines** simultaneously with same database

## **🔧 Troubleshooting**

### **Backup Script Issues**
```bash
# Make sure scripts are executable
chmod +x backup_database.sh restore_database.sh

# Run from project root directory
cd trendy/
./backup_database.sh
```

### **Restore Issues**
```bash
# Check if backup file exists
ls -la database_backups/

# Verify database after restore
cd trendy_web_and_api/trendy
python manage.py check --database default
```

### **Permission Issues**
```bash
# Fix file permissions
chmod 644 database_backups/*.sqlite3
chmod 644 database_backups/*.json
```

This folder helps you safely share and backup your SQLite database across development machines! 🗄️✨
