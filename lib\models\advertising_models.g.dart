// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'advertising_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AdPlacementImpl _$$AdPlacementImplFromJson(Map<String, dynamic> json) =>
    _$AdPlacementImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      placementType: json['placement_type'] as String,
      location: json['location'] as String,
      isActive: json['is_active'] as bool,
      frequency: (json['frequency'] as num).toInt(),
      maxPerSession: (json['max_per_session'] as num).toInt(),
      pointsReward: (json['points_reward'] as num).toInt(),
      minUserLevel: (json['min_user_level'] as num).toInt(),
      premiumUsersOnly: json['premium_users_only'] as bool,
      freeUsersOnly: json['free_users_only'] as bool,
    );

Map<String, dynamic> _$$AdPlacementImplToJson(_$AdPlacementImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'placement_type': instance.placementType,
      'location': instance.location,
      'is_active': instance.isActive,
      'frequency': instance.frequency,
      'max_per_session': instance.maxPerSession,
      'points_reward': instance.pointsReward,
      'min_user_level': instance.minUserLevel,
      'premium_users_only': instance.premiumUsersOnly,
      'free_users_only': instance.freeUsersOnly,
    };

_$AdNetworkImpl _$$AdNetworkImplFromJson(Map<String, dynamic> json) =>
    _$AdNetworkImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['display_name'] as String,
      isActive: json['is_active'] as bool,
      priority: (json['priority'] as num).toInt(),
      revenueSharePercentage:
          (json['revenue_share_percentage'] as num).toDouble(),
    );

Map<String, dynamic> _$$AdNetworkImplToJson(_$AdNetworkImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'display_name': instance.displayName,
      'is_active': instance.isActive,
      'priority': instance.priority,
      'revenue_share_percentage': instance.revenueSharePercentage,
    };

_$RewardedAdSessionImpl _$$RewardedAdSessionImplFromJson(
        Map<String, dynamic> json) =>
    _$RewardedAdSessionImpl(
      id: json['id'] as String,
      sessionId: json['session_id'] as String,
      placement:
          AdPlacement.fromJson(json['placement'] as Map<String, dynamic>),
      adNetwork: AdNetwork.fromJson(json['ad_network'] as Map<String, dynamic>),
      status: json['status'] as String,
      pointsOffered: (json['points_offered'] as num).toInt(),
      pointsAwarded: (json['points_awarded'] as num).toInt(),
      rewardClaimed: json['reward_claimed'] as bool,
      startedAt: DateTime.parse(json['started_at'] as String),
      completedAt: json['completed_at'] == null
          ? null
          : DateTime.parse(json['completed_at'] as String),
    );

Map<String, dynamic> _$$RewardedAdSessionImplToJson(
        _$RewardedAdSessionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'session_id': instance.sessionId,
      'placement': instance.placement,
      'ad_network': instance.adNetwork,
      'status': instance.status,
      'points_offered': instance.pointsOffered,
      'points_awarded': instance.pointsAwarded,
      'reward_claimed': instance.rewardClaimed,
      'started_at': instance.startedAt.toIso8601String(),
      'completed_at': instance.completedAt?.toIso8601String(),
    };

_$SponsoredContentImpl _$$SponsoredContentImplFromJson(
        Map<String, dynamic> json) =>
    _$SponsoredContentImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      sponsorName: json['sponsor_name'] as String,
      sponsorType: json['sponsor_type'] as String,
      sponsorLogo: json['sponsor_logo'] as String?,
      imageUrl: json['image_url'] as String?,
      videoUrl: json['video_url'] as String?,
      callToAction: json['call_to_action'] as String,
      targetUrl: json['target_url'] as String,
      status: json['status'] as String,
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      totalImpressions: (json['total_impressions'] as num).toInt(),
      totalClicks: (json['total_clicks'] as num).toInt(),
    );

Map<String, dynamic> _$$SponsoredContentImplToJson(
        _$SponsoredContentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'content': instance.content,
      'sponsor_name': instance.sponsorName,
      'sponsor_type': instance.sponsorType,
      'sponsor_logo': instance.sponsorLogo,
      'image_url': instance.imageUrl,
      'video_url': instance.videoUrl,
      'call_to_action': instance.callToAction,
      'target_url': instance.targetUrl,
      'status': instance.status,
      'start_date': instance.startDate.toIso8601String(),
      'end_date': instance.endDate.toIso8601String(),
      'total_impressions': instance.totalImpressions,
      'total_clicks': instance.totalClicks,
    };

_$AdSettingsImpl _$$AdSettingsImplFromJson(Map<String, dynamic> json) =>
    _$AdSettingsImpl(
      adsEnabled: json['ads_enabled'] as bool,
      rewardedAdsEnabled: json['rewarded_ads_enabled'] as bool,
      sponsoredContentEnabled: json['sponsored_content_enabled'] as bool,
      maxAdsPerSession: (json['max_ads_per_session'] as num).toInt(),
      minTimeBetweenAds: (json['min_time_between_ads'] as num).toInt(),
      skipAdsForPremium: json['skip_ads_for_premium'] as bool,
      basePointsPerAd: (json['base_points_per_ad'] as num).toInt(),
      bonusPointsMultiplier:
          (json['bonus_points_multiplier'] as num).toDouble(),
      maxDailyAdPoints: (json['max_daily_ad_points'] as num).toInt(),
    );

Map<String, dynamic> _$$AdSettingsImplToJson(_$AdSettingsImpl instance) =>
    <String, dynamic>{
      'ads_enabled': instance.adsEnabled,
      'rewarded_ads_enabled': instance.rewardedAdsEnabled,
      'sponsored_content_enabled': instance.sponsoredContentEnabled,
      'max_ads_per_session': instance.maxAdsPerSession,
      'min_time_between_ads': instance.minTimeBetweenAds,
      'skip_ads_for_premium': instance.skipAdsForPremium,
      'base_points_per_ad': instance.basePointsPerAd,
      'bonus_points_multiplier': instance.bonusPointsMultiplier,
      'max_daily_ad_points': instance.maxDailyAdPoints,
    };

_$AdImpressionImpl _$$AdImpressionImplFromJson(Map<String, dynamic> json) =>
    _$AdImpressionImpl(
      id: json['id'] as String,
      placement:
          AdPlacement.fromJson(json['placement'] as Map<String, dynamic>),
      adNetwork: AdNetwork.fromJson(json['ad_network'] as Map<String, dynamic>),
      userId: json['user_id'] as String?,
      sessionId: json['session_id'] as String?,
      wasClicked: json['was_clicked'] as bool,
      pointsAwarded: (json['points_awarded'] as num).toInt(),
      revenueGenerated: (json['revenue_generated'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$$AdImpressionImplToJson(_$AdImpressionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'placement': instance.placement,
      'ad_network': instance.adNetwork,
      'user_id': instance.userId,
      'session_id': instance.sessionId,
      'was_clicked': instance.wasClicked,
      'points_awarded': instance.pointsAwarded,
      'revenue_generated': instance.revenueGenerated,
      'created_at': instance.createdAt.toIso8601String(),
    };
