import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/advertising_models.dart';
import '../services/advertising_service.dart';
import '../services/auth_service.dart';
import '../providers/provider.dart';

// Advertising State
class AdvertisingState {
  final List<AdPlacement> placements;
  final List<SponsoredContent> sponsoredContent;
  final List<RewardedAdSession> adHistory;
  final AdSettings? settings;
  final Map<String, dynamic>? analytics;
  final bool isLoading;
  final String? error;
  final bool canWatchAds;
  final int dailyAdPoints;
  final int totalAdPoints;
  final RewardedAdSession? currentAdSession;

  const AdvertisingState({
    this.placements = const [],
    this.sponsoredContent = const [],
    this.adHistory = const [],
    this.settings,
    this.analytics,
    this.isLoading = false,
    this.error,
    this.canWatchAds = false,
    this.dailyAdPoints = 0,
    this.totalAdPoints = 0,
    this.currentAdSession,
  });

  AdvertisingState copyWith({
    List<AdPlacement>? placements,
    List<SponsoredContent>? sponsoredContent,
    List<RewardedAdSession>? adHistory,
    AdSettings? settings,
    Map<String, dynamic>? analytics,
    bool? isLoading,
    String? error,
    bool? canWatchAds,
    int? dailyAdPoints,
    int? totalAdPoints,
    RewardedAdSession? currentAdSession,
  }) {
    return AdvertisingState(
      placements: placements ?? this.placements,
      sponsoredContent: sponsoredContent ?? this.sponsoredContent,
      adHistory: adHistory ?? this.adHistory,
      settings: settings ?? this.settings,
      analytics: analytics ?? this.analytics,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      canWatchAds: canWatchAds ?? this.canWatchAds,
      dailyAdPoints: dailyAdPoints ?? this.dailyAdPoints,
      totalAdPoints: totalAdPoints ?? this.totalAdPoints,
      currentAdSession: currentAdSession ?? this.currentAdSession,
    );
  }
}

// Advertising Notifier
class AdvertisingNotifier extends StateNotifier<AdvertisingState> {
  final AdvertisingService _advertisingService;

  AdvertisingNotifier(this._advertisingService)
      : super(const AdvertisingState());

  // Load initial advertising data
  Future<void> loadAdvertisingData() async {
    print('AdvertisingProvider: Starting loadAdvertisingData...');
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Check if user is authenticated
      final token = await AuthService.getToken();
      print(
          'AdvertisingProvider: Token check - authenticated: ${token != null}');

      if (token == null) {
        // User not authenticated, load basic advertising data (sponsored content only)
        try {
          print(
              'AdvertisingProvider: Loading sponsored content for unauthenticated user...');
          final sponsoredContent =
              await _advertisingService.getSponsoredContent();
          print(
              'AdvertisingProvider: Loaded ${sponsoredContent.length} sponsored content items');
          state = state.copyWith(
            settings: null,
            placements: [],
            sponsoredContent: sponsoredContent,
            analytics: {},
            canWatchAds: false,
            dailyAdPoints: 0,
            totalAdPoints: 0,
            isLoading: false,
          );
        } catch (e) {
          print('Error loading sponsored content for unauthenticated user: $e');
          state = state.copyWith(
            settings: null,
            placements: [],
            sponsoredContent: [],
            analytics: {},
            canWatchAds: false,
            dailyAdPoints: 0,
            totalAdPoints: 0,
            isLoading: false,
          );
        }
        return;
      }

      // Load all advertising data in parallel
      print(
          'AdvertisingProvider: Loading advertising data for authenticated user...');
      late final List<dynamic> futures;
      try {
        futures = await Future.wait([
          _advertisingService.getAdSettings(),
          _advertisingService.getAdPlacements(),
          _advertisingService.getSponsoredContent(),
          _advertisingService.checkAdAvailability(),
          _advertisingService.getUserAdAnalytics(),
        ]);
        print(
            'AdvertisingProvider: All advertising API calls completed successfully');
      } catch (e) {
        print('AdvertisingProvider: Error in Future.wait: $e');
        rethrow;
      }

      final settings = futures[0] as AdSettings;
      final placements = futures[1] as List<AdPlacement>;
      final sponsoredContent = futures[2] as List<SponsoredContent>;
      final availability = futures[3] as Map<String, dynamic>;
      final analytics = futures[4] as Map<String, dynamic>;

      print(
          'AdvertisingProvider: Loaded ${sponsoredContent.length} sponsored content items for authenticated user');

      state = state.copyWith(
        settings: settings,
        placements: placements,
        sponsoredContent: sponsoredContent,
        analytics: analytics,
        canWatchAds: availability['can_watch_ads'] ?? false,
        dailyAdPoints: analytics['daily_points_earned'] ?? 0,
        totalAdPoints: analytics['total_points_earned'] ?? 0,
        isLoading: false,
      );
    } catch (e) {
      // If there's an error, provide default advertising state instead of error state
      state = state.copyWith(
        settings: null,
        placements: [],
        sponsoredContent: [],
        analytics: {},
        canWatchAds: false,
        dailyAdPoints: 0,
        totalAdPoints: 0,
        isLoading: false,
        error: null, // Don't show error for advertising data
      );
    }
  }

  // Load ad history
  Future<void> loadAdHistory() async {
    try {
      final history = await _advertisingService.getUserAdHistory();
      state = state.copyWith(adHistory: history);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  // Start rewarded ad session
  Future<RewardedAdSession?> startRewardedAd(String placementId) async {
    try {
      final session =
          await _advertisingService.startRewardedAdSession(placementId);
      state = state.copyWith(currentAdSession: session);
      return session;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  // Complete rewarded ad session
  Future<RewardedAdSession?> completeRewardedAd(String sessionId) async {
    try {
      final session =
          await _advertisingService.completeRewardedAdSession(sessionId);

      // Update state with completed session
      state = state.copyWith(
        currentAdSession: session,
        dailyAdPoints: state.dailyAdPoints + session.pointsAwarded,
        totalAdPoints: state.totalAdPoints + session.pointsAwarded,
      );

      // Add to history
      final updatedHistory = [session, ...state.adHistory];
      state = state.copyWith(adHistory: updatedHistory);

      return session;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  // Record ad impression
  Future<void> recordImpression({
    required String placementId,
    required String adNetworkId,
    String? sessionId,
    bool wasClicked = false,
  }) async {
    try {
      await _advertisingService.recordAdImpression(
        placementId: placementId,
        adNetworkId: adNetworkId,
        sessionId: sessionId,
        wasClicked: wasClicked,
      );
    } catch (e) {
      // Don't update state for impression errors
      print('Failed to record impression: $e');
    }
  }

  // Click sponsored content
  Future<void> clickSponsoredContent(String contentId) async {
    try {
      final result = await _advertisingService.clickSponsoredContent(contentId);
      if (result != null && result['success'] == true) {
        print(
            '✅ Sponsored content click recorded. Points awarded: ${result['points_awarded'] ?? 0}');
      }
    } catch (e) {
      print('Failed to record sponsored content click: $e');
    }
  }

  // Record sponsored content impression
  Future<void> recordSponsoredContentImpression(String contentId) async {
    try {
      final result =
          await _advertisingService.recordSponsoredContentImpression(contentId);
      if (result != null && result['success'] == true) {
        print(
            '✅ Sponsored content impression recorded. Points awarded: ${result['points_awarded'] ?? 0}');
      }
    } catch (e) {
      print('Failed to record sponsored content impression: $e');
    }
  }

  // Refresh ad availability
  Future<void> refreshAvailability() async {
    try {
      final availability = await _advertisingService.checkAdAvailability();
      state = state.copyWith(
        canWatchAds: availability['can_watch_ads'] ?? false,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  // Refresh analytics
  Future<void> refreshAnalytics() async {
    try {
      final analytics = await _advertisingService.getUserAdAnalytics();
      state = state.copyWith(
        analytics: analytics,
        dailyAdPoints: analytics['daily_points_earned'] ?? 0,
        totalAdPoints: analytics['total_points_earned'] ?? 0,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  // Clear current ad session
  void clearCurrentSession() {
    state = state.copyWith(currentAdSession: null);
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Check if user should see ads
  bool shouldShowAds() {
    final settings = state.settings;
    if (settings == null || !settings.adsEnabled) {
      return false;
    }
    return state.canWatchAds;
  }

  // Get available rewarded ad placements
  List<AdPlacement> getRewardedAdPlacements() {
    return state.placements
        .where((placement) =>
            placement.placementType == 'rewarded' && placement.isActive)
        .toList();
  }

  // Get banner ad placements
  List<AdPlacement> getBannerAdPlacements() {
    return state.placements
        .where((placement) =>
            placement.placementType == 'banner' && placement.isActive)
        .toList();
  }

  // Get sponsored content by type
  List<SponsoredContent> getSponsoredContentByType(String contentType) {
    final filteredContent = state.sponsoredContent
        .where((content) => content.status == 'active')
        .toList();
    print(
        'AdvertisingProvider: getSponsoredContentByType($contentType) - total: ${state.sponsoredContent.length}, filtered: ${filteredContent.length}');
    return filteredContent;
  }
}

// Providers
final advertisingServiceProvider = Provider<AdvertisingService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return AdvertisingService(apiService.dio);
});

final advertisingProvider =
    StateNotifierProvider<AdvertisingNotifier, AdvertisingState>((ref) {
  final advertisingService = ref.read(advertisingServiceProvider);
  return AdvertisingNotifier(advertisingService);
});

// Helper providers
final canWatchAdsProvider = Provider<bool>((ref) {
  final advertisingState = ref.watch(advertisingProvider);
  return advertisingState.canWatchAds;
});

final dailyAdPointsProvider = Provider<int>((ref) {
  final advertisingState = ref.watch(advertisingProvider);
  return advertisingState.dailyAdPoints;
});

final rewardedAdPlacementsProvider = Provider<List<AdPlacement>>((ref) {
  final advertisingNotifier = ref.read(advertisingProvider.notifier);
  return advertisingNotifier.getRewardedAdPlacements();
});
