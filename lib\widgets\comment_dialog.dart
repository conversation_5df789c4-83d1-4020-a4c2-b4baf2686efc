import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/provider.dart';

class CommentDialog extends ConsumerWidget {
  final String slug;
  final int? parentId;

  const CommentDialog({super.key, required this.slug, this.parentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = TextEditingController();

    return AlertDialog(
      title: Text(parentId == null ? 'Add Comment' : 'Add Reply'),
      content: TextField(
        controller: controller,
        decoration: const InputDecoration(hintText: 'Write your comment...'),
        maxLines: 3,
      ),
      actions: [
        TextButton(
          onPressed: Navigator.of(context).pop,
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            ref.read(commentsProvider(slug).notifier).createComment(controller.text);
            Navigator.pop(context);
          },
          child: const Text('Post'),
        ),
      ],
    );
  }
}