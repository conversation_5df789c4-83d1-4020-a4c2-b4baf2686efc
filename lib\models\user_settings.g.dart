// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserSettingsImpl _$$UserSettingsImplFromJson(Map<String, dynamic> json) =>
    _$UserSettingsImpl(
      emailNotifications: json['email_notifications'] as bool? ?? true,
      pushNotifications: json['push_notifications'] as bool? ?? true,
      commentNotifications: json['comment_notifications'] as bool? ?? true,
      likeNotifications: json['like_notifications'] as bool? ?? true,
      followNotifications: json['follow_notifications'] as bool? ?? true,
      profileVisibility: json['profile_visibility'] as String? ?? 'public',
      showEmail: json['show_email'] as bool? ?? false,
      showPhone: json['show_phone'] as bool? ?? false,
      contentLanguage: json['content_language'] as String? ?? 'en',
      postsPerPage: (json['posts_per_page'] as num?)?.toInt() ?? 10,
      autoPlayVideos: json['auto_play_videos'] as bool? ?? true,
      theme: json['theme'] as String? ?? 'auto',
    );

Map<String, dynamic> _$$UserSettingsImplToJson(_$UserSettingsImpl instance) =>
    <String, dynamic>{
      'email_notifications': instance.emailNotifications,
      'push_notifications': instance.pushNotifications,
      'comment_notifications': instance.commentNotifications,
      'like_notifications': instance.likeNotifications,
      'follow_notifications': instance.followNotifications,
      'profile_visibility': instance.profileVisibility,
      'show_email': instance.showEmail,
      'show_phone': instance.showPhone,
      'content_language': instance.contentLanguage,
      'posts_per_page': instance.postsPerPage,
      'auto_play_videos': instance.autoPlayVideos,
      'theme': instance.theme,
    };
