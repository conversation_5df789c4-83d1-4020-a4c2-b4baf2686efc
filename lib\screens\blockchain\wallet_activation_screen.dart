import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/blockchain_service.dart';
import '../../theme/app_theme.dart';
import '../../widgets/styled_components.dart';

class WalletActivationScreen extends StatefulWidget {
  const WalletActivationScreen({Key? key}) : super(key: key);

  @override
  State<WalletActivationScreen> createState() => _WalletActivationScreenState();
}

class _WalletActivationScreenState extends State<WalletActivationScreen> {
  final TextEditingController _codeController = TextEditingController();
  final BlockchainService _blockchainService = BlockchainService();
  bool _isLoading = false;
  bool _isActivating = false;
  String _message = '';
  bool _isSuccess = false;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  Future<void> _sendActivationCode() async {
    setState(() {
      _isLoading = true;
      _message = '';
    });

    try {
      final response = await _blockchainService.sendActivationCode();
      setState(() {
        _isLoading = false;
        _message = response.message;
        _isSuccess = response.success;
      });

      if (response.success) {
        // In development, show the activation code
        if (response.data != null && response.data!['activation_code'] != null) {
          _showActivationCodeDialog(response.data!['activation_code']);
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _message = 'Error sending activation code: $e';
        _isSuccess = false;
      });
    }
  }

  Future<void> _activateWallet() async {
    if (_codeController.text.trim().isEmpty) {
      setState(() {
        _message = 'Please enter the activation code';
        _isSuccess = false;
      });
      return;
    }

    setState(() {
      _isActivating = true;
      _message = '';
    });

    try {
      final response = await _blockchainService.activateWallet(_codeController.text.trim());
      setState(() {
        _isActivating = false;
        _message = response.message;
        _isSuccess = response.success;
      });

      if (response.success) {
        // Show success dialog and navigate back
        _showSuccessDialog();
      }
    } catch (e) {
      setState(() {
        _isActivating = false;
        _message = 'Error activating wallet: $e';
        _isSuccess = false;
      });
    }
  }

  void _showActivationCodeDialog(String code) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Activation Code'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Your activation code is:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  code,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 2,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'In production, this would be sent to your email.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: code));
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Code copied to clipboard')),
                );
              },
              child: const Text('Copy & Close'),
            ),
          ],
        );
      },
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('🎉 Wallet Activated!'),
          content: const Text(
            'Your blockchain wallet has been successfully activated. You can now earn TRD tokens and NFT achievements!',
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(true); // Return to previous screen with success
              },
              child: const Text('Continue'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Activate Blockchain Wallet'),
        backgroundColor: AppTheme.blockchainPrimary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: AppTheme.blockchainPrimary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.security,
                    size: 64,
                    color: AppTheme.blockchainPrimary,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Activate Your Wallet',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Your blockchain wallet was created but needs to be activated for security. We\'ll send you a 6-digit code.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Step 1: Send Code
            const Text(
              'Step 1: Request Activation Code',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            StyledButton(
              text: 'Send Activation Code',
              onPressed: _isLoading ? null : _sendActivationCode,
              type: ButtonType.blockchain,
              isLoading: _isLoading,
              icon: const Icon(Icons.email, color: Colors.white),
            ),
            
            const SizedBox(height: 32),
            
            // Step 2: Enter Code
            const Text(
              'Step 2: Enter Activation Code',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              keyboardType: TextInputType.number,
              maxLength: 6,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                letterSpacing: 4,
              ),
              decoration: InputDecoration(
                labelText: 'Activation Code',
                hintText: 'Enter 6-digit code',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[50],
                counterText: '',
              ),
            ),
            
            const SizedBox(height: 24),
            
            StyledButton(
              text: 'Activate Wallet',
              onPressed: _isActivating ? null : _activateWallet,
              type: ButtonType.blockchain,
              isLoading: _isActivating,
              icon: const Icon(Icons.check_circle, color: Colors.white),
            ),
            
            const SizedBox(height: 24),
            
            // Message
            if (_message.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _isSuccess ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isSuccess ? Colors.green : Colors.red,
                    width: 1,
                  ),
                ),
                child: Text(
                  _message,
                  style: TextStyle(
                    color: _isSuccess ? Colors.green[700] : Colors.red[700],
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
