#!/bin/bash

# 🚀 Trendy App Setup Script for New Machines
# This script sets up the development environment on a new machine

set -e  # Exit on any error

echo "🚀 Setting up Trendy App development environment..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

print_status "Python 3 is installed"

# Check if we're in the right directory
if [ ! -f "trendy_web_and_api/trendy/manage.py" ]; then
    print_error "Please run this script from the trendy project root directory"
    exit 1
fi

print_status "Found Django project"

# Navigate to Django directory
cd trendy_web_and_api/trendy

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_info "Creating virtual environment..."
    python3 -m venv venv
    print_status "Virtual environment created"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_info "Upgrading pip..."
pip install --upgrade pip

# Install requirements
if [ -f "requirements.txt" ]; then
    print_info "Installing Python dependencies..."
    pip install -r requirements.txt
    print_status "Dependencies installed"
else
    print_warning "requirements.txt not found, installing basic dependencies..."
    pip install django djangorestframework django-cors-headers python-decouple
fi

# Run migrations
print_info "Running database migrations..."
python manage.py migrate
print_status "Database migrations completed"

# Load development data if fixtures exist
if [ -d "fixtures" ] && [ "$(ls -A fixtures/)" ]; then
    print_info "Loading development data from fixtures..."
    python manage.py setup_dev_data
    print_status "Development data loaded"
else
    print_warning "No fixtures found. You may need to create a superuser manually."
    echo ""
    read -p "Would you like to create a superuser now? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        python manage.py createsuperuser
    fi
fi

# Check if server can start
print_info "Testing Django server..."
python manage.py check
print_status "Django configuration is valid"

echo ""
print_status "🎉 Setup completed successfully!"
echo ""
print_info "To start the development server:"
echo "  cd trendy_web_and_api/trendy"
echo "  source venv/bin/activate"
echo "  python manage.py runserver 0.0.0.0:8000"
echo ""
print_info "To setup Flutter:"
echo "  cd ../../  # Go back to project root"
echo "  flutter pub get"
echo "  flutter run"
echo ""
print_info "Access the admin panel at: http://localhost:8000/admin/"
echo ""

# Deactivate virtual environment
deactivate
