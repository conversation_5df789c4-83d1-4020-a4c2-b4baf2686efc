"""
URL configuration for blockchain app
"""

from django.urls import path
from . import views

app_name = 'blockchain'

urlpatterns = [
    # Wallet endpoints
    path('wallet/', views.get_user_wallet, name='get_user_wallet'),
    path('wallet/create/', views.create_wallet, name='create_wallet'),
    path('wallet/send-activation-code/', views.send_activation_code, name='send_activation_code'),
    path('wallet/activate/', views.activate_wallet, name='activate_wallet'),

    # NFT endpoints
    path('nfts/', views.get_user_nfts, name='get_user_nfts'),
    path('nfts/mint/', views.mint_achievement_nft, name='mint_achievement_nft'),

    # Token endpoints
    path('tokens/reward/', views.reward_tokens, name='reward_tokens'),

    # Staking endpoints
    path('staking/pools/', views.get_staking_pools, name='get_staking_pools'),
    path('staking/stake/', views.stake_tokens, name='stake_tokens'),
    path('staking/positions/', views.get_user_stakes, name='get_user_stakes'),
]
