from django.db import transaction
from django.db.models import Sum
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from decimal import Decimal

from .models import PayPalReward, UserPayPalReward, PayPalSettings, UserLevel
from .services import GamificationService


class PayPalRewardService:
    """Service for handling PayPal monetary rewards"""
    
    @staticmethod
    def get_available_rewards_for_user(user):
        """Get PayPal rewards available for a specific user"""
        user_level = GamificationService.get_or_create_user_level(user)
        settings = PayPalSettings.get_settings()
        
        if not settings.rewards_enabled:
            return []
        
        # Get rewards user hasn't claimed yet
        claimed_reward_ids = UserPayPalReward.objects.filter(
            user=user
        ).values_list('reward_id', flat=True)
        
        available_rewards = PayPalReward.objects.filter(
            status='active'
        ).exclude(id__in=claimed_reward_ids)
        
        # Filter by user qualifications
        qualified_rewards = []
        for reward in available_rewards:
            if PayPalRewardService.check_user_qualification(user, user_level, reward):
                qualified_rewards.append(reward)
        
        return qualified_rewards
    
    @staticmethod
    def check_user_qualification(user, user_level, reward):
        """Check if user qualifies for a specific reward"""
        # Check account age
        account_age = (timezone.now() - user.date_joined).days
        if account_age < reward.minimum_account_age_days:
            return False
        
        # Check points requirement
        if reward.points_required and user_level.total_points < reward.points_required:
            return False
        
        # Check level requirement
        if reward.level_required and user_level.current_level < reward.level_required:
            return False
        
        # Check streak requirement
        if reward.streak_required:
            max_streak = max(
                user_level.reading_streak,
                user_level.writing_streak,
                user_level.engagement_streak
            )
            if max_streak < reward.streak_required:
                return False
        
        # Check custom requirements (engagement-focused)
        if reward.requirements:
            for req_key, req_value in reward.requirements.items():
                if req_key == 'min_posts_read' and user_level.total_posts_read < req_value:
                    return False
                elif req_key == 'min_comments' and user_level.total_comments_made < req_value:
                    return False
                elif req_key == 'min_likes' and user_level.total_likes_given < req_value:
                    return False
                elif req_key == 'min_voice_comments' and user_level.total_voice_comments < req_value:
                    return False
                # Monthly requirements (for recurring rewards)
                elif req_key == 'monthly_posts_read':
                    # This would need to be calculated based on current month activity
                    # For now, we'll skip monthly requirements in qualification check
                    continue
                elif req_key == 'monthly_comments':
                    continue
                elif req_key == 'monthly_likes':
                    continue
        
        # Check if reward is still available
        if not reward.is_available:
            return False
        
        return True
    
    @staticmethod
    def claim_reward(user, reward_id, paypal_email):
        """Claim a PayPal reward for a user"""
        try:
            with transaction.atomic():
                reward = PayPalReward.objects.select_for_update().get(id=reward_id)
                user_level = GamificationService.get_or_create_user_level(user)
                settings = PayPalSettings.get_settings()
                
                # Verify user qualification
                if not PayPalRewardService.check_user_qualification(user, user_level, reward):
                    return False, "You don't qualify for this reward"
                
                # Check if user already claimed this reward
                if UserPayPalReward.objects.filter(user=user, reward=reward).exists():
                    return False, "You have already claimed this reward"
                
                # Check monthly limits
                current_month_claims = UserPayPalReward.objects.filter(
                    user=user,
                    claim_date__gte=timezone.now().replace(day=1)
                ).aggregate(total=Sum('usd_amount'))['total'] or Decimal('0')
                
                if current_month_claims + reward.usd_amount > settings.maximum_monthly_payout_per_user:
                    return False, f"Monthly limit of ${settings.maximum_monthly_payout_per_user} exceeded"
                
                # Check reward availability
                if reward.max_total_claims and reward.current_claims >= reward.max_total_claims:
                    return False, "This reward is no longer available"
                
                # Create claim
                user_reward = UserPayPalReward.objects.create(
                    user=user,
                    reward=reward,
                    paypal_email=paypal_email,
                    usd_amount=reward.usd_amount,
                    user_points_at_claim=user_level.total_points,
                    user_level_at_claim=user_level.current_level,
                    status='pending'
                )
                
                # Update reward claim count
                reward.current_claims += 1
                reward.save(update_fields=['current_claims'])
                
                # Send notification emails
                PayPalRewardService.send_claim_notification(user_reward)
                
                return True, f"Successfully claimed ${reward.usd_amount} reward! Pending admin approval."
                
        except PayPalReward.DoesNotExist:
            return False, "Reward not found"
        except Exception as e:
            return False, f"Error claiming reward: {str(e)}"
    
    @staticmethod
    def approve_reward_claim(claim_id, admin_user, notes=""):
        """Approve a PayPal reward claim (admin action)"""
        try:
            with transaction.atomic():
                claim = UserPayPalReward.objects.select_for_update().get(id=claim_id)
                
                if claim.status != 'pending':
                    return False, "Claim is not in pending status"
                
                claim.status = 'approved'
                claim.approved_date = timezone.now()
                claim.approved_by = admin_user
                claim.admin_notes = notes
                claim.save()
                
                # Send approval notification
                PayPalRewardService.send_approval_notification(claim)
                
                return True, "Reward claim approved successfully"
                
        except UserPayPalReward.DoesNotExist:
            return False, "Claim not found"
        except Exception as e:
            return False, f"Error approving claim: {str(e)}"
    
    @staticmethod
    def process_paypal_payment(claim_id):
        """Process PayPal payment for approved claim using real PayPal API"""
        try:
            claim = UserPayPalReward.objects.get(id=claim_id, status__in=['approved', 'processing'])
            settings = PayPalSettings.get_settings()

            # Update status to processing
            claim.status = 'processing'
            claim.save()

            # Use simulated PayPal payout for development
            # In production, this would use real PayPal API
            payment_result = PayPalRewardService.simulate_paypal_payment(
                email=claim.paypal_email,
                amount=float(claim.usd_amount),
                description=f"Trendy App Reward: {claim.reward.name}"
            )

            if payment_result['success']:
                claim.status = 'paid'
                claim.paid_date = timezone.now()
                claim.paypal_transaction_id = payment_result.get('transaction_id', '')
                claim.paypal_batch_id = payment_result.get('batch_id', '')
                claim.save()

                # Send payment confirmation
                PayPalRewardService.send_payment_confirmation(claim)

                return True, "Payment processed successfully"
            else:
                claim.status = 'failed'
                claim.admin_notes += f"\nPayment failed: {payment_result.get('error', 'Unknown error')}"
                claim.save()

                return False, f"Payment failed: {payment_result.get('error', 'Unknown error')}"

        except UserPayPalReward.DoesNotExist:
            return False, "Claim not found"
        except Exception as e:
            return False, f"Error processing payment: {str(e)}"
    
    @staticmethod
    def simulate_paypal_payment(email, amount, description):
        """Simulate PayPal payment (replace with real PayPal API integration)"""
        # This is a simulation - replace with actual PayPal API calls
        import uuid
        
        # Simulate 95% success rate
        import random
        if random.random() < 0.95:
            return {
                'success': True,
                'transaction_id': f"TXN_{uuid.uuid4().hex[:12].upper()}",
                'batch_id': f"BATCH_{uuid.uuid4().hex[:8].upper()}",
                'amount': amount,
                'email': email
            }
        else:
            return {
                'success': False,
                'error': 'Insufficient funds in PayPal account'
            }
    
    @staticmethod
    def send_claim_notification(user_reward):
        """Send email notification for new reward claim"""
        settings = PayPalSettings.get_settings()
        
        if settings.admin_email_notifications:
            # Send to admin
            send_mail(
                subject=f"New PayPal Reward Claim - ${user_reward.usd_amount}",
                message=f"""
New PayPal reward claim submitted:

User: {user_reward.user.username}
Email: {user_reward.user.email}
PayPal Email: {user_reward.paypal_email}
Reward: {user_reward.reward.name}
Amount: ${user_reward.usd_amount}
User Level: {user_reward.user_level_at_claim}
User Points: {user_reward.user_points_at_claim}

Please review and approve in the admin panel.
                """,
                from_email='<EMAIL>',
                recipient_list=['<EMAIL>'],
                fail_silently=True,
            )
        
        if settings.user_email_notifications:
            # Send to user
            send_mail(
                subject=f"PayPal Reward Claim Submitted - ${user_reward.usd_amount}",
                message=f"""
Hi {user_reward.user.username},

Your PayPal reward claim has been submitted successfully!

Reward: {user_reward.reward.name}
Amount: ${user_reward.usd_amount}
PayPal Email: {user_reward.paypal_email}
Status: Pending Review

We'll review your claim and send payment within 3-5 business days.

Thanks for being an active member of Trendy!
                """,
                from_email='<EMAIL>',
                recipient_list=[user_reward.user.email],
                fail_silently=True,
            )
    
    @staticmethod
    def send_approval_notification(user_reward):
        """Send email notification for approved reward claim"""
        send_mail(
            subject=f"PayPal Reward Approved - ${user_reward.usd_amount}",
            message=f"""
Hi {user_reward.user.username},

Great news! Your PayPal reward claim has been approved!

Reward: {user_reward.reward.name}
Amount: ${user_reward.usd_amount}
PayPal Email: {user_reward.paypal_email}

Payment will be processed within 24 hours.

Thanks for being an amazing member of Trendy!
            """,
            from_email='<EMAIL>',
            recipient_list=[user_reward.user.email],
            fail_silently=True,
        )
    
    @staticmethod
    def send_payment_confirmation(user_reward):
        """Send email notification for completed payment"""
        send_mail(
            subject=f"PayPal Payment Sent - ${user_reward.usd_amount}",
            message=f"""
Hi {user_reward.user.username},

Your PayPal reward payment has been sent!

Reward: {user_reward.reward.name}
Amount: ${user_reward.usd_amount}
PayPal Email: {user_reward.paypal_email}
Transaction ID: {user_reward.paypal_transaction_id}
Date: {user_reward.paid_date.strftime('%Y-%m-%d %H:%M')}

The payment should appear in your PayPal account within a few minutes.

Keep up the great work on Trendy!
            """,
            from_email='<EMAIL>',
            recipient_list=[user_reward.user.email],
            fail_silently=True,
        )
