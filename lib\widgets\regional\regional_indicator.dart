import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/models/country.dart';
import 'package:trendy/providers/regional_provider.dart';

class RegionalIndicator extends ConsumerWidget {
  final bool showChangeButton;
  final VoidCallback? onChangePressed;
  final bool isCompact;

  const RegionalIndicator({
    Key? key,
    this.showChangeButton = true,
    this.onChangePressed,
    this.isCompact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final effectiveCountry = ref.watch(effectiveCountryProvider);
    final preferencesAsync = ref.watch(regionalPreferencesNotifierProvider);

    return preferencesAsync.when(
      data: (preferences) => _buildIndicator(
        context,
        effectiveCountry,
        preferences,
      ),
      loading: () => _buildLoadingIndicator(),
      error: (error, stack) => _buildErrorIndicator(error),
    );
  }

  Widget _buildIndicator(
    BuildContext context,
    Country? effectiveCountry,
    RegionalPreferences preferences,
  ) {
    final isGlobal = effectiveCountry == null;
    final flag = isGlobal ? '🌍' : (effectiveCountry.flagEmoji ?? '🏳️');
    final countryName = isGlobal
        ? 'Global'
        : (effectiveCountry.displayName ?? effectiveCountry.name);
    final subtitle = _getSubtitle(preferences, effectiveCountry);

    if (isCompact) {
      return _buildCompactIndicator(context, flag, countryName, subtitle);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Text(flag, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Viewing: ',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    Text(
                      countryName,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade800,
                      ),
                    ),
                  ],
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (showChangeButton)
            TextButton.icon(
              onPressed: onChangePressed,
              icon: const Icon(Icons.swap_horiz, size: 16),
              label: const Text('Change'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue.shade700,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCompactIndicator(
    BuildContext context,
    String flag,
    String countryName,
    String? subtitle,
  ) {
    return InkWell(
      onTap: onChangePressed,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(flag, style: const TextStyle(fontSize: 16)),
            const SizedBox(width: 6),
            Text(
              countryName,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.blue.shade800,
              ),
            ),
            if (showChangeButton) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.keyboard_arrow_down,
                size: 16,
                color: Colors.blue.shade700,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading region...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorIndicator(Object error) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Error loading region',
              style: TextStyle(
                fontSize: 14,
                color: Colors.red.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String? _getSubtitle(
      RegionalPreferences preferences, Country? effectiveCountry) {
    if (effectiveCountry == null) {
      return preferences.showGlobalContent
          ? 'Including global content'
          : 'Global content only';
    }

    final isPreferred = preferences.preferredCountry?.id == effectiveCountry.id;
    final isDetected = preferences.detectedCountry?.id == effectiveCountry.id;

    if (isPreferred) {
      return 'Your preferred region';
    } else if (isDetected) {
      return 'Auto-detected from your location';
    } else {
      return effectiveCountry.region?.name;
    }
  }
}

class RegionalIndicatorChip extends ConsumerWidget {
  final VoidCallback? onTap;

  const RegionalIndicatorChip({
    Key? key,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final effectiveCountry = ref.watch(effectiveCountryProvider);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.blue.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              effectiveCountry?.flagEmoji ?? '🌍',
              style: const TextStyle(fontSize: 12),
            ),
            const SizedBox(width: 4),
            Text(
              effectiveCountry?.code ?? 'Global',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.blue.shade800,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
