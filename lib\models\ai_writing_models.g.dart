// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_writing_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AIWritingPreferencesImpl _$$AIWritingPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$AIWritingPreferencesImpl(
      preferredTone: json['preferredTone'] as String? ?? 'professional',
      preferredStyle: json['preferredStyle'] as String? ?? 'blog',
      targetAudience: json['targetAudience'] as String? ?? 'general audience',
      enableGrammarSuggestions:
          json['enableGrammarSuggestions'] as bool? ?? true,
      enableSeoSuggestions: json['enableSeoSuggestions'] as bool? ?? true,
      enableContentGeneration: json['enableContentGeneration'] as bool? ?? true,
      enableReadabilityAnalysis:
          json['enableReadabilityAnalysis'] as bool? ?? true,
      enableAutoComplete: json['enableAutoComplete'] as bool? ?? true,
      preferredWordCount: (json['preferredWordCount'] as num?)?.toInt() ?? 800,
      includeReferences: json['includeReferences'] as bool? ?? true,
      includeImagesSuggestions:
          json['includeImagesSuggestions'] as bool? ?? true,
    );

Map<String, dynamic> _$$AIWritingPreferencesImplToJson(
        _$AIWritingPreferencesImpl instance) =>
    <String, dynamic>{
      'preferredTone': instance.preferredTone,
      'preferredStyle': instance.preferredStyle,
      'targetAudience': instance.targetAudience,
      'enableGrammarSuggestions': instance.enableGrammarSuggestions,
      'enableSeoSuggestions': instance.enableSeoSuggestions,
      'enableContentGeneration': instance.enableContentGeneration,
      'enableReadabilityAnalysis': instance.enableReadabilityAnalysis,
      'enableAutoComplete': instance.enableAutoComplete,
      'preferredWordCount': instance.preferredWordCount,
      'includeReferences': instance.includeReferences,
      'includeImagesSuggestions': instance.includeImagesSuggestions,
    };

_$ContentIdeaImpl _$$ContentIdeaImplFromJson(Map<String, dynamic> json) =>
    _$ContentIdeaImpl(
      title: json['title'] as String,
      description: json['description'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$ContentIdeaImplToJson(_$ContentIdeaImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'tags': instance.tags,
    };

_$ContentOutlineImpl _$$ContentOutlineImplFromJson(Map<String, dynamic> json) =>
    _$ContentOutlineImpl(
      introduction: (json['introduction'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      mainSections: (json['mainSections'] as List<dynamic>)
          .map((e) => OutlineSection.fromJson(e as Map<String, dynamic>))
          .toList(),
      conclusion: (json['conclusion'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      estimatedWordCount: (json['estimatedWordCount'] as num).toInt(),
    );

Map<String, dynamic> _$$ContentOutlineImplToJson(
        _$ContentOutlineImpl instance) =>
    <String, dynamic>{
      'introduction': instance.introduction,
      'mainSections': instance.mainSections,
      'conclusion': instance.conclusion,
      'estimatedWordCount': instance.estimatedWordCount,
    };

_$OutlineSectionImpl _$$OutlineSectionImplFromJson(Map<String, dynamic> json) =>
    _$OutlineSectionImpl(
      title: json['title'] as String,
      points:
          (json['points'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$OutlineSectionImplToJson(
        _$OutlineSectionImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
      'points': instance.points,
    };

_$GrammarImprovementImpl _$$GrammarImprovementImplFromJson(
        Map<String, dynamic> json) =>
    _$GrammarImprovementImpl(
      improvedText: json['improvedText'] as String,
      changes: (json['changes'] as List<dynamic>)
          .map((e) => TextChange.fromJson(e as Map<String, dynamic>))
          .toList(),
      readabilityScore: (json['readabilityScore'] as num).toDouble(),
    );

Map<String, dynamic> _$$GrammarImprovementImplToJson(
        _$GrammarImprovementImpl instance) =>
    <String, dynamic>{
      'improvedText': instance.improvedText,
      'changes': instance.changes,
      'readabilityScore': instance.readabilityScore,
    };

_$TextChangeImpl _$$TextChangeImplFromJson(Map<String, dynamic> json) =>
    _$TextChangeImpl(
      original: json['original'] as String,
      improved: json['improved'] as String,
      reason: json['reason'] as String,
    );

Map<String, dynamic> _$$TextChangeImplToJson(_$TextChangeImpl instance) =>
    <String, dynamic>{
      'original': instance.original,
      'improved': instance.improved,
      'reason': instance.reason,
    };

_$SEOSuggestionsImpl _$$SEOSuggestionsImplFromJson(Map<String, dynamic> json) =>
    _$SEOSuggestionsImpl(
      titleSuggestions: (json['titleSuggestions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      metaDescription: json['metaDescription'] as String,
      keywords:
          (json['keywords'] as List<dynamic>).map((e) => e as String).toList(),
      contentSuggestions: (json['contentSuggestions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      readabilityIssues: (json['readabilityIssues'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$SEOSuggestionsImplToJson(
        _$SEOSuggestionsImpl instance) =>
    <String, dynamic>{
      'titleSuggestions': instance.titleSuggestions,
      'metaDescription': instance.metaDescription,
      'keywords': instance.keywords,
      'contentSuggestions': instance.contentSuggestions,
      'readabilityIssues': instance.readabilityIssues,
    };

_$ReadabilityAnalysisImpl _$$ReadabilityAnalysisImplFromJson(
        Map<String, dynamic> json) =>
    _$ReadabilityAnalysisImpl(
      wordCount: (json['wordCount'] as num).toInt(),
      sentenceCount: (json['sentenceCount'] as num).toInt(),
      paragraphCount: (json['paragraphCount'] as num).toInt(),
      avgWordsPerSentence: (json['avgWordsPerSentence'] as num).toDouble(),
      avgSentencesPerParagraph:
          (json['avgSentencesPerParagraph'] as num).toDouble(),
      readabilityScore: (json['readabilityScore'] as num).toDouble(),
      readingLevel: json['readingLevel'] as String,
      estimatedReadingTime: (json['estimatedReadingTime'] as num).toInt(),
      suggestions: (json['suggestions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$ReadabilityAnalysisImplToJson(
        _$ReadabilityAnalysisImpl instance) =>
    <String, dynamic>{
      'wordCount': instance.wordCount,
      'sentenceCount': instance.sentenceCount,
      'paragraphCount': instance.paragraphCount,
      'avgWordsPerSentence': instance.avgWordsPerSentence,
      'avgSentencesPerParagraph': instance.avgSentencesPerParagraph,
      'readabilityScore': instance.readabilityScore,
      'readingLevel': instance.readingLevel,
      'estimatedReadingTime': instance.estimatedReadingTime,
      'suggestions': instance.suggestions,
    };

_$AIWritingSessionImpl _$$AIWritingSessionImplFromJson(
        Map<String, dynamic> json) =>
    _$AIWritingSessionImpl(
      id: json['id'] as String,
      postId: json['postId'] as String?,
      sessionData: json['sessionData'] as Map<String, dynamic>,
      status: json['status'] as String? ?? 'active',
      suggestionsGenerated:
          (json['suggestionsGenerated'] as num?)?.toInt() ?? 0,
      suggestionsAccepted: (json['suggestionsAccepted'] as num?)?.toInt() ?? 0,
      wordsGenerated: (json['wordsGenerated'] as num?)?.toInt() ?? 0,
      timeSavedMinutes: (json['timeSavedMinutes'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$AIWritingSessionImplToJson(
        _$AIWritingSessionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'postId': instance.postId,
      'sessionData': instance.sessionData,
      'status': instance.status,
      'suggestionsGenerated': instance.suggestionsGenerated,
      'suggestionsAccepted': instance.suggestionsAccepted,
      'wordsGenerated': instance.wordsGenerated,
      'timeSavedMinutes': instance.timeSavedMinutes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_$ContentSuggestionImpl _$$ContentSuggestionImplFromJson(
        Map<String, dynamic> json) =>
    _$ContentSuggestionImpl(
      id: json['id'] as String,
      sessionId: json['sessionId'] as String,
      suggestionType: json['suggestionType'] as String,
      originalText: json['originalText'] as String,
      suggestedText: json['suggestedText'] as String,
      explanation: json['explanation'] as String,
      confidenceScore: (json['confidenceScore'] as num).toDouble(),
      startPosition: (json['startPosition'] as num?)?.toInt(),
      endPosition: (json['endPosition'] as num?)?.toInt(),
      status: json['status'] as String? ?? 'pending',
      userFeedback: json['userFeedback'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$ContentSuggestionImplToJson(
        _$ContentSuggestionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sessionId': instance.sessionId,
      'suggestionType': instance.suggestionType,
      'originalText': instance.originalText,
      'suggestedText': instance.suggestedText,
      'explanation': instance.explanation,
      'confidenceScore': instance.confidenceScore,
      'startPosition': instance.startPosition,
      'endPosition': instance.endPosition,
      'status': instance.status,
      'userFeedback': instance.userFeedback,
      'createdAt': instance.createdAt.toIso8601String(),
    };

_$ContentTemplateImpl _$$ContentTemplateImplFromJson(
        Map<String, dynamic> json) =>
    _$ContentTemplateImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      templateContent: json['templateContent'] as String,
      category: json['category'] as String,
      estimatedWordCount: (json['estimatedWordCount'] as num).toInt(),
      difficultyLevel: json['difficultyLevel'] as String,
      isPublic: json['isPublic'] as bool,
      usageCount: (json['usageCount'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$ContentTemplateImplToJson(
        _$ContentTemplateImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'templateContent': instance.templateContent,
      'category': instance.category,
      'estimatedWordCount': instance.estimatedWordCount,
      'difficultyLevel': instance.difficultyLevel,
      'isPublic': instance.isPublic,
      'usageCount': instance.usageCount,
      'createdAt': instance.createdAt.toIso8601String(),
    };

_$AIUsageAnalyticsImpl _$$AIUsageAnalyticsImplFromJson(
        Map<String, dynamic> json) =>
    _$AIUsageAnalyticsImpl(
      featureUsed: json['featureUsed'] as String,
      usageCount: (json['usageCount'] as num).toInt(),
      totalTimeSavedMinutes: (json['totalTimeSavedMinutes'] as num).toInt(),
      totalWordsGenerated: (json['totalWordsGenerated'] as num).toInt(),
      year: (json['year'] as num).toInt(),
      month: (json['month'] as num).toInt(),
    );

Map<String, dynamic> _$$AIUsageAnalyticsImplToJson(
        _$AIUsageAnalyticsImpl instance) =>
    <String, dynamic>{
      'featureUsed': instance.featureUsed,
      'usageCount': instance.usageCount,
      'totalTimeSavedMinutes': instance.totalTimeSavedMinutes,
      'totalWordsGenerated': instance.totalWordsGenerated,
      'year': instance.year,
      'month': instance.month,
    };

_$ContentIdeaRequestImpl _$$ContentIdeaRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ContentIdeaRequestImpl(
      topic: json['topic'] as String,
      count: (json['count'] as num?)?.toInt() ?? 5,
    );

Map<String, dynamic> _$$ContentIdeaRequestImplToJson(
        _$ContentIdeaRequestImpl instance) =>
    <String, dynamic>{
      'topic': instance.topic,
      'count': instance.count,
    };

_$ContentOutlineRequestImpl _$$ContentOutlineRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ContentOutlineRequestImpl(
      title: json['title'] as String,
    );

Map<String, dynamic> _$$ContentOutlineRequestImplToJson(
        _$ContentOutlineRequestImpl instance) =>
    <String, dynamic>{
      'title': instance.title,
    };

_$GrammarImprovementRequestImpl _$$GrammarImprovementRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$GrammarImprovementRequestImpl(
      text: json['text'] as String,
    );

Map<String, dynamic> _$$GrammarImprovementRequestImplToJson(
        _$GrammarImprovementRequestImpl instance) =>
    <String, dynamic>{
      'text': instance.text,
    };

_$SEOSuggestionsRequestImpl _$$SEOSuggestionsRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SEOSuggestionsRequestImpl(
      content: json['content'] as String,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$$SEOSuggestionsRequestImplToJson(
        _$SEOSuggestionsRequestImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
      'title': instance.title,
    };

_$TextCompletionRequestImpl _$$TextCompletionRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$TextCompletionRequestImpl(
      partialText: json['partialText'] as String,
      context: json['context'] as String?,
    );

Map<String, dynamic> _$$TextCompletionRequestImplToJson(
        _$TextCompletionRequestImpl instance) =>
    <String, dynamic>{
      'partialText': instance.partialText,
      'context': instance.context,
    };

_$ReadabilityAnalysisRequestImpl _$$ReadabilityAnalysisRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ReadabilityAnalysisRequestImpl(
      content: json['content'] as String,
    );

Map<String, dynamic> _$$ReadabilityAnalysisRequestImplToJson(
        _$ReadabilityAnalysisRequestImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
    };
