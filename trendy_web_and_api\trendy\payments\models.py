from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal

User = get_user_model()


class PayPalAccount(models.Model):
    """Store PayPal account configuration"""
    ACCOUNT_TYPES = [
        ('business', 'Business Account'),
        ('personal', 'Personal Account'),
    ]
    
    ENVIRONMENT_CHOICES = [
        ('sandbox', 'Sandbox (Testing)'),
        ('live', 'Live (Production)'),
    ]
    
    # Account identification
    name = models.CharField(max_length=100, default='Trendy App PayPal')
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPES, default='business')
    environment = models.CharField(max_length=10, choices=ENVIRONMENT_CHOICES, default='sandbox')
    
    # PayPal API credentials
    client_id = models.CharField(max_length=200)
    client_secret = models.CharField(max_length=200)
    
    # Business account details
    business_email = models.EmailField()
    business_name = models.CharField(max_length=200, default='Trendy App LLC')
    
    # Webhook configuration
    webhook_id = models.CharField(max_length=100, blank=True)
    webhook_url = models.URLField(blank=True)
    
    # Settings
    is_active = models.BooleanField(default=True)
    auto_accept_payments = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "PayPal Account"
        verbose_name_plural = "PayPal Accounts"
    
    def __str__(self):
        return f"{self.name} ({self.environment})"
    
    @classmethod
    def get_active_account(cls):
        """Get the active PayPal account"""
        return cls.objects.filter(is_active=True).first()


class PaymentTransaction(models.Model):
    """Track all PayPal payment transactions (both incoming and outgoing)"""
    TRANSACTION_TYPES = [
        ('incoming', 'Incoming Payment (User to Admin)'),
        ('outgoing', 'Outgoing Payment (Admin to User)'),
    ]
    
    PAYMENT_PURPOSES = [
        ('premium_subscription', 'Premium Subscription'),
        ('tier_unlock', 'Reward Tier Unlock'),
        ('point_boost', 'Point Boost Package'),
        ('virtual_item', 'Virtual Item Purchase'),
        ('streak_protection', 'Streak Protection'),
        ('reward_payout', 'Reward Payout'),
        ('referral_bonus', 'Referral Bonus'),
    ]
    
    TRANSACTION_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]
    
    # Basic transaction info
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='paypal_transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    payment_purpose = models.CharField(max_length=30, choices=PAYMENT_PURPOSES)
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS, default='pending')
    
    # Amount details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    
    # PayPal details
    paypal_payment_id = models.CharField(max_length=100, blank=True)
    paypal_payer_id = models.CharField(max_length=100, blank=True)
    paypal_batch_id = models.CharField(max_length=100, blank=True)  # For payouts
    paypal_item_id = models.CharField(max_length=100, blank=True)   # For individual payout items
    
    # Email addresses
    payer_email = models.EmailField(blank=True)  # User's PayPal email
    payee_email = models.EmailField(blank=True)  # Admin's PayPal email
    
    # Transaction details
    description = models.TextField()
    reference_id = models.CharField(max_length=100, unique=True)  # Our internal reference
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Error handling
    error_message = models.TextField(blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'transaction_type']),
            models.Index(fields=['status', 'transaction_type']),
            models.Index(fields=['paypal_payment_id']),
            models.Index(fields=['reference_id']),
        ]
    
    def __str__(self):
        direction = "←" if self.transaction_type == 'incoming' else "→"
        return f"{direction} ${self.amount} - {self.user.username} - {self.payment_purpose}"


class PayPalWebhook(models.Model):
    """Store PayPal webhook events for processing"""
    WEBHOOK_EVENTS = [
        ('PAYMENT.CAPTURE.COMPLETED', 'Payment Capture Completed'),
        ('PAYMENT.CAPTURE.DENIED', 'Payment Capture Denied'),
        ('PAYMENTS.PAYMENT.CREATED', 'Payment Created'),
        ('CHECKOUT.ORDER.APPROVED', 'Order Approved'),
        ('BILLING.SUBSCRIPTION.CREATED', 'Subscription Created'),
        ('BILLING.SUBSCRIPTION.CANCELLED', 'Subscription Cancelled'),
        ('PAYMENTS.PAYOUTS-ITEM.SUCCEEDED', 'Payout Item Succeeded'),
        ('PAYMENTS.PAYOUTS-ITEM.FAILED', 'Payout Item Failed'),
    ]
    
    # Webhook identification
    webhook_id = models.CharField(max_length=100)
    event_type = models.CharField(max_length=50, choices=WEBHOOK_EVENTS)
    event_id = models.CharField(max_length=100, unique=True)
    
    # Event data
    resource_id = models.CharField(max_length=100)  # Payment ID, Order ID, etc.
    resource_type = models.CharField(max_length=50)
    
    # Raw webhook data
    raw_data = models.JSONField()
    
    # Processing status
    processed = models.BooleanField(default=False)
    processed_at = models.DateTimeField(null=True, blank=True)
    
    # Related transaction
    payment_transaction = models.ForeignKey(
        PaymentTransaction, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True
    )
    
    # Metadata
    received_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-received_at']
        indexes = [
            models.Index(fields=['event_type', 'processed']),
            models.Index(fields=['resource_id']),
            models.Index(fields=['event_id']),
        ]
    
    def __str__(self):
        return f"{self.event_type} - {self.resource_id}"


class PayPalPayoutBatch(models.Model):
    """Track batch payouts to multiple users"""
    BATCH_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('success', 'Success'),
        ('denied', 'Denied'),
        ('canceled', 'Canceled'),
    ]
    
    # Batch identification
    batch_id = models.CharField(max_length=100, unique=True)
    paypal_batch_id = models.CharField(max_length=100, blank=True)
    
    # Batch details
    batch_status = models.CharField(max_length=20, choices=BATCH_STATUS, default='pending')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_items = models.PositiveIntegerField()
    currency = models.CharField(max_length=3, default='USD')
    
    # Processing details
    created_at = models.DateTimeField(auto_now_add=True)
    submitted_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Error handling
    error_message = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['batch_status']),
            models.Index(fields=['paypal_batch_id']),
        ]
    
    def __str__(self):
        return f"Batch {self.batch_id} - ${self.total_amount} ({self.total_items} items)"


class PaymentSettings(models.Model):
    """Global payment system settings"""
    # PayPal configuration
    paypal_mode = models.CharField(
        max_length=10, 
        choices=[('sandbox', 'Sandbox'), ('live', 'Live')], 
        default='sandbox'
    )
    
    # Payment limits
    minimum_payment = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    maximum_payment = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('500.00'))
    
    # Payout settings
    minimum_payout = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('5.00'))
    maximum_daily_payout = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1000.00'))
    payout_processing_fee = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.30'))
    
    # Batch processing
    auto_process_payouts = models.BooleanField(default=True)
    batch_size = models.PositiveIntegerField(default=50)
    batch_frequency_hours = models.PositiveIntegerField(default=24)
    
    # Security settings
    require_email_verification = models.BooleanField(default=True)
    webhook_verification = models.BooleanField(default=True)
    
    # Notifications
    admin_email_notifications = models.BooleanField(default=True)
    user_email_notifications = models.BooleanField(default=True)
    
    # System controls
    payments_enabled = models.BooleanField(default=True)
    payouts_enabled = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Payment Settings"
        verbose_name_plural = "Payment Settings"
    
    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and PaymentSettings.objects.exists():
            raise ValueError("Only one PaymentSettings instance is allowed")
        super().save(*args, **kwargs)
    
    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class UserPayPalProfile(models.Model):
    """Store user's PayPal information for payments"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='paypal_profile')
    
    # PayPal account details
    paypal_email = models.EmailField()
    paypal_account_verified = models.BooleanField(default=False)
    
    # Verification details
    verification_code = models.CharField(max_length=10, blank=True)
    verification_sent_at = models.DateTimeField(null=True, blank=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    
    # Payment preferences
    auto_accept_payments = models.BooleanField(default=True)
    notification_preferences = models.JSONField(default=dict)
    
    # Statistics
    total_payments_received = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_payments_made = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    successful_transactions = models.PositiveIntegerField(default=0)
    failed_transactions = models.PositiveIntegerField(default=0)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['paypal_email']),
            models.Index(fields=['paypal_account_verified']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.paypal_email}"
    
    @property
    def is_verified(self):
        """Check if PayPal account is verified"""
        return self.paypal_account_verified and self.verified_at is not None
