import 'package:freezed_annotation/freezed_annotation.dart';

part 'quiz.freezed.dart';
part 'quiz.g.dart';

@freezed
class Quiz with _$Quiz {
  const factory Quiz({
    required int id,
    @Default('') String instructions,
    @Json<PERSON>ey(name: 'time_limit') int? timeLimit,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'show_correct_answers') @Default(true) bool showCorrectAnswers,
    @<PERSON>sonKey(name: 'randomize_questions') @Default(false) bool randomizeQuestions,
    @<PERSON>son<PERSON>ey(name: 'passing_score') @Default(70) int passingScore,
    @<PERSON>son<PERSON>ey(name: 'total_attempts') @Default(0) int totalAttempts,
    @Json<PERSON>ey(name: 'average_score') @Default(0.0) double averageScore,
    @Json<PERSON>ey(name: 'created_at') required DateTime createdAt,
    
    @Default([]) List<QuizQuestion> questions,
    @<PERSON>son<PERSON><PERSON>(name: 'user_attempts') @Default([]) List<QuizAttempt> userAttempts,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'best_attempt') QuizAttempt? bestAttempt,
  }) = _Quiz;

  factory Quiz.fromJson(Map<String, dynamic> json) => _$QuizFromJson(json);
}

@freezed
class QuizQuestion with _$QuizQuestion {
  const factory QuizQuestion({
    required int id,
    @JsonKey(name: 'question_type') @Default('multiple_choice') String questionType,
    @JsonKey(name: 'question_text') required String questionText,
    @Default('') String explanation,
    @Default(1) int points,
    @Default(0) int position,
    
    @Default([]) List<QuizAnswer> answers,
  }) = _QuizQuestion;

  factory QuizQuestion.fromJson(Map<String, dynamic> json) => _$QuizQuestionFromJson(json);
}

@freezed
class QuizAnswer with _$QuizAnswer {
  const factory QuizAnswer({
    required int id,
    @JsonKey(name: 'answer_text') required String answerText,
    @JsonKey(name: 'is_correct') @Default(false) bool isCorrect,
    @Default(0) int position,
  }) = _QuizAnswer;

  factory QuizAnswer.fromJson(Map<String, dynamic> json) => _$QuizAnswerFromJson(json);
}

@freezed
class QuizAttempt with _$QuizAttempt {
  const factory QuizAttempt({
    required int id,
    @Default(0.0) double score,
    @JsonKey(name: 'total_points') @Default(0) int totalPoints,
    @JsonKey(name: 'earned_points') @Default(0) int earnedPoints,
    @JsonKey(name: 'time_taken') @Default(0) int timeTaken,
    @JsonKey(name: 'completed_at') required DateTime completedAt,
    @Default({}) Map<String, dynamic> answers,
    @Default(false) bool passed,
  }) = _QuizAttempt;

  factory QuizAttempt.fromJson(Map<String, dynamic> json) => _$QuizAttemptFromJson(json);
}

enum QuizQuestionType {
  multipleChoice,
  trueFalse,
  textInput,
  numberInput,
}

extension QuizQuestionTypeExtension on QuizQuestionType {
  String get value {
    switch (this) {
      case QuizQuestionType.multipleChoice:
        return 'multiple_choice';
      case QuizQuestionType.trueFalse:
        return 'true_false';
      case QuizQuestionType.textInput:
        return 'text_input';
      case QuizQuestionType.numberInput:
        return 'number_input';
    }
  }

  String get displayName {
    switch (this) {
      case QuizQuestionType.multipleChoice:
        return 'Multiple Choice';
      case QuizQuestionType.trueFalse:
        return 'True/False';
      case QuizQuestionType.textInput:
        return 'Text Input';
      case QuizQuestionType.numberInput:
        return 'Number Input';
    }
  }

  static QuizQuestionType fromString(String value) {
    switch (value) {
      case 'multiple_choice':
        return QuizQuestionType.multipleChoice;
      case 'true_false':
        return QuizQuestionType.trueFalse;
      case 'text_input':
        return QuizQuestionType.textInput;
      case 'number_input':
        return QuizQuestionType.numberInput;
      default:
        throw ArgumentError('Unknown quiz question type: $value');
    }
  }
}
