// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserSettings _$UserSettingsFromJson(Map<String, dynamic> json) {
  return _UserSettings.fromJson(json);
}

/// @nodoc
mixin _$UserSettings {
  @JsonKey(name: 'email_notifications')
  bool get emailNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'push_notifications')
  bool get pushNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'comment_notifications')
  bool get commentNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'like_notifications')
  bool get likeNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'follow_notifications')
  bool get followNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'profile_visibility')
  String get profileVisibility => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_email')
  bool get showEmail => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_phone')
  bool get showPhone => throw _privateConstructorUsedError;
  @JsonKey(name: 'content_language')
  String get contentLanguage => throw _privateConstructorUsedError;
  @JsonKey(name: 'posts_per_page')
  int get postsPerPage => throw _privateConstructorUsedError;
  @JsonKey(name: 'auto_play_videos')
  bool get autoPlayVideos => throw _privateConstructorUsedError;
  String get theme => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserSettingsCopyWith<UserSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserSettingsCopyWith<$Res> {
  factory $UserSettingsCopyWith(
          UserSettings value, $Res Function(UserSettings) then) =
      _$UserSettingsCopyWithImpl<$Res, UserSettings>;
  @useResult
  $Res call(
      {@JsonKey(name: 'email_notifications') bool emailNotifications,
      @JsonKey(name: 'push_notifications') bool pushNotifications,
      @JsonKey(name: 'comment_notifications') bool commentNotifications,
      @JsonKey(name: 'like_notifications') bool likeNotifications,
      @JsonKey(name: 'follow_notifications') bool followNotifications,
      @JsonKey(name: 'profile_visibility') String profileVisibility,
      @JsonKey(name: 'show_email') bool showEmail,
      @JsonKey(name: 'show_phone') bool showPhone,
      @JsonKey(name: 'content_language') String contentLanguage,
      @JsonKey(name: 'posts_per_page') int postsPerPage,
      @JsonKey(name: 'auto_play_videos') bool autoPlayVideos,
      String theme});
}

/// @nodoc
class _$UserSettingsCopyWithImpl<$Res, $Val extends UserSettings>
    implements $UserSettingsCopyWith<$Res> {
  _$UserSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailNotifications = null,
    Object? pushNotifications = null,
    Object? commentNotifications = null,
    Object? likeNotifications = null,
    Object? followNotifications = null,
    Object? profileVisibility = null,
    Object? showEmail = null,
    Object? showPhone = null,
    Object? contentLanguage = null,
    Object? postsPerPage = null,
    Object? autoPlayVideos = null,
    Object? theme = null,
  }) {
    return _then(_value.copyWith(
      emailNotifications: null == emailNotifications
          ? _value.emailNotifications
          : emailNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      pushNotifications: null == pushNotifications
          ? _value.pushNotifications
          : pushNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      commentNotifications: null == commentNotifications
          ? _value.commentNotifications
          : commentNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      likeNotifications: null == likeNotifications
          ? _value.likeNotifications
          : likeNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      followNotifications: null == followNotifications
          ? _value.followNotifications
          : followNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      profileVisibility: null == profileVisibility
          ? _value.profileVisibility
          : profileVisibility // ignore: cast_nullable_to_non_nullable
              as String,
      showEmail: null == showEmail
          ? _value.showEmail
          : showEmail // ignore: cast_nullable_to_non_nullable
              as bool,
      showPhone: null == showPhone
          ? _value.showPhone
          : showPhone // ignore: cast_nullable_to_non_nullable
              as bool,
      contentLanguage: null == contentLanguage
          ? _value.contentLanguage
          : contentLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      postsPerPage: null == postsPerPage
          ? _value.postsPerPage
          : postsPerPage // ignore: cast_nullable_to_non_nullable
              as int,
      autoPlayVideos: null == autoPlayVideos
          ? _value.autoPlayVideos
          : autoPlayVideos // ignore: cast_nullable_to_non_nullable
              as bool,
      theme: null == theme
          ? _value.theme
          : theme // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserSettingsImplCopyWith<$Res>
    implements $UserSettingsCopyWith<$Res> {
  factory _$$UserSettingsImplCopyWith(
          _$UserSettingsImpl value, $Res Function(_$UserSettingsImpl) then) =
      __$$UserSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'email_notifications') bool emailNotifications,
      @JsonKey(name: 'push_notifications') bool pushNotifications,
      @JsonKey(name: 'comment_notifications') bool commentNotifications,
      @JsonKey(name: 'like_notifications') bool likeNotifications,
      @JsonKey(name: 'follow_notifications') bool followNotifications,
      @JsonKey(name: 'profile_visibility') String profileVisibility,
      @JsonKey(name: 'show_email') bool showEmail,
      @JsonKey(name: 'show_phone') bool showPhone,
      @JsonKey(name: 'content_language') String contentLanguage,
      @JsonKey(name: 'posts_per_page') int postsPerPage,
      @JsonKey(name: 'auto_play_videos') bool autoPlayVideos,
      String theme});
}

/// @nodoc
class __$$UserSettingsImplCopyWithImpl<$Res>
    extends _$UserSettingsCopyWithImpl<$Res, _$UserSettingsImpl>
    implements _$$UserSettingsImplCopyWith<$Res> {
  __$$UserSettingsImplCopyWithImpl(
      _$UserSettingsImpl _value, $Res Function(_$UserSettingsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailNotifications = null,
    Object? pushNotifications = null,
    Object? commentNotifications = null,
    Object? likeNotifications = null,
    Object? followNotifications = null,
    Object? profileVisibility = null,
    Object? showEmail = null,
    Object? showPhone = null,
    Object? contentLanguage = null,
    Object? postsPerPage = null,
    Object? autoPlayVideos = null,
    Object? theme = null,
  }) {
    return _then(_$UserSettingsImpl(
      emailNotifications: null == emailNotifications
          ? _value.emailNotifications
          : emailNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      pushNotifications: null == pushNotifications
          ? _value.pushNotifications
          : pushNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      commentNotifications: null == commentNotifications
          ? _value.commentNotifications
          : commentNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      likeNotifications: null == likeNotifications
          ? _value.likeNotifications
          : likeNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      followNotifications: null == followNotifications
          ? _value.followNotifications
          : followNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      profileVisibility: null == profileVisibility
          ? _value.profileVisibility
          : profileVisibility // ignore: cast_nullable_to_non_nullable
              as String,
      showEmail: null == showEmail
          ? _value.showEmail
          : showEmail // ignore: cast_nullable_to_non_nullable
              as bool,
      showPhone: null == showPhone
          ? _value.showPhone
          : showPhone // ignore: cast_nullable_to_non_nullable
              as bool,
      contentLanguage: null == contentLanguage
          ? _value.contentLanguage
          : contentLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      postsPerPage: null == postsPerPage
          ? _value.postsPerPage
          : postsPerPage // ignore: cast_nullable_to_non_nullable
              as int,
      autoPlayVideos: null == autoPlayVideos
          ? _value.autoPlayVideos
          : autoPlayVideos // ignore: cast_nullable_to_non_nullable
              as bool,
      theme: null == theme
          ? _value.theme
          : theme // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserSettingsImpl extends _UserSettings {
  const _$UserSettingsImpl(
      {@JsonKey(name: 'email_notifications') this.emailNotifications = true,
      @JsonKey(name: 'push_notifications') this.pushNotifications = true,
      @JsonKey(name: 'comment_notifications') this.commentNotifications = true,
      @JsonKey(name: 'like_notifications') this.likeNotifications = true,
      @JsonKey(name: 'follow_notifications') this.followNotifications = true,
      @JsonKey(name: 'profile_visibility') this.profileVisibility = 'public',
      @JsonKey(name: 'show_email') this.showEmail = false,
      @JsonKey(name: 'show_phone') this.showPhone = false,
      @JsonKey(name: 'content_language') this.contentLanguage = 'en',
      @JsonKey(name: 'posts_per_page') this.postsPerPage = 10,
      @JsonKey(name: 'auto_play_videos') this.autoPlayVideos = true,
      this.theme = 'auto'})
      : super._();

  factory _$UserSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserSettingsImplFromJson(json);

  @override
  @JsonKey(name: 'email_notifications')
  final bool emailNotifications;
  @override
  @JsonKey(name: 'push_notifications')
  final bool pushNotifications;
  @override
  @JsonKey(name: 'comment_notifications')
  final bool commentNotifications;
  @override
  @JsonKey(name: 'like_notifications')
  final bool likeNotifications;
  @override
  @JsonKey(name: 'follow_notifications')
  final bool followNotifications;
  @override
  @JsonKey(name: 'profile_visibility')
  final String profileVisibility;
  @override
  @JsonKey(name: 'show_email')
  final bool showEmail;
  @override
  @JsonKey(name: 'show_phone')
  final bool showPhone;
  @override
  @JsonKey(name: 'content_language')
  final String contentLanguage;
  @override
  @JsonKey(name: 'posts_per_page')
  final int postsPerPage;
  @override
  @JsonKey(name: 'auto_play_videos')
  final bool autoPlayVideos;
  @override
  @JsonKey()
  final String theme;

  @override
  String toString() {
    return 'UserSettings(emailNotifications: $emailNotifications, pushNotifications: $pushNotifications, commentNotifications: $commentNotifications, likeNotifications: $likeNotifications, followNotifications: $followNotifications, profileVisibility: $profileVisibility, showEmail: $showEmail, showPhone: $showPhone, contentLanguage: $contentLanguage, postsPerPage: $postsPerPage, autoPlayVideos: $autoPlayVideos, theme: $theme)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserSettingsImpl &&
            (identical(other.emailNotifications, emailNotifications) ||
                other.emailNotifications == emailNotifications) &&
            (identical(other.pushNotifications, pushNotifications) ||
                other.pushNotifications == pushNotifications) &&
            (identical(other.commentNotifications, commentNotifications) ||
                other.commentNotifications == commentNotifications) &&
            (identical(other.likeNotifications, likeNotifications) ||
                other.likeNotifications == likeNotifications) &&
            (identical(other.followNotifications, followNotifications) ||
                other.followNotifications == followNotifications) &&
            (identical(other.profileVisibility, profileVisibility) ||
                other.profileVisibility == profileVisibility) &&
            (identical(other.showEmail, showEmail) ||
                other.showEmail == showEmail) &&
            (identical(other.showPhone, showPhone) ||
                other.showPhone == showPhone) &&
            (identical(other.contentLanguage, contentLanguage) ||
                other.contentLanguage == contentLanguage) &&
            (identical(other.postsPerPage, postsPerPage) ||
                other.postsPerPage == postsPerPage) &&
            (identical(other.autoPlayVideos, autoPlayVideos) ||
                other.autoPlayVideos == autoPlayVideos) &&
            (identical(other.theme, theme) || other.theme == theme));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      emailNotifications,
      pushNotifications,
      commentNotifications,
      likeNotifications,
      followNotifications,
      profileVisibility,
      showEmail,
      showPhone,
      contentLanguage,
      postsPerPage,
      autoPlayVideos,
      theme);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserSettingsImplCopyWith<_$UserSettingsImpl> get copyWith =>
      __$$UserSettingsImplCopyWithImpl<_$UserSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserSettingsImplToJson(
      this,
    );
  }
}

abstract class _UserSettings extends UserSettings {
  const factory _UserSettings(
      {@JsonKey(name: 'email_notifications') final bool emailNotifications,
      @JsonKey(name: 'push_notifications') final bool pushNotifications,
      @JsonKey(name: 'comment_notifications') final bool commentNotifications,
      @JsonKey(name: 'like_notifications') final bool likeNotifications,
      @JsonKey(name: 'follow_notifications') final bool followNotifications,
      @JsonKey(name: 'profile_visibility') final String profileVisibility,
      @JsonKey(name: 'show_email') final bool showEmail,
      @JsonKey(name: 'show_phone') final bool showPhone,
      @JsonKey(name: 'content_language') final String contentLanguage,
      @JsonKey(name: 'posts_per_page') final int postsPerPage,
      @JsonKey(name: 'auto_play_videos') final bool autoPlayVideos,
      final String theme}) = _$UserSettingsImpl;
  const _UserSettings._() : super._();

  factory _UserSettings.fromJson(Map<String, dynamic> json) =
      _$UserSettingsImpl.fromJson;

  @override
  @JsonKey(name: 'email_notifications')
  bool get emailNotifications;
  @override
  @JsonKey(name: 'push_notifications')
  bool get pushNotifications;
  @override
  @JsonKey(name: 'comment_notifications')
  bool get commentNotifications;
  @override
  @JsonKey(name: 'like_notifications')
  bool get likeNotifications;
  @override
  @JsonKey(name: 'follow_notifications')
  bool get followNotifications;
  @override
  @JsonKey(name: 'profile_visibility')
  String get profileVisibility;
  @override
  @JsonKey(name: 'show_email')
  bool get showEmail;
  @override
  @JsonKey(name: 'show_phone')
  bool get showPhone;
  @override
  @JsonKey(name: 'content_language')
  String get contentLanguage;
  @override
  @JsonKey(name: 'posts_per_page')
  int get postsPerPage;
  @override
  @JsonKey(name: 'auto_play_videos')
  bool get autoPlayVideos;
  @override
  String get theme;
  @override
  @JsonKey(ignore: true)
  _$$UserSettingsImplCopyWith<_$UserSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
