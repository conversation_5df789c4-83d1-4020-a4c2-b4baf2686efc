import 'package:freezed_annotation/freezed_annotation.dart';

part 'maintenance_status.freezed.dart';
part 'maintenance_status.g.dart';

@freezed
class MaintenanceStatus with _$MaintenanceStatus {
  const factory MaintenanceStatus({
    @Default(false) bool isActive,
    @Default('') String title,
    @Default('') String message,
    @Default('') String maintenanceType,
    @Default('') String status,
    DateTime? scheduledStart,
    DateTime? scheduledEnd,
    DateTime? actualStart,
    DateTime? actualEnd,
    @Default(true) bool allowAdminAccess,
    @Default(false) bool allowStaffAccess,
  }) = _MaintenanceStatus;

  factory MaintenanceStatus.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceStatusFromJson(json);
}

@freezed
class FeatureToggle with _$FeatureToggle {
  const factory FeatureToggle({
    @Default('') String name,
    @Default('') String displayName,
    @Default('') String description,
    @Default('') String featureType,
    @Default(true) bool isEnabled,
    @Default(true) bool isGlobal,
    @Default(true) bool enabledForAdmins,
    @Default(true) bool enabledForStaff,
    @Default(true) bool enabledForUsers,
    DateTime? disableStart,
    DateTime? disableEnd,
    @Default('This feature is temporarily unavailable.') String disabledMessage,
  }) = _FeatureToggle;

  factory FeatureToggle.fromJson(Map<String, dynamic> json) =>
      _$FeatureToggleFromJson(json);
}

@freezed
class SystemStatus with _$SystemStatus {
  const factory SystemStatus({
    @Default(false) bool maintenanceActive,
    MaintenanceStatus? activeMaintenance,
    @Default({}) Map<String, FeatureToggle> featureToggles,
    @Default({}) Map<String, bool> featureStatus,
    DateTime? lastChecked,
  }) = _SystemStatus;

  factory SystemStatus.fromJson(Map<String, dynamic> json) =>
      _$SystemStatusFromJson(json);
}
