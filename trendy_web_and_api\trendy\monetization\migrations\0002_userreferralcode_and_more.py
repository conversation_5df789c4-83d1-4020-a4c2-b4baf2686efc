# Generated by Django 5.1.7 on 2025-06-24 14:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('monetization', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserReferralCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(db_index=True, max_length=12, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('usage_count', models.PositiveIntegerField(default=0)),
            ],
        ),
        migrations.RemoveIndex(
            model_name='referralprogram',
            name='monetizatio_referra_26dc66_idx',
        ),
        migrations.RemoveField(
            model_name='referralprogram',
            name='referral_code',
        ),
        migrations.AddField(
            model_name='referralprogram',
            name='referral_code_used',
            field=models.CharField(default='LEGACY', max_length=12),
            preserve_default=False,
        ),
        migrations.AddIndex(
            model_name='referralprogram',
            index=models.Index(fields=['referral_code_used'], name='monetizatio_referra_cb5c8b_idx'),
        ),
        migrations.AddIndex(
            model_name='referralprogram',
            index=models.Index(fields=['referee_joined_at'], name='monetizatio_referee_059117_idx'),
        ),
        migrations.AddField(
            model_name='userreferralcode',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='referral_code_obj', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='userreferralcode',
            index=models.Index(fields=['code'], name='monetizatio_code_1ffd65_idx'),
        ),
        migrations.AddIndex(
            model_name='userreferralcode',
            index=models.Index(fields=['user'], name='monetizatio_user_id_f76951_idx'),
        ),
    ]
