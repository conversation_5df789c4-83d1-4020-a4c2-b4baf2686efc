import 'package:freezed_annotation/freezed_annotation.dart';

part 'code_playground.freezed.dart';
part 'code_playground.g.dart';

@freezed
class CodePlayground with _$CodePlayground {
  const factory CodePlayground({
    required int id,
    @Default('python') String language,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'initial_code') @Default('') String initialCode,
    @JsonKey(name: 'expected_output') @Default('') String expectedOutput,
    @Default('') String instructions,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_editable') @Default(true) bool isEditable,
    @<PERSON><PERSON><PERSON>ey(name: 'show_line_numbers') @Default(true) bool showLineNumbers,
    @J<PERSON><PERSON>ey(name: 'created_at') required DateTime createdAt,
  }) = _CodePlayground;

  factory CodePlayground.fromJson(Map<String, dynamic> json) => _$CodePlaygroundFromJson(json);
}

enum CodeLanguage {
  python,
  javascript,
  html,
  css,
  sql,
  json,
  markdown,
}

extension CodeLanguageExtension on CodeLanguage {
  String get value {
    switch (this) {
      case CodeLanguage.python:
        return 'python';
      case CodeLanguage.javascript:
        return 'javascript';
      case CodeLanguage.html:
        return 'html';
      case CodeLanguage.css:
        return 'css';
      case CodeLanguage.sql:
        return 'sql';
      case CodeLanguage.json:
        return 'json';
      case CodeLanguage.markdown:
        return 'markdown';
    }
  }

  String get displayName {
    switch (this) {
      case CodeLanguage.python:
        return 'Python';
      case CodeLanguage.javascript:
        return 'JavaScript';
      case CodeLanguage.html:
        return 'HTML';
      case CodeLanguage.css:
        return 'CSS';
      case CodeLanguage.sql:
        return 'SQL';
      case CodeLanguage.json:
        return 'JSON';
      case CodeLanguage.markdown:
        return 'Markdown';
    }
  }

  String get fileExtension {
    switch (this) {
      case CodeLanguage.python:
        return '.py';
      case CodeLanguage.javascript:
        return '.js';
      case CodeLanguage.html:
        return '.html';
      case CodeLanguage.css:
        return '.css';
      case CodeLanguage.sql:
        return '.sql';
      case CodeLanguage.json:
        return '.json';
      case CodeLanguage.markdown:
        return '.md';
    }
  }

  static CodeLanguage fromString(String value) {
    switch (value) {
      case 'python':
        return CodeLanguage.python;
      case 'javascript':
        return CodeLanguage.javascript;
      case 'html':
        return CodeLanguage.html;
      case 'css':
        return CodeLanguage.css;
      case 'sql':
        return CodeLanguage.sql;
      case 'json':
        return CodeLanguage.json;
      case 'markdown':
        return CodeLanguage.markdown;
      default:
        throw ArgumentError('Unknown code language: $value');
    }
  }
}
