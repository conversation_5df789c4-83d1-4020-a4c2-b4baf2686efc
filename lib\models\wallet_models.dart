import 'package:freezed_annotation/freezed_annotation.dart';

part 'wallet_models.freezed.dart';
part 'wallet_models.g.dart';

@freezed
class WalletOverview with _$WalletOverview {
  const factory WalletOverview({
    required WalletInfo wallet,
    @Json<PERSON>ey(name: 'recent_transactions') @Default([]) List<WalletTransaction> recentTransactions,
    @Json<PERSON>ey(name: 'pending_deposits') @Default(0) int pendingDeposits,
    @<PERSON>son<PERSON>ey(name: 'pending_withdrawals') @Default(0) int pendingWithdrawals,
  }) = _WalletOverview;

  factory WalletOverview.fromJson(Map<String, dynamic> json) =>
      _$WalletOverviewFromJson(json);
}

@freezed
class WalletInfo with _$WalletInfo {
  const factory WalletInfo({
    @Default(0.0) double balance,
    @Default('\$0.00') @<PERSON>sonKey(name: 'formatted_balance') String formattedBalance,
    @Default(true) @<PERSON>sonKey(name: 'is_active') bool isActive,
    @Default(false) @JsonKey(name: 'is_verified') bool isVerified,
    @Default(0.0) @Json<PERSON>ey(name: 'daily_spent') double dailySpent,
    @Default(0.0) @JsonKey(name: 'monthly_spent') double monthlySpent,
    @Default(100.0) @JsonKey(name: 'daily_limit') double dailyLimit,
    @Default(1000.0) @JsonKey(name: 'monthly_limit') double monthlyLimit,
    @Default(0) @JsonKey(name: 'total_transactions') int totalTransactions,
    @Default(0) @JsonKey(name: 'total_credits') int totalCredits,
    @Default(0) @JsonKey(name: 'total_debits') int totalDebits,
    @Default(0.0) @JsonKey(name: 'total_deposited') double totalDeposited,
    @Default(0.0) @JsonKey(name: 'total_spent') double totalSpent,
  }) = _WalletInfo;

  factory WalletInfo.fromJson(Map<String, dynamic> json) =>
      _$WalletInfoFromJson(json);
}

@freezed
class WalletTransaction with _$WalletTransaction {
  const factory WalletTransaction({
    @Default('') String id,
    @Default('') String type,
    @Default('') String purpose,
    @Default('0.00') String amount,
    @Default('0.00') String formattedAmount,
    @Default('0.00') String balanceAfter,
    @Default('pending') String status,
    @Default('') String description,
    @Default('') String createdAt,
    String? completedAt,
    String? paymentMethod,
    String? referenceId,
  }) = _WalletTransaction;

  factory WalletTransaction.fromJson(Map<String, dynamic> json) =>
      _$WalletTransactionFromJson(json);
}

@freezed
class WalletDepositRequest with _$WalletDepositRequest {
  const factory WalletDepositRequest({
    @Default('') String depositRequestId,
    @Default({}) Map<String, dynamic> paymentData,
    @Default('') String message,
  }) = _WalletDepositRequest;

  factory WalletDepositRequest.fromJson(Map<String, dynamic> json) =>
      _$WalletDepositRequestFromJson(json);
}

@freezed
class WalletWithdrawalRequest with _$WalletWithdrawalRequest {
  const factory WalletWithdrawalRequest({
    @Default('') String withdrawalRequestId,
    @Default('') String message,
  }) = _WalletWithdrawalRequest;

  factory WalletWithdrawalRequest.fromJson(Map<String, dynamic> json) =>
      _$WalletWithdrawalRequestFromJson(json);
}

@freezed
class WalletSettings with _$WalletSettings {
  const factory WalletSettings({
    @Default('5.00') String minimumDeposit,
    @Default('500.00') String maximumDeposit,
    @Default('10.00') String minimumWithdrawal,
    @Default('1000.00') String maximumWithdrawal,
    @Default('0.00') String withdrawalFeePercentage,
    @Default('0.00') String withdrawalFeeFixed,
    @Default('0.00') String depositFeePercentage,
    @Default(true) bool walletsEnabled,
    @Default(true) bool depositsEnabled,
    @Default(true) bool withdrawalsEnabled,
    @Default(false) bool requireVerification,
    @Default('100.00') String dailyWithdrawalLimit,
    @Default('1000.00') String monthlyWithdrawalLimit,
  }) = _WalletSettings;

  factory WalletSettings.fromJson(Map<String, dynamic> json) =>
      _$WalletSettingsFromJson(json);

  // Helper factory to handle snake_case conversion from backend
  factory WalletSettings.fromBackendJson(Map<String, dynamic> json) {
    return WalletSettings(
      minimumDeposit: json['minimum_deposit']?.toString() ?? '5.00',
      maximumDeposit: json['maximum_deposit']?.toString() ?? '500.00',
      minimumWithdrawal: json['minimum_withdrawal']?.toString() ?? '10.00',
      maximumWithdrawal: json['maximum_withdrawal']?.toString() ?? '1000.00',
      withdrawalFeePercentage: json['withdrawal_fee_percentage']?.toString() ?? '0.00',
      withdrawalFeeFixed: json['withdrawal_fee_fixed']?.toString() ?? '0.00',
      depositFeePercentage: json['deposit_fee_percentage']?.toString() ?? '0.00',
      walletsEnabled: json['wallets_enabled'] ?? true,
      depositsEnabled: json['deposits_enabled'] ?? true,
      withdrawalsEnabled: json['withdrawals_enabled'] ?? true,
      requireVerification: json['require_verification'] ?? false,
      dailyWithdrawalLimit: json['daily_withdrawal_limit']?.toString() ?? '100.00',
      monthlyWithdrawalLimit: json['monthly_withdrawal_limit']?.toString() ?? '1000.00',
    );
  }
}

@freezed
class WalletSpendResult with _$WalletSpendResult {
  const factory WalletSpendResult({
    @Default('') String transactionId,
    @Default('0.00') String newBalance,
    @Default('0.00') String formattedBalance,
    @Default('') String message,
  }) = _WalletSpendResult;

  factory WalletSpendResult.fromJson(Map<String, dynamic> json) =>
      _$WalletSpendResultFromJson(json);
}

@freezed
class WalletState with _$WalletState {
  const factory WalletState({
    WalletOverview? overview,
    WalletSettings? settings,
    @Default(false) bool isLoading,
    String? error,
    @Default([]) List<WalletTransaction> transactions,
    @Default(false) bool hasMoreTransactions,
  }) = _WalletState;

  factory WalletState.fromJson(Map<String, dynamic> json) =>
      _$WalletStateFromJson(json);
}
