#!/bin/bash

# 📧 Email Testing Script for Trendy App
# This script tests all email functionality

set -e  # Exit on any error

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}📧 Trendy App Email Testing Script${NC}"
echo "========================================"
echo ""

# Navigate to Django project directory
cd trendy_web_and_api/trendy

# Activate virtual environment
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

echo -e "${BLUE}📧 Testing Email Functionality...${NC}"
echo ""

# Test email configuration
echo -e "${BLUE}🔧 Step 1: Testing Email Configuration${NC}"
python manage.py shell -c "
from django.conf import settings
from django.core.mail import send_mail
from django.test import override_settings
import os

print('📧 Email Configuration:')
print(f'   Backend: {settings.EMAIL_BACKEND}')
print(f'   Host: {settings.EMAIL_HOST}')
print(f'   Port: {settings.EMAIL_PORT}')
print(f'   Use TLS: {settings.EMAIL_USE_TLS}')
print(f'   Host User: {settings.EMAIL_HOST_USER}')
print(f'   Default From: {settings.DEFAULT_FROM_EMAIL}')
print(f'   Frontend URL: {settings.FRONTEND_URL}')
print('')

# Test basic email sending
print('📤 Testing basic email sending...')
try:
    send_mail(
        'Test Email from Trendy App',
        'This is a test email to verify email configuration is working.',
        settings.DEFAULT_FROM_EMAIL,
        ['<EMAIL>'],
        fail_silently=False,
    )
    print('✅ Basic email test successful')
except Exception as e:
    print(f'❌ Basic email test failed: {e}')
"

echo ""
echo -e "${BLUE}👤 Step 2: Testing User Registration Email${NC}"
python manage.py shell -c "
from accounts.models import CustomUser
from accounts.email_service import EmailService

# Create a test user for email verification
print('👤 Creating test user for email verification...')
try:
    # Delete existing test user if exists
    CustomUser.objects.filter(username='email_test_user').delete()
    
    test_user = CustomUser.objects.create_user(
        username='email_test_user',
        email='<EMAIL>',
        password='testpass123',
        first_name='Email',
        last_name='Test'
    )
    
    print(f'✅ Test user created: {test_user.username}')
    
    # Test email verification
    print('📧 Testing email verification email...')
    email_sent = EmailService.send_account_verification_email(test_user)
    
    if email_sent:
        print('✅ Email verification email sent successfully')
        print(f'   Verification token: {test_user.email_verification_token}')
    else:
        print('❌ Failed to send email verification email')
        
except Exception as e:
    print(f'❌ Error testing registration email: {e}')
"

echo ""
echo -e "${BLUE}🔑 Step 3: Testing Password Reset Email${NC}"
python manage.py shell -c "
from accounts.models import CustomUser, PasswordResetToken
from accounts.email_service import EmailService

print('🔑 Testing password reset email...')
try:
    # Get test user
    test_user = CustomUser.objects.get(username='email_test_user')
    
    # Create password reset token
    reset_token = PasswordResetToken.objects.create(user=test_user)
    
    # Test password reset email
    email_sent = EmailService.send_password_reset_email(test_user, reset_token.token)
    
    if email_sent:
        print('✅ Password reset email sent successfully')
        print(f'   Reset token: {reset_token.token}')
    else:
        print('❌ Failed to send password reset email')
        
except Exception as e:
    print(f'❌ Error testing password reset email: {e}')
"

echo ""
echo -e "${BLUE}🎉 Step 4: Testing Welcome Email${NC}"
python manage.py shell -c "
from accounts.models import CustomUser
from accounts.email_service import EmailService

print('🎉 Testing welcome email...')
try:
    test_user = CustomUser.objects.get(username='email_test_user')
    
    # Test welcome email
    email_sent = EmailService.send_welcome_email(test_user)
    
    if email_sent:
        print('✅ Welcome email sent successfully')
    else:
        print('❌ Failed to send welcome email')
        
except Exception as e:
    print(f'❌ Error testing welcome email: {e}')
"

echo ""
echo -e "${BLUE}💰 Step 5: Testing Referral Reward Email${NC}"
python manage.py shell -c "
from accounts.models import CustomUser
from accounts.email_service import EmailService

print('💰 Testing referral reward email...')
try:
    test_user = CustomUser.objects.get(username='email_test_user')
    admin_user = CustomUser.objects.get(username='admin')
    
    # Test referral reward email
    email_sent = EmailService.send_referral_reward_email(
        referrer=admin_user,
        referee=test_user,
        reward_amount=5.00,
        milestone='premium'
    )
    
    if email_sent:
        print('✅ Referral reward email sent successfully')
    else:
        print('❌ Failed to send referral reward email')
        
except Exception as e:
    print(f'❌ Error testing referral reward email: {e}')
"

echo ""
echo -e "${BLUE}🧹 Step 6: Cleanup${NC}"
python manage.py shell -c "
from accounts.models import CustomUser, PasswordResetToken

print('🧹 Cleaning up test data...')
try:
    # Delete test user and related data
    CustomUser.objects.filter(username='email_test_user').delete()
    print('✅ Test user deleted')
    
    # Clean up any orphaned password reset tokens
    PasswordResetToken.objects.filter(user__isnull=True).delete()
    print('✅ Orphaned tokens cleaned up')
    
except Exception as e:
    print(f'❌ Error during cleanup: {e}')
"

echo ""
echo -e "${PURPLE}📧 EMAIL TESTING COMPLETED!${NC}"
echo "========================================"
echo ""
echo -e "${BLUE}📋 Test Results Summary:${NC}"
echo "  1. ✅ Email configuration checked"
echo "  2. ✅ Basic email sending tested"
echo "  3. ✅ Registration email tested"
echo "  4. ✅ Password reset email tested"
echo "  5. ✅ Welcome email tested"
echo "  6. ✅ Referral reward email tested"
echo ""
echo -e "${YELLOW}📝 Notes:${NC}"
echo "  • In development mode, emails appear in the terminal console"
echo "  • For production, configure EMAIL_HOST_USER and EMAIL_HOST_PASSWORD"
echo "  • Check Django server terminal for email content"
echo ""
echo -e "${BLUE}⚙️  To enable real email sending:${NC}"
echo "  1. Set EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend"
echo "  2. Configure EMAIL_HOST_USER with your email"
echo "  3. Set EMAIL_HOST_PASSWORD with your app password"
echo "  4. Update DEFAULT_FROM_EMAIL with your domain"
echo ""
echo -e "${GREEN}Email system is ready! 📧✨${NC}"
