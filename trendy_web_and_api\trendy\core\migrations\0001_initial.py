# Generated by Django 4.2.23 on 2025-08-03 22:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="FeatureToggle",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        help_text="Feature identifier", max_length=100, unique=True
                    ),
                ),
                (
                    "display_name",
                    models.Char<PERSON>ield(
                        help_text="Human-readable feature name", max_length=200
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, help_text="Feature description"),
                ),
                (
                    "feature_type",
                    models.CharField(
                        choices=[
                            ("api", "API Endpoint"),
                            ("ui", "UI Feature"),
                            ("service", "Background Service"),
                            ("integration", "Third-party Integration"),
                            ("experimental", "Experimental Feature"),
                        ],
                        default="ui",
                        max_length=20,
                    ),
                ),
                (
                    "is_enabled",
                    models.BooleanField(
                        default=True, help_text="Is feature currently enabled"
                    ),
                ),
                (
                    "is_global",
                    models.BooleanField(default=True, help_text="Apply to all users"),
                ),
                (
                    "enabled_for_admins",
                    models.BooleanField(
                        default=True, help_text="Enable for admin users"
                    ),
                ),
                (
                    "enabled_for_staff",
                    models.BooleanField(
                        default=True, help_text="Enable for staff users"
                    ),
                ),
                (
                    "enabled_for_users",
                    models.BooleanField(
                        default=True, help_text="Enable for regular users"
                    ),
                ),
                (
                    "disable_start",
                    models.DateTimeField(
                        blank=True, help_text="When to disable feature", null=True
                    ),
                ),
                (
                    "disable_end",
                    models.DateTimeField(
                        blank=True, help_text="When to re-enable feature", null=True
                    ),
                ),
                (
                    "disabled_message",
                    models.TextField(
                        default="This feature is temporarily unavailable.",
                        help_text="Message shown when feature is disabled",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Feature Toggle",
                "verbose_name_plural": "Feature Toggles",
                "ordering": ["display_name"],
            },
        ),
        migrations.CreateModel(
            name="SystemMaintenance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(help_text="Maintenance title", max_length=200),
                ),
                (
                    "description",
                    models.TextField(help_text="Detailed description of maintenance"),
                ),
                (
                    "maintenance_type",
                    models.CharField(
                        choices=[
                            ("full", "Full System Maintenance"),
                            ("partial", "Partial Feature Maintenance"),
                            ("database", "Database Maintenance"),
                            ("upgrade", "System Upgrade"),
                            ("emergency", "Emergency Maintenance"),
                        ],
                        default="partial",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                (
                    "scheduled_start",
                    models.DateTimeField(
                        help_text="When maintenance is scheduled to start"
                    ),
                ),
                (
                    "scheduled_end",
                    models.DateTimeField(
                        help_text="When maintenance is scheduled to end"
                    ),
                ),
                ("actual_start", models.DateTimeField(blank=True, null=True)),
                ("actual_end", models.DateTimeField(blank=True, null=True)),
                (
                    "is_active",
                    models.BooleanField(
                        default=False, help_text="Is maintenance currently active"
                    ),
                ),
                (
                    "allow_admin_access",
                    models.BooleanField(
                        default=True, help_text="Allow admin users during maintenance"
                    ),
                ),
                (
                    "allow_staff_access",
                    models.BooleanField(
                        default=False, help_text="Allow staff users during maintenance"
                    ),
                ),
                (
                    "public_message",
                    models.TextField(
                        default="System is currently under maintenance. Please try again later.",
                        help_text="Message shown to users during maintenance",
                    ),
                ),
                (
                    "admin_message",
                    models.TextField(
                        blank=True, help_text="Internal message for admin users"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "System Maintenance",
                "verbose_name_plural": "System Maintenance",
                "ordering": ["-scheduled_start"],
            },
        ),
        migrations.CreateModel(
            name="MaintenanceLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "log_type",
                    models.CharField(
                        choices=[
                            ("start", "Maintenance Started"),
                            ("end", "Maintenance Ended"),
                            ("update", "Settings Updated"),
                            ("feature_toggle", "Feature Toggled"),
                            ("error", "Error Occurred"),
                            ("info", "Information"),
                        ],
                        max_length=20,
                    ),
                ),
                ("message", models.TextField()),
                ("details", models.JSONField(blank=True, default=dict)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "feature_toggle",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="core.featuretoggle",
                    ),
                ),
                (
                    "maintenance",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="core.systemmaintenance",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Maintenance Log",
                "verbose_name_plural": "Maintenance Logs",
                "ordering": ["-timestamp"],
            },
        ),
    ]
