/// Media optimization and gallery service for Trendy Blog Platform
/// Handles API calls for media optimization, progressive loading, and gallery data

import 'dart:convert';
import 'dart:ui' show Size;
import 'package:http/http.dart' as http;
import '../models/media_models.dart';
import '../config/api_config.dart';

/// Helper function to format media URLs
String _formatMediaUrl(String? url) {
  if (url == null || url.isEmpty) return '';

  // If it's already a full URL, return as is
  if (url.startsWith('http')) {
    return url;
  }

  // Use ApiConfig to format the URL
  return ApiConfig.getMediaUrl(url);
}

class MediaService {
  static const String baseUrl = ApiConfig.baseUrl;

  /// Get optimized media for a specific post
  static Future<List<OptimizedMedia>> getOptimizedMedia(int postId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/posts/$postId/media/optimized/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final mediaItems = data['media_items'] as List<dynamic>;
        
        return mediaItems
            .map((item) => OptimizedMedia.fromJson(item))
            .toList();
      } else {
        throw Exception('Failed to load optimized media: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching optimized media: $e');
    }
  }

  /// Get media gallery data for full-screen viewing
  static Future<MediaGalleryData> getMediaGallery(int postId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/posts/$postId/media/gallery/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return MediaGalleryData.fromJson(data);
      } else {
        throw Exception('Failed to load media gallery: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching media gallery: $e');
    }
  }

  /// Get progressive loading data for a specific media item
  static Future<ProgressiveLoadingData> getProgressiveData(int mediaId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/media/$mediaId/progressive/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ProgressiveLoadingData.fromJson(data);
      } else {
        throw Exception('Failed to load progressive data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching progressive data: $e');
    }
  }

  /// Trigger media optimization (requires authentication)
  static Future<Map<String, dynamic>> optimizeMedia(
    int mediaId, 
    String authToken
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/v1/media/$mediaId/optimize/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $authToken',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to optimize media: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error optimizing media: $e');
    }
  }

  /// Get batch optimization status for multiple media items
  static Future<Map<String, dynamic>> getBatchOptimizationStatus(
    List<int> mediaIds
  ) async {
    try {
      final idsString = mediaIds.join(',');
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/media/batch-status/?ids=$idsString'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load batch status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching batch status: $e');
    }
  }

  /// Get overall media optimization statistics
  static Future<Map<String, dynamic>> getOptimizationStats() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/media/stats/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load optimization stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching optimization stats: $e');
    }
  }

  /// Preload images for better performance
  static Future<void> preloadImages(
    List<String> imageUrls,
    Function(String url)? onImageLoaded,
  ) async {
    for (final url in imageUrls) {
      try {
        final response = await http.head(Uri.parse(url));
        if (response.statusCode == 200) {
          onImageLoaded?.call(url);
        }
      } catch (e) {
        // Silently fail for preloading
        print('Failed to preload image: $url');
      }
    }
  }

  /// Get image dimensions without downloading the full image
  static Future<Size?> getImageDimensions(String imageUrl) async {
    try {
      final response = await http.head(Uri.parse(imageUrl));
      
      // Try to get dimensions from headers (if server provides them)
      final contentLength = response.headers['content-length'];
      if (contentLength != null) {
        // This is a simplified approach - in practice, you might need
        // to download a small portion of the image to get dimensions
        return null;
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Check if URL is accessible
  static Future<bool> isUrlAccessible(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Get optimal image URL based on device capabilities
  static String getOptimalImageUrl(
    OptimizedMedia media, {
    required double devicePixelRatio,
    required double viewportWidth,
    required double viewportHeight,
  }) {
    if (!media.isOptimized || media.optimizedUrls == null) {
      return media.src ?? '';
    }

    // Calculate required resolution
    final requiredWidth = viewportWidth * devicePixelRatio;
    final requiredHeight = viewportHeight * devicePixelRatio;
    final requiredPixels = requiredWidth * requiredHeight;

    // Choose appropriate size based on required resolution
    if (requiredPixels < 400 * 400) {
      return media.optimizedUrls!['small'] ?? 
             media.optimizedUrls!['thumbnail'] ?? 
             media.src ?? '';
    } else if (requiredPixels < 800 * 600) {
      return media.optimizedUrls!['medium'] ?? 
             media.optimizedUrls!['small'] ?? 
             media.src ?? '';
    } else {
      return media.optimizedUrls!['large'] ?? 
             media.optimizedUrls!['medium'] ?? 
             media.src ?? '';
    }
  }

  /// Generate srcset string for responsive images
  static String generateSrcSet(Map<String, String> optimizedUrls) {
    final srcsetParts = <String>[];
    
    final sizeWidths = {
      'small': '400w',
      'medium': '800w',
      'large': '1200w',
    };

    for (final entry in sizeWidths.entries) {
      final sizeName = entry.key;
      final width = entry.value;
      
      if (optimizedUrls.containsKey(sizeName)) {
        srcsetParts.add('${optimizedUrls[sizeName]} $width');
      }
    }

    return srcsetParts.join(', ');
  }

  /// Calculate loading priority based on viewport position
  static LoadingPriority calculateLoadingPriority({
    required int itemIndex,
    required int currentIndex,
    required int totalItems,
  }) {
    final distance = (itemIndex - currentIndex).abs();
    
    if (distance == 0) {
      return LoadingPriority.high;
    } else if (distance == 1) {
      return LoadingPriority.medium;
    } else if (distance <= 3) {
      return LoadingPriority.low;
    } else {
      return LoadingPriority.lazy;
    }
  }
}

enum LoadingPriority {
  high,    // Current item
  medium,  // Adjacent items
  low,     // Nearby items
  lazy,    // Distant items
}
