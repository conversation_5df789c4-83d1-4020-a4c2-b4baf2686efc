"""
Celery tasks for automated wallet operations
"""

from celery import shared_task
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

@shared_task
def auto_complete_deposit(deposit_request_id):
    """
    Automatically complete a deposit after verification delay
    """
    try:
        from .models import WalletDepositRequest
        from .services.automated_transactions import AutomatedTransactionService
        
        deposit_request = WalletDepositRequest.objects.get(id=deposit_request_id)
        
        if deposit_request.status == 'processing':
            # Auto-complete the deposit
            result = AutomatedTransactionService.complete_deposit(deposit_request_id)
            
            if result['success']:
                logger.info(f"Auto-completed deposit {deposit_request_id}")
            else:
                logger.error(f"Failed to auto-complete deposit {deposit_request_id}: {result['message']}")
        
    except Exception as e:
        logger.error(f"Error in auto_complete_deposit task: {str(e)}")

@shared_task
def cleanup_expired_verification_codes():
    """
    Clean up expired verification codes
    """
    try:
        from .models import TransactionVerificationCode
        
        expired_codes = TransactionVerificationCode.objects.filter(
            expires_at__lt=timezone.now(),
            is_used=False
        )
        
        count = expired_codes.count()
        expired_codes.delete()
        
        logger.info(f"Cleaned up {count} expired verification codes")
        
    except Exception as e:
        logger.error(f"Error in cleanup_expired_verification_codes task: {str(e)}")

@shared_task
def process_pending_withdrawals():
    """
    Process pending withdrawals that have been verified
    """
    try:
        from .models import WalletWithdrawalRequest
        from .services.automated_transactions import AutomatedTransactionService
        
        # Find withdrawals that have been processing for more than 5 minutes
        cutoff_time = timezone.now() - timedelta(minutes=5)
        
        pending_withdrawals = WalletWithdrawalRequest.objects.filter(
            status='processing',
            created_at__lt=cutoff_time
        )
        
        for withdrawal in pending_withdrawals:
            # Check if verification code was used
            from .models import TransactionVerificationCode
            
            verification = TransactionVerificationCode.objects.filter(
                transaction_id=str(withdrawal.id),
                transaction_type='withdrawal',
                is_used=True
            ).first()
            
            if verification:
                # Complete the withdrawal
                result = AutomatedTransactionService.complete_withdrawal(str(withdrawal.id))
                
                if result['success']:
                    logger.info(f"Auto-completed withdrawal {withdrawal.id}")
                else:
                    logger.error(f"Failed to auto-complete withdrawal {withdrawal.id}: {result['message']}")
        
    except Exception as e:
        logger.error(f"Error in process_pending_withdrawals task: {str(e)}")

@shared_task
def send_daily_wallet_summary():
    """
    Send daily wallet summary to users with recent activity
    """
    try:
        from django.contrib.auth import get_user_model
        from .models import WalletTransaction
        from django.core.mail import send_mail
        from django.template.loader import render_to_string
        from django.utils.html import strip_tags
        from django.conf import settings
        
        User = get_user_model()
        yesterday = timezone.now() - timedelta(days=1)
        
        # Find users with transactions in the last 24 hours
        users_with_activity = User.objects.filter(
            wallet__transactions__created_at__gte=yesterday
        ).distinct()
        
        for user in users_with_activity:
            # Get recent transactions
            recent_transactions = WalletTransaction.objects.filter(
                wallet__user=user,
                created_at__gte=yesterday
            ).order_by('-created_at')
            
            if recent_transactions.exists():
                # Calculate totals
                total_credits = sum(
                    t.amount for t in recent_transactions 
                    if t.transaction_type == 'credit'
                )
                total_debits = sum(
                    t.amount for t in recent_transactions 
                    if t.transaction_type == 'debit'
                )
                
                # Send summary email
                context = {
                    'user': user,
                    'transactions': recent_transactions,
                    'total_credits': total_credits,
                    'total_debits': total_debits,
                    'current_balance': user.wallet.balance,
                    'date': yesterday.date()
                }
                
                subject = f'Trendy - Daily Wallet Summary for {yesterday.strftime("%B %d, %Y")}'
                html_message = render_to_string('emails/daily_wallet_summary.html', context)
                plain_message = strip_tags(html_message)
                
                send_mail(
                    subject=subject,
                    message=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_message,
                    fail_silently=True
                )
                
                logger.info(f"Sent daily wallet summary to {user.email}")
        
    except Exception as e:
        logger.error(f"Error in send_daily_wallet_summary task: {str(e)}")

@shared_task
def detect_suspicious_activity():
    """
    Detect and flag suspicious wallet activity
    """
    try:
        from .models import WalletTransaction, UserWallet
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        last_hour = timezone.now() - timedelta(hours=1)
        
        # Check for unusual activity patterns
        suspicious_users = []
        
        # Pattern 1: Too many transactions in short time
        users_with_many_transactions = User.objects.filter(
            wallet__transactions__created_at__gte=last_hour
        ).annotate(
            transaction_count=models.Count('wallet__transactions')
        ).filter(transaction_count__gte=10)
        
        for user in users_with_many_transactions:
            suspicious_users.append({
                'user': user,
                'reason': 'High transaction frequency',
                'count': user.transaction_count
            })
        
        # Pattern 2: Large withdrawal amounts
        large_withdrawals = WalletTransaction.objects.filter(
            created_at__gte=last_hour,
            transaction_type='debit',
            purpose='withdrawal',
            amount__gte=100
        )
        
        for transaction in large_withdrawals:
            suspicious_users.append({
                'user': transaction.wallet.user,
                'reason': 'Large withdrawal',
                'amount': transaction.amount
            })
        
        # Send alerts to admins if suspicious activity detected
        if suspicious_users:
            # Send email to admins
            admin_emails = User.objects.filter(is_staff=True).values_list('email', flat=True)
            
            context = {
                'suspicious_activities': suspicious_users,
                'timestamp': timezone.now()
            }
            
            subject = 'Trendy - Suspicious Wallet Activity Detected'
            html_message = render_to_string('emails/suspicious_activity_alert.html', context)
            plain_message = strip_tags(html_message)
            
            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=list(admin_emails),
                html_message=html_message,
                fail_silently=True
            )
            
            logger.warning(f"Detected {len(suspicious_users)} suspicious activities")
        
    except Exception as e:
        logger.error(f"Error in detect_suspicious_activity task: {str(e)}")

@shared_task
def auto_reward_achievements():
    """
    Automatically reward users for achievements and add to wallet
    """
    try:
        from gamification.models import UserBadge, Badge
        from .services.automated_transactions import AutomatedTransactionService
        from decimal import Decimal
        
        # Find recently earned badges that haven't been rewarded
        recent_badges = UserBadge.objects.filter(
            earned_at__gte=timezone.now() - timedelta(hours=1),
            rewarded=False
        )
        
        for user_badge in recent_badges:
            # Calculate reward amount based on badge rarity
            reward_amounts = {
                'common': Decimal('1.00'),
                'uncommon': Decimal('2.50'),
                'rare': Decimal('5.00'),
                'epic': Decimal('10.00'),
                'legendary': Decimal('25.00')
            }
            
            reward_amount = reward_amounts.get(user_badge.badge.rarity, Decimal('1.00'))
            
            # Add reward to user's wallet
            wallet = user_badge.user.wallet
            old_balance = wallet.balance
            wallet.balance += reward_amount
            wallet.save()
            
            # Create transaction record
            from .models import WalletTransaction
            
            WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='credit',
                purpose='reward',
                amount=reward_amount,
                balance_before=old_balance,
                balance_after=wallet.balance,
                status='completed',
                description=f'Achievement reward: {user_badge.badge.name}',
                external_transaction_id=f'achievement_{user_badge.id}'
            )
            
            # Mark as rewarded
            user_badge.rewarded = True
            user_badge.save()
            
            # Send notification email
            AutomatedTransactionService.send_achievement_reward_email(
                user_badge.user,
                user_badge.badge.name,
                reward_amount,
                wallet.balance
            )
            
            logger.info(f"Rewarded ${reward_amount} to {user_badge.user.username} for {user_badge.badge.name}")
        
    except Exception as e:
        logger.error(f"Error in auto_reward_achievements task: {str(e)}")
