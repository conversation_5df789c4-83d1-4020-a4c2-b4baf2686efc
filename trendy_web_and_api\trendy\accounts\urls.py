from django.urls import path
from . import views, role_views

app_name = 'accounts'

urlpatterns = [
    # Authentication
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    
    # Profile
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # Password Reset
    path('password-reset/', views.PasswordResetRequestView.as_view(), name='password_reset'),
    path('password-reset-confirm/', views.PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    
    # Email Verification
    path('verify-email/', views.EmailVerificationView.as_view(), name='verify_email'),
    path('resend-verification/', views.ResendVerificationView.as_view(), name='resend_verification'),
    
    # Settings
    path('settings/', views.UserSettingsView.as_view(), name='settings'),
    
    # Notifications
    path('notifications/', views.NotificationListView.as_view(), name='notifications'),
    path('notifications/<int:pk>/', views.NotificationDetailView.as_view(), name='notification_detail'),
    path('notifications/mark-all-read/', views.MarkAllNotificationsReadView.as_view(), name='mark_all_read'),
    path('notifications/count/', views.notification_count, name='notification_count'),

    # Welcome Bonus
    path('welcome-bonus/', views.get_welcome_bonus_info, name='welcome_bonus'),

    # Public user profiles
    path('users/<str:username>/', views.get_user_profile, name='user_profile'),
    path('users/<str:username>/posts/', views.get_user_posts, name='user_posts'),

    # Role Management API
    path('permissions/', role_views.user_permissions, name='user_permissions'),
    path('check-content-permission/', role_views.check_content_creation_permission, name='check_content_permission'),
    path('admin/users/by-role/', role_views.list_users_by_role, name='list_users_by_role'),
    path('admin/users/<int:user_id>/promote/', role_views.promote_user_to_content_creator, name='promote_user'),
    path('admin/users/<int:user_id>/demote/', role_views.demote_user_to_regular, name='demote_user'),

    # Advanced Permission Management API
    path('admin/users/bulk-promote/', role_views.bulk_promote_users, name='bulk_promote_users'),
    path('admin/users/bulk-demote/', role_views.bulk_demote_users, name='bulk_demote_users'),
    path('admin/analytics/roles/', role_views.role_analytics, name='role_analytics'),
    path('admin/audit/permissions/', role_views.permission_audit_log, name='permission_audit_log'),
    path('admin/users/search/', role_views.search_users_by_criteria, name='search_users_by_criteria'),
]
