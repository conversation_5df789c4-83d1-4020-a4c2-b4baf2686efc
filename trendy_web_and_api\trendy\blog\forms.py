from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth import get_user_model
from .models import Post, Comment, Category, Tag, PostMedia

User = get_user_model()

class UserRegistrationForm(UserCreationForm):
    email = forms.EmailField(required=True)

    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2')

class UserProfileForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name')

class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True

class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result

class PostForm(forms.ModelForm):
    tags_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter tags separated by commas (e.g., technology, news, blog)',
            'id': 'tags-input'
        }),
        help_text='Enter tags separated by commas'
    )
    media_files = MultipleFileField(
        required=False,
        widget=MultipleFileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*, video/*'
        }),
        help_text='Upload multiple images or videos'
    )

    # AI assistance fields
    use_ai_assistance = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'id': 'use-ai-assistance'
        }),
        help_text='Enable AI writing assistance for suggestions and improvements'
    )

    ai_tone = forms.ChoiceField(
        required=False,
        choices=[
            ('professional', 'Professional'),
            ('casual', 'Casual'),
            ('friendly', 'Friendly'),
            ('formal', 'Formal'),
            ('creative', 'Creative'),
            ('technical', 'Technical'),
            ('conversational', 'Conversational'),
        ],
        widget=forms.Select(attrs={
            'class': 'form-control',
            'id': 'ai-tone-select'
        }),
        help_text='Choose the tone for AI assistance'
    )

    class Meta:
        model = Post
        fields = ('title', 'category', 'content', 'status', 'is_featured', 'reference')
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control ai-enhanced-textarea',
                'rows': 15,
                'id': 'content-textarea',
                'data-ai-enabled': 'true'
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-control ai-enhanced-input',
                'id': 'title-input',
                'data-ai-enabled': 'true'
            }),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'reference': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['tags_input'].initial = ', '.join(tag.name for tag in self.instance.tags.all())

    def save(self, commit=True):
        post = super().save(commit=False)
        if commit:
            post.save()
            # Handle tags
            tags_input = self.cleaned_data.get('tags_input', '')
            if tags_input:
                tag_names = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
                tags = []
                for tag_name in tag_names:
                    tag, created = Tag.objects.get_or_create(name=tag_name)
                    tags.append(tag)
                post.tags.set(tags)
            else:
                post.tags.clear()

            # Handle media files
            media_files = self.files.getlist('media_files')
            captions = self.data.getlist('media_captions[]')
            titles = self.data.getlist('media_titles[]')
            descriptions = self.data.getlist('media_descriptions[]')
            
            for i, media_file in enumerate(media_files):
                if media_file:
                    PostMedia.objects.create(
                        post=post,
                        media_type='image' if media_file.content_type.startswith('image/') else 'video',
                        image=media_file if media_file.content_type.startswith('image/') else None,
                        video=media_file if not media_file.content_type.startswith('image/') else None,
                        caption=captions[i] if i < len(captions) else '',
                        title=titles[i] if i < len(titles) else '',
                        description=descriptions[i] if i < len(descriptions) else '',
                        order=i
                    )

        return post

class CommentForm(forms.ModelForm):
    class Meta:
        model = Comment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

class NewsletterForm(forms.Form):
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Enter your email'})
    )