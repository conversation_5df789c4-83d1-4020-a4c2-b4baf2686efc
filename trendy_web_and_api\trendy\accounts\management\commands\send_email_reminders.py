"""
Management command to send email reminders and notifications
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from accounts.email_service import EmailService
from accounts.notification_service import NotificationService

User = get_user_model()


class Command(BaseCommand):
    help = 'Send email reminders and notifications to users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['verification', 'paypal_setup', 'premium_expiring', 'all'],
            default='all',
            help='Type of reminder to send'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending emails'
        )

    def handle(self, *args, **options):
        reminder_type = options['type']
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No emails will be sent'))
        
        if reminder_type in ['verification', 'all']:
            self.send_verification_reminders(dry_run)
        
        if reminder_type in ['paypal_setup', 'all']:
            self.send_paypal_setup_reminders(dry_run)
        
        if reminder_type in ['premium_expiring', 'all']:
            self.send_premium_expiring_reminders(dry_run)

    def send_verification_reminders(self, dry_run=False):
        """Send email verification reminders to unverified users"""
        self.stdout.write('Checking for users needing email verification reminders...')
        
        # Find users who registered 24+ hours ago but haven't verified email
        cutoff_date = timezone.now() - timedelta(hours=24)
        
        unverified_users = User.objects.filter(
            is_email_verified=False,
            is_staff=False,
            is_superuser=False,
            date_joined__lt=cutoff_date
        ).exclude(
            # Don't send to users who got a reminder in the last 24 hours
            email_verification_sent_at__gte=cutoff_date
        )
        
        count = unverified_users.count()
        self.stdout.write(f'Found {count} users needing verification reminders')
        
        if not dry_run and count > 0:
            sent_count = 0
            for user in unverified_users:
                try:
                    # Send email reminder
                    email_sent = EmailService.send_account_verification_email(user)
                    
                    # Send in-app notification
                    NotificationService.send_email_verification_reminder(user)
                    
                    if email_sent:
                        sent_count += 1
                        self.stdout.write(f'  ✓ Sent reminder to {user.email}')
                    else:
                        self.stdout.write(f'  ✗ Failed to send to {user.email}')
                        
                except Exception as e:
                    self.stdout.write(f'  ✗ Error sending to {user.email}: {str(e)}')
            
            self.stdout.write(self.style.SUCCESS(f'Sent {sent_count}/{count} verification reminders'))
        elif dry_run and count > 0:
            for user in unverified_users[:5]:  # Show first 5
                self.stdout.write(f'  Would send to: {user.email} (joined {user.date_joined})')
            if count > 5:
                self.stdout.write(f'  ... and {count - 5} more users')

    def send_paypal_setup_reminders(self, dry_run=False):
        """Send PayPal setup reminders to verified users without PayPal"""
        self.stdout.write('Checking for users needing PayPal setup reminders...')
        
        # Find verified users who don't have PayPal set up
        from payments.models import UserPayPalProfile
        
        # Users who are verified but don't have PayPal profile
        users_without_paypal = User.objects.filter(
            is_email_verified=True,
            is_staff=False,
            is_superuser=False
        ).exclude(
            id__in=UserPayPalProfile.objects.values_list('user_id', flat=True)
        )
        
        # Filter to users who joined 3+ days ago (give them time to explore first)
        cutoff_date = timezone.now() - timedelta(days=3)
        users_without_paypal = users_without_paypal.filter(date_joined__lt=cutoff_date)
        
        count = users_without_paypal.count()
        self.stdout.write(f'Found {count} users needing PayPal setup reminders')
        
        if not dry_run and count > 0:
            sent_count = 0
            for user in users_without_paypal:
                try:
                    # Send in-app notification
                    NotificationService.send_paypal_setup_notification(user)
                    sent_count += 1
                    self.stdout.write(f'  ✓ Sent PayPal reminder to {user.email}')
                        
                except Exception as e:
                    self.stdout.write(f'  ✗ Error sending to {user.email}: {str(e)}')
            
            self.stdout.write(self.style.SUCCESS(f'Sent {sent_count}/{count} PayPal setup reminders'))
        elif dry_run and count > 0:
            for user in users_without_paypal[:5]:  # Show first 5
                self.stdout.write(f'  Would send to: {user.email} (joined {user.date_joined})')
            if count > 5:
                self.stdout.write(f'  ... and {count - 5} more users')

    def send_premium_expiring_reminders(self, dry_run=False):
        """Send premium expiring reminders"""
        self.stdout.write('Checking for premium subscriptions expiring soon...')
        
        from monetization.models import PremiumSubscription
        
        # Find subscriptions expiring in 3 days
        expiring_date = timezone.now() + timedelta(days=3)
        
        expiring_subscriptions = PremiumSubscription.objects.filter(
            status='active',
            end_date__lte=expiring_date,
            end_date__gt=timezone.now()
        )
        
        count = expiring_subscriptions.count()
        self.stdout.write(f'Found {count} premium subscriptions expiring soon')
        
        if not dry_run and count > 0:
            sent_count = 0
            for subscription in expiring_subscriptions:
                try:
                    # Send notification
                    NotificationService.send_premium_notification(
                        subscription.user, 
                        'expiring_soon', 
                        subscription.plan
                    )
                    sent_count += 1
                    self.stdout.write(f'  ✓ Sent expiring reminder to {subscription.user.email}')
                        
                except Exception as e:
                    self.stdout.write(f'  ✗ Error sending to {subscription.user.email}: {str(e)}')
            
            self.stdout.write(self.style.SUCCESS(f'Sent {sent_count}/{count} premium expiring reminders'))
        elif dry_run and count > 0:
            for sub in expiring_subscriptions[:5]:  # Show first 5
                self.stdout.write(f'  Would send to: {sub.user.email} (expires {sub.end_date})')
            if count > 5:
                self.stdout.write(f'  ... and {count - 5} more subscriptions')
