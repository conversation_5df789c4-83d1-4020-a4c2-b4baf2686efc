import uuid
from rest_framework import serializers
from .models import Category, Post, Comment, PostMedia, Tag
from django.contrib.auth import get_user_model, authenticate
from django.utils.text import slugify
from django.core.paginator import Paginator

User = get_user_model()



class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    confirm_password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})

    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'confirm_password', 'first_name', 'last_name')
        extra_kwargs = {
            'password': {'write_only': True},
            'confirm_password': {'write_only': True},
        }

    def validate(self, data):
        if data['password'] != data['confirm_password']:
            raise serializers.ValidationError("Passwords do not match.")
        return data

    def create(self, validated_data):
        validated_data.pop('confirm_password')
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data.get('email', ''),
            password=validated_data['password'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', '')
        )
        return user
    
    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("This email is already registered.")
        return value

    def validate_username(self, value):
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("Username already exists.")
        return value
    
    
    def validate_password(self, value):
        if len(value) < 10:
            raise serializers.ValidationError("Password must be at least 10 characters")
        if not any(char.isdigit() for char in value):
            raise serializers.ValidationError("Password must contain at least one number")
        return value

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(style={'input_type': 'password'})

    def validate(self, data):
        credentials = {
            'password': data['password']
        }
        
        if '@' in data['email_or_username']:
            credentials['email'] = data['email_or_username']
        else:
            credentials['username'] = data['email_or_username']
            
        user = authenticate(**credentials)
        user = authenticate(**data)
        if user and user.is_active:
            return user
        raise serializers.ValidationError("Invalid credentials.")

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name')

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class PostMediaSerializer(serializers.ModelSerializer):
    video_url = serializers.SerializerMethodField()
    image_url_full = serializers.SerializerMethodField()

    class Meta:
        model = PostMedia
        fields = ['id', 'media_type', 'image', 'image_url', 'image_url_full', 'video', 'video_url', 'thumbnail',
                 'caption', 'title', 'description', 'order', 'created_at']

    def get_video_url(self, obj):
        """Return the full URL for video files"""
        if obj.video:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.video.url)
            return obj.video.url
        elif obj.video_url:
            return obj.video_url
        return None

    def get_image_url_full(self, obj):
        """Return the full URL for image files"""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        elif obj.image_url:
            return obj.image_url
        return None

    def validate(self, data):
        # Ensure either image or video is provided
        if not data.get('image') and not data.get('video') and not data.get('image_url') and not data.get('video_url'):
            raise serializers.ValidationError("Either image, video, image_url, or video_url must be provided")

        # Validate media type matches content
        if data['media_type'] == 'image' and not data.get('image') and not data.get('image_url'):
            raise serializers.ValidationError("Image media type requires image content")
        elif data['media_type'] == 'video' and not data.get('video') and not data.get('video_url'):
            raise serializers.ValidationError("Video media type requires video content")

        return data

class CommentSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.username', read_only=True)
    author_email = serializers.CharField(source='author.email', read_only=True)
    like_count = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()
    replies = serializers.SerializerMethodField()

    class Meta:
        model = Comment
        fields = [
            'id', 'content', 'author_name', 'author_email', 'created_at',
            'updated_at', 'parent', 'like_count', 'is_liked', 'replies'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'author_name', 'author_email']

    def get_like_count(self, obj):
        try:
            return obj.likes.count()
        except:
            return 0

    def get_is_liked(self, obj):
        try:
            request = self.context.get('request')
            if request and request.user.is_authenticated:
                return obj.likes.filter(id=request.user.id).exists()
            return False
        except:
            return False

    def get_replies(self, obj):
        try:
            # Return empty list for now to avoid recursion issues
            return []
        except:
            return []

    def create(self, validated_data):
        try:
            # The post will be set by the view
            validated_data['author'] = self.context['request'].user
            comment = super().create(validated_data)

            # Award gamification points for commenting
            try:
                from gamification.services import GamificationService
                GamificationService.update_engagement_activity(comment.author, 'comment', comment.id)
                print(f"✅ Awarded comment points to {comment.author.username} for comment {comment.id}")
            except Exception as e:
                print(f"❌ Failed to award comment points: {e}")

            return comment
        except Exception as e:
            raise serializers.ValidationError(f"Error creating comment: {str(e)}")

    def update(self, instance, validated_data):
        try:
            instance.content = validated_data.get('content', instance.content)
            instance.save()
            return instance
        except Exception as e:
            raise serializers.ValidationError(f"Error updating comment: {str(e)}")
    
class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ['id', 'name', 'slug']

class PostSerializer(serializers.ModelSerializer):
    author = UserSerializer(read_only=True)
    category = CategorySerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    comment_count = serializers.SerializerMethodField()
    like_count = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()
    media_items = PostMediaSerializer(many=True, read_only=True)

    class Meta:
        model = Post
        fields = [
            'id', 'title', 'slug', 'content', 'author', 'category', 'tags',
            'created_at', 'updated_at', 'views', 'is_featured', 'status',
            'reference', 'comment_count', 'like_count', 'is_liked', 'media_items'
        ]

    def get_like_count(self, obj):
        try:
            return obj.likes.count()
        except:
            return 0

    def get_comment_count(self, obj):
        try:
            return obj.comments.count()
        except:
            return 0

    def get_is_liked(self, obj):
        try:
            request = self.context.get('request')
            if request and request.user.is_authenticated:
                return obj.likes.filter(id=request.user.id).exists()
            return False
        except:
            return False
    # def create(self, validated_data):
    #     validated_data['slug'] = generate_unique_slug(validated_data['title'])
    #     return super().create(validated_data)
    
    def generate_unique_slug(title):
        slug = slugify(title)
        while Post.objects.filter(slug=slug).exists():
            slug = f"{slug}-{uuid.uuid4().hex[:4]}"
        return slug

class LikeSerializer(serializers.Serializer):
    user_id = serializers.IntegerField(read_only=True)
    content_type = serializers.ChoiceField(
            choices=['post', 'comment'],
            help_text="Type of object to like ('post' or 'comment')"
        )    
    object_id = serializers.IntegerField()
    is_liked = serializers.BooleanField() 
    
    def validate_object_id(self, value):
        model = {
            'post': Post,
            'comment': Comment
        }.get(self.initial_data.get('content_type'))
        
        if not model.objects.filter(id=value).exists():
            raise serializers.ValidationError("Invalid object ID for this content type")
        return value