from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from gamification.models import Badge, UserBadge

User = get_user_model()

class Command(BaseCommand):
    help = 'Populate sample badges for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample badges...')
        
        # Create sample badges
        badges_data = [
            {
                'name': 'First Steps',
                'description': 'Welcome to Trendy! You\'ve taken your first steps.',
                'badge_type': 'milestone',
                'rarity': 'common',
                'icon': '👋',
                'color': '#4CAF50',
                'requirements': {'action': 'signup'},
                'points_reward': 10,
            },
            {
                'name': 'Reading Enthusiast',
                'description': 'Read 10 posts to earn this badge.',
                'badge_type': 'reading',
                'rarity': 'common',
                'icon': '📚',
                'color': '#2196F3',
                'requirements': {'posts_read': 10},
                'points_reward': 25,
            },
            {
                'name': 'Social Butterfly',
                'description': 'Make 5 comments to connect with the community.',
                'badge_type': 'engagement',
                'rarity': 'uncommon',
                'icon': '🦋',
                'color': '#FF9800',
                'requirements': {'comments_made': 5},
                'points_reward': 50,
            },
            {
                'name': 'Content Creator',
                'description': 'Write your first post and share your thoughts.',
                'badge_type': 'writing',
                'rarity': 'uncommon',
                'icon': '✍️',
                'color': '#9C27B0',
                'requirements': {'posts_written': 1},
                'points_reward': 75,
            },
            {
                'name': 'Streak Master',
                'description': 'Maintain a 7-day reading streak.',
                'badge_type': 'milestone',
                'rarity': 'rare',
                'icon': '🔥',
                'color': '#F44336',
                'requirements': {'reading_streak': 7},
                'points_reward': 100,
            },
            {
                'name': 'Community Helper',
                'description': 'Give 20 likes to support fellow users.',
                'badge_type': 'community',
                'rarity': 'rare',
                'icon': '❤️',
                'color': '#E91E63',
                'requirements': {'likes_given': 20},
                'points_reward': 150,
            },
            {
                'name': 'Voice of Trendy',
                'description': 'Make 10 voice comments to share your voice.',
                'badge_type': 'engagement',
                'rarity': 'epic',
                'icon': '🎤',
                'color': '#673AB7',
                'requirements': {'voice_comments': 10},
                'points_reward': 200,
            },
            {
                'name': 'Trendy Legend',
                'description': 'Reach level 10 and become a Trendy legend.',
                'badge_type': 'special',
                'rarity': 'legendary',
                'icon': '👑',
                'color': '#FFD700',
                'requirements': {'level': 10},
                'points_reward': 500,
            },
        ]
        
        created_count = 0
        for badge_data in badges_data:
            badge, created = Badge.objects.get_or_create(
                name=badge_data['name'],
                defaults=badge_data
            )
            if created:
                created_count += 1
                self.stdout.write(f'Created badge: {badge.name}')
            else:
                self.stdout.write(f'Badge already exists: {badge.name}')
        
        self.stdout.write(f'Created {created_count} new badges')
        
        # Award some badges to test user
        try:
            test_user = User.objects.get(username='testuser')
            
            # Award first steps badge
            first_steps = Badge.objects.get(name='First Steps')
            user_badge, created = UserBadge.objects.get_or_create(
                user=test_user,
                badge=first_steps,
                defaults={'progress_data': {'awarded_for': 'signup'}}
            )
            if created:
                self.stdout.write(f'Awarded "First Steps" badge to {test_user.username}')
            
            # Award reading enthusiast badge
            reading_badge = Badge.objects.get(name='Reading Enthusiast')
            user_badge, created = UserBadge.objects.get_or_create(
                user=test_user,
                badge=reading_badge,
                defaults={'progress_data': {'posts_read': 15}}
            )
            if created:
                self.stdout.write(f'Awarded "Reading Enthusiast" badge to {test_user.username}')
                
        except User.DoesNotExist:
            self.stdout.write('Test user not found, skipping badge awards')
        
        self.stdout.write(self.style.SUCCESS('Successfully populated badges!'))
