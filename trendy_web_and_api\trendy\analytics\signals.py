from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from blog.models import Post
from .models import ContentAnalytics

@receiver(post_save, sender=Post)
def analyze_post_content(sender, instance, created, **kwargs):
    """Automatically analyze post content when a post is created or updated"""
    try:
        # Only analyze published posts
        if instance.status == 'published':
            ContentAnalytics.analyze_content(instance)
    except Exception as e:
        print(f"Error analyzing post content: {e}")

@receiver(post_delete, sender=Post)
def cleanup_post_analytics(sender, instance, **kwargs):
    """Clean up analytics when a post is deleted"""
    try:
        ContentAnalytics.objects.filter(post=instance).delete()
    except Exception as e:
        print(f"Error cleaning up post analytics: {e}")
