{% extends 'blog/base.html' %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto text-center">
            <div class="author-header mb-5">
                <img src="{{ author.profile.image.url }}" 
                     class="rounded-circle shadow mb-3" 
                     width="120" 
                     height="120"
                     alt="{{ author.username }}">
                <h1 class="display-5 fw-bold">{{ author.get_full_name|default:author.username }}</h1>
                <p class="text-muted">{{ author.profile.title|default:"Writer" }}</p>
            </div>
            
            <div class="row g-4">
                {% for post in posts %}
                <div class="col-md-6">
                    <!-- Use your existing post card component here -->
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="empty-state text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No posts by this author yet.</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}