// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blockchain_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BlockchainWalletImpl _$$BlockchainWalletImplFromJson(
        Map<String, dynamic> json) =>
    _$BlockchainWalletImpl(
      address: json['address'] as String,
      network: json['network'] as String,
      isPrimary: json['isPrimary'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      balances: (json['balances'] as List<dynamic>)
          .map((e) => TokenBalance.fromJson(e as Map<String, dynamic>))
          .toList(),
      isActive: json['isActive'] as bool? ?? false,
      activatedAt: json['activatedAt'] == null
          ? null
          : DateTime.parse(json['activatedAt'] as String),
    );

Map<String, dynamic> _$$BlockchainWalletImplToJson(
        _$BlockchainWalletImpl instance) =>
    <String, dynamic>{
      'address': instance.address,
      'network': instance.network,
      'isPrimary': instance.isPrimary,
      'createdAt': instance.createdAt.toIso8601String(),
      'balances': instance.balances,
      'isActive': instance.isActive,
      'activatedAt': instance.activatedAt?.toIso8601String(),
    };

_$TokenBalanceImpl _$$TokenBalanceImplFromJson(Map<String, dynamic> json) =>
    _$TokenBalanceImpl(
      contractName: json['contractName'] as String,
      contractAddress: json['contractAddress'] as String,
      balance: json['balance'] as String,
      stakedBalance: json['stakedBalance'] as String,
      totalBalance: json['totalBalance'] as String,
    );

Map<String, dynamic> _$$TokenBalanceImplToJson(_$TokenBalanceImpl instance) =>
    <String, dynamic>{
      'contractName': instance.contractName,
      'contractAddress': instance.contractAddress,
      'balance': instance.balance,
      'stakedBalance': instance.stakedBalance,
      'totalBalance': instance.totalBalance,
    };

_$NFTAssetImpl _$$NFTAssetImplFromJson(Map<String, dynamic> json) =>
    _$NFTAssetImpl(
      id: (json['id'] as num).toInt(),
      tokenId: (json['tokenId'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String,
      rarity: (json['rarity'] as num).toInt(),
      rarityDisplay: json['rarityDisplay'] as String,
      contractAddress: json['contractAddress'] as String,
      network: json['network'] as String,
      mintedAt: DateTime.parse(json['mintedAt'] as String),
      attributes: json['attributes'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$$NFTAssetImplToJson(_$NFTAssetImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'tokenId': instance.tokenId,
      'name': instance.name,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'rarity': instance.rarity,
      'rarityDisplay': instance.rarityDisplay,
      'contractAddress': instance.contractAddress,
      'network': instance.network,
      'mintedAt': instance.mintedAt.toIso8601String(),
      'attributes': instance.attributes,
    };

_$StakingPoolImpl _$$StakingPoolImplFromJson(Map<String, dynamic> json) =>
    _$StakingPoolImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      apyPercentage: json['apyPercentage'] as String,
      minimumStake: json['minimumStake'] as String,
      maximumStake: json['maximumStake'] as String?,
      totalStaked: json['totalStaked'] as String,
      activeStakers: (json['activeStakers'] as num).toInt(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
    );

Map<String, dynamic> _$$StakingPoolImplToJson(_$StakingPoolImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'apyPercentage': instance.apyPercentage,
      'minimumStake': instance.minimumStake,
      'maximumStake': instance.maximumStake,
      'totalStaked': instance.totalStaked,
      'activeStakers': instance.activeStakers,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
    };

_$UserStakeImpl _$$UserStakeImplFromJson(Map<String, dynamic> json) =>
    _$UserStakeImpl(
      id: (json['id'] as num).toInt(),
      poolName: json['poolName'] as String,
      amountStaked: json['amountStaked'] as String,
      rewardsEarned: json['rewardsEarned'] as String,
      pendingRewards: json['pendingRewards'] as String,
      apyPercentage: json['apyPercentage'] as String,
      stakedAt: DateTime.parse(json['stakedAt'] as String),
    );

Map<String, dynamic> _$$UserStakeImplToJson(_$UserStakeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'poolName': instance.poolName,
      'amountStaked': instance.amountStaked,
      'rewardsEarned': instance.rewardsEarned,
      'pendingRewards': instance.pendingRewards,
      'apyPercentage': instance.apyPercentage,
      'stakedAt': instance.stakedAt.toIso8601String(),
    };

_$BlockchainTransactionImpl _$$BlockchainTransactionImplFromJson(
        Map<String, dynamic> json) =>
    _$BlockchainTransactionImpl(
      id: json['id'] as String,
      type: json['type'] as String,
      status: json['status'] as String,
      amount: json['amount'] as String?,
      txHash: json['txHash'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$BlockchainTransactionImplToJson(
        _$BlockchainTransactionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'status': instance.status,
      'amount': instance.amount,
      'txHash': instance.txHash,
      'createdAt': instance.createdAt.toIso8601String(),
      'description': instance.description,
    };

_$AchievementNotificationImpl _$$AchievementNotificationImplFromJson(
        Map<String, dynamic> json) =>
    _$AchievementNotificationImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      rarity: (json['rarity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String,
      rewards: json['rewards'] as Map<String, dynamic>,
      unlockedAt: DateTime.parse(json['unlockedAt'] as String),
      isRead: json['isRead'] as bool? ?? false,
    );

Map<String, dynamic> _$$AchievementNotificationImplToJson(
        _$AchievementNotificationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'rarity': instance.rarity,
      'imageUrl': instance.imageUrl,
      'rewards': instance.rewards,
      'unlockedAt': instance.unlockedAt.toIso8601String(),
      'isRead': instance.isRead,
    };

_$BlockchainApiResponseImpl _$$BlockchainApiResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$BlockchainApiResponseImpl(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$BlockchainApiResponseImplToJson(
        _$BlockchainApiResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };
