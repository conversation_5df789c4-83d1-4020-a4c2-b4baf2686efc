import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/providers/auth_provider.dart';
import 'package:trendy/providers/role_provider.dart';
import 'package:trendy/services/role_service.dart';

/// Widget that conditionally shows content based on user roles
class RoleBasedWidget extends ConsumerWidget {
  final Widget child;
  final List<String>? allowedRoles;
  final List<String>? requiredPermissions;
  final bool requireContentCreator;
  final bool requireAdmin;
  final bool requireAuthentication;
  final Widget? fallback;
  final String? fallbackMessage;

  const RoleBasedWidget({
    Key? key,
    required this.child,
    this.allowedRoles,
    this.requiredPermissions,
    this.requireContentCreator = false,
    this.requireAdmin = false,
    this.requireAuthentication = true,
    this.fallback,
    this.fallbackMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(enhancedAuthProvider);
    final roleInfoAsync = ref.watch(roleInfoProvider);

    // Check authentication first
    if (requireAuthentication && !authState.isAuthenticated) {
      return fallback ??
          _buildFallback(context, 'Please log in to access this feature');
    }

    return roleInfoAsync.when(
      data: (roleInfo) {
        if (roleInfo == null && requireAuthentication) {
          return fallback ??
              _buildFallback(context, 'Unable to verify permissions');
        }

        // Check admin requirement
        if (requireAdmin && !RoleService.isAdmin(roleInfo)) {
          return fallback ?? _buildFallback(context, 'Admin access required');
        }

        // Check content creator requirement
        if (requireContentCreator &&
            roleInfo != null &&
            !roleInfo.canCreateContent) {
          return fallback ??
              _buildFallback(context, 'Content creator access required');
        }

        // Check specific roles
        if (allowedRoles != null && roleInfo != null) {
          if (!allowedRoles!.contains(roleInfo.role)) {
            return fallback ??
                _buildFallback(context, 'Insufficient permissions');
          }
        }

        // Check specific permissions
        if (requiredPermissions != null && roleInfo != null) {
          for (String permission in requiredPermissions!) {
            if (!RoleService.hasPermission(roleInfo, permission)) {
              return fallback ??
                  _buildFallback(
                    context,
                    'Missing required permission: $permission',
                  );
            }
          }
        }

        // All checks passed, show the child
        return child;
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) =>
          fallback ?? _buildFallback(context, 'Error loading permissions'),
    );
  }

  Widget _buildFallback(BuildContext context, String message) {
    if (fallbackMessage != null) {
      message = fallbackMessage!;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock_outline,
            size: 48,
            color: Theme.of(context).disabledColor,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).disabledColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Widget that shows user's role badge
class RoleBadge extends ConsumerWidget {
  final bool showDescription;
  final double? fontSize;

  const RoleBadge({Key? key, this.showDescription = false, this.fontSize})
    : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final roleInfoAsync = ref.watch(roleInfoProvider);

    return roleInfoAsync.when(
      data: (roleInfo) {
        if (roleInfo == null) return const SizedBox.shrink();

        final color = Color(
          int.parse(
            RoleService.getRoleBadgeColor(
              roleInfo.role,
            ).replaceFirst('#', '0xFF'),
          ),
        );

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                RoleService.getRoleIcon(roleInfo.role),
                style: TextStyle(fontSize: fontSize ?? 12),
              ),
              const SizedBox(width: 4),
              Text(
                roleInfo.roleDisplayName,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.w600,
                  fontSize: fontSize ?? 12,
                ),
              ),
              if (showDescription) ...[
                const SizedBox(width: 4),
                Text(
                  '• ${roleInfo.roleDescription}',
                  style: TextStyle(
                    color: color.withValues(alpha: 0.8),
                    fontSize: (fontSize ?? 12) - 1,
                  ),
                ),
              ],
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }
}

/// Widget that shows content creation prompt for non-creators
class ContentCreationPrompt extends ConsumerWidget {
  final String message;
  final VoidCallback? onContactAdmin;

  const ContentCreationPrompt({
    Key? key,
    this.message = 'You need content creator permissions to create posts.',
    this.onContactAdmin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Always return empty widget - regular users should not see content creator prompts
    // Only admins can promote users to content creator role through admin interface
    return const SizedBox.shrink();
  }
}

/// Floating Action Button that only shows for content creators
class ContentCreatorFAB extends ConsumerWidget {
  final VoidCallback onPressed;
  final Widget icon;
  final String? tooltip;

  const ContentCreatorFAB({
    Key? key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return RoleBasedWidget(
      requireContentCreator: true,
      fallback: const SizedBox.shrink(),
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        child: icon,
      ),
    );
  }
}
