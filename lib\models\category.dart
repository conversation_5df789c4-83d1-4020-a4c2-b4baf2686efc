import 'package:freezed_annotation/freezed_annotation.dart';

part 'category.freezed.dart';
part 'category.g.dart';

@freezed
class Category with _$Category {
  const Category._();

  const factory Category({
    required int id,
    required String name,
    required String slug,
    required String description,
    @Json<PERSON>ey(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
  }) = _Category;

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
}