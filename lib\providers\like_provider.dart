import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../models/post.dart';
import '../models/comment.dart';

// Like state for tracking like status across the app
class LikeState {
  final Map<String, bool> postLikes;
  final Map<String, int> postLikeCounts;
  final Map<String, bool> commentLikes;
  final Map<String, int> commentLikeCounts;

  const LikeState({
    this.postLikes = const {},
    this.postLikeCounts = const {},
    this.commentLikes = const {},
    this.commentLikeCounts = const {},
  });

  LikeState copyWith({
    Map<String, bool>? postLikes,
    Map<String, int>? postLikeCounts,
    Map<String, bool>? commentLikes,
    Map<String, int>? commentLikeCounts,
  }) {
    return LikeState(
      postLikes: postLikes ?? this.postLikes,
      postLikeCounts: postLikeCounts ?? this.postLikeCounts,
      commentLikes: commentLikes ?? this.commentLikes,
      commentLikeCounts: commentLikeCounts ?? this.commentLikeCounts,
    );
  }

  // Helper methods to get like status
  bool isPostLiked(int postId) => postLikes['post_$postId'] ?? false;
  int getPostLikeCount(int postId) => postLikeCounts['post_$postId'] ?? 0;
  bool isCommentLiked(int commentId) => commentLikes['comment_$commentId'] ?? false;
  int getCommentLikeCount(int commentId) => commentLikeCounts['comment_$commentId'] ?? 0;
}

// Like notifier for managing like state
class LikeNotifier extends StateNotifier<LikeState> {
  final ApiService _apiService;

  LikeNotifier(this._apiService) : super(const LikeState());

  // Update post like status from API response or local data
  void updatePostLike(int postId, bool isLiked, int likeCount) {
    final newPostLikes = Map<String, bool>.from(state.postLikes);
    final newPostLikeCounts = Map<String, int>.from(state.postLikeCounts);
    
    newPostLikes['post_$postId'] = isLiked;
    newPostLikeCounts['post_$postId'] = likeCount;
    
    state = state.copyWith(
      postLikes: newPostLikes,
      postLikeCounts: newPostLikeCounts,
    );
  }

  // Update comment like status from API response or local data
  void updateCommentLike(int commentId, bool isLiked, int likeCount) {
    final newCommentLikes = Map<String, bool>.from(state.commentLikes);
    final newCommentLikeCounts = Map<String, int>.from(state.commentLikeCounts);
    
    newCommentLikes['comment_$commentId'] = isLiked;
    newCommentLikeCounts['comment_$commentId'] = likeCount;
    
    state = state.copyWith(
      commentLikes: newCommentLikes,
      commentLikeCounts: newCommentLikeCounts,
    );
  }

  // Toggle post like with API call
  Future<void> togglePostLike(int postId) async {
    try {
      // Optimistic update
      final currentLiked = state.isPostLiked(postId);
      final currentCount = state.getPostLikeCount(postId);
      
      updatePostLike(postId, !currentLiked, currentLiked ? currentCount - 1 : currentCount + 1);
      
      // API call
      final response = await _apiService.toggleLike('posts', postId.toString());
      
      // Update with actual response
      updatePostLike(
        postId, 
        response['is_liked'] ?? !currentLiked,
        response['like_count'] ?? (currentLiked ? currentCount - 1 : currentCount + 1),
      );
    } catch (e) {
      // Revert optimistic update on error
      final currentLiked = state.isPostLiked(postId);
      final currentCount = state.getPostLikeCount(postId);
      updatePostLike(postId, !currentLiked, currentLiked ? currentCount + 1 : currentCount - 1);
      rethrow;
    }
  }

  // Toggle comment like with API call
  Future<void> toggleCommentLike(int commentId) async {
    try {
      // Optimistic update
      final currentLiked = state.isCommentLiked(commentId);
      final currentCount = state.getCommentLikeCount(commentId);
      
      updateCommentLike(commentId, !currentLiked, currentLiked ? currentCount - 1 : currentCount + 1);
      
      // API call
      await _apiService.toggleCommentLike(commentId);
      
      // The optimistic update should be correct, but we could fetch fresh data if needed
    } catch (e) {
      // Revert optimistic update on error
      final currentLiked = state.isCommentLiked(commentId);
      final currentCount = state.getCommentLikeCount(commentId);
      updateCommentLike(commentId, !currentLiked, currentLiked ? currentCount + 1 : currentCount - 1);
      rethrow;
    }
  }

  // Initialize like state from posts
  void initializeFromPosts(List<Post> posts) {
    final newPostLikes = Map<String, bool>.from(state.postLikes);
    final newPostLikeCounts = Map<String, int>.from(state.postLikeCounts);
    
    for (final post in posts) {
      newPostLikes['post_${post.id}'] = post.isLiked;
      newPostLikeCounts['post_${post.id}'] = post.likeCount;
    }
    
    state = state.copyWith(
      postLikes: newPostLikes,
      postLikeCounts: newPostLikeCounts,
    );
  }

  // Initialize like state from comments
  void initializeFromComments(List<Comment> comments) {
    final newCommentLikes = Map<String, bool>.from(state.commentLikes);
    final newCommentLikeCounts = Map<String, int>.from(state.commentLikeCounts);
    
    for (final comment in comments) {
      newCommentLikes['comment_${comment.id}'] = comment.isLiked;
      newCommentLikeCounts['comment_${comment.id}'] = comment.likeCount;
    }
    
    state = state.copyWith(
      commentLikes: newCommentLikes,
      commentLikeCounts: newCommentLikeCounts,
    );
  }

  // Clear all like state (useful for logout)
  void clearLikeState() {
    state = const LikeState();
  }
}

// Like provider
final likeProvider = StateNotifierProvider<LikeNotifier, LikeState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return LikeNotifier(apiService);
});

// API Service Provider (imported from other providers)
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});
