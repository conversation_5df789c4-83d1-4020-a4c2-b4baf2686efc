import 'package:flutter/foundation.dart';
import 'connectivity_service.dart';
import 'offline_storage_service.dart';
import 'error_reporting_service.dart';
import '../models/app_error.dart';

class AppInitializationService {
  static final AppInitializationService _instance =
      AppInitializationService._internal();
  factory AppInitializationService() => _instance;
  AppInitializationService._internal();

  bool _isInitialized = false;
  final List<String> _initializationSteps = [];

  /// Check if app is initialized
  bool get isInitialized => _isInitialized;

  /// Get initialization steps for debugging
  List<String> get initializationSteps =>
      List.unmodifiable(_initializationSteps);

  /// Initialize all app services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _logStep('Starting app initialization...');

      // 1. Initialize connectivity service
      _logStep('Initializing connectivity service...');
      final connectivityService = ConnectivityService();
      await connectivityService.initialize();
      _logStep('✅ Connectivity service initialized');

      // 2. Initialize offline storage
      _logStep('Initializing offline storage...');
      final storageService = OfflineStorageService();
      await storageService.initialize();
      _logStep('✅ Offline storage initialized');

      // 3. Initialize error reporting
      _logStep('Initializing error reporting...');
      final errorService = ErrorReportingService();
      errorService.initialize(connectivityService);
      _logStep('✅ Error reporting initialized');

      // 4. Perform initial health checks
      _logStep('Performing health checks...');
      await _performHealthChecks(connectivityService, storageService);
      _logStep('✅ Health checks completed');

      _isInitialized = true;
      _logStep('🎉 App initialization completed successfully');
    } catch (e) {
      _logStep('❌ App initialization failed: $e');

      // Report initialization error
      final errorService = ErrorReportingService();
      errorService.reportError(
        AppError(
          type: AppErrorType.unknown,
          message: 'App initialization failed: $e',
          userMessage: 'Failed to start the app properly.',
          suggestion:
              'Please restart the app. If the problem persists, reinstall the app.',
          isRetryable: true,
          isTemporary: false,
        ),
      );

      rethrow;
    }
  }

  /// Perform health checks
  Future<void> _performHealthChecks(
    ConnectivityService connectivityService,
    OfflineStorageService storageService,
  ) async {
    // Check connectivity
    if (connectivityService.isOnline) {
      _logStep('  ✅ Internet connectivity available');
    } else {
      _logStep('  ⚠️  No internet connectivity - offline mode');
    }

    // Check storage
    final cacheSize = await storageService.getCacheSize();
    _logStep('  ✅ Storage accessible (cache: ${_formatBytes(cacheSize)})');

    // Check if we have cached data for offline use
    final hasCachedData = await storageService.hasCachedData();
    if (hasCachedData) {
      _logStep('  ✅ Cached data available for offline use');
    } else {
      _logStep('  ⚠️  No cached data - will need internet for initial load');
    }
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// Log initialization step
  void _logStep(String step) {
    _initializationSteps.add('${DateTime.now().toIso8601String()}: $step');
    if (kDebugMode) {
      print('🚀 $step');
    }
  }

  /// Reset initialization state (for testing)
  void reset() {
    _isInitialized = false;
    _initializationSteps.clear();
  }

  /// Get initialization summary
  Map<String, dynamic> getInitializationSummary() {
    return {
      'is_initialized': _isInitialized,
      'steps_completed': _initializationSteps.length,
      'initialization_steps': _initializationSteps,
      'services_status': {
        'connectivity': ConnectivityService().currentStatus.name,
        'storage': OfflineStorageService().isInitialized,
        'error_reporting': ErrorReportingService().errorHistory.length,
      },
    };
  }
}
