{% extends 'blog/base.html' %}

{% block title %}Contact Us - Trendy Blog{% endblock %}

{% block content %}
<div class="contact-page">
    <!-- Hero Section -->
    <div class="contact-hero">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="contact-title">Get in Touch</h1>
                    <p class="contact-subtitle">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-5">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="contact-form-card">
                    <h3 class="mb-4">Send us a Message</h3>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" enctype="multipart/form-data" id="contactForm">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                           value="{% if user.is_authenticated %}{{ user.get_full_name|default:user.username }}{% endif %}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" required
                                           value="{% if user.is_authenticated %}{{ user.email }}{% endif %}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">Select a topic</option>
                                        <option value="general">General Inquiry</option>
                                        <option value="technical">Technical Support</option>
                                        <option value="billing">Billing & Payments</option>
                                        <option value="account">Account Issues</option>
                                        <option value="content">Content & Publishing</option>
                                        <option value="blockchain">Blockchain & Wallet</option>
                                        <option value="partnership">Partnership Opportunities</option>
                                        <option value="press">Press & Media</option>
                                        <option value="feedback">Feedback & Suggestions</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">Priority Level</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="low">Low - General inquiry</option>
                                        <option value="medium" selected>Medium - Standard issue</option>
                                        <option value="high">High - Urgent matter</option>
                                        <option value="critical">Critical - Emergency</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required
                                      placeholder="Please provide detailed information about your inquiry or issue..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="attachment" class="form-label">Attachment (Optional)</label>
                            <input type="file" class="form-control" id="attachment" name="attachment"
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt">
                            <div class="form-text">Supported formats: Images, PDF, Word documents, Text files (Max 10MB)</div>
                        </div>

                        {% if user.is_authenticated %}
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for updates and tips
                                </label>
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </div>
                    </form>
                    
                    <form method="post" class="contact-form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-user me-2"></i>Full Name
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           name="name" 
                                           placeholder="Enter your full name"
                                           value="{% if user.is_authenticated %}{{ user.get_full_name }}{% endif %}"
                                           required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address
                                    </label>
                                    <input type="email" 
                                           class="form-control" 
                                           name="email" 
                                           placeholder="Enter your email address"
                                           value="{% if user.is_authenticated %}{{ user.email }}{% endif %}"
                                           required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-tag me-2"></i>Subject
                                    </label>
                                    <select class="form-select" name="subject" required>
                                        <option value="">Select a topic</option>
                                        <option value="general">General Inquiry</option>
                                        <option value="technical">Technical Support</option>
                                        <option value="account">Account Issues</option>
                                        <option value="billing">Billing Questions</option>
                                        <option value="feature">Feature Request</option>
                                        <option value="bug">Bug Report</option>
                                        <option value="partnership">Partnership</option>
                                        <option value="press">Press Inquiry</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-exclamation-circle me-2"></i>Priority
                                    </label>
                                    <select class="form-select" name="priority">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                        <option value="urgent">Urgent</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label">
                                <i class="fas fa-comment me-2"></i>Message
                            </label>
                            <textarea class="form-control" 
                                      name="message" 
                                      rows="6" 
                                      placeholder="Please describe your inquiry in detail..."
                                      required></textarea>
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
                                <label class="form-check-label" for="newsletter">
                                    Subscribe to our newsletter for updates and tips
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info-card">
                    <h4 class="mb-4">Contact Information</h4>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Email</h6>
                            <p><EMAIL></p>
                            <small>We'll respond within 24 hours</small>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Phone</h6>
                            <p>+****************</p>
                            <small>Mon-Fri, 9AM-6PM EST</small>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Address</h6>
                            <p>123 Innovation Street<br>Tech City, TC 12345</p>
                            <small>Visit us by appointment</small>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="contact-details">
                            <h6>Live Chat</h6>
                            <p>Available 24/7</p>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="startLiveChat()">
                                Start Chat
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="social-media-card mt-4">
                    <h5 class="mb-3">Follow Us</h5>
                    <div class="social-links">
                        <a href="#" class="social-link twitter">
                            <i class="fab fa-twitter"></i>
                            <span>@TrendyBlog</span>
                        </a>
                        <a href="#" class="social-link facebook">
                            <i class="fab fa-facebook"></i>
                            <span>Trendy Blog</span>
                        </a>
                        <a href="#" class="social-link linkedin">
                            <i class="fab fa-linkedin"></i>
                            <span>Trendy Blog</span>
                        </a>
                        <a href="#" class="social-link instagram">
                            <i class="fab fa-instagram"></i>
                            <span>@trendy_blog</span>
                        </a>
                    </div>
                </div>

                <!-- FAQ Link -->
                <div class="faq-card mt-4">
                    <h5 class="mb-3">Need Quick Help?</h5>
                    <p>Check out our frequently asked questions for instant answers.</p>
                    <a href="{% url 'support' %}" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-question-circle me-2"></i>Visit Support Center
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-page {
    min-height: 100vh;
}

.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    margin-top: -80px;
    text-align: center;
}

.contact-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.contact-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.contact-form-card,
.contact-info-card,
.social-media-card,
.faq-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    height: fit-content;
}

.contact-form-card h3 {
    color: var(--text-color);
    font-weight: 600;
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-icon {
    width: 45px;
    height: 45px;
    background: rgba(79, 70, 229, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    flex-shrink: 0;
}

.contact-details h6 {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 5px;
}

.contact-details p {
    color: var(--text-color);
    margin-bottom: 5px;
}

.contact-details small {
    color: #6c757d;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.social-link:hover {
    color: white;
    transform: translateX(5px);
}

.social-link.twitter:hover {
    background: #1da1f2;
    border-color: #1da1f2;
}

.social-link.facebook:hover {
    background: #4267b2;
    border-color: #4267b2;
}

.social-link.linkedin:hover {
    background: #0077b5;
    border-color: #0077b5;
}

.social-link.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    border-color: #bc1888;
}

.social-link i {
    font-size: 1.2rem;
    width: 20px;
}

@media (max-width: 768px) {
    .contact-hero {
        padding: 60px 0;
    }
    
    .contact-title {
        font-size: 2rem;
    }
    
    .contact-form-card,
    .contact-info-card,
    .social-media-card,
    .faq-card {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}
</style>

<script>
function startLiveChat() {
    // This would integrate with your live chat service
    alert('Live chat feature coming soon! Please use the contact form for now.');
}

// Form submission handling
document.querySelector('.contact-form').addEventListener('submit', function(e) {
    // Add loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitBtn.disabled = true;
    
    // In a real app, the form would be submitted normally
    // This is just for demo purposes
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show mt-3';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>Message sent!</strong> We'll get back to you within 24 hours.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        this.insertBefore(alert, this.firstChild);
        this.reset();
    }, 2000);
});
</script>
{% endblock %}
