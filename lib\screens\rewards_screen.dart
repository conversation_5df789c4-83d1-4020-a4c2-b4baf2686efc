import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';
import '../providers/rewards_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/gamification/user_level_widget.dart';
import '../models/reward_models.dart';
import '../services/compliant_payment_service.dart';

class RewardsScreen extends ConsumerStatefulWidget {
  const RewardsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<RewardsScreen> createState() => _RewardsScreenState();
}

class _RewardsScreenState extends ConsumerState<RewardsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _tierInfo;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Load dynamic data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && ref.read(enhancedAuthProvider).isAuthenticated) {
        ref.read(rewardsProvider.notifier).loadAvailableRewards();
        ref.read(rewardsProvider.notifier).loadUserRewards();
        ref.read(rewardsProvider.notifier).loadPayPalProfile();
        _loadTierInfo();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);

    if (!authState.isAuthenticated) {
      return _buildUnauthenticatedView();
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          '💰 Rewards & Earnings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline, color: AppTheme.primaryColor),
            onPressed: () => _showHelpDialog(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          tabs: const [
            Tab(text: 'Starter'),
            Tab(text: 'Engagement'),
            Tab(text: 'Achievement'),
            Tab(text: 'Elite'),
          ],
        ),
      ),
      body: Column(
        children: [
          // User progress overview
          const UserLevelWidget(),
          
          // PayPal setup reminder
          _buildPayPalSetupCard(),
          
          // Reward tiers
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildRewardTier('starter'),
                _buildRewardTier('engagement'),
                _buildRewardTier('achievement'),
                _buildRewardTier('elite'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnauthenticatedView() {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          '💰 Rewards & Earnings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.monetization_on,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Start Earning Real Money!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Join Trendy to earn PayPal rewards by reading posts, engaging with content, and referring friends.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/auth');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Join Trendy & Start Earning',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPayPalSetupCard() {
    final paypalProfile = ref.watch(rewardsProvider).paypalProfile;

    if (paypalProfile != null) {
      // User already has a PayPal profile - show status
      return Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.green[50]!, Colors.green[100]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green[200]!),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.green[600],
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'PayPal Account Connected',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    paypalProfile.paypalEmail,
                    style: TextStyle(
                      color: Colors.green[700],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (!paypalProfile.isVerified)
                    Text(
                      'Verification pending',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => _showPayPalUpdateDialog(paypalProfile.paypalEmail),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Update'),
            ),
          ],
        ),
      );
    } else {
      // User needs to set up PayPal
      return Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue[50]!, Colors.blue[100]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.blue[600],
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Set Up PayPal to Receive Rewards',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Add your PayPal email to claim real money rewards',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => _showPayPalSetupDialog(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Set Up'),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildRewardTier(String tier) {
    final rewardsState = ref.watch(rewardsProvider);
    final rewards = rewardsState.availableRewards
        .where((reward) => reward.tier == tier)
        .toList();
    final isUnlocked = _isTierUnlocked(tier);
    final unlockPrice = _getTierUnlockPrice(tier);

    // Show loading state
    if (rewardsState.isLoading && rewards.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (rewardsState.error != null && rewards.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Failed to load rewards',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  ref.read(rewardsProvider.notifier).loadAvailableRewards();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Tier header
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: _getTierGradient(tier),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                '${tier.toUpperCase()} TIER',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (!isUnlocked) ...[
                const SizedBox(height: 8),
                Text(
                  'Unlock for \$${unlockPrice.toStringAsFixed(2)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: () => _unlockTier(tier),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: AppTheme.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text('Unlock \$${unlockPrice.toStringAsFixed(2)}'),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Rewards list
        ...rewards.map((reward) => _buildRewardCard(reward, isUnlocked)),
      ],
    );
  }

  Widget _buildRewardCard(PayPalReward reward, bool isUnlocked) {
    final canClaim = isUnlocked && reward.pointsRequired <= 750; // TODO: Get real user points
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: canClaim ? Colors.green[300]! : Colors.grey[300]!,
          width: canClaim ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Reward icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: canClaim ? Colors.green[100] : Colors.grey[100],
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.monetization_on,
                color: canClaim ? Colors.green[600] : Colors.grey[600],
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            
            // Reward details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '\$${reward.amount.toStringAsFixed(2)} PayPal Reward',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${reward.pointsRequired} points required',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    reward.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            
            // Claim button
            if (canClaim)
              ElevatedButton(
                onPressed: () => _claimReward(reward),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Claim'),
              )
            else if (!isUnlocked)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Text(
                  'Locked',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              )
            else
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '${reward.pointsRequired - 750} more',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Helper methods

  Future<void> _loadTierInfo() async {
    try {
      final response = await ref.read(apiServiceProvider).getUnlockedTiers();
      if (mounted) {
        setState(() {
          _tierInfo = response;
        });
      }
    } catch (e) {
      print('Error loading tier info: $e');
    }
  }

  bool _isTierUnlocked(String tier) {
    if (_tierInfo == null) {
      // Default to starter tier unlocked while loading
      return tier == 'starter';
    }

    final tierInfo = _tierInfo!['tier_info'] as Map<String, dynamic>?;
    if (tierInfo == null) return tier == 'starter';

    final tierData = tierInfo[tier] as Map<String, dynamic>?;
    return tierData?['unlocked'] == true;
  }

  double _getTierUnlockPrice(String tier) {
    if (_tierInfo == null) {
      // Default prices while loading
      switch (tier) {
        case 'starter': return 0.0;
        case 'engagement': return 2.99;
        case 'achievement': return 4.99;
        case 'elite': return 9.99;
        default: return 0.0;
      }
    }

    final tierInfo = _tierInfo!['tier_info'] as Map<String, dynamic>?;
    if (tierInfo == null) return 0.0;

    final tierData = tierInfo[tier] as Map<String, dynamic>?;
    final priceStr = tierData?['price'] as String?;
    return double.tryParse(priceStr ?? '0.0') ?? 0.0;
  }

  LinearGradient _getTierGradient(String tier) {
    switch (tier) {
      case 'starter':
        return LinearGradient(colors: [Colors.green[400]!, Colors.green[600]!]);
      case 'engagement':
        return LinearGradient(colors: [Colors.blue[400]!, Colors.blue[600]!]);
      case 'achievement':
        return LinearGradient(colors: [Colors.orange[400]!, Colors.orange[600]!]);
      case 'elite':
        return LinearGradient(colors: [Colors.purple[400]!, Colors.purple[600]!]);
      default:
        return LinearGradient(colors: [Colors.grey[400]!, Colors.grey[600]!]);
    }
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('💰 How Rewards Work'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('1. Earn points by reading posts and engaging'),
              SizedBox(height: 8),
              Text('2. Unlock higher tiers for better rewards'),
              SizedBox(height: 8),
              Text('3. Claim PayPal rewards when you have enough points'),
              SizedBox(height: 8),
              Text('4. Get paid within 24 hours of approval'),
              SizedBox(height: 16),
              Text('💎 Premium users earn 2x points!'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it!'),
          ),
        ],
      ),
    );
  }

  void _showPayPalSetupDialog() {
    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('💳 Set Up PayPal'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'PayPal Email Address',
                hintText: '<EMAIL>',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            Text(
              'We use PayPal for secure, instant money transfers. You\'ll receive payments within 24 hours of approval.',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final email = emailController.text.trim();
              if (email.isEmpty || !email.contains('@')) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a valid email address'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();

              final success = await ref.read(rewardsProvider.notifier)
                  .setupPayPalProfile(email);

              if (!mounted) return;

              if (success) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('PayPal email saved! You can now claim rewards.'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else {
                final error = ref.read(rewardsProvider).error ?? 'Failed to setup PayPal profile';
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(error),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showPayPalUpdateDialog(String currentEmail) {
    final emailController = TextEditingController(text: currentEmail);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('💳 Update PayPal Email'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'PayPal Email Address',
                hintText: '<EMAIL>',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            Text(
              'Update your PayPal email address for receiving rewards. Changes may require re-verification.',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final email = emailController.text.trim();
              if (email.isEmpty || !email.contains('@')) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a valid email address'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              if (email == currentEmail) {
                Navigator.pop(context);
                return;
              }

              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();

              final success = await ref.read(rewardsProvider.notifier)
                  .setupPayPalProfile(email);

              if (!mounted) return;

              if (success) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('PayPal email updated successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else {
                final error = ref.read(rewardsProvider).error ?? 'Failed to update PayPal profile';
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text(error),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _unlockTier(String tier) async {
    try {
      // Call the backend API to initiate tier unlock
      final response = await ref.read(apiServiceProvider).unlockTier(tier);

      if (response['payment_required'] == true) {
        // Payment required - redirect to payment screen
        final price = response['price'];

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Unlock ${tier.toUpperCase()} Tier'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Unlock the ${tier} tier for \$${price.toStringAsFixed(2)}?\n\n'
                  'This will give you access to higher value rewards and exclusive benefits.',
                ),
                const SizedBox(height: 16),
                const Text(
                  'You will be redirected to the payment screen to complete your purchase.',
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _redirectToPayment(tier, price);
                },
                child: const Text('Continue to Payment'),
              ),
            ],
          ),
        );
      } else if (response['success'] == true) {
        // Free tier unlocked successfully
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${tier.toUpperCase()} tier unlocked!'),
            backgroundColor: Colors.green,
          ),
        );
        // Reload rewards to reflect the new tier
        ref.read(rewardsProvider.notifier).loadAvailableRewards();
      } else {
        // Error occurred
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['error'] ?? 'Failed to unlock tier'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _redirectToPayment(String tier, double price) {
    // Navigate to store screen with payment intent
    Navigator.pushNamed(
      context,
      '/store',
      arguments: {
        'payment_intent': 'tier_unlock',
        'tier': tier,
        'price': price,
      },
    ).then((_) {
      // Reload rewards when returning from payment
      ref.read(rewardsProvider.notifier).loadAvailableRewards();
    });
  }

  void _claimReward(PayPalReward reward) async {
    final paypalProfile = ref.read(rewardsProvider).paypalProfile;

    if (paypalProfile == null || !paypalProfile.isVerified) {
      _showPayPalSetupDialog();
      return;
    }

    // Use compliant payment service for PayPal reward claims (this is a payout, not a purchase)
    final success = await CompliantPaymentService.claimPayPalReward(
      context,
      rewardId: reward.id,
      paypalEmail: paypalProfile.paypalEmail,
    );

    if (success && mounted) {
      // Reload rewards to reflect the claimed reward
      ref.read(rewardsProvider.notifier).loadAvailableRewards();
      ref.read(rewardsProvider.notifier).loadUserRewards();
    }
  }
}
