# 🚀 Trendy App Setup Instructions

## **Quick Setup for New Machines**

### **Option 1: Automated Setup (Recommended)**
```bash
# Clone the repository
git clone <your-repository-url>
cd trendy

# Run the automated setup script
./setup_new_machine.sh
```

### **Option 2: Manual Setup**
```bash
# 1. Clone repository
git clone <your-repository-url>
cd trendy

# 2. Setup Django backend
cd trendy_web_and_api/trendy
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# or venv\Scripts\activate  # Windows

pip install -r requirements.txt
python manage.py migrate
python manage.py setup_dev_data  # Load development data

# 3. Setup Flutter frontend
cd ../../  # Back to project root
flutter pub get
```

## **Starting the Application**

### **Backend (Django)**
```bash
cd trendy_web_and_api/trendy
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

### **Frontend (Flutter)**
```bash
cd trendy  # Project root
flutter run
```

## **Database Data Sharing**

### **❌ Problem with SQLite + Git**
- SQLite files are binary and cause merge conflicts
- Large database files slow down Git operations
- Multiple developers can't work simultaneously
- Database corruption can occur during Git merges

### **✅ Solution: Use Fixtures**

#### **Exporting Data (When you have new data to share)**
```bash
cd trendy_web_and_api/trendy
source venv/bin/activate

# Export all data
python manage.py dumpdata --indent=2 --output=fixtures/latest_data.json

# Or export by specific apps
python manage.py dumpdata accounts --indent=2 --output=fixtures/users_data.json
python manage.py dumpdata wallet --indent=2 --output=fixtures/wallet_data.json
python manage.py dumpdata blog --indent=2 --output=fixtures/blog_data.json

# Commit the fixtures
git add fixtures/
git commit -m "Update development data fixtures"
git push
```

#### **Loading Data (On other machines)**
```bash
# Pull latest changes
git pull

# Load the updated data
cd trendy_web_and_api/trendy
source venv/bin/activate
python manage.py setup_dev_data --reset
```

## **Network Configuration**

### **Current Setup**
- **Django Server**: `http://**************:8000`
- **Flutter App**: Configured to use network IP
- **CORS**: Enabled for cross-origin requests

### **For Mobile Testing**
1. Ensure mobile device is on same WiFi network
2. Django server runs on `0.0.0.0:8000` (accessible from network)
3. Flutter app automatically connects to network server

## **Development Workflow**

### **Daily Development**
1. **Start Django server**: `python manage.py runserver 0.0.0.0:8000`
2. **Start Flutter app**: `flutter run`
3. **Work on features** using local SQLite database
4. **Export important data** to fixtures when needed
5. **Commit fixtures** (not database files) to Git

### **Sharing Data with Team**
```bash
# When you have new important data
python manage.py dumpdata accounts wallet blog --indent=2 --output=fixtures/team_data.json
git add fixtures/team_data.json
git commit -m "Add latest team development data"
git push

# Team members update their data
git pull
python manage.py setup_dev_data --reset
```

## **Available Features**

### **✅ Working Features**
- **👤 User Authentication**: Login, register, profile management
- **💰 Wallet System**: Add money, spend from wallet, transaction history
- **🛒 Shop Features**: Premium subscriptions, point boost purchases
- **🎮 Gamification**: Points, achievements, rewards, streaks
- **📱 Social Features**: Posts, comments, likes, follows
- **🔔 Notifications**: Real-time notifications system

### **💰 Monetization Features**
- **Premium Subscriptions**: $9.99/month with 2x point multiplier
- **Point Boost Packages**: Quick Start ($1.99), Ultimate Boost ($19.99)
- **Wallet-First Payments**: Instant purchases from wallet balance
- **PayPal Fallback**: External payment when wallet insufficient

## **Admin Access**

### **Django Admin**
- **URL**: `http://localhost:8000/admin/`
- **Create superuser**: `python manage.py createsuperuser`
- **Manage**: Users, posts, wallet transactions, monetization settings

## **Troubleshooting**

### **Common Issues**

#### **Database Issues**
```bash
# Reset database completely
python manage.py flush --noinput
python manage.py migrate
python manage.py setup_dev_data
```

#### **Missing Dependencies**
```bash
# Reinstall Python packages
pip install -r requirements.txt

# Reinstall Flutter packages
flutter pub get
```

#### **Network Connection Issues**
```bash
# Check if server is running
curl http://**************:8000/api/v1/monetization/settings/

# Check firewall (Linux)
sudo ufw allow 8000
```

#### **Flutter Build Issues**
```bash
# Clean Flutter build
flutter clean
flutter pub get
flutter run
```

### **Getting Help**
1. Check this README first
2. Look at `DATABASE_SETUP.md` for detailed database instructions
3. Check `NETWORK_CONFIG.md` for network setup details
4. Review Django logs for backend issues
5. Check Flutter console for frontend issues

## **File Structure**
```
trendy/
├── setup_new_machine.sh          # Automated setup script
├── SETUP_INSTRUCTIONS.md         # This file
├── DATABASE_SETUP.md             # Database setup guide
├── NETWORK_CONFIG.md              # Network configuration
├── lib/                           # Flutter source code
├── trendy_web_and_api/
│   └── trendy/
│       ├── manage.py              # Django management
│       ├── fixtures/              # Database fixtures (commit these)
│       ├── db.sqlite3            # SQLite database (DON'T commit)
│       └── requirements.txt      # Python dependencies
└── .gitignore                    # Git ignore rules
```

## **Next Steps**
1. **Run the setup**: Use `./setup_new_machine.sh`
2. **Start development**: Django + Flutter servers
3. **Test features**: Wallet, shop, social features
4. **Share data**: Export fixtures when you have new data
5. **Deploy**: Follow production deployment guide

Happy coding! 🎉
