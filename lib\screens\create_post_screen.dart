import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:dio/dio.dart';
import 'dart:io';
import '../widgets/ai_writing_widgets.dart';
import '../widgets/ai_writing_settings.dart';
import '../widgets/styled_components.dart';
import '../models/post.dart';
import '../models/category.dart';
import '../models/country.dart';
import '../models/paginated_response.dart';
import '../providers/provider.dart';
import '../providers/regional_provider.dart';

import '../theme/app_theme.dart';

class CreatePostScreen extends ConsumerStatefulWidget {
  final Post? editingPost;

  const CreatePostScreen({Key? key, this.editingPost}) : super(key: key);

  @override
  ConsumerState<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends ConsumerState<CreatePostScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagsController = TextEditingController();
  final _scrollController = ScrollController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isPublished = false;
  bool _isSaving = false;
  bool _showAIPanel = true;
  int? _selectedCategoryId;
  List<File> _selectedImages = [];
  List<File> _selectedVideos = [];
  final ImagePicker _imagePicker = ImagePicker();

  // Regional targeting
  bool _isGlobal = true;
  List<int> _selectedCountryIds = [];

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    // Start animation
    _animationController.forward();

    if (widget.editingPost != null) {
      _populateFields(widget.editingPost!);
    }
  }

  void _populateFields(Post post) {
    _titleController.text = post.title;
    _contentController.text = post.content;
    _tagsController.text = post.tags.map((tag) => tag.name).join(', ');
    _isPublished = post.status == 'published';
    _selectedCategoryId = post.category.id;
  }

  // Image picker methods
  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images.map((xfile) => File(xfile.path)));
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick images: $e');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // Video picker methods
  Future<void> _pickVideos() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: true,
      );

      if (result != null) {
        setState(() {
          _selectedVideos.addAll(
            result.paths
                .where((path) => path != null)
                .map((path) => File(path!)),
          );
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick videos: $e');
    }
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesState = ref.watch(categoriesProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(categoriesState),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.arrow_back_ios_new,
            size: 18,
            color: AppTheme.textPrimary,
          ),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        widget.editingPost != null ? 'Edit Post' : 'Create Post',
        style: const TextStyle(
          fontWeight: FontWeight.w700,
          fontSize: 20,
          color: AppTheme.textPrimary,
        ),
      ),
      actions: [
        // AI Assistant Toggle
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _showAIPanel
                  ? AppTheme.primaryColor
                  : AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.smart_toy,
              size: 20,
              color: _showAIPanel ? Colors.white : AppTheme.textSecondary,
            ),
          ),
          onPressed: () => setState(() => _showAIPanel = !_showAIPanel),
          tooltip: 'Toggle AI Assistant',
        ),

        // AI Settings
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.settings,
              size: 20,
              color: AppTheme.textSecondary,
            ),
          ),
          onPressed: () => Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AIWritingSettings()),
          ),
          tooltip: 'AI Settings',
        ),

        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildBody(AsyncValue<PaginatedResponse<Category>> categoriesState) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            children: [
              // AI Writing Panel
              if (_showAIPanel)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: AIWritingPanel(
                    titleController: _titleController,
                    contentController: _contentController,
                    onSuggestionApplied: () => setState(() {}),
                  ),
                ),

              // Main Form
              Padding(
                padding: const EdgeInsets.all(AppTheme.spacingLg),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTitleField(),
                      const SizedBox(height: AppTheme.spacingLg),
                      _buildCategoryField(categoriesState),
                      const SizedBox(height: AppTheme.spacingLg),
                      _buildContentField(),
                      const SizedBox(height: AppTheme.spacingLg),
                      _buildMediaSection(),
                      const SizedBox(height: AppTheme.spacingLg),
                      _buildTagsField(),
                      const SizedBox(height: AppTheme.spacingLg),
                      _buildRegionalTargetingSection(),
                      const SizedBox(height: AppTheme.spacingLg),
                      _buildPublishingOptions(),
                      const SizedBox(height: AppTheme.spacingXl),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleField() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.title,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Post Title',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _titleController,
            decoration: InputDecoration(
              hintText: 'Enter an engaging title for your post...',
              hintStyle: TextStyle(
                color: AppTheme.textSecondary.withValues(alpha: 0.7),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppTheme.primaryColor,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.all(16),
            ),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimary,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Title is required';
              }
              if (value.trim().length < 5) {
                return 'Title must be at least 5 characters long';
              }
              return null;
            },
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryField(
    AsyncValue<PaginatedResponse<Category>> categoriesState,
  ) {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.category,
                  color: AppTheme.secondaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Category',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          categoriesState.when(
            data: (paginatedCategories) {
              final categories = paginatedCategories.results;
              return DropdownButtonFormField<int>(
                value: _selectedCategoryId,
                decoration: InputDecoration(
                  hintText: 'Select a category',
                  hintStyle: TextStyle(
                    color: AppTheme.textSecondary.withValues(alpha: 0.7),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppTheme.borderColor),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: AppTheme.borderColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: AppTheme.primaryColor,
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.all(16),
                ),
                items: categories.map((category) {
                  return DropdownMenuItem<int>(
                    value: category.id,
                    child: Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() => _selectedCategoryId = value);
                },
                validator: (value) {
                  if (value == null) {
                    return 'Please select a category';
                  }
                  return null;
                },
              );
            },
            loading: () => Container(
              height: 56,
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.borderColor),
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
            error: (error, stack) => Container(
              height: 56,
              decoration: BoxDecoration(
                color: AppTheme.errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.errorColor),
              ),
              child: Center(
                child: Text(
                  'Failed to load categories',
                  style: TextStyle(color: AppTheme.errorColor),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentField() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.article,
                  color: AppTheme.accentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Content',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _contentController,
            decoration: InputDecoration(
              hintText: 'Write your amazing content here...',
              hintStyle: TextStyle(
                color: AppTheme.textSecondary.withValues(alpha: 0.7),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppTheme.primaryColor,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.all(16),
              alignLabelWithHint: true,
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: AppTheme.textPrimary,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Content is required';
              }
              if (value.trim().length < 50) {
                return 'Content must be at least 50 characters long';
              }
              return null;
            },
            maxLines: 15,
            minLines: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildMediaSection() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.photo_library,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Media',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  StyledButton(
                    text: 'Images',
                    onPressed: _pickImages,
                    type: ButtonType.secondary,
                    icon: const Icon(Icons.add_photo_alternate, size: 18),
                  ),
                  const SizedBox(width: 8),
                  StyledButton(
                    text: 'Videos',
                    onPressed: _pickVideos,
                    type: ButtonType.secondary,
                    icon: const Icon(Icons.videocam, size: 18),
                  ),
                ],
              ),
            ],
          ),
          if (_selectedImages.isNotEmpty) ...[
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.file(
                            _selectedImages[index],
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppTheme.errorColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
          if (_selectedVideos.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Text(
              'Selected Videos',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedVideos.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 12),
                    child: Stack(
                      children: [
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: AppTheme.surfaceColor,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppTheme.borderColor),
                          ),
                          child: const Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.play_circle_outline,
                                size: 40,
                                color: AppTheme.primaryColor,
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Video',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _removeVideo(index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppTheme.errorColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTagsField() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.local_offer,
                  color: AppTheme.secondaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Tags',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _tagsController,
            decoration: InputDecoration(
              hintText:
                  'Enter tags separated by commas (e.g., tech, mobile, flutter)',
              hintStyle: TextStyle(
                color: AppTheme.textSecondary.withValues(alpha: 0.7),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: AppTheme.primaryColor,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.all(16),
            ),
            style: const TextStyle(fontSize: 16, color: AppTheme.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildRegionalTargetingSection() {
    return Consumer(
      builder: (context, ref, child) {
        final countriesAsync = ref.watch(countriesProvider);

        return StyledCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.public,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Regional Targeting',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Global toggle
              Row(
                children: [
                  Switch(
                    value: _isGlobal,
                    onChanged: (value) {
                      setState(() {
                        _isGlobal = value;
                        if (value) {
                          _selectedCountryIds.clear();
                        }
                      });
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Make this post visible globally',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),

              if (!_isGlobal) ...[
                const SizedBox(height: 16),
                const Text(
                  'Select target countries:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                countriesAsync.when(
                  data: (countries) => _buildCountrySelection(countries),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Text(
                    'Error loading countries: $error',
                    style: const TextStyle(color: AppTheme.errorColor),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildCountrySelection(List<Country> countries) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.borderColor),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.builder(
        itemCount: countries.length,
        itemBuilder: (context, index) {
          final country = countries[index];
          final isSelected = _selectedCountryIds.contains(country.id);

          return CheckboxListTile(
            title: Row(
              children: [
                if (country.flagEmoji != null) ...[
                  Text(country.flagEmoji!),
                  const SizedBox(width: 8),
                ],
                Expanded(child: Text(country.name)),
              ],
            ),
            value: isSelected,
            onChanged: (bool? value) {
              setState(() {
                if (value == true) {
                  _selectedCountryIds.add(country.id);
                } else {
                  _selectedCountryIds.remove(country.id);
                }
              });
            },
            activeColor: AppTheme.primaryColor,
            dense: true,
          );
        },
      ),
    );
  }

  Widget _buildPublishingOptions() {
    return StyledCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.publish,
                  color: AppTheme.accentColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Publishing Options',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: _isPublished
                  ? AppTheme.successColor.withValues(alpha: 0.1)
                  : AppTheme.warningColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _isPublished
                    ? AppTheme.successColor.withValues(alpha: 0.3)
                    : AppTheme.warningColor.withValues(alpha: 0.3),
              ),
            ),
            child: SwitchListTile(
              title: Text(
                _isPublished ? 'Publish immediately' : 'Save as draft',
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              subtitle: Text(
                _isPublished
                    ? 'Post will be visible to all readers'
                    : 'Post will be saved as draft for later',
                style: TextStyle(
                  color: AppTheme.textSecondary.withValues(alpha: 0.8),
                ),
              ),
              value: _isPublished,
              onChanged: (value) {
                setState(() => _isPublished = value);
              },
              activeColor: AppTheme.successColor,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: StyledButton(
            text: 'Cancel',
            onPressed: _isSaving ? null : () => Navigator.pop(context),
            type: ButtonType.secondary,
            isFullWidth: true,
          ),
        ),
        const SizedBox(width: AppTheme.spacingMd),
        Expanded(
          flex: 2,
          child: StyledButton(
            text: widget.editingPost != null ? 'Update Post' : 'Create Post',
            onPressed: _isSaving ? null : _savePost,
            type: ButtonType.primary,
            isLoading: _isSaving,
            isFullWidth: true,
            icon: _isSaving
                ? null
                : Icon(
                    widget.editingPost != null ? Icons.update : Icons.publish,
                    size: 18,
                    color: Colors.white,
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _savePost() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isSaving = true);

    try {
      final apiService = ref.read(apiServiceProvider);
      final tags = _tagsController.text
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();

      // Prepare media items
      List<Map<String, dynamic>> mediaItems = [];

      // Add images
      for (int i = 0; i < _selectedImages.length; i++) {
        mediaItems.add({
          'media_type': 'image',
          'file': await MultipartFile.fromFile(_selectedImages[i].path),
          'order': i,
        });
      }

      // Add videos
      for (int i = 0; i < _selectedVideos.length; i++) {
        mediaItems.add({
          'media_type': 'video',
          'file': await MultipartFile.fromFile(_selectedVideos[i].path),
          'order': _selectedImages.length + i,
        });
      }

      if (widget.editingPost != null) {
        // Update existing post
        await apiService.updatePost(
          slug: widget.editingPost!.slug,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          categoryId: _selectedCategoryId,
          mediaItems: mediaItems.isNotEmpty ? mediaItems : null,
          isGlobal: _isGlobal,
          targetCountryIds: _isGlobal ? null : _selectedCountryIds,
        );
        _showSuccessSnackBar('Post updated successfully!');
      } else {
        // Create new post
        await apiService.createPost(
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          categoryId: _selectedCategoryId,
          mediaItems: mediaItems.isNotEmpty ? mediaItems : null,
          isGlobal: _isGlobal,
          targetCountryIds: _isGlobal ? null : _selectedCountryIds,
        );
        _showSuccessSnackBar('Post created successfully!');
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to save post: $e');
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
