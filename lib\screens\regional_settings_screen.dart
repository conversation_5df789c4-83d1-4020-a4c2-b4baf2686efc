import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/models/country.dart';
import 'package:trendy/providers/regional_provider.dart';
import 'package:trendy/widgets/regional/country_selector.dart';
import 'package:trendy/widgets/regional/regional_indicator.dart';

class RegionalSettingsScreen extends ConsumerStatefulWidget {
  const RegionalSettingsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<RegionalSettingsScreen> createState() =>
      _RegionalSettingsScreenState();
}

class _RegionalSettingsScreenState
    extends ConsumerState<RegionalSettingsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final preferencesAsync = ref.watch(regionalPreferencesNotifierProvider);
    final statsAsync = ref.watch(regionalStatsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Regional Settings'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(regionalPreferencesNotifierProvider.notifier).refresh();
              ref.refresh(regionalStatsProvider);
            },
          ),
        ],
      ),
      body: preferencesAsync.when(
        data: (preferences) => _buildContent(context, preferences, statsAsync),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    RegionalPreferences preferences,
    AsyncValue<RegionalStats> statsAsync,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current region indicator
          const RegionalIndicator(showChangeButton: false),
          const SizedBox(height: 24),

          // Country selection
          _buildSection(
            title: 'Country Selection',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Choose your preferred country to see relevant content',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 12),
                CountrySelector(
                  selectedCountry: preferences.preferredCountry,
                  onCountrySelected: _onCountrySelected,
                  showGlobalOption: true,
                  hintText: 'Select your preferred country',
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Settings toggles
          _buildSection(
            title: 'Content Settings',
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Show Global Content'),
                  subtitle:
                      const Text('Include international posts in your feed'),
                  value: preferences.showGlobalContent,
                  onChanged: _isLoading
                      ? null
                      : (value) => _updateGlobalContent(value),
                  activeColor: Colors.blue,
                ),
                const Divider(),
                SwitchListTile(
                  title: const Text('Auto-detect Location'),
                  subtitle: const Text(
                      'Automatically detect your country from IP address'),
                  value: preferences.autoDetectLocation,
                  onChanged:
                      _isLoading ? null : (value) => _updateAutoDetect(value),
                  activeColor: Colors.blue,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Location detection
          _buildSection(
            title: 'Location Detection',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (preferences.detectedCountry != null) ...[
                  Row(
                    children: [
                      Text(preferences.detectedCountry!.flagEmoji ?? '🏳️'),
                      const SizedBox(width: 8),
                      Text(
                        'Detected: ${preferences.detectedCountry!.displayName ?? preferences.detectedCountry!.name}',
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _detectLocation,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.location_searching),
                    label: Text(
                        _isLoading ? 'Detecting...' : 'Detect My Location'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Statistics
          statsAsync.when(
            data: (stats) => _buildStatsSection(stats),
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),

          const SizedBox(height: 24),

          // Reset preferences
          _buildSection(
            title: 'Reset',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Clear your preferred country and use auto-detected location',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _clearPreferences,
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear Preferred Country'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  Widget _buildStatsSection(RegionalStats stats) {
    return _buildSection(
      title: 'Content Statistics',
      child: Column(
        children: [
          _buildStatRow('Total Posts', stats.totalPosts.toString()),
          _buildStatRow('Global Posts', stats.globalPosts.toString()),
          _buildStatRow('Regional Posts', stats.regionalPosts.toString()),
          _buildStatRow(
              'Available Countries', stats.availableCountries.toString()),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: Colors.grey.shade700),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading regional settings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: TextStyle(color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              ref.read(regionalPreferencesNotifierProvider.notifier).refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _onCountrySelected(Country? country) async {
    if (_isLoading) return; // Prevent multiple simultaneous calls

    setState(() => _isLoading = true);
    try {
      if (country != null) {
        await ref
            .read(regionalPreferencesNotifierProvider.notifier)
            .setPreferredCountry(country.code);
      } else {
        await ref
            .read(regionalPreferencesNotifierProvider.notifier)
            .clearPreferredCountry();
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              country != null
                  ? 'Country set to ${country.displayName ?? country.name ?? 'Unknown'}'
                  : 'Switched to global content',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _updateGlobalContent(bool value) async {
    setState(() => _isLoading = true);
    try {
      await ref
          .read(regionalPreferencesNotifierProvider.notifier)
          .updateSettings(showGlobalContent: value);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _updateAutoDetect(bool value) async {
    setState(() => _isLoading = true);
    try {
      await ref
          .read(regionalPreferencesNotifierProvider.notifier)
          .updateSettings(autoDetectLocation: value);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _detectLocation() async {
    if (_isLoading) return; // Prevent multiple simultaneous calls

    setState(() => _isLoading = true);
    try {
      await ref
          .read(regionalPreferencesNotifierProvider.notifier)
          .detectLocation();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location detected successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error detecting location: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _clearPreferences() async {
    setState(() => _isLoading = true);
    try {
      await ref
          .read(regionalPreferencesNotifierProvider.notifier)
          .clearPreferredCountry();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Preferred country cleared'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}
