import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/maintenance_status.dart';
import '../services/maintenance_service.dart';
import 'provider.dart';

// Provider for MaintenanceService
final maintenanceServiceProvider = Provider<MaintenanceService>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return MaintenanceService(apiService);
});

// State notifier for system status
class SystemStatusNotifier extends StateNotifier<AsyncValue<SystemStatus>> {
  final MaintenanceService _maintenanceService;
  Timer? _periodicTimer;
  
  SystemStatusNotifier(this._maintenanceService) : super(const AsyncValue.loading()) {
    _checkSystemStatus();
    _startPeriodicCheck();
  }

  @override
  void dispose() {
    _periodicTimer?.cancel();
    super.dispose();
  }

  /// Start periodic status checking (every 2 minutes)
  void _startPeriodicCheck() {
    _periodicTimer = Timer.periodic(const Duration(minutes: 2), (_) {
      _checkSystemStatus();
    });
  }

  /// Check system status
  Future<void> _checkSystemStatus() async {
    try {
      final status = await _maintenanceService.getSystemStatus();
      state = AsyncValue.data(status);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Force refresh system status
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await _checkSystemStatus();
  }

  /// Check if a specific feature is enabled
  Future<bool> isFeatureEnabled(String featureName) async {
    try {
      return await _maintenanceService.isFeatureEnabled(featureName);
    } catch (e) {
      return true; // Default to enabled on error
    }
  }

  /// Get current maintenance status
  bool get isMaintenanceActive {
    return state.when(
      data: (status) => status.maintenanceActive,
      loading: () => false,
      error: (_, __) => false,
    );
  }

  /// Get current maintenance message
  String get maintenanceMessage {
    return state.when(
      data: (status) => status.activeMaintenance?.message ?? '',
      loading: () => '',
      error: (_, __) => '',
    );
  }

  /// Get maintenance end time
  DateTime? get maintenanceEndTime {
    return state.when(
      data: (status) => status.activeMaintenance?.scheduledEnd,
      loading: () => null,
      error: (_, __) => null,
    );
  }
}

// Provider for system status
final systemStatusProvider = StateNotifierProvider<SystemStatusNotifier, AsyncValue<SystemStatus>>((ref) {
  final maintenanceService = ref.watch(maintenanceServiceProvider);
  return SystemStatusNotifier(maintenanceService);
});

// Provider for maintenance status (simplified)
final maintenanceStatusProvider = Provider<bool>((ref) {
  final systemStatus = ref.watch(systemStatusProvider);
  return systemStatus.when(
    data: (status) => status.maintenanceActive,
    loading: () => false,
    error: (_, __) => false,
  );
});

// Provider for feature status
final featureStatusProvider = Provider.family<bool, String>((ref, featureName) {
  final systemStatus = ref.watch(systemStatusProvider);
  return systemStatus.when(
    data: (status) => status.featureStatus[featureName] ?? true,
    loading: () => true,
    error: (_, __) => true,
  );
});

// Provider for maintenance message
final maintenanceMessageProvider = Provider<String>((ref) {
  final systemStatus = ref.watch(systemStatusProvider);
  return systemStatus.when(
    data: (status) => status.activeMaintenance?.message ?? '',
    loading: () => '',
    error: (_, __) => '',
  );
});

// Provider for maintenance end time
final maintenanceEndTimeProvider = Provider<DateTime?>((ref) {
  final systemStatus = ref.watch(systemStatusProvider);
  return systemStatus.when(
    data: (status) => status.activeMaintenance?.scheduledEnd,
    loading: () => null,
    error: (_, __) => null,
  );
});
