// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reward_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PayPalRewardImpl _$$PayPalRewardImplFromJson(Map<String, dynamic> json) =>
    _$PayPalRewardImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      pointsRequired: (json['pointsRequired'] as num).toInt(),
      tier: json['tier'] as String,
      isActive: json['isActive'] as bool,
      isLimitedTime: json['isLimitedTime'] as bool? ?? false,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      maxClaims: (json['maxClaims'] as num?)?.toInt() ?? 0,
      currentClaims: (json['currentClaims'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$PayPalRewardImplToJson(_$PayPalRewardImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'amount': instance.amount,
      'pointsRequired': instance.pointsRequired,
      'tier': instance.tier,
      'isActive': instance.isActive,
      'isLimitedTime': instance.isLimitedTime,
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'maxClaims': instance.maxClaims,
      'currentClaims': instance.currentClaims,
    };

_$UserPayPalRewardImpl _$$UserPayPalRewardImplFromJson(
        Map<String, dynamic> json) =>
    _$UserPayPalRewardImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      rewardId: json['rewardId'] as String,
      reward: PayPalReward.fromJson(json['reward'] as Map<String, dynamic>),
      amount: (json['amount'] as num).toDouble(),
      status: json['status'] as String,
      paypalEmail: json['paypalEmail'] as String,
      claimedAt: json['claimedAt'] == null
          ? null
          : DateTime.parse(json['claimedAt'] as String),
      approvedAt: json['approvedAt'] == null
          ? null
          : DateTime.parse(json['approvedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      paypalTransactionId: json['paypalTransactionId'] as String?,
      adminNotes: json['adminNotes'] as String?,
    );

Map<String, dynamic> _$$UserPayPalRewardImplToJson(
        _$UserPayPalRewardImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'rewardId': instance.rewardId,
      'reward': instance.reward,
      'amount': instance.amount,
      'status': instance.status,
      'paypalEmail': instance.paypalEmail,
      'claimedAt': instance.claimedAt?.toIso8601String(),
      'approvedAt': instance.approvedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'paypalTransactionId': instance.paypalTransactionId,
      'adminNotes': instance.adminNotes,
    };

_$UserPayPalProfileImpl _$$UserPayPalProfileImplFromJson(
        Map<String, dynamic> json) =>
    _$UserPayPalProfileImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      paypalEmail: json['paypalEmail'] as String,
      isVerified: json['isVerified'] as bool,
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
      totalEarnings: (json['totalEarnings'] as num?)?.toDouble() ?? 0.0,
      totalRewardsClaimed: (json['totalRewardsClaimed'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$$UserPayPalProfileImplToJson(
        _$UserPayPalProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'paypalEmail': instance.paypalEmail,
      'isVerified': instance.isVerified,
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'totalEarnings': instance.totalEarnings,
      'totalRewardsClaimed': instance.totalRewardsClaimed,
      'isActive': instance.isActive,
    };

_$ReferralDataImpl _$$ReferralDataImplFromJson(Map<String, dynamic> json) =>
    _$ReferralDataImpl(
      id: json['id'] as String,
      referrerId: json['referrerId'] as String,
      refereeId: json['refereeId'] as String,
      referralCode: json['referralCode'] as String,
      friendName: json['friendName'] as String,
      friendLevel: (json['friendLevel'] as num).toInt(),
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      earnedAmount: (json['earnedAmount'] as num).toDouble(),
      wentPremium: json['wentPremium'] as bool,
      reachedLevel5: json['reachedLevel5'] as bool? ?? false,
      madePurchase: json['madePurchase'] as bool? ?? false,
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$ReferralDataImplToJson(_$ReferralDataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'referrerId': instance.referrerId,
      'refereeId': instance.refereeId,
      'referralCode': instance.referralCode,
      'friendName': instance.friendName,
      'friendLevel': instance.friendLevel,
      'joinedAt': instance.joinedAt.toIso8601String(),
      'earnedAmount': instance.earnedAmount,
      'wentPremium': instance.wentPremium,
      'reachedLevel5': instance.reachedLevel5,
      'madePurchase': instance.madePurchase,
      'totalRevenue': instance.totalRevenue,
    };

_$ReferralStatsImpl _$$ReferralStatsImplFromJson(Map<String, dynamic> json) =>
    _$ReferralStatsImpl(
      totalReferrals: (json['totalReferrals'] as num?)?.toInt() ?? 0,
      totalEarned: (json['totalEarned'] as num?)?.toDouble() ?? 0.0,
      premiumReferrals: (json['premiumReferrals'] as num?)?.toInt() ?? 0,
      activeReferrals: (json['activeReferrals'] as num?)?.toInt() ?? 0,
      level5Referrals: (json['level5Referrals'] as num?)?.toInt() ?? 0,
      referralCode: json['referralCode'] as String?,
    );

Map<String, dynamic> _$$ReferralStatsImplToJson(_$ReferralStatsImpl instance) =>
    <String, dynamic>{
      'totalReferrals': instance.totalReferrals,
      'totalEarned': instance.totalEarned,
      'premiumReferrals': instance.premiumReferrals,
      'activeReferrals': instance.activeReferrals,
      'level5Referrals': instance.level5Referrals,
      'referralCode': instance.referralCode,
    };

_$VirtualItemImpl _$$VirtualItemImplFromJson(Map<String, dynamic> json) =>
    _$VirtualItemImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      isActive: json['isActive'] as bool,
      isLimitedTime: json['isLimitedTime'] as bool? ?? false,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      maxPurchases: (json['maxPurchases'] as num?)?.toInt() ?? 0,
      effects: json['effects'] as Map<String, dynamic>?,
      imageUrl: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$$VirtualItemImplToJson(_$VirtualItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': instance.category,
      'price': instance.price,
      'currency': instance.currency,
      'isActive': instance.isActive,
      'isLimitedTime': instance.isLimitedTime,
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'maxPurchases': instance.maxPurchases,
      'effects': instance.effects,
      'imageUrl': instance.imageUrl,
    };

_$UserVirtualItemImpl _$$UserVirtualItemImplFromJson(
        Map<String, dynamic> json) =>
    _$UserVirtualItemImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      itemId: json['itemId'] as String,
      item: VirtualItem.fromJson(json['item'] as Map<String, dynamic>),
      purchasedAt: DateTime.parse(json['purchasedAt'] as String),
      isActive: json['isActive'] as bool,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      usesRemaining: (json['usesRemaining'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$UserVirtualItemImplToJson(
        _$UserVirtualItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'itemId': instance.itemId,
      'item': instance.item,
      'purchasedAt': instance.purchasedAt.toIso8601String(),
      'isActive': instance.isActive,
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'usesRemaining': instance.usesRemaining,
    };

_$PointBoostPackageImpl _$$PointBoostPackageImplFromJson(
        Map<String, dynamic> json) =>
    _$PointBoostPackageImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      basePoints: (json['basePoints'] as num).toInt(),
      bonusPoints: (json['bonusPoints'] as num).toInt(),
      totalPoints: (json['totalPoints'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      isActive: json['isActive'] as bool,
      isPopular: json['isPopular'] as bool? ?? false,
      isLimitedTime: json['isLimitedTime'] as bool? ?? false,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$$PointBoostPackageImplToJson(
        _$PointBoostPackageImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'basePoints': instance.basePoints,
      'bonusPoints': instance.bonusPoints,
      'totalPoints': instance.totalPoints,
      'price': instance.price,
      'currency': instance.currency,
      'isActive': instance.isActive,
      'isPopular': instance.isPopular,
      'isLimitedTime': instance.isLimitedTime,
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

_$PremiumSubscriptionImpl _$$PremiumSubscriptionImplFromJson(
        Map<String, dynamic> json) =>
    _$PremiumSubscriptionImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      plan: json['plan'] as String,
      status: json['status'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      monthlyPrice: (json['monthlyPrice'] as num).toDouble(),
      totalPaid: (json['totalPaid'] as num).toDouble(),
      lastPaymentDate: json['lastPaymentDate'] == null
          ? null
          : DateTime.parse(json['lastPaymentDate'] as String),
      nextPaymentDate: json['nextPaymentDate'] == null
          ? null
          : DateTime.parse(json['nextPaymentDate'] as String),
      pointMultiplier: (json['pointMultiplier'] as num?)?.toDouble() ?? 2.0,
      dailyStreakBonus: (json['dailyStreakBonus'] as num?)?.toInt() ?? 15,
      voiceCommentsLimit: (json['voiceCommentsLimit'] as num?)?.toInt() ?? -1,
    );

Map<String, dynamic> _$$PremiumSubscriptionImplToJson(
        _$PremiumSubscriptionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'plan': instance.plan,
      'status': instance.status,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'monthlyPrice': instance.monthlyPrice,
      'totalPaid': instance.totalPaid,
      'lastPaymentDate': instance.lastPaymentDate?.toIso8601String(),
      'nextPaymentDate': instance.nextPaymentDate?.toIso8601String(),
      'pointMultiplier': instance.pointMultiplier,
      'dailyStreakBonus': instance.dailyStreakBonus,
      'voiceCommentsLimit': instance.voiceCommentsLimit,
    };

_$PaymentTransactionImpl _$$PaymentTransactionImplFromJson(
        Map<String, dynamic> json) =>
    _$PaymentTransactionImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      transactionType: json['transactionType'] as String,
      paymentPurpose: json['paymentPurpose'] as String,
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      paypalPaymentId: json['paypalPaymentId'] as String?,
      paypalPayerId: json['paypalPayerId'] as String?,
      referenceId: json['referenceId'] as String?,
      description: json['description'] as String?,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$PaymentTransactionImplToJson(
        _$PaymentTransactionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'transactionType': instance.transactionType,
      'paymentPurpose': instance.paymentPurpose,
      'amount': instance.amount,
      'currency': instance.currency,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'processedAt': instance.processedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'paypalPaymentId': instance.paypalPaymentId,
      'paypalPayerId': instance.paypalPayerId,
      'referenceId': instance.referenceId,
      'description': instance.description,
      'errorMessage': instance.errorMessage,
    };

_$AdStatsImpl _$$AdStatsImplFromJson(Map<String, dynamic> json) =>
    _$AdStatsImpl(
      todayImpressions: (json['todayImpressions'] as num?)?.toInt() ?? 0,
      todayPoints: (json['todayPoints'] as num?)?.toInt() ?? 0,
      dailyLimitRemaining: (json['dailyLimitRemaining'] as num?)?.toInt() ?? 0,
      totalImpressions: (json['totalImpressions'] as num?)?.toInt() ?? 0,
      totalClicks: (json['totalClicks'] as num?)?.toInt() ?? 0,
      totalPointsEarned: (json['totalPointsEarned'] as num?)?.toInt() ?? 0,
      completedRewardedAds:
          (json['completedRewardedAds'] as num?)?.toInt() ?? 0,
      clickThroughRate: (json['clickThroughRate'] as num?)?.toDouble() ?? 0.0,
      maxDailyAdPoints: (json['maxDailyAdPoints'] as num?)?.toInt() ?? 0,
      maxAdsPerSession: (json['maxAdsPerSession'] as num?)?.toInt() ?? 0,
      minTimeBetweenAds: (json['minTimeBetweenAds'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$AdStatsImplToJson(_$AdStatsImpl instance) =>
    <String, dynamic>{
      'todayImpressions': instance.todayImpressions,
      'todayPoints': instance.todayPoints,
      'dailyLimitRemaining': instance.dailyLimitRemaining,
      'totalImpressions': instance.totalImpressions,
      'totalClicks': instance.totalClicks,
      'totalPointsEarned': instance.totalPointsEarned,
      'completedRewardedAds': instance.completedRewardedAds,
      'clickThroughRate': instance.clickThroughRate,
      'maxDailyAdPoints': instance.maxDailyAdPoints,
      'maxAdsPerSession': instance.maxAdsPerSession,
      'minTimeBetweenAds': instance.minTimeBetweenAds,
    };
