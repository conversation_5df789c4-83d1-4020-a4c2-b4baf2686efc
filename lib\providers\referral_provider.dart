import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../models/reward_models.dart';

// Referral state
class ReferralState {
  final List<ReferralData> referrals;
  final ReferralStats stats;
  final bool isLoading;
  final String? error;

  const ReferralState({
    this.referrals = const [],
    this.stats = const ReferralStats(),
    this.isLoading = false,
    this.error,
  });

  ReferralState copyWith({
    List<ReferralData>? referrals,
    ReferralStats? stats,
    bool? isLoading,
    String? error,
  }) {
    return ReferralState(
      referrals: referrals ?? this.referrals,
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Referral notifier
class ReferralNotifier extends StateNotifier<ReferralState> {
  final ApiService _apiService;

  ReferralNotifier(this._apiService) : super(const ReferralState());

  // Load referral data
  Future<void> loadReferralData() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the actual backend API
      final response = await _apiService.getReferralData();

      if (response['success'] == true) {
        final referralsData = response['referrals'] as List? ?? [];
        final statsData = response['stats'] as Map<String, dynamic>? ?? {};

        final referrals = referralsData.map((json) {
          return ReferralData(
            id: json['id']?.toString() ?? '',
            referrerId: json['referrer_id']?.toString() ?? '',
            refereeId: json['referee_id']?.toString() ?? '',
            referralCode: json['referral_code']?.toString() ?? '',
            friendName: json['friend_name']?.toString() ?? '',
            friendLevel: json['friend_level'] ?? 1,
            joinedAt: json['joined_at'] != null ? DateTime.tryParse(json['joined_at']) ?? DateTime.now() : DateTime.now(),
            earnedAmount: double.tryParse(json['earned_amount']?.toString() ?? '0') ?? 0.0,
            wentPremium: json['went_premium'] ?? false,
            reachedLevel5: json['reached_level_5'] ?? false,
            madePurchase: json['made_purchase'] ?? false,
            totalRevenue: double.tryParse(json['total_revenue']?.toString() ?? '0') ?? 0.0,
          );
        }).toList();

        final stats = ReferralStats(
          totalReferrals: statsData['total_referrals'] ?? 0,
          totalEarned: double.tryParse(statsData['total_earned']?.toString() ?? '0') ?? 0.0,
          premiumReferrals: statsData['premium_referrals'] ?? 0,
          activeReferrals: statsData['active_referrals'] ?? 0,
          level5Referrals: statsData['level_5_referrals'] ?? 0,
          referralCode: statsData['referral_code']?.toString() ?? '',
        );

        state = state.copyWith(
          referrals: referrals,
          stats: stats,
          isLoading: false,
        );
      } else {
        // Fallback to empty data if API fails
        state = state.copyWith(
          referrals: [],
          stats: const ReferralStats(
            totalReferrals: 0,
            totalEarned: 0.0,
            premiumReferrals: 0,
            activeReferrals: 0,
            level5Referrals: 0,
            referralCode: '',
          ),
          isLoading: false,
          error: response['message'] ?? 'Failed to load referral data from server',
        );
      }
    } catch (e) {
      print('Error loading referral data: $e');
      state = state.copyWith(
        isLoading: false,
        referrals: [],
        stats: const ReferralStats(
          totalReferrals: 0,
          totalEarned: 0.0,
          premiumReferrals: 0,
          activeReferrals: 0,
          level5Referrals: 0,
          referralCode: '',
        ),
        error: 'Failed to load referral data: ${e.toString()}',
      );
    }
  }

  // Validate referral code
  Future<bool> validateReferralCode(String code) async {
    try {
      final response = await _apiService.validateReferralCode(code);
      return response['valid'] == true;
    } catch (e) {
      return false;
    }
  }

  // Get referral code
  String? getReferralCode() {
    return state.stats.referralCode;
  }

  // Get total earnings from referrals
  double getTotalEarnings() {
    return state.stats.totalEarned;
  }

  // Get referrals by status
  List<ReferralData> getReferralsByStatus(String status) {
    switch (status) {
      case 'premium':
        return state.referrals.where((r) => r.wentPremium).toList();
      case 'level5':
        return state.referrals.where((r) => r.reachedLevel5).toList();
      case 'active':
        return state.referrals.where((r) => !r.wentPremium && !r.reachedLevel5).toList();
      default:
        return state.referrals;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final referralProvider = StateNotifierProvider<ReferralNotifier, ReferralState>((ref) {
  return ReferralNotifier(ApiService());
});

// Convenience providers
final referralStatsProvider = Provider<ReferralStats>((ref) {
  return ref.watch(referralProvider).stats;
});

final referralListProvider = Provider<List<ReferralData>>((ref) {
  return ref.watch(referralProvider).referrals;
});

final referralCodeProvider = Provider<String?>((ref) {
  final notifier = ref.read(referralProvider.notifier);
  return notifier.getReferralCode();
});

final totalReferralEarningsProvider = Provider<double>((ref) {
  final notifier = ref.read(referralProvider.notifier);
  return notifier.getTotalEarnings();
});

final premiumReferralsProvider = Provider<List<ReferralData>>((ref) {
  final notifier = ref.read(referralProvider.notifier);
  return notifier.getReferralsByStatus('premium');
});

final level5ReferralsProvider = Provider<List<ReferralData>>((ref) {
  final notifier = ref.read(referralProvider.notifier);
  return notifier.getReferralsByStatus('level5');
});

final activeReferralsProvider = Provider<List<ReferralData>>((ref) {
  final notifier = ref.read(referralProvider.notifier);
  return notifier.getReferralsByStatus('active');
});
