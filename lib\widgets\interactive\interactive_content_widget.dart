import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/interactive_block.dart';
// import '../../models/code_playground.dart';
import '../../services/interactive_service.dart';

import 'poll_widget.dart';
import 'quiz_widget.dart';

class InteractiveContentWidget extends ConsumerStatefulWidget {
  final int postId;

  const InteractiveContentWidget({super.key, required this.postId});

  @override
  ConsumerState<InteractiveContentWidget> createState() =>
      _InteractiveContentWidgetState();
}

class _InteractiveContentWidgetState
    extends ConsumerState<InteractiveContentWidget> {
  final InteractiveService _interactiveService = InteractiveService();

  List<InteractiveBlock>? _blocks;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadInteractiveBlocks();
  }

  Future<void> _loadInteractiveBlocks() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final blocks = await _interactiveService.getPostInteractiveBlocks(
        widget.postId,
      );

      setState(() {
        _blocks = blocks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.red),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Failed to load interactive content: $_error',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            IconButton(
              onPressed: _loadInteractiveBlocks,
              icon: const Icon(Icons.refresh, color: Colors.red),
            ),
          ],
        ),
      );
    }

    if (_blocks == null || _blocks!.isEmpty) {
      return const SizedBox.shrink(); // No interactive content
    }

    return Column(
      children: _blocks!.map((block) => _buildInteractiveBlock(block)).toList(),
    );
  }

  Widget _buildInteractiveBlock(InteractiveBlock block) {
    switch (block.blockType) {
      case 'poll':
        if (block.poll != null) {
          return PollWidget(
            poll: block.poll!,
            onVoted: () {
              // Optionally refresh the block or show feedback
            },
          );
        }
        break;

      case 'quiz':
        if (block.quiz != null) {
          return QuizWidget(
            quiz: block.quiz!,
            onCompleted: (attempt) {
              // Handle quiz completion
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Quiz completed! Score: ${attempt.score}/${block.quiz!.questions.length}',
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
          );
        }
        break;

      // case 'code':
      //   if (block.codePlayground != null) {
      //     return _buildCodePlaygroundWidget(block.codePlayground!);
      //   }
      //   break;

      default:
        return _buildUnsupportedBlock(block);
    }

    return _buildErrorBlock(block);
  }

  Widget _buildUnsupportedBlock(InteractiveBlock block) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.extension,
                  color: Colors.orange,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      block.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    if (block.description.isNotEmpty)
                      Text(
                        block.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.orange.withValues(alpha: 0.8),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Interactive content type "${block.blockType}" is not yet supported in this version.',
                  style: const TextStyle(fontSize: 12, color: Colors.orange),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorBlock(InteractiveBlock block) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      block.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const Text(
                      'Failed to load interactive content',
                      style: TextStyle(fontSize: 14, color: Colors.red),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget _buildCodePlaygroundWidget(CodePlayground codePlayground) {
  //   return Container(
  //     margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
  //     padding: const EdgeInsets.all(20),
  //     decoration: BoxDecoration(
  //       color: Colors.green.withValues(alpha: 0.1),
  //       borderRadius: BorderRadius.circular(16),
  //       border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Row(
  //           children: [
  //             Container(
  //               padding: const EdgeInsets.all(8),
  //               decoration: BoxDecoration(
  //                 color: Colors.green.withValues(alpha: 0.2),
  //                 borderRadius: BorderRadius.circular(8),
  //               ),
  //               child: const Icon(Icons.code, color: Colors.green, size: 20),
  //             ),
  //             const SizedBox(width: 12),
  //             Expanded(
  //               child: Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   const Text(
  //                     'Code Playground',
  //                     style: TextStyle(
  //                       fontSize: 16,
  //                       fontWeight: FontWeight.bold,
  //                       color: Colors.green,
  //                     ),
  //                   ),
  //                   if (codePlayground.instructions.isNotEmpty)
  //                     Text(
  //                       codePlayground.instructions,
  //                       style: TextStyle(
  //                         fontSize: 14,
  //                         color: Colors.green.withValues(alpha: 0.8),
  //                       ),
  //                     ),
  //                 ],
  //               ),
  //             ),
  //           ],
  //         ),
  //         const SizedBox(height: 16),
  //         Container(
  //           padding: const EdgeInsets.all(12),
  //           decoration: BoxDecoration(
  //             color: Colors.grey.withValues(alpha: 0.1),
  //             borderRadius: BorderRadius.circular(8),
  //             border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
  //           ),
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Row(
  //                 children: [
  //                   const Icon(Icons.code, size: 16, color: Colors.grey),
  //                   const SizedBox(width: 8),
  //                   Text(
  //                     codePlayground.language.toUpperCase(),
  //                     style: const TextStyle(
  //                       fontWeight: FontWeight.w600,
  //                       color: Colors.grey,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               const SizedBox(height: 8),
  //               Container(
  //                 width: double.infinity,
  //                 padding: const EdgeInsets.all(12),
  //                 decoration: BoxDecoration(
  //                   color: Colors.black87,
  //                   borderRadius: BorderRadius.circular(6),
  //                 ),
  //                 child: Text(
  //                   codePlayground.initialCode.isNotEmpty
  //                       ? codePlayground.initialCode
  //                       : '// Code playground functionality\nconsole.log("Hello, World!");',
  //                   style: const TextStyle(
  //                     fontFamily: 'monospace',
  //                     color: Colors.green,
  //                     fontSize: 14,
  //                   ),
  //                 ),
  //               ),
  //               const SizedBox(height: 12),
  //               SizedBox(
  //                 width: double.infinity,
  //                 child: ElevatedButton.icon(
  //                   onPressed: () {
  //                     ScaffoldMessenger.of(context).showSnackBar(
  //                       const SnackBar(
  //                         content: Text('Code execution feature coming soon!'),
  //                         backgroundColor: Colors.green,
  //                       ),
  //                     );
  //                   },
  //                   icon: const Icon(Icons.play_arrow),
  //                   label: const Text('Run Code'),
  //                   style: ElevatedButton.styleFrom(
  //                     backgroundColor: Colors.green,
  //                     foregroundColor: Colors.white,
  //                   ),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
