// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'interactive_block.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InteractiveBlock _$InteractiveBlockFromJson(Map<String, dynamic> json) {
  return _InteractiveBlock.fromJson(json);
}

/// @nodoc
mixin _$InteractiveBlock {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'block_type')
  String get blockType => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  int get position => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool get isActive => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt =>
      throw _privateConstructorUsedError; // Related content (only one will be populated based on block_type)
  Poll? get poll => throw _privateConstructorUsedError;
  Quiz? get quiz => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InteractiveBlockCopyWith<InteractiveBlock> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InteractiveBlockCopyWith<$Res> {
  factory $InteractiveBlockCopyWith(
          InteractiveBlock value, $Res Function(InteractiveBlock) then) =
      _$InteractiveBlockCopyWithImpl<$Res, InteractiveBlock>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'block_type') String blockType,
      String title,
      String description,
      int position,
      @JsonKey(name: 'is_active') bool isActive,
      Map<String, dynamic> metadata,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      Poll? poll,
      Quiz? quiz});

  $PollCopyWith<$Res>? get poll;
  $QuizCopyWith<$Res>? get quiz;
}

/// @nodoc
class _$InteractiveBlockCopyWithImpl<$Res, $Val extends InteractiveBlock>
    implements $InteractiveBlockCopyWith<$Res> {
  _$InteractiveBlockCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? blockType = null,
    Object? title = null,
    Object? description = null,
    Object? position = null,
    Object? isActive = null,
    Object? metadata = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? poll = freezed,
    Object? quiz = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      blockType: null == blockType
          ? _value.blockType
          : blockType // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      poll: freezed == poll
          ? _value.poll
          : poll // ignore: cast_nullable_to_non_nullable
              as Poll?,
      quiz: freezed == quiz
          ? _value.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as Quiz?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PollCopyWith<$Res>? get poll {
    if (_value.poll == null) {
      return null;
    }

    return $PollCopyWith<$Res>(_value.poll!, (value) {
      return _then(_value.copyWith(poll: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $QuizCopyWith<$Res>? get quiz {
    if (_value.quiz == null) {
      return null;
    }

    return $QuizCopyWith<$Res>(_value.quiz!, (value) {
      return _then(_value.copyWith(quiz: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InteractiveBlockImplCopyWith<$Res>
    implements $InteractiveBlockCopyWith<$Res> {
  factory _$$InteractiveBlockImplCopyWith(_$InteractiveBlockImpl value,
          $Res Function(_$InteractiveBlockImpl) then) =
      __$$InteractiveBlockImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'block_type') String blockType,
      String title,
      String description,
      int position,
      @JsonKey(name: 'is_active') bool isActive,
      Map<String, dynamic> metadata,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      Poll? poll,
      Quiz? quiz});

  @override
  $PollCopyWith<$Res>? get poll;
  @override
  $QuizCopyWith<$Res>? get quiz;
}

/// @nodoc
class __$$InteractiveBlockImplCopyWithImpl<$Res>
    extends _$InteractiveBlockCopyWithImpl<$Res, _$InteractiveBlockImpl>
    implements _$$InteractiveBlockImplCopyWith<$Res> {
  __$$InteractiveBlockImplCopyWithImpl(_$InteractiveBlockImpl _value,
      $Res Function(_$InteractiveBlockImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? blockType = null,
    Object? title = null,
    Object? description = null,
    Object? position = null,
    Object? isActive = null,
    Object? metadata = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? poll = freezed,
    Object? quiz = freezed,
  }) {
    return _then(_$InteractiveBlockImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      blockType: null == blockType
          ? _value.blockType
          : blockType // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      metadata: null == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      poll: freezed == poll
          ? _value.poll
          : poll // ignore: cast_nullable_to_non_nullable
              as Poll?,
      quiz: freezed == quiz
          ? _value.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as Quiz?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InteractiveBlockImpl implements _InteractiveBlock {
  const _$InteractiveBlockImpl(
      {required this.id,
      @JsonKey(name: 'block_type') required this.blockType,
      required this.title,
      this.description = '',
      this.position = 0,
      @JsonKey(name: 'is_active') this.isActive = true,
      final Map<String, dynamic> metadata = const {},
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt,
      this.poll,
      this.quiz})
      : _metadata = metadata;

  factory _$InteractiveBlockImpl.fromJson(Map<String, dynamic> json) =>
      _$$InteractiveBlockImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'block_type')
  final String blockType;
  @override
  final String title;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final int position;
  @override
  @JsonKey(name: 'is_active')
  final bool isActive;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
// Related content (only one will be populated based on block_type)
  @override
  final Poll? poll;
  @override
  final Quiz? quiz;

  @override
  String toString() {
    return 'InteractiveBlock(id: $id, blockType: $blockType, title: $title, description: $description, position: $position, isActive: $isActive, metadata: $metadata, createdAt: $createdAt, updatedAt: $updatedAt, poll: $poll, quiz: $quiz)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InteractiveBlockImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.blockType, blockType) ||
                other.blockType == blockType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.poll, poll) || other.poll == poll) &&
            (identical(other.quiz, quiz) || other.quiz == quiz));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      blockType,
      title,
      description,
      position,
      isActive,
      const DeepCollectionEquality().hash(_metadata),
      createdAt,
      updatedAt,
      poll,
      quiz);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InteractiveBlockImplCopyWith<_$InteractiveBlockImpl> get copyWith =>
      __$$InteractiveBlockImplCopyWithImpl<_$InteractiveBlockImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InteractiveBlockImplToJson(
      this,
    );
  }
}

abstract class _InteractiveBlock implements InteractiveBlock {
  const factory _InteractiveBlock(
      {required final int id,
      @JsonKey(name: 'block_type') required final String blockType,
      required final String title,
      final String description,
      final int position,
      @JsonKey(name: 'is_active') final bool isActive,
      final Map<String, dynamic> metadata,
      @JsonKey(name: 'created_at') required final DateTime createdAt,
      @JsonKey(name: 'updated_at') required final DateTime updatedAt,
      final Poll? poll,
      final Quiz? quiz}) = _$InteractiveBlockImpl;

  factory _InteractiveBlock.fromJson(Map<String, dynamic> json) =
      _$InteractiveBlockImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'block_type')
  String get blockType;
  @override
  String get title;
  @override
  String get description;
  @override
  int get position;
  @override
  @JsonKey(name: 'is_active')
  bool get isActive;
  @override
  Map<String, dynamic> get metadata;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  @override // Related content (only one will be populated based on block_type)
  Poll? get poll;
  @override
  Quiz? get quiz;
  @override
  @JsonKey(ignore: true)
  _$$InteractiveBlockImplCopyWith<_$InteractiveBlockImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
