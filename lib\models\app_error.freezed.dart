// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppError _$AppErrorFromJson(Map<String, dynamic> json) {
  return _AppError.fromJson(json);
}

/// @nodoc
mixin _$AppError {
  AppErrorType get type => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String get userMessage => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;
  String? get suggestion => throw _privateConstructorUsedError;
  int? get statusCode => throw _privateConstructorUsedError;
  bool? get isRetryable => throw _privateConstructorUsedError;
  bool get isTemporary => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AppErrorCopyWith<AppError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) then) =
      _$AppErrorCopyWithImpl<$Res, AppError>;
  @useResult
  $Res call(
      {AppErrorType type,
      String message,
      String userMessage,
      String? details,
      String? suggestion,
      int? statusCode,
      bool? isRetryable,
      bool isTemporary});
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res, $Val extends AppError>
    implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? message = null,
    Object? userMessage = null,
    Object? details = freezed,
    Object? suggestion = freezed,
    Object? statusCode = freezed,
    Object? isRetryable = freezed,
    Object? isTemporary = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AppErrorType,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      suggestion: freezed == suggestion
          ? _value.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      isRetryable: freezed == isRetryable
          ? _value.isRetryable
          : isRetryable // ignore: cast_nullable_to_non_nullable
              as bool?,
      isTemporary: null == isTemporary
          ? _value.isTemporary
          : isTemporary // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$AppErrorImplCopyWith(
          _$AppErrorImpl value, $Res Function(_$AppErrorImpl) then) =
      __$$AppErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AppErrorType type,
      String message,
      String userMessage,
      String? details,
      String? suggestion,
      int? statusCode,
      bool? isRetryable,
      bool isTemporary});
}

/// @nodoc
class __$$AppErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$AppErrorImpl>
    implements _$$AppErrorImplCopyWith<$Res> {
  __$$AppErrorImplCopyWithImpl(
      _$AppErrorImpl _value, $Res Function(_$AppErrorImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? message = null,
    Object? userMessage = null,
    Object? details = freezed,
    Object? suggestion = freezed,
    Object? statusCode = freezed,
    Object? isRetryable = freezed,
    Object? isTemporary = null,
  }) {
    return _then(_$AppErrorImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as AppErrorType,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      suggestion: freezed == suggestion
          ? _value.suggestion
          : suggestion // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      isRetryable: freezed == isRetryable
          ? _value.isRetryable
          : isRetryable // ignore: cast_nullable_to_non_nullable
              as bool?,
      isTemporary: null == isTemporary
          ? _value.isTemporary
          : isTemporary // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppErrorImpl implements _AppError {
  const _$AppErrorImpl(
      {required this.type,
      required this.message,
      required this.userMessage,
      this.details,
      this.suggestion,
      this.statusCode,
      this.isRetryable,
      this.isTemporary = false});

  factory _$AppErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppErrorImplFromJson(json);

  @override
  final AppErrorType type;
  @override
  final String message;
  @override
  final String userMessage;
  @override
  final String? details;
  @override
  final String? suggestion;
  @override
  final int? statusCode;
  @override
  final bool? isRetryable;
  @override
  @JsonKey()
  final bool isTemporary;

  @override
  String toString() {
    return 'AppError(type: $type, message: $message, userMessage: $userMessage, details: $details, suggestion: $suggestion, statusCode: $statusCode, isRetryable: $isRetryable, isTemporary: $isTemporary)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppErrorImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.suggestion, suggestion) ||
                other.suggestion == suggestion) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.isRetryable, isRetryable) ||
                other.isRetryable == isRetryable) &&
            (identical(other.isTemporary, isTemporary) ||
                other.isTemporary == isTemporary));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, message, userMessage,
      details, suggestion, statusCode, isRetryable, isTemporary);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AppErrorImplCopyWith<_$AppErrorImpl> get copyWith =>
      __$$AppErrorImplCopyWithImpl<_$AppErrorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppErrorImplToJson(
      this,
    );
  }
}

abstract class _AppError implements AppError {
  const factory _AppError(
      {required final AppErrorType type,
      required final String message,
      required final String userMessage,
      final String? details,
      final String? suggestion,
      final int? statusCode,
      final bool? isRetryable,
      final bool isTemporary}) = _$AppErrorImpl;

  factory _AppError.fromJson(Map<String, dynamic> json) =
      _$AppErrorImpl.fromJson;

  @override
  AppErrorType get type;
  @override
  String get message;
  @override
  String get userMessage;
  @override
  String? get details;
  @override
  String? get suggestion;
  @override
  int? get statusCode;
  @override
  bool? get isRetryable;
  @override
  bool get isTemporary;
  @override
  @JsonKey(ignore: true)
  _$$AppErrorImplCopyWith<_$AppErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
