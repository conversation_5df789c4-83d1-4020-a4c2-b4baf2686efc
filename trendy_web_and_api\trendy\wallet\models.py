from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class UserWallet(models.Model):
    """User's digital wallet for storing money"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet')
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Security and verification
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    verification_date = models.DateTimeField(null=True, blank=True)
    
    # Limits and controls
    daily_spend_limit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('100.00'))
    monthly_spend_limit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('500.00'))
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['balance']),
        ]
    
    def __str__(self):
        return f"{self.user.username}'s Wallet - ${self.balance}"
    
    @property
    def formatted_balance(self):
        return f"${self.balance:.2f}"
    
    def can_spend(self, amount):
        """Check if user can spend the specified amount"""
        return self.balance >= amount and self.is_active
    
    def get_daily_spent(self):
        """Get amount spent today"""
        today = timezone.now().date()
        return WalletTransaction.objects.filter(
            wallet=self,
            transaction_type='debit',
            created_at__date=today
        ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')
    
    def get_monthly_spent(self):
        """Get amount spent this month"""
        now = timezone.now()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        return WalletTransaction.objects.filter(
            wallet=self,
            transaction_type='debit',
            created_at__gte=month_start
        ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')


class WalletTransaction(models.Model):
    """Individual wallet transactions"""
    TRANSACTION_TYPES = [
        ('credit', 'Credit (Money In)'),
        ('debit', 'Debit (Money Out)'),
    ]
    
    TRANSACTION_PURPOSES = [
        ('deposit', 'Wallet Deposit'),
        ('withdrawal', 'Wallet Withdrawal'),
        ('purchase_subscription', 'Premium Subscription'),
        ('purchase_points', 'Point Boost Purchase'),
        ('purchase_virtual_item', 'Virtual Item Purchase'),
        ('refund', 'Refund'),
        ('admin_adjustment', 'Admin Adjustment'),
        ('reward_payout', 'Reward Payout'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic transaction info
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    wallet = models.ForeignKey(UserWallet, on_delete=models.CASCADE, related_name='transactions')
    
    # Transaction details
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TYPES)
    purpose = models.CharField(max_length=30, choices=TRANSACTION_PURPOSES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Balances
    balance_before = models.DecimalField(max_digits=10, decimal_places=2)
    balance_after = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Status and metadata
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    description = models.TextField(blank=True)
    reference_id = models.CharField(max_length=100, blank=True)  # External reference (PayPal, etc.)
    
    # Payment details (for deposits/withdrawals)
    payment_method = models.CharField(max_length=50, blank=True)  # paypal, stripe, etc.
    external_transaction_id = models.CharField(max_length=200, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['wallet', 'transaction_type']),
            models.Index(fields=['wallet', 'status']),
            models.Index(fields=['wallet', 'created_at']),
            models.Index(fields=['purpose', 'status']),
        ]
    
    def __str__(self):
        return f"{self.wallet.user.username} - {self.get_transaction_type_display()} ${self.amount}"
    
    @property
    def formatted_amount(self):
        return f"${self.amount:.2f}"


class WalletDepositRequest(models.Model):
    """Requests to add money to wallet"""
    STATUS_CHOICES = [
        ('pending', 'Pending Payment'),
        ('processing', 'Processing Payment'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    wallet = models.ForeignKey(UserWallet, on_delete=models.CASCADE, related_name='deposit_requests')
    
    # Deposit details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50, default='paypal')
    
    # Payment processing
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    payment_order_id = models.CharField(max_length=200, blank=True)
    external_transaction_id = models.CharField(max_length=200, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['wallet', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return f"Deposit ${self.amount} to {self.wallet.user.username}'s wallet"


class WalletWithdrawalRequest(models.Model):
    """Requests to withdraw money from wallet"""
    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('processing', 'Processing Payment'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    wallet = models.ForeignKey(UserWallet, on_delete=models.CASCADE, related_name='withdrawal_requests')
    
    # Withdrawal details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    withdrawal_method = models.CharField(max_length=50, default='paypal')
    paypal_email = models.EmailField()
    
    # Processing
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    external_transaction_id = models.CharField(max_length=200, blank=True)
    
    # Admin fields
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_withdrawals')
    admin_notes = models.TextField(blank=True)
    rejection_reason = models.TextField(blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['wallet', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]
    
    def __str__(self):
        return f"Withdraw ${self.amount} from {self.wallet.user.username}'s wallet"


class WalletSettings(models.Model):
    """Global wallet system settings"""
    # Deposit settings
    minimum_deposit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('5.00'))
    maximum_deposit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('500.00'))
    deposit_fee_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    
    # Withdrawal settings
    minimum_withdrawal = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('10.00'))
    maximum_withdrawal = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1000.00'))
    withdrawal_fee_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('2.50'))
    withdrawal_fee_fixed = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    
    # System controls
    wallets_enabled = models.BooleanField(default=True)
    deposits_enabled = models.BooleanField(default=True)
    withdrawals_enabled = models.BooleanField(default=True)
    require_verification = models.BooleanField(default=True)
    
    # Limits
    daily_withdrawal_limit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('100.00'))
    monthly_withdrawal_limit = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1000.00'))
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Wallet Settings"
        verbose_name_plural = "Wallet Settings"
    
    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and WalletSettings.objects.exists():
            raise ValueError("Only one WalletSettings instance is allowed")
        super().save(*args, **kwargs)
    
    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class TransactionVerificationCode(models.Model):
    """Store verification codes for transactions"""
    TRANSACTION_TYPES = [
        ('deposit', 'Deposit'),
        ('withdrawal', 'Withdrawal'),
        ('purchase', 'Purchase'),
        ('transfer', 'Transfer'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verification_codes')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    transaction_id = models.CharField(max_length=100)
    verification_code = models.CharField(max_length=10)

    # Status tracking
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    used_at = models.DateTimeField(null=True, blank=True)

    # Security
    attempts = models.PositiveIntegerField(default=0)
    max_attempts = models.PositiveIntegerField(default=3)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'transaction_type', 'is_used']),
            models.Index(fields=['verification_code', 'expires_at']),
            models.Index(fields=['transaction_id']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.transaction_type} - {self.verification_code}"

    @property
    def is_expired(self):
        """Check if verification code is expired"""
        return timezone.now() > self.expires_at

    @property
    def is_valid(self):
        """Check if verification code is valid for use"""
        return not self.is_used and not self.is_expired and self.attempts < self.max_attempts
    
    def __str__(self):
        return "Wallet System Settings"
