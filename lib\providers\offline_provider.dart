import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/app_error.dart';
import '../models/post.dart';
import '../models/category.dart';
import '../models/user.dart';
import '../models/paginated_response.dart';
import '../services/connectivity_service.dart';
import '../services/offline_storage_service.dart';
import '../services/api_service.dart';
import 'provider.dart';

// Connectivity provider
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService();
});

final connectivityStatusProvider = StreamProvider<ConnectionStatus>((ref) {
  final service = ref.watch(connectivityServiceProvider);
  return service.statusStream;
});

final isOnlineProvider = Provider<bool>((ref) {
  final connectivityStatus = ref.watch(connectivityStatusProvider);
  return connectivityStatus.when(
    data: (status) => status == ConnectionStatus.online || status == ConnectionStatus.slow,
    loading: () => false,
    error: (_, __) => false,
  );
});

// Offline storage provider
final offlineStorageServiceProvider = Provider<OfflineStorageService>((ref) {
  return OfflineStorageService();
});

// Offline-aware posts provider
class OfflinePostsNotifier extends StateNotifier<AsyncValue<PaginatedResponse<Post>>> {
  OfflinePostsNotifier(this._apiService, this._storageService, this._connectivityService)
      : super(const AsyncValue.loading()) {
    _initialize();
  }

  final ApiService _apiService;
  final OfflineStorageService _storageService;
  final ConnectivityService _connectivityService;
  
  StreamSubscription<ConnectionStatus>? _connectivitySubscription;
  String? _currentCategory;
  String? _currentSearch;

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  void _initialize() {
    // Listen to connectivity changes
    _connectivitySubscription = _connectivityService.statusStream.listen((status) {
      if (status == ConnectionStatus.online) {
        // Try to refresh data when coming back online
        _refreshFromServer();
      }
    });

    // Load initial data
    loadPosts();
  }

  /// Load posts with offline-first approach
  Future<void> loadPosts({String? category, String? search, bool forceRefresh = false}) async {
    _currentCategory = category;
    _currentSearch = search;

    try {
      // If we can make API calls and not forcing offline, try server first
      if (_connectivityService.canMakeApiCalls() && !forceRefresh) {
        await _loadFromServer();
      } else {
        // Load from cache first
        await _loadFromCache();
        
        // Try to refresh from server in background if online
        if (_connectivityService.canMakeApiCalls()) {
          _refreshFromServer();
        }
      }
    } catch (e) {
      // If server fails, try cache as fallback
      await _loadFromCache();
      
      if (state.value == null) {
        // No cached data available
        state = AsyncValue.error(
          AppErrorHandler.handleError(e),
          StackTrace.current,
        );
      }
    }
  }

  /// Load posts from server
  Future<void> _loadFromServer() async {
    try {
      final response = await _apiService.getPosts(
        category: _currentCategory,
        search: _currentSearch,
      );

      state = AsyncValue.data(response);
      
      // Cache the successful response
      await _storageService.cachePosts(
        response,
        category: _currentCategory,
        search: _currentSearch,
      );
      await _storageService.updateLastSyncTime();
      
    } catch (e) {
      throw AppErrorHandler.handleError(e);
    }
  }

  /// Load posts from cache
  Future<void> _loadFromCache() async {
    try {
      final cachedPosts = await _storageService.getCachedPosts(
        category: _currentCategory,
        search: _currentSearch,
      );

      if (cachedPosts != null) {
        state = AsyncValue.data(cachedPosts);
      }
    } catch (e) {
      print('Failed to load from cache: $e');
    }
  }

  /// Refresh from server in background
  Future<void> _refreshFromServer() async {
    try {
      await _loadFromServer();
    } catch (e) {
      // Silent failure for background refresh
      print('Background refresh failed: $e');
    }
  }

  /// Force refresh from server
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await loadPosts(
      category: _currentCategory,
      search: _currentSearch,
      forceRefresh: true,
    );
  }

  /// Get current error if any
  AppError? get currentError {
    return state.when(
      data: (_) => null,
      loading: () => null,
      error: (error, _) => error is AppError ? error : AppErrorHandler.handleError(error),
    );
  }

  /// Check if data is from cache
  bool get isFromCache {
    return !_connectivityService.canMakeApiCalls() && state.hasValue;
  }
}

// Provider for offline-aware posts
final offlinePostsProvider = StateNotifierProvider<OfflinePostsNotifier, AsyncValue<PaginatedResponse<Post>>>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  final storageService = ref.watch(offlineStorageServiceProvider);
  final connectivityService = ref.watch(connectivityServiceProvider);
  
  return OfflinePostsNotifier(apiService, storageService, connectivityService);
});

// Offline-aware categories provider
class OfflineCategoriesNotifier extends StateNotifier<AsyncValue<PaginatedResponse<Category>>> {
  OfflineCategoriesNotifier(this._apiService, this._storageService, this._connectivityService)
      : super(const AsyncValue.loading()) {
    _initialize();
  }

  final ApiService _apiService;
  final OfflineStorageService _storageService;
  final ConnectivityService _connectivityService;

  void _initialize() {
    loadCategories();
  }

  /// Load categories with offline-first approach
  Future<void> loadCategories({bool forceRefresh = false}) async {
    try {
      // Try cache first for better UX
      if (!forceRefresh) {
        await _loadFromCache();
      }

      // If we can make API calls, try to refresh from server
      if (_connectivityService.canMakeApiCalls()) {
        await _loadFromServer();
      } else if (state.value == null) {
        // No cached data and offline
        state = AsyncValue.error(
          AppErrorHandler.networkError('No internet connection and no cached data available'),
          StackTrace.current,
        );
      }
    } catch (e) {
      // If we have cached data, keep it and show error silently
      if (state.value == null) {
        state = AsyncValue.error(
          AppErrorHandler.handleError(e),
          StackTrace.current,
        );
      }
    }
  }

  Future<void> _loadFromServer() async {
    try {
      final response = await _apiService.getCategories();
      state = AsyncValue.data(response);
      
      // Cache the successful response
      await _storageService.cacheCategories(response);
      await _storageService.updateLastSyncTime();
    } catch (e) {
      throw AppErrorHandler.handleError(e);
    }
  }

  Future<void> _loadFromCache() async {
    try {
      final cachedCategories = await _storageService.getCachedCategories();
      if (cachedCategories != null) {
        state = AsyncValue.data(cachedCategories);
      }
    } catch (e) {
      print('Failed to load categories from cache: $e');
    }
  }

  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await loadCategories(forceRefresh: true);
  }
}

// Provider for offline-aware categories
final offlineCategoriesProvider = StateNotifierProvider<OfflineCategoriesNotifier, AsyncValue<PaginatedResponse<Category>>>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  final storageService = ref.watch(offlineStorageServiceProvider);
  final connectivityService = ref.watch(connectivityServiceProvider);
  
  return OfflineCategoriesNotifier(apiService, storageService, connectivityService);
});

// Cache statistics provider
final cacheStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final storageService = ref.watch(offlineStorageServiceProvider);
  return await storageService.getCacheStats();
});

// Provider to check if we have cached data
final hasCachedDataProvider = FutureProvider<bool>((ref) async {
  final storageService = ref.watch(offlineStorageServiceProvider);
  return await storageService.hasCachedData();
});

// Provider for offline mode toggle
final offlineModeProvider = StateNotifierProvider<OfflineModeNotifier, bool>((ref) {
  final storageService = ref.watch(offlineStorageServiceProvider);
  return OfflineModeNotifier(storageService);
});

class OfflineModeNotifier extends StateNotifier<bool> {
  OfflineModeNotifier(this._storageService) : super(false) {
    _loadOfflineMode();
  }

  final OfflineStorageService _storageService;

  void _loadOfflineMode() {
    state = _storageService.isOfflineModeEnabled;
  }

  Future<void> setOfflineMode(bool enabled) async {
    await _storageService.setOfflineModeEnabled(enabled);
    state = enabled;
  }
}
