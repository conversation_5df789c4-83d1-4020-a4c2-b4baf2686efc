# 👥 Team Collaboration Guide - Database & Development

## **🎯 Quick Start for New Team Members**

### **1. First Time Setup**
```bash
# Clone the repository
git clone <repository-url>
cd trendy

# Run automated setup (recommended)
./setup_new_machine.sh

# Or manual setup
cd trendy_web_and_api/trendy
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py setup_dev_data
```

### **2. Daily Development**
```bash
# Start Django backend
cd trendy_web_and_api/trendy
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000

# Start Flutter frontend (new terminal)
cd trendy
flutter run
```

## **🗄️ Database Collaboration Strategy**

### **❌ What NOT to Do**
- **Don't commit `db.sqlite3`** - Binary files cause conflicts
- **Don't share database files** via email/Slack
- **Don't use SQLite in production**
- **Don't ignore migrations**

### **✅ What TO Do**
- **Use fixtures** for sharing development data
- **Commit fixture files** to Git
- **Use PostgreSQL** for production
- **Always run migrations** after pulling changes

## **📊 Data Sharing Workflow**

### **When You Have New Important Data**
```bash
# 1. Export your data to fixtures
cd trendy_web_and_api/trendy
source venv/bin/activate

# Export specific apps (recommended)
python manage.py dumpdata accounts --indent=2 --output=fixtures/users_data.json
python manage.py dumpdata wallet --indent=2 --output=fixtures/wallet_data.json
python manage.py dumpdata blog --indent=2 --output=fixtures/blog_data.json
python manage.py dumpdata gamification --indent=2 --output=fixtures/gamification_data.json

# 2. Commit and push fixtures
git add fixtures/
git commit -m "Update development data with new users/posts/wallet data"
git push
```

### **When You Need Latest Data**
```bash
# 1. Pull latest changes
git pull

# 2. Load updated data
cd trendy_web_and_api/trendy
source venv/bin/activate
python manage.py setup_dev_data --reset

# This will:
# - Reset the database
# - Run migrations
# - Load all fixture data
```

## **🔄 Development Workflow**

### **Starting a New Feature**
```bash
# 1. Pull latest changes
git pull

# 2. Update your database
python manage.py setup_dev_data --reset

# 3. Create feature branch
git checkout -b feature/new-feature

# 4. Start development
python manage.py runserver 0.0.0.0:8000
```

### **Sharing Your Work**
```bash
# 1. Export any new test data
python manage.py dumpdata accounts wallet blog --indent=2 --output=fixtures/feature_data.json

# 2. Commit your changes
git add .
git commit -m "Add new feature with test data"

# 3. Push and create PR
git push origin feature/new-feature
```

## **🧪 Testing Data Management**

### **Test Users Available**
After running `setup_dev_data`, you'll have:
- **Admin user**: `admin` (superuser)
- **Test users**: `sarah_johnson`, `mike_chen`, `alex_rivera`
- **All users have wallets** with $50 starting balance

### **Test Data Includes**
- **Users**: Multiple test accounts with profiles
- **Wallets**: Pre-funded wallets for testing payments
- **Posts**: Sample blog posts and comments
- **Categories**: Blog categories and tags

### **Creating Additional Test Data**
```bash
# Create more test users
python manage.py shell -c "
from accounts.models import CustomUser
from wallet.models import UserWallet

user = CustomUser.objects.create_user('testuser', '<EMAIL>', 'password123')
wallet = UserWallet.objects.create(user=user, balance=100.00)
print(f'Created user: {user.username} with wallet balance: {wallet.balance}')
"

# Export the new data
python manage.py dumpdata accounts wallet --indent=2 --output=fixtures/updated_test_data.json
```

## **🌐 Network Development**

### **Current Configuration**
- **Django Server**: `http://**************:8000`
- **Flutter App**: Configured for network access
- **Mobile Testing**: Devices on same WiFi can connect

### **Team Network Setup**
1. **Each developer** runs Django on `0.0.0.0:8000`
2. **Update Flutter config** with your IP address in `lib/config/api_config.dart`
3. **Mobile devices** connect to developer's IP for testing

## **🚀 Production Considerations**

### **Database Migration Path**
1. **Development**: SQLite + Fixtures
2. **Staging**: PostgreSQL + Migrations
3. **Production**: PostgreSQL + Proper backups

### **Environment Variables**
```bash
# .env file (don't commit this)
DEBUG=True
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://user:pass@localhost/dbname
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
```

## **🛠️ Troubleshooting**

### **Common Issues & Solutions**

#### **"Fixture loading failed"**
```bash
# Reset everything
python manage.py flush --noinput
python manage.py migrate
python manage.py setup_dev_data
```

#### **"Database is locked"**
```bash
# Stop Django server first, then:
rm db.sqlite3
python manage.py migrate
python manage.py setup_dev_data
```

#### **"Migration conflicts"**
```bash
# Reset migrations (development only!)
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
find . -path "*/migrations/*.pyc" -delete
python manage.py makemigrations
python manage.py migrate
```

#### **"Network connection failed"**
```bash
# Check server is running
curl http://**************:8000/api/v1/monetization/settings/

# Update Flutter config with correct IP
# Edit lib/config/api_config.dart
```

## **📋 Team Checklist**

### **Before Starting Work**
- [ ] Pull latest changes (`git pull`)
- [ ] Update database (`python manage.py setup_dev_data --reset`)
- [ ] Start Django server (`python manage.py runserver 0.0.0.0:8000`)
- [ ] Test API endpoints work
- [ ] Start Flutter app (`flutter run`)

### **Before Committing**
- [ ] Test your changes work
- [ ] Export important data to fixtures
- [ ] Add fixtures to Git (`git add fixtures/`)
- [ ] Write descriptive commit message
- [ ] Push changes (`git push`)

### **Code Review Checklist**
- [ ] Migrations included if models changed
- [ ] Fixtures updated if test data changed
- [ ] No `db.sqlite3` files committed
- [ ] Network configuration documented
- [ ] Tests pass on clean database

## **🎉 Success Metrics**

### **You'll Know It's Working When:**
- ✅ New team members can setup in < 5 minutes
- ✅ Everyone has the same test data
- ✅ No database conflicts in Git
- ✅ Mobile testing works across devices
- ✅ Features work consistently across machines

This approach ensures smooth collaboration and consistent development environments! 🚀
