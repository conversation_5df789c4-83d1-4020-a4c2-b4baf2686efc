#!/usr/bin/env python3
"""
Quick fix script to set up blockchain networks for existing users
Run this after creating superusers to ensure blockchain wallets can be created
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.join(os.path.dirname(__file__), 'trendy_web_and_api', 'trendy'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from django.conf import settings
from blockchain.models import BlockchainNetwork, SmartContract
from accounts.models import User
from blockchain.services import BlockchainService


def setup_blockchain_networks():
    """Set up blockchain networks from settings"""
    print("🌐 Setting up blockchain networks...")
    
    for network_name, config in settings.BLOCKCHAIN_NETWORKS.items():
        network, created = BlockchainNetwork.objects.get_or_create(
            name=network_name,
            defaults={
                'chain_id': config['chain_id'],
                'rpc_url': config['rpc_url'],
                'explorer_url': config['explorer_url'],
                'native_token': config['native_token'],
                'gas_price_gwei': config['gas_price_gwei'],
                'is_active': True
            }
        )
        
        if created:
            print(f"   ✅ Created: {network.get_name_display()}")
        else:
            print(f"   ℹ️  Already exists: {network.get_name_display()}")


def setup_basic_contracts():
    """Set up basic smart contracts"""
    print("📄 Setting up basic smart contracts...")
    
    # Mock TRD Token ABI (simplified)
    token_abi = [
        {
            "inputs": [],
            "name": "name",
            "outputs": [{"internalType": "string", "name": "", "type": "string"}],
            "stateMutability": "view",
            "type": "function"
        }
    ]
    
    # Create contracts for each network
    for network in BlockchainNetwork.objects.filter(is_active=True):
        # TRD Token Contract
        token_contract, created = SmartContract.objects.get_or_create(
            network=network,
            contract_type='token',
            name='Trendy Token',
            defaults={
                'address': '******************************************',  # Mock address
                'abi': token_abi,
                'is_active': True
            }
        )
        
        if created:
            print(f"   ✅ Created TRD Token on {network.get_name_display()}")


def create_missing_wallets():
    """Create blockchain wallets for users who don't have them"""
    print("👛 Creating missing blockchain wallets...")
    
    try:
        # Get the default network
        default_network = BlockchainNetwork.objects.get(
            name=settings.DEFAULT_BLOCKCHAIN_NETWORK,
            is_active=True
        )
        
        # Find users without blockchain wallets
        users_without_wallets = User.objects.filter(blockchain_wallets__isnull=True).distinct()
        
        if not users_without_wallets.exists():
            print("   ℹ️  All users already have blockchain wallets")
            return
        
        blockchain_service = BlockchainService(network_name=default_network.name)
        
        for user in users_without_wallets:
            try:
                wallet = blockchain_service.create_user_wallet(user)
                print(f"   ✅ Created wallet for {user.username}: {wallet.address}")
            except Exception as e:
                print(f"   ❌ Failed to create wallet for {user.username}: {e}")
                
    except BlockchainNetwork.DoesNotExist:
        print(f"   ❌ Default network '{settings.DEFAULT_BLOCKCHAIN_NETWORK}' not found")
    except Exception as e:
        print(f"   ❌ Error creating wallets: {e}")


def main():
    """Main function"""
    print("🔧 Fixing Blockchain Setup for Trendy App")
    print("=" * 50)
    
    try:
        # Step 1: Set up blockchain networks
        setup_blockchain_networks()
        
        # Step 2: Set up basic contracts
        setup_basic_contracts()
        
        # Step 3: Create missing wallets
        create_missing_wallets()
        
        print("\n" + "=" * 50)
        print("✅ Blockchain setup completed successfully!")
        print("\n📋 What was done:")
        print("   🌐 Blockchain networks created")
        print("   📄 Basic smart contracts set up")
        print("   👛 Missing user wallets created")
        print("\n💡 You can now create superusers without errors!")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
