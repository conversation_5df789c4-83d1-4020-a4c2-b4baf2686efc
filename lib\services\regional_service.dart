import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:trendy/config/api_config.dart';
import 'package:trendy/models/country.dart';
import 'package:trendy/services/auth_service.dart';

class RegionalService {
  static const String _baseUrl = '${ApiConfig.baseUrl}/api/v1/regional';

  /// Get list of available countries
  static Future<List<Country>> getCountries() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/countries/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> countriesJson = data['countries'];
          return countriesJson.map((json) => Country.fromJson(json)).toList();
        }
      }
      throw Exception('Failed to load countries');
    } catch (e) {
      throw Exception('Error fetching countries: $e');
    }
  }

  /// Get list of available regions with countries
  static Future<List<Region>> getRegions() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/regions/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> regionsJson = data['regions'];
          return regionsJson.map((json) => Region.fromJson(json)).toList();
        }
      }
      throw Exception('Failed to load regions');
    } catch (e) {
      throw Exception('Error fetching regions: $e');
    }
  }

  /// Get user's regional preferences
  static Future<RegionalPreferences> getUserRegionalPreferences() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/preferences/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return RegionalPreferences.fromJson(data['preferences']);
        }
      }
      throw Exception('Failed to load regional preferences');
    } catch (e) {
      throw Exception('Error fetching regional preferences: $e');
    }
  }

  /// Set user's preferred country
  static Future<bool> setPreferredCountry(String countryCode) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/preferences/country/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: json.encode({'country_code': countryCode}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      throw Exception('Error setting preferred country: $e');
    }
  }

  /// Update regional settings
  static Future<bool> updateRegionalSettings({
    bool? showGlobalContent,
    bool? autoDetectLocation,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final Map<String, dynamic> body = {};
      if (showGlobalContent != null) {
        body['show_global_content'] = showGlobalContent;
      }
      if (autoDetectLocation != null) {
        body['auto_detect_location'] = autoDetectLocation;
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/preferences/settings/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      throw Exception('Error updating regional settings: $e');
    }
  }

  /// Manually trigger location detection with enhanced fallback methods
  static Future<Country?> detectLocation() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      // Try enhanced detection first (with multiple fallback methods)
      final response = await http.post(
        Uri.parse('$_baseUrl/detect-location-enhanced/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true && data['detected_country'] != null) {
          print('✅ Location detected using method: ${data['method']}');
          print('📍 Detected country: ${data['detected_country']['name']}');
          return Country.fromJson(data['detected_country']);
        }
      } else {
        // If enhanced detection fails, try the original method as fallback
        final fallbackResponse = await http.post(
          Uri.parse('$_baseUrl/detect-location/'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Token $token',
          },
        );

        if (fallbackResponse.statusCode == 200) {
          final fallbackData = json.decode(fallbackResponse.body);
          if (fallbackData['success'] == true &&
              fallbackData['detected_country'] != null) {
            print('✅ Location detected using fallback method');
            print(
                '📍 Detected country: ${fallbackData['detected_country']['name']}');
            return Country.fromJson(fallbackData['detected_country']);
          }
        }
      }
      return null;
    } catch (e) {
      throw Exception('Error detecting location: $e');
    }
  }

  /// Get regional content statistics
  static Future<RegionalStats> getRegionalStats() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/stats/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return RegionalStats.fromJson(data['stats']);
        }
      }
      throw Exception('Failed to load regional stats');
    } catch (e) {
      throw Exception('Error fetching regional stats: $e');
    }
  }

  /// Get user's location detection history
  static Future<List<LocationHistory>> getLocationHistory() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/location-history/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> historyJson = data['history'];
          return historyJson
              .map((json) => LocationHistory.fromJson(json))
              .toList();
        }
      }
      throw Exception('Failed to load location history');
    } catch (e) {
      throw Exception('Error fetching location history: $e');
    }
  }

  /// Clear user's preferred country (use detected country)
  static Future<bool> clearPreferredCountry() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/preferences/country/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: json.encode({'country_code': ''}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      }
      return false;
    } catch (e) {
      throw Exception('Error clearing preferred country: $e');
    }
  }

  /// Get country by code
  static Future<Country?> getCountryByCode(String code) async {
    try {
      final countries = await getCountries();
      return countries.firstWhere(
        (country) => country.code.toLowerCase() == code.toLowerCase(),
        orElse: () => throw Exception('Country not found'),
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if user has regional preferences set
  static Future<bool> hasRegionalPreferences() async {
    try {
      final preferences = await getUserRegionalPreferences();
      return preferences.preferredCountry != null ||
          preferences.detectedCountry != null;
    } catch (e) {
      return false;
    }
  }
}
