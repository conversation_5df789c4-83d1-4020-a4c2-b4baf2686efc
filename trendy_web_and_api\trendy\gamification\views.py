from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count, Sum, Avg, Q
from django.db import transaction
from django.utils import timezone
from .models import Badge, UserBadge, Challenge, ChallengeParticipation, UserLevel, PointTransaction, PayPalReward, UserPayPalReward, PayPalSettings
from .serializers import (
    BadgeSerializer, UserBadgeSerializer, ChallengeSerializer, ChallengeParticipationSerializer,
    UserLevelSerializer, PointTransactionSerializer, UserProfileSerializer,
    LeaderboardSerializer, ChallengeStatsSerializer, GamificationStatsSerializer,
    JoinChallengeSerializer, UpdateChallengeProgressSerializer
)
from .services import GamificationService

@api_view(['GET'])
@permission_classes([AllowAny])
def badges_list(request):
    """Get all available badges"""
    try:
        # Filter out secret badges unless user has earned them
        badges = Badge.objects.filter(is_active=True)

        if request.user.is_authenticated:
            # Show secret badges if user has earned them
            earned_badge_ids = UserBadge.objects.filter(user=request.user).values_list('badge_id', flat=True)
            badges = badges.filter(Q(is_secret=False) | Q(id__in=earned_badge_ids))
        else:
            badges = badges.filter(is_secret=False)

        serializer = BadgeSerializer(badges, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_badges(request):
    """Get user's earned badges"""
    try:
        badges = UserBadge.objects.filter(user=request.user).select_related('badge').order_by('-earned_at')
        serializer = UserBadgeSerializer(badges, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def challenges_list(request):
    """Get all challenges"""
    try:
        challenges = Challenge.objects.filter(is_active=True).order_by('-is_featured', '-start_date')

        # Filter by type if specified
        challenge_type = request.GET.get('type')
        if challenge_type:
            challenges = challenges.filter(challenge_type=challenge_type)

        # Filter by status
        status_filter = request.GET.get('status')
        if status_filter == 'ongoing':
            from django.utils import timezone
            now = timezone.now()
            challenges = challenges.filter(start_date__lte=now, end_date__gte=now)
        elif status_filter == 'upcoming':
            from django.utils import timezone
            now = timezone.now()
            challenges = challenges.filter(start_date__gt=now)

        serializer = ChallengeSerializer(
            challenges,
            many=True,
            context={'request': request}
        )
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def join_challenge(request, challenge_id):
    """Join a challenge"""
    try:
        challenge = get_object_or_404(Challenge, id=challenge_id, is_active=True)

        # Check if challenge is ongoing
        if not challenge.is_ongoing:
            return Response({
                'error': 'Challenge is not currently active'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user is already participating
        if ChallengeParticipation.objects.filter(user=request.user, challenge=challenge).exists():
            return Response({
                'error': 'You are already participating in this challenge'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check participant limit
        if challenge.max_participants:
            current_participants = challenge.participant_count
            if current_participants >= challenge.max_participants:
                return Response({
                    'error': 'Challenge has reached maximum participants'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Create participation
        participation = ChallengeParticipation.objects.create(
            user=request.user,
            challenge=challenge
        )

        serializer = ChallengeParticipationSerializer(participation)
        return Response({
            'message': 'Successfully joined challenge',
            'participation': serializer.data
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_challenge_progress(request, challenge_id):
    """Update progress in a challenge"""
    try:
        serializer = UpdateChallengeProgressSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        progress_data = serializer.validated_data['progress_data']

        participation = GamificationService.update_challenge_progress(
            user=request.user,
            challenge_id=challenge_id,
            progress_data=progress_data
        )

        if not participation:
            return Response({
                'error': 'You are not participating in this challenge'
            }, status=status.HTTP_404_NOT_FOUND)

        response_serializer = ChallengeParticipationSerializer(participation)
        return Response({
            'message': 'Progress updated successfully',
            'participation': response_serializer.data
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """Get user's gamification profile"""
    try:
        serializer = UserProfileSerializer(request.user, context={'request': request})
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_level(request):
    """Get user's level information"""
    try:
        user_level = GamificationService.get_or_create_user_level(request.user)
        serializer = UserLevelSerializer(user_level)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def leaderboard(request):
    """Get leaderboard data"""
    try:
        # Get top users by points
        limit = min(int(request.GET.get('limit', 50)), 100)

        users = UserLevel.objects.select_related('user').order_by(
            '-current_level', '-total_points'
        )[:limit]

        leaderboard_data = []
        for rank, user_level in enumerate(users, 1):
            badge_count = UserBadge.objects.filter(user=user_level.user).count()

            leaderboard_data.append({
                'user_id': user_level.user.id,
                'username': user_level.user.username,
                'total_points': user_level.total_points,
                'current_level': user_level.current_level,
                'rank': rank,
                'badge_count': badge_count,
            })

        serializer = LeaderboardSerializer(leaderboard_data, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_transactions(request):
    """Get user's point transactions"""
    try:
        transactions = PointTransaction.objects.filter(user=request.user).order_by('-created_at')

        # Pagination
        limit = min(int(request.GET.get('limit', 20)), 100)
        offset = int(request.GET.get('offset', 0))
        transactions = transactions[offset:offset + limit]

        serializer = PointTransactionSerializer(transactions, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def challenge_stats(request):
    """Get challenge statistics"""
    try:
        if not request.user.is_staff:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        total_challenges = Challenge.objects.count()
        active_challenges = Challenge.objects.filter(is_active=True).count()

        # Get completion stats
        participations = ChallengeParticipation.objects.all()
        completed_challenges = participations.filter(is_completed=True).count()
        total_participants = participations.values('user').distinct().count()

        completion_rate = (completed_challenges / participations.count() * 100) if participations.count() > 0 else 0

        stats = {
            'total_challenges': total_challenges,
            'active_challenges': active_challenges,
            'completed_challenges': completed_challenges,
            'total_participants': total_participants,
            'completion_rate': round(completion_rate, 2),
        }

        serializer = ChallengeStatsSerializer(stats)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def gamification_stats(request):
    """Get overall gamification statistics"""
    try:
        if not request.user.is_staff:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        total_users = UserLevel.objects.count()
        total_points = UserLevel.objects.aggregate(Sum('total_points'))['total_points__sum'] or 0
        total_badges = UserBadge.objects.count()
        avg_level = UserLevel.objects.aggregate(Avg('current_level'))['current_level__avg'] or 0

        # Most popular badge
        popular_badge = UserBadge.objects.values('badge__name').annotate(
            count=Count('id')
        ).order_by('-count').first()

        # Top challenge
        top_challenge = ChallengeParticipation.objects.values('challenge__title').annotate(
            count=Count('id')
        ).order_by('-count').first()

        stats = {
            'total_users': total_users,
            'total_points_awarded': total_points,
            'total_badges_earned': total_badges,
            'average_level': round(avg_level, 2),
            'most_popular_badge': popular_badge or {},
            'top_challenge': top_challenge or {},
        }

        serializer = GamificationStatsSerializer(stats)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def debug_headers(request):
    """Debug endpoint to check what headers are being sent"""
    return Response({
        'headers': dict(request.headers),
        'user': str(request.user),
        'is_authenticated': request.user.is_authenticated,
        'auth_header': request.headers.get('Authorization', 'NOT FOUND'),
        'method': request.method,
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def award_points_manual(request):
    """Manually award points (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        user_id = request.data.get('user_id')
        points = request.data.get('points')
        description = request.data.get('description', 'Manual point award')

        if not user_id or not points:
            return Response({
                'error': 'user_id and points are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = get_object_or_404(User, id=user_id)

        user_level, levels_gained = GamificationService.award_points(
            user=user,
            points=int(points),
            transaction_type='bonus',
            description=description
        )

        return Response({
            'message': f'Awarded {points} points to {user.username}',
            'levels_gained': levels_gained,
            'new_level': user_level.current_level,
            'total_points': user_level.total_points
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


# PayPal Reward Endpoints

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def paypal_rewards_list(request):
    """Get available PayPal rewards for the authenticated user"""
    try:
        from .paypal_service import PayPalRewardService
        from django.utils import timezone
        from decimal import Decimal

        user = request.user
        settings = PayPalSettings.get_settings()

        if not settings.rewards_enabled:
            return Response({
                'success': False,
                'message': 'PayPal rewards are currently disabled'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        # Get user level info
        user_level = GamificationService.get_or_create_user_level(user)

        # Get available rewards
        available_rewards = PayPalRewardService.get_available_rewards_for_user(user)

        # Get claimed rewards
        claimed_rewards = UserPayPalReward.objects.filter(user=user).select_related('reward')

        # Format available rewards
        available_data = []
        for reward in available_rewards:
            available_data.append({
                'id': reward.id,
                'name': reward.name,
                'description': reward.description,
                'usd_amount': str(reward.usd_amount),
                'reward_type': reward.reward_type,
                'points_required': reward.points_required,
                'level_required': reward.level_required,
                'streak_required': reward.streak_required,
                'requirements': reward.requirements,
                'is_featured': reward.is_featured,
                'claims_remaining': reward.claims_remaining if reward.claims_remaining != float('inf') else None,
                'minimum_account_age_days': reward.minimum_account_age_days,
            })

        # Format claimed rewards
        claimed_data = []
        for claim in claimed_rewards:
            claimed_data.append({
                'id': claim.id,
                'reward_name': claim.reward.name,
                'usd_amount': str(claim.usd_amount),
                'paypal_email': claim.paypal_email,
                'status': claim.status,
                'claim_date': claim.claim_date.isoformat(),
                'approved_date': claim.approved_date.isoformat() if claim.approved_date else None,
                'paid_date': claim.paid_date.isoformat() if claim.paid_date else None,
                'paypal_transaction_id': claim.paypal_transaction_id,
            })

        # Calculate user stats
        account_age_days = (timezone.now() - user.date_joined).days

        # Get monthly claim total
        current_month_claims = UserPayPalReward.objects.filter(
            user=user,
            claim_date__gte=timezone.now().replace(day=1)
        ).aggregate(total=Sum('usd_amount'))['total'] or Decimal('0')

        return Response({
            'success': True,
            'data': {
                'user_stats': {
                    'level': user_level.current_level,
                    'total_points': user_level.total_points,
                    'reading_streak': user_level.reading_streak,
                    'writing_streak': user_level.writing_streak,
                    'account_age_days': account_age_days,
                    'monthly_claims_total': str(current_month_claims),
                    'monthly_limit': str(settings.maximum_monthly_payout_per_user),
                    'monthly_remaining': str(settings.maximum_monthly_payout_per_user - current_month_claims),
                },
                'available_rewards': available_data,
                'claimed_rewards': claimed_data,
                'settings': {
                    'minimum_payout': str(settings.minimum_payout),
                    'maximum_monthly_payout': str(settings.maximum_monthly_payout_per_user),
                    'require_email_verification': settings.require_email_verification,
                    'minimum_account_age_days': settings.minimum_account_age_days,
                }
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching PayPal rewards: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_paypal_rewards(request):
    """Get user's PayPal reward history"""
    try:
        user = request.user

        # Get user's claimed rewards
        user_rewards = UserPayPalReward.objects.filter(user=user).order_by('-claim_date')

        rewards_data = []
        for reward in user_rewards:
            rewards_data.append({
                'id': reward.id,
                'reward_name': reward.reward.name,
                'reward_type': reward.reward.reward_type,
                'usd_amount': str(reward.usd_amount),
                'paypal_email': reward.paypal_email,
                'status': reward.status,
                'claim_date': reward.claim_date.isoformat() if reward.claim_date else None,
                'paid_date': reward.paid_date.isoformat() if reward.paid_date else None,
                'user_points_at_claim': reward.user_points_at_claim,
                'user_level_at_claim': reward.user_level_at_claim,
            })

        return Response({
            'success': True,
            'rewards': rewards_data,
            'total_earned': sum(float(r.usd_amount) for r in user_rewards if r.status == 'completed'),
            'total_pending': sum(float(r.usd_amount) for r in user_rewards if r.status in ['pending', 'approved']),
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error fetching user PayPal rewards: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def claim_paypal_reward(request, reward_id):
    """Claim a PayPal reward"""
    try:
        from .paypal_service import PayPalRewardService

        user = request.user
        paypal_email = request.data.get('paypal_email')

        if not paypal_email:
            return Response({
                'success': False,
                'message': 'PayPal email is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Use the PayPal reward service to claim the reward
        success, message = PayPalRewardService.claim_reward(user, reward_id, paypal_email)

        if success:
            # Get updated user level for response
            user_level = GamificationService.get_or_create_user_level(user)

            # Get the reward for response details
            try:
                reward = PayPalReward.objects.get(id=reward_id)
                return Response({
                    'success': True,
                    'message': message,
                    'remaining_points': user_level.total_points,
                    'reward_amount': str(reward.usd_amount),
                })
            except PayPalReward.DoesNotExist:
                return Response({
                    'success': True,
                    'message': message,
                    'remaining_points': user_level.total_points,
                })
        else:
            # Determine appropriate status code based on error message
            if 'not found' in message.lower():
                status_code = status.HTTP_404_NOT_FOUND
            elif 'qualify' in message.lower() or 'insufficient' in message.lower() or 'already claimed' in message.lower():
                status_code = status.HTTP_400_BAD_REQUEST
            else:
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

            return Response({
                'success': False,
                'message': message
            }, status=status_code)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error claiming reward: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Tier Unlock Endpoints

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_unlocked_tiers(request):
    """Get user's unlocked reward tiers"""
    try:
        from monetization.services import MonetizationService

        user = request.user
        unlocked_tiers = MonetizationService.get_user_unlocked_tiers(user)

        # Get tier prices for reference
        from monetization.models import MonetizationSettings
        settings = MonetizationSettings.get_settings()

        tier_info = {
            'starter': {'price': '0.00', 'unlocked': True},  # Always unlocked
            'engagement': {
                'price': str(settings.engagement_tier_price),
                'unlocked': 'engagement' in unlocked_tiers
            },
            'achievement': {
                'price': str(settings.achievement_tier_price),
                'unlocked': 'achievement' in unlocked_tiers
            },
            'elite': {
                'price': str(settings.elite_tier_price),
                'unlocked': 'elite' in unlocked_tiers
            },
        }

        return Response({
            'success': True,
            'unlocked_tiers': unlocked_tiers,
            'tier_info': tier_info
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def unlock_tier(request):
    """Unlock a reward tier (requires payment for paid tiers)"""
    try:
        from monetization.services import MonetizationService

        tier = request.data.get('tier')
        payment_method = request.data.get('payment_method', 'stripe')

        if not tier:
            return Response({
                'success': False,
                'error': 'Tier is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # For paid tiers, this should redirect to payment flow
        # For now, we'll return payment required status
        from monetization.models import MonetizationSettings
        settings = MonetizationSettings.get_settings()

        tier_prices = {
            'starter': 0.00,
            'engagement': float(settings.engagement_tier_price),
            'achievement': float(settings.achievement_tier_price),
            'elite': float(settings.elite_tier_price),
        }

        if tier not in tier_prices:
            return Response({
                'success': False,
                'error': 'Invalid tier'
            }, status=status.HTTP_400_BAD_REQUEST)

        price = tier_prices[tier]

        if price > 0:
            # Return payment required - frontend should redirect to payment
            return Response({
                'success': False,
                'payment_required': True,
                'tier': tier,
                'price': price,
                'message': f'Payment of ${price:.2f} required to unlock {tier} tier'
            }, status=status.HTTP_402_PAYMENT_REQUIRED)
        else:
            # Free tier - unlock immediately
            success, message = MonetizationService.unlock_reward_tier(
                user=request.user,
                tier=tier,
                payment_method=payment_method
            )

            return Response({
                'success': success,
                'message': message
            })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Admin Reward Management Endpoints

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_pending_rewards(request):
    """Get all pending reward claims (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        pending_claims = UserPayPalReward.objects.filter(
            status='pending'
        ).select_related('user', 'reward').order_by('-claim_date')

        claims_data = []
        for claim in pending_claims:
            claims_data.append({
                'id': claim.id,
                'user_id': claim.user.id,
                'username': claim.user.username,
                'user_email': claim.user.email,
                'reward_name': claim.reward.name,
                'usd_amount': str(claim.usd_amount),
                'paypal_email': claim.paypal_email,
                'status': claim.status,
                'claim_date': claim.claim_date.isoformat(),
                'user_points_at_claim': claim.user_points_at_claim,
                'user_level_at_claim': claim.user_level_at_claim,
                'verification_notes': claim.verification_notes,
            })

        return Response({
            'success': True,
            'pending_claims': claims_data,
            'total_pending': len(claims_data),
            'total_amount': sum(float(claim.usd_amount) for claim in pending_claims)
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def admin_approve_reward(request, claim_id):
    """Approve a reward claim (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        from .paypal_service import PayPalRewardService

        notes = request.data.get('notes', '')
        success, message = PayPalRewardService.approve_reward_claim(claim_id, request.user, notes)

        if success:
            return Response({
                'success': True,
                'message': message
            })
        else:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def admin_process_payment(request, claim_id):
    """Process PayPal payment for approved claim (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        from .paypal_service import PayPalRewardService

        success, message = PayPalRewardService.process_paypal_payment(claim_id)

        if success:
            return Response({
                'success': True,
                'message': message
            })
        else:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_reward_stats(request):
    """Get reward system statistics (admin only)"""
    try:
        if not request.user.is_staff:
            return Response({
                'success': False,
                'message': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        from django.db.models import Count, Sum

        # Get claim statistics
        total_claims = UserPayPalReward.objects.count()
        pending_claims = UserPayPalReward.objects.filter(status='pending').count()
        approved_claims = UserPayPalReward.objects.filter(status='approved').count()
        paid_claims = UserPayPalReward.objects.filter(status='paid').count()

        # Get monetary statistics
        total_claimed = UserPayPalReward.objects.aggregate(
            total=Sum('usd_amount')
        )['total'] or 0

        total_pending = UserPayPalReward.objects.filter(
            status='pending'
        ).aggregate(total=Sum('usd_amount'))['total'] or 0

        total_paid = UserPayPalReward.objects.filter(
            status='paid'
        ).aggregate(total=Sum('usd_amount'))['total'] or 0

        return Response({
            'success': True,
            'stats': {
                'total_claims': total_claims,
                'pending_claims': pending_claims,
                'approved_claims': approved_claims,
                'paid_claims': paid_claims,
                'total_claimed_amount': float(total_claimed),
                'total_pending_amount': float(total_pending),
                'total_paid_amount': float(total_paid),
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def engagement_status(request):
    """Get user's engagement status and limits"""
    try:
        from .models import EngagementSettings, UserEngagementTracker, PostReadingHistory, EngagementHistory

        settings = EngagementSettings.get_settings()
        tracker = GamificationService.get_or_create_engagement_tracker(request.user)
        tracker.reset_daily_counters_if_needed()

        # Get user's activity counts
        reading_count = PostReadingHistory.objects.filter(user=request.user).count()
        engagement_count = EngagementHistory.objects.filter(user=request.user).count()

        return Response({
            'success': True,
            'settings': {
                'fraud_detection_enabled': settings.enable_fraud_detection,
                'daily_limits': {
                    'posts_read': settings.max_posts_read_per_day,
                    'comments': settings.max_comments_per_day,
                    'likes': settings.max_likes_per_day,
                },
                'requirements': {
                    'min_reading_time': settings.min_reading_time_seconds,
                    'min_scroll_percentage': settings.min_scroll_percentage,
                },
                'cooldowns': {
                    'like_cooldown': settings.like_cooldown,
                    'comment_cooldown': settings.comment_cooldown,
                }
            },
            'user_status': {
                'today': {
                    'posts_read': tracker.posts_read_today,
                    'comments': tracker.comments_today,
                    'likes': tracker.likes_today,
                },
                'total': {
                    'posts_read': reading_count,
                    'engagements': engagement_count,
                },
                'flags': {
                    'is_flagged': tracker.is_flagged,
                    'flag_reason': tracker.flag_reason if tracker.is_flagged else None,
                },
                'remaining_today': {
                    'posts_read': max(0, settings.max_posts_read_per_day - tracker.posts_read_today),
                    'comments': max(0, settings.max_comments_per_day - tracker.comments_today),
                    'likes': max(0, settings.max_likes_per_day - tracker.likes_today),
                }
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_reading_activity(request):
    """Validate if user can earn points for reading activity"""
    try:
        post_id = request.data.get('post_id')
        time_spent = request.data.get('time_spent', 0)
        scroll_percentage = request.data.get('scroll_percentage', 0.0)

        if not post_id:
            return Response({
                'success': False,
                'error': 'Post ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        can_earn, reason = GamificationService.can_earn_reading_points(
            request.user, post_id, time_spent, scroll_percentage
        )

        return Response({
            'success': True,
            'can_earn_points': can_earn,
            'reason': reason
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_engagement_activity(request):
    """Validate if user can earn points for engagement activity"""
    try:
        activity_type = request.data.get('activity_type')
        object_id = request.data.get('object_id')
        target_type = request.data.get('target_type', 'post')

        if not activity_type or not object_id:
            return Response({
                'success': False,
                'error': 'Activity type and object ID are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        can_earn, reason = GamificationService.can_earn_engagement_points(
            request.user, activity_type, object_id, target_type
        )

        return Response({
            'success': True,
            'can_earn_points': can_earn,
            'reason': reason
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def conversion_settings(request):
    """Get point conversion settings and user status"""
    try:
        from .models import PointConversionSettings, UserStorePoints

        settings = PointConversionSettings.get_settings()
        user_level = GamificationService.get_or_create_user_level(request.user)
        user_store_points, created = UserStorePoints.objects.get_or_create(
            user=request.user,
            defaults={'balance': 0}
        )

        # Calculate user's conversion rate
        user_conversion_rate = settings.calculate_conversion_rate(request.user)

        # Check premium status
        is_premium = False
        try:
            from monetization.services import MonetizationService
            premium_status = MonetizationService.get_user_premium_status(request.user)
            is_premium = premium_status.get('is_premium', False)
        except:
            pass

        user_store_points.reset_daily_conversions_if_needed()

        return Response({
            'success': True,
            'settings': {
                'conversion_enabled': settings.conversion_enabled,
                'maintenance_mode': settings.maintenance_mode,
                'base_conversion_rate': settings.base_conversion_rate,
                'user_conversion_rate': user_conversion_rate,
                'minimum_conversion': settings.minimum_conversion_amount,
                'maximum_conversion': settings.maximum_conversion_amount,
                'daily_limit': settings.daily_conversion_limit,
                'conversion_fee_percentage': settings.conversion_fee_percentage,
                'conversion_fee_fixed': settings.conversion_fee_fixed,
                'level_bonus_enabled': settings.level_bonus_enabled,
                'premium_bonus_enabled': settings.premium_bonus_enabled,
            },
            'user_status': {
                'gamification_points': user_level.total_points,
                'store_points': user_store_points.balance,
                'user_level': user_level.current_level,
                'is_premium': is_premium,
                'daily_conversions_used': user_store_points.daily_conversions_today,
                'daily_conversions_remaining': settings.daily_conversion_limit - user_store_points.daily_conversions_today,
                'total_converted': user_store_points.total_converted,
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def preview_conversion(request):
    """Preview point conversion without executing it"""
    try:
        gamification_points = request.data.get('gamification_points')

        if not gamification_points:
            return Response({
                'success': False,
                'error': 'Gamification points amount is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            gamification_points = int(gamification_points)
        except ValueError:
            return Response({
                'success': False,
                'error': 'Invalid gamification points amount'
            }, status=status.HTTP_400_BAD_REQUEST)

        success, result = GamificationService.get_conversion_preview(request.user, gamification_points)

        if success:
            return Response({
                'success': True,
                'preview': result
            })
        else:
            return Response({
                'success': False,
                'error': result
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def convert_points(request):
    """Convert gamification points to store points"""
    try:
        gamification_points = request.data.get('gamification_points')

        if not gamification_points:
            return Response({
                'success': False,
                'error': 'Gamification points amount is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            gamification_points = int(gamification_points)
        except ValueError:
            return Response({
                'success': False,
                'error': 'Invalid gamification points amount'
            }, status=status.HTTP_400_BAD_REQUEST)

        success, result = GamificationService.convert_gamification_to_store_points(request.user, gamification_points)

        if success:
            return Response({
                'success': True,
                'conversion_result': result
            })
        else:
            return Response({
                'success': False,
                'error': result
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def conversion_history(request):
    """Get user's point conversion history"""
    try:
        from .models import PointConversionTransaction

        transactions = PointConversionTransaction.objects.filter(
            user=request.user
        ).order_by('-created_at')

        # Pagination
        limit = min(int(request.GET.get('limit', 20)), 100)
        offset = int(request.GET.get('offset', 0))
        transactions = transactions[offset:offset + limit]

        transaction_data = []
        for transaction in transactions:
            transaction_data.append({
                'id': transaction.id,
                'gamification_points_spent': transaction.gamification_points_spent,
                'store_points_received': transaction.store_points_received,
                'conversion_rate': transaction.conversion_rate,
                'total_fee': transaction.total_fee,
                'status': transaction.status,
                'created_at': transaction.created_at.isoformat(),
                'completed_at': transaction.completed_at.isoformat() if transaction.completed_at else None,
                'user_level_at_conversion': transaction.user_level_at_conversion,
                'was_premium': transaction.was_premium,
            })

        return Response({
            'success': True,
            'transactions': transaction_data,
            'total_count': PointConversionTransaction.objects.filter(user=request.user).count()
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def store_points_balance(request):
    """Get user's current store points balance"""
    try:
        balance = GamificationService.get_user_store_points_balance(request.user)

        return Response({
            'success': True,
            'store_points_balance': balance
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def unified_user_points(request):
    """Get all user points data in a single optimized request with caching"""
    try:
        from .models import UserStorePoints
        from .performance_utils import DataCache, RequestThrottler

        # Check throttling (allow requests every 3 seconds)
        if RequestThrottler.is_throttled(request.user, 'unified_points', 3):
            return Response({
                'success': False,
                'error': 'Too many requests. Please wait 3 seconds between requests.',
                'throttled': True
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        # Try to get cached data first
        cached_data = DataCache.get_cached_user_points(request.user.id)
        if cached_data:
            cached_data['cached'] = True
            cached_data['cache_timestamp'] = timezone.now().isoformat()
            RequestThrottler.record_request(request.user, 'unified_points')
            return Response(cached_data)

        # Record request for throttling
        RequestThrottler.record_request(request.user, 'unified_points')

        # Get user level with all gamification data
        user_level = GamificationService.get_or_create_user_level(request.user)

        # Get store points balance
        user_store_points, created = UserStorePoints.objects.get_or_create(
            user=request.user,
            defaults={'balance': 0}
        )
        user_store_points.reset_daily_conversions_if_needed()

        # Get conversion settings for rates
        from .models import PointConversionSettings
        settings = PointConversionSettings.get_settings()
        user_conversion_rate = settings.calculate_conversion_rate(request.user)

        # Get recent transactions (limited to avoid performance issues)
        recent_transactions = PointTransaction.objects.filter(
            user=request.user
        ).order_by('-created_at')[:5]

        transaction_data = []
        for transaction in recent_transactions:
            transaction_data.append({
                'id': transaction.id,
                'type': transaction.transaction_type,
                'points': transaction.points,
                'description': transaction.description,
                'created_at': transaction.created_at.isoformat(),
            })

        # Check premium status
        is_premium = False
        try:
            from monetization.services import MonetizationService
            premium_status = MonetizationService.get_user_premium_status(request.user)
            is_premium = premium_status.get('is_premium', False)
        except:
            pass

        response_data = {
            'success': True,
            'data': {
                # Gamification Points
                'gamification': {
                    'total_points': user_level.total_points,
                    'current_level': user_level.current_level,
                    'points_to_next_level': user_level.points_to_next_level,
                    'level_progress_percentage': user_level.level_progress_percentage,
                    'reading_streak': user_level.reading_streak,
                    'writing_streak': user_level.writing_streak,
                    'engagement_streak': user_level.engagement_streak,
                    'total_posts_read': user_level.total_posts_read,
                    'total_posts_written': user_level.total_posts_written,
                    'total_comments_made': user_level.total_comments_made,
                    'total_likes_given': user_level.total_likes_given,
                },

                # Store Points
                'store': {
                    'balance': user_store_points.balance,
                    'total_earned': user_store_points.total_earned,
                    'total_spent': user_store_points.total_spent,
                    'total_converted': user_store_points.total_converted,
                    'daily_conversions_today': user_store_points.daily_conversions_today,
                    'daily_conversions_remaining': settings.daily_conversion_limit - user_store_points.daily_conversions_today,
                },

                # Conversion Info
                'conversion': {
                    'enabled': settings.conversion_enabled,
                    'base_rate': settings.base_conversion_rate,
                    'user_rate': user_conversion_rate,
                    'fee_percentage': settings.conversion_fee_percentage,
                    'fee_fixed': settings.conversion_fee_fixed,
                    'daily_limit': settings.daily_conversion_limit,
                    'min_amount': settings.minimum_conversion_amount,
                    'max_amount': settings.maximum_conversion_amount,
                },

                # User Status
                'user': {
                    'username': request.user.username,
                    'level': user_level.current_level,
                    'is_premium': is_premium,
                    'date_joined': request.user.date_joined.isoformat(),
                },

                # Recent Activity (limited for performance)
                'recent_transactions': transaction_data,

                # Cache timestamp for client-side caching
                'last_updated': timezone.now().isoformat(),
                'cached': False,
            }
        }

        # Cache the response for 5 minutes
        DataCache.cache_user_points(request.user.id, response_data, timeout=300)

        return Response(response_data)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def spend_points(request):
    """Spend gamification points for rewards or purchases"""
    try:
        user = request.user
        points = request.data.get('points')
        description = request.data.get('description', 'Points spent')

        if not points or points <= 0:
            return Response({
                'success': False,
                'message': 'Invalid points amount'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get user level
        user_level = UserLevel.objects.filter(user=user).first()
        if not user_level:
            return Response({
                'success': False,
                'message': 'User level not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check if user has enough points
        if user_level.total_points < points:
            return Response({
                'success': False,
                'message': f'Insufficient points. You have {user_level.total_points} but need {points}.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Deduct points
        user_level.total_points -= points
        user_level.save()

        # Create transaction record
        PointTransaction.objects.create(
            user=user,
            transaction_type='spend',
            points=-points,  # Negative for spending
            description=description
        )

        return Response({
            'success': True,
            'message': f'Successfully spent {points} points',
            'points_spent': points,
            'remaining_points': user_level.total_points
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'Error spending points: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)