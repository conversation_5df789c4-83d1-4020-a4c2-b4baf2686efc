import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';
part 'app_error.g.dart';

@freezed
class AppError with _$AppError {
  const factory AppError({
    required AppErrorType type,
    required String message,
    required String userMessage,
    String? details,
    String? suggestion,
    int? statusCode,
    bool? isRetryable,
    @Default(false) bool isTemporary,
  }) = _AppError;

  factory AppError.fromJson(Map<String, dynamic> json) => _$AppErrorFromJson(json);
}

enum AppErrorType {
  network,
  server,
  authentication,
  authorization,
  validation,
  notFound,
  maintenance,
  featureDisabled,
  storage,
  unknown,
}

class AppErrorHandler {
  /// Convert various error types to user-friendly AppError
  static AppError handleError(dynamic error) {
    if (error is AppError) {
      return error;
    }

    if (error is DioException) {
      return _handleDioError(error);
    }

    if (error is FormatException) {
      return AppError(
        type: AppErrorType.validation,
        message: 'Data format error: ${error.message}',
        userMessage: 'The data received is in an unexpected format.',
        suggestion: 'Please try again. If the problem persists, contact support.',
        isRetryable: true,
        isTemporary: true,
      );
    }

    // Generic error handling
    return AppError(
      type: AppErrorType.unknown,
      message: error.toString(),
      userMessage: 'Something went wrong.',
      suggestion: 'Please try again. If the problem persists, contact support.',
      isRetryable: true,
      isTemporary: true,
    );
  }

  /// Handle Dio-specific errors
  static AppError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppError(
          type: AppErrorType.network,
          message: 'Request timeout: ${error.message}',
          userMessage: 'The request is taking too long to complete.',
          suggestion: 'Please check your internet connection and try again.',
          isRetryable: true,
          isTemporary: true,
        );

      case DioExceptionType.connectionError:
        return AppError(
          type: AppErrorType.network,
          message: 'Connection error: ${error.message}',
          userMessage: 'Unable to connect to the server.',
          suggestion: 'Please check your internet connection and try again.',
          isRetryable: true,
          isTemporary: true,
        );

      case DioExceptionType.badResponse:
        return _handleHttpError(error);

      case DioExceptionType.cancel:
        return AppError(
          type: AppErrorType.unknown,
          message: 'Request was cancelled',
          userMessage: 'The request was cancelled.',
          suggestion: 'Please try again.',
          isRetryable: true,
          isTemporary: true,
        );

      case DioExceptionType.unknown:
        return AppError(
          type: AppErrorType.network,
          message: 'Unknown network error: ${error.message}',
          userMessage: 'A network error occurred.',
          suggestion: 'Please check your internet connection and try again.',
          isRetryable: true,
          isTemporary: true,
        );

      default:
        return AppError(
          type: AppErrorType.unknown,
          message: error.message ?? 'Unknown error',
          userMessage: 'An unexpected error occurred.',
          suggestion: 'Please try again.',
          isRetryable: true,
          isTemporary: true,
        );
    }
  }

  /// Handle HTTP response errors
  static AppError _handleHttpError(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        return AppError(
          type: AppErrorType.validation,
          message: 'Bad request: ${error.message}',
          userMessage: 'The request contains invalid data.',
          details: _extractErrorDetails(responseData),
          suggestion: 'Please check your input and try again.',
          statusCode: statusCode,
          isRetryable: false,
        );

      case 401:
        return AppError(
          type: AppErrorType.authentication,
          message: 'Unauthorized: ${error.message}',
          userMessage: 'You need to sign in to access this feature.',
          suggestion: 'Please sign in and try again.',
          statusCode: statusCode,
          isRetryable: false,
        );

      case 403:
        return AppError(
          type: AppErrorType.authorization,
          message: 'Forbidden: ${error.message}',
          userMessage: 'You don\'t have permission to access this feature.',
          suggestion: 'Contact support if you believe this is an error.',
          statusCode: statusCode,
          isRetryable: false,
        );

      case 404:
        return AppError(
          type: AppErrorType.notFound,
          message: 'Not found: ${error.message}',
          userMessage: 'The requested content was not found.',
          suggestion: 'Please check if the content still exists.',
          statusCode: statusCode,
          isRetryable: false,
        );

      case 429:
        return AppError(
          type: AppErrorType.server,
          message: 'Too many requests: ${error.message}',
          userMessage: 'You\'re making requests too quickly.',
          suggestion: 'Please wait a moment and try again.',
          statusCode: statusCode,
          isRetryable: true,
          isTemporary: true,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        // Check if it's maintenance mode
        if (responseData is Map && responseData['error'] == 'maintenance_mode') {
          return AppError(
            type: AppErrorType.maintenance,
            message: 'System maintenance: ${responseData['message']}',
            userMessage: responseData['message'] ?? 'The system is currently under maintenance.',
            suggestion: 'Please try again later.',
            statusCode: statusCode,
            isRetryable: true,
            isTemporary: true,
          );
        }

        // Check if it's a feature disabled error
        if (responseData is Map && responseData['error'] == 'feature_disabled') {
          return AppError(
            type: AppErrorType.featureDisabled,
            message: 'Feature disabled: ${responseData['message']}',
            userMessage: responseData['message'] ?? 'This feature is temporarily unavailable.',
            suggestion: 'Please try again later.',
            statusCode: statusCode,
            isRetryable: true,
            isTemporary: true,
          );
        }

        return AppError(
          type: AppErrorType.server,
          message: 'Server error (${statusCode}): ${error.message}',
          userMessage: 'The server is experiencing issues.',
          suggestion: 'Please try again in a few minutes.',
          statusCode: statusCode,
          isRetryable: true,
          isTemporary: true,
        );

      default:
        return AppError(
          type: AppErrorType.server,
          message: 'HTTP error (${statusCode}): ${error.message}',
          userMessage: 'An error occurred while communicating with the server.',
          suggestion: 'Please try again.',
          statusCode: statusCode,
          isRetryable: true,
          isTemporary: true,
        );
    }
  }

  /// Extract error details from response data
  static String? _extractErrorDetails(dynamic responseData) {
    if (responseData is Map) {
      // Try to extract meaningful error messages
      if (responseData.containsKey('detail')) {
        return responseData['detail'].toString();
      }
      if (responseData.containsKey('message')) {
        return responseData['message'].toString();
      }
      if (responseData.containsKey('error')) {
        return responseData['error'].toString();
      }
      // Handle validation errors
      if (responseData.containsKey('errors')) {
        final errors = responseData['errors'];
        if (errors is Map) {
          return errors.values.join(', ');
        }
      }
    }
    return null;
  }

  /// Create network error
  static AppError networkError([String? details]) {
    return AppError(
      type: AppErrorType.network,
      message: 'Network error: ${details ?? 'Connection failed'}',
      userMessage: 'Unable to connect to the internet.',
      suggestion: 'Please check your internet connection and try again.',
      details: details,
      isRetryable: true,
      isTemporary: true,
    );
  }

  /// Create server error
  static AppError serverError([String? details]) {
    return AppError(
      type: AppErrorType.server,
      message: 'Server error: ${details ?? 'Server unavailable'}',
      userMessage: 'The server is currently unavailable.',
      suggestion: 'Please try again in a few minutes.',
      details: details,
      isRetryable: true,
      isTemporary: true,
    );
  }

  /// Create authentication error
  static AppError authenticationError([String? details]) {
    return AppError(
      type: AppErrorType.authentication,
      message: 'Authentication error: ${details ?? 'Not authenticated'}',
      userMessage: 'You need to sign in to access this feature.',
      suggestion: 'Please sign in and try again.',
      details: details,
      isRetryable: false,
    );
  }

  /// Create storage error
  static AppError storageError([String? details]) {
    return AppError(
      type: AppErrorType.storage,
      message: 'Storage error: ${details ?? 'Storage operation failed'}',
      userMessage: 'Unable to save or retrieve data.',
      suggestion: 'Please try again or free up some storage space.',
      details: details,
      isRetryable: true,
      isTemporary: true,
    );
  }

  /// Create maintenance error
  static AppError maintenanceError([String? message]) {
    return AppError(
      type: AppErrorType.maintenance,
      message: 'Maintenance mode: ${message ?? 'System under maintenance'}',
      userMessage: message ?? 'The system is currently under maintenance.',
      suggestion: 'Please try again later.',
      isRetryable: true,
      isTemporary: true,
    );
  }
}
