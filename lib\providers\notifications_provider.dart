import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/notification.dart';
import '../models/paginated_response.dart';
import '../services/api_service.dart';
import 'auth_provider.dart';

// Notifications State
class NotificationsState {
  final List<AppNotification> notifications;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final int unreadCount;
  final bool hasMorePages;
  final int currentPage;

  const NotificationsState({
    this.notifications = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.unreadCount = 0,
    this.hasMorePages = true,
    this.currentPage = 1,
  });

  NotificationsState copyWith({
    List<AppNotification>? notifications,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    int? unreadCount,
    bool? hasMorePages,
    int? currentPage,
  }) {
    return NotificationsState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error,
      unreadCount: unreadCount ?? this.unreadCount,
      hasMorePages: hasMorePages ?? this.hasMorePages,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

// Notifications Notifier
class NotificationsNotifier extends StateNotifier<NotificationsState> {
  final ApiService _apiService;

  NotificationsNotifier(this._apiService) : super(const NotificationsState());

  Future<void> loadNotifications({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(isLoading: true, error: null, currentPage: 1);
    } else if (state.isLoading || state.isLoadingMore) {
      return;
    }

    try {
      final response = await _apiService.getNotifications(page: refresh ? 1 : state.currentPage);
      
      if (refresh) {
        state = state.copyWith(
          notifications: response.results,
          isLoading: false,
          hasMorePages: response.next != null,
          currentPage: 1,
        );
      } else {
        state = state.copyWith(
          notifications: [...state.notifications, ...response.results],
          isLoadingMore: false,
          hasMorePages: response.next != null,
        );
      }
      
      // Update unread count
      await _updateUnreadCount();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadMoreNotifications() async {
    if (!state.hasMorePages || state.isLoadingMore) return;

    state = state.copyWith(isLoadingMore: true);
    
    try {
      final nextPage = state.currentPage + 1;
      final response = await _apiService.getNotifications(page: nextPage);
      
      state = state.copyWith(
        notifications: [...state.notifications, ...response.results],
        isLoadingMore: false,
        hasMorePages: response.next != null,
        currentPage: nextPage,
      );
    } catch (e) {
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  Future<void> markAsRead(int notificationId) async {
    try {
      await _apiService.markNotificationAsRead(notificationId);
      
      // Update local state
      final updatedNotifications = state.notifications.map((notification) {
        if (notification.id == notificationId) {
          return notification.copyWith(isRead: true);
        }
        return notification;
      }).toList();
      
      state = state.copyWith(notifications: updatedNotifications);
      
      // Update unread count
      await _updateUnreadCount();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> markAllAsRead() async {
    try {
      await _apiService.markAllNotificationsAsRead();
      
      // Update local state
      final updatedNotifications = state.notifications.map((notification) {
        return notification.copyWith(isRead: true);
      }).toList();
      
      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: 0,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> _updateUnreadCount() async {
    try {
      final countData = await _apiService.getNotificationCount();
      state = state.copyWith(unreadCount: countData['unread_count'] ?? 0);
    } catch (e) {
      // Don't update error state for count failures
    }
  }

  Future<void> refreshUnreadCount() async {
    await _updateUnreadCount();
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Get notifications by type
  List<AppNotification> getNotificationsByType(String type) {
    return state.notifications.where((n) => n.notificationType == type).toList();
  }

  // Get unread notifications
  List<AppNotification> get unreadNotifications {
    return state.notifications.where((n) => !n.isRead).toList();
  }
}

// Notifications Provider
final notificationsProvider = StateNotifierProvider<NotificationsNotifier, NotificationsState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return NotificationsNotifier(apiService);
});

// Auto-load notifications when user is authenticated
final autoLoadNotificationsProvider = Provider<void>((ref) {
  final authState = ref.watch(enhancedAuthProvider);
  final notificationsNotifier = ref.read(notificationsProvider.notifier);

  if (authState.isAuthenticated && !authState.isLoading) {
    // Load notifications when user is authenticated
    Future.microtask(() => notificationsNotifier.loadNotifications(refresh: true));
  }
});

// Unread count provider for easy access
final unreadCountProvider = Provider<int>((ref) {
  final notificationsState = ref.watch(notificationsProvider);
  return notificationsState.unreadCount;
});
