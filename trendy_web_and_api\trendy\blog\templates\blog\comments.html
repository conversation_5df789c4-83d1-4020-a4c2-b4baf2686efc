<div class="comments-scroll-container" style="max-height: 65vh; overflow-y: auto;">
    {% for comment in comments %}
    <div class="comment-card {% if comment.parent %}ms-3{% endif %}">
        <div class="comment-main bg-light rounded-2 p-2 mb-2">
            <div class="d-flex gap-2 align-items-start">
                <!-- Avatar & Collapse Toggle -->
                <div class="d-flex flex-column align-items-center">
                    <div class="avatar-xs flex-shrink-0">
                        {% if comment.author.profile.image %}
                            <img src="{{ comment.author.profile.image.url }}" 
                                 alt="{{ comment.author.username }}" 
                                 class="rounded-circle"
                                 width="32"
                                 height="32">
                        {% else %}
                            <div class="avatar-initials bg-gradient text-white rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 32px; height: 32px;">
                                {{ comment.author.get_full_name|default:comment.author.username|slice:":2"|upper }}
                            </div>
                        {% endif %}
                    </div>
                    {% if comment.replies.exists %}
                    <button class="btn btn-toggle py-0 px-1 mt-1" 
                            type="button" 
                            data-bs-toggle="collapse" 
                            data-bs-target="#repliesCollapse{{ comment.id }}"
                            aria-expanded="true">
                        <i class="fas fa-chevron-down toggle-icon text-xs"></i>
                    </button>
                    {% endif %}
                </div>

                <!-- Comment Content -->
                <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <div>
                            <span class="fw-medium text-sm">{{ comment.author.get_full_name|default:comment.author.username }}</span>
                            <small class="text-muted d-block text-xs">{{ comment.created_at|timesince }} ago</small>
                        </div>
                        {% if user.is_authenticated %}
                        <form method="post" action="{% url 'comment-like' comment.id %}" class="like-form">
                            {% csrf_token %}
                            <button type="submit" class="btn-like btn-like-xs {% if user in comment.likes.all %}liked{% endif %}">
                                <i class="fas fa-heart"></i>
                                <span class="text-xs">{{ comment.like_count }}</span>
                            </button>
                        </form>
                        {% endif %}
                    </div>

                    <p class="comment-text text-sm mb-1">{{ comment.content }}</p>
                    
                    <!-- Reply Form -->
                    {% if user.is_authenticated %}
                    <div class="mt-1">
                        <button class="btn btn-link btn-reply p-0 text-muted text-xs" 
                                type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#replyForm{{ comment.id }}">
                            <i class="fas fa-reply me-1"></i>Reply
                        </button>
                        <div class="collapse" id="replyForm{{ comment.id }}">
                            <form method="post" action="{% url 'post-comment' post.slug %}" class="mt-1">
                                {% csrf_token %}
                                <input type="hidden" name="parent" value="{{ comment.id }}">
                                <div class="input-group input-group-sm">
                                    <input type="text" name="content" 
                                           class="form-control form-control-xs" 
                                           placeholder="Write reply..."
                                           required>
                                    <button type="submit" class="btn btn-gradient btn-xs">
                                        Post
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Collapsible Replies -->
        {% if comment.replies.exists %}
        <div class="collapse show" id="repliesCollapse{{ comment.id }}">
            <div class="replies-container ms-3 ps-2 border-start">
                {% include "blog/comments.html" with comments=comment.replies.all %}
            </div>
        </div>
        {% endif %}
    </div>
    {% endfor %}
</div>

<style>
    .comments-scroll-container {
        scrollbar-width: thin;
        scrollbar-color: #d1d5db transparent;
    }
    
    .comments-scroll-container::-webkit-scrollbar {
        width: 6px;
    }
    
    .comments-scroll-container::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 4px;
    }

    .btn-toggle {
        background: none;
        border: none;
        color: #6c757d;
        transition: transform 0.2s ease;
        line-height: 1;
    }

    .btn-toggle[aria-expanded="false"] .toggle-icon {
        transform: rotate(-90deg);
    }

    .toggle-icon {
        transition: transform 0.2s ease;
        font-size: 0.75rem;
    }

    .comment-main {
        border: 1px solid #eceef1;
        transition: background 0.15s ease;
    }

    .avatar-initials {
        font-size: 0.75rem;
        font-weight: 600;
    }

    .btn-like-xs {
        padding: 2px 6px;
        font-size: 0.75rem;
    }

    .replies-container {
        border-left: 2px solid #eceef1;
        margin-left: 1.5rem;
    }

    @media (max-width: 768px) {
        .comments-scroll-container {
            max-height: 55vh;
        }
        
        .replies-container {
            margin-left: 0.5rem;
            padding-left: 0.5rem;
        }
        
        .comment-main {
            padding: 0.5rem;
        }
        
        .btn-toggle {
            padding: 0.15rem;
        }
    }
</style>