import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/services/role_service.dart';
import 'package:trendy/providers/auth_provider.dart';

/// Provider for user role information
final roleInfoProvider = FutureProvider<RoleInfo?>((ref) async {
  final authState = ref.watch(enhancedAuthProvider);

  // If user is not authenticated, return null
  if (!authState.isAuthenticated) {
    return null;
  }

  // Fetch role information
  return await RoleService.getUserPermissions();
});

/// Provider for checking if user can create content
final canCreateContentProvider = FutureProvider<bool>((ref) async {
  final authState = ref.watch(enhancedAuthProvider);

  // If user is not authenticated, return false
  if (!authState.isAuthenticated) {
    return false;
  }

  // Check content creation permission
  return await RoleService.canCreateContent();
});

/// Provider for users by role (Admin only)
final usersByRoleProvider =
    FutureProvider<Map<String, List<dynamic>>?>((ref) async {
  final authState = ref.watch(enhancedAuthProvider);

  // If user is not authenticated, return null
  if (!authState.isAuthenticated) {
    return null;
  }

  // Fetch users by role
  return await RoleService.getUsersByRole();
});

/// Provider for role-based navigation items
final navigationItemsProvider = Provider<List<NavigationItem>>((ref) {
  final roleInfoAsync = ref.watch(roleInfoProvider);

  return roleInfoAsync.when(
    data: (roleInfo) => _getNavigationItems(roleInfo),
    loading: () => _getBasicNavigationItems(),
    error: (error, stack) => _getBasicNavigationItems(),
  );
});

class NavigationItem {
  final String title;
  final String route;
  final IconData icon;
  final bool requiresAuth;
  final bool requiresContentCreator;
  final bool requiresAdmin;
  final List<String>? requiredPermissions;

  NavigationItem({
    required this.title,
    required this.route,
    required this.icon,
    this.requiresAuth = false,
    this.requiresContentCreator = false,
    this.requiresAdmin = false,
    this.requiredPermissions,
  });
}

List<NavigationItem> _getNavigationItems(RoleInfo? roleInfo) {
  final items = <NavigationItem>[
    NavigationItem(
      title: 'Home',
      route: '/',
      icon: Icons.home,
    ),
    NavigationItem(
      title: 'Community',
      route: '/community',
      icon: Icons.people,
    ),
  ];

  // Add content creation items for content creators
  if (roleInfo?.canCreateContent == true) {
    items.addAll([
      NavigationItem(
        title: 'Create Post',
        route: '/create-post',
        icon: Icons.edit,
        requiresAuth: true,
        requiresContentCreator: true,
      ),
      NavigationItem(
        title: 'My Posts',
        route: '/my-posts',
        icon: Icons.article,
        requiresAuth: true,
        requiresContentCreator: true,
      ),
    ]);
  }

  // Add admin items for admins
  if (RoleService.isAdmin(roleInfo)) {
    items.addAll([
      NavigationItem(
        title: 'Admin Panel',
        route: '/admin',
        icon: Icons.admin_panel_settings,
        requiresAuth: true,
        requiresAdmin: true,
      ),
      NavigationItem(
        title: 'User Management',
        route: '/admin/users',
        icon: Icons.manage_accounts,
        requiresAuth: true,
        requiresAdmin: true,
      ),
    ]);
  }

  // Add profile and settings for authenticated users
  if (roleInfo != null) {
    items.addAll([
      NavigationItem(
        title: 'Profile',
        route: '/profile',
        icon: Icons.person,
        requiresAuth: true,
      ),
      NavigationItem(
        title: 'Settings',
        route: '/settings',
        icon: Icons.settings,
        requiresAuth: true,
      ),
    ]);
  }

  return items;
}

List<NavigationItem> _getBasicNavigationItems() {
  return [
    NavigationItem(
      title: 'Home',
      route: '/',
      icon: Icons.home,
    ),
    NavigationItem(
      title: 'Community',
      route: '/community',
      icon: Icons.people,
    ),
  ];
}

/// Provider for checking specific permissions
final permissionProvider = Provider.family<bool, String>((ref, permission) {
  final roleInfoAsync = ref.watch(roleInfoProvider);

  return roleInfoAsync.when(
    data: (roleInfo) => RoleService.hasPermission(roleInfo, permission),
    loading: () => false,
    error: (error, stack) => false,
  );
});

/// Provider for checking if user is admin
final isAdminProvider = Provider<bool>((ref) {
  final roleInfoAsync = ref.watch(roleInfoProvider);

  return roleInfoAsync.when(
    data: (roleInfo) => RoleService.isAdmin(roleInfo),
    loading: () => false,
    error: (error, stack) => false,
  );
});

/// Provider for checking if user can moderate
final canModerateProvider = Provider<bool>((ref) {
  final roleInfoAsync = ref.watch(roleInfoProvider);

  return roleInfoAsync.when(
    data: (roleInfo) => RoleService.canModerate(roleInfo),
    loading: () => false,
    error: (error, stack) => false,
  );
});

/// State notifier for role management actions
class RoleManagementNotifier extends StateNotifier<AsyncValue<void>> {
  RoleManagementNotifier() : super(const AsyncValue.data(null));

  Future<void> promoteUser(int userId) async {
    state = const AsyncValue.loading();
    try {
      final success = await RoleService.promoteUserToContentCreator(userId);
      if (success) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.error('Failed to promote user', StackTrace.current);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> demoteUser(int userId) async {
    state = const AsyncValue.loading();
    try {
      final success = await RoleService.demoteUserToRegular(userId);
      if (success) {
        state = const AsyncValue.data(null);
      } else {
        state = AsyncValue.error('Failed to demote user', StackTrace.current);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

final roleManagementProvider =
    StateNotifierProvider<RoleManagementNotifier, AsyncValue<void>>((ref) {
  return RoleManagementNotifier();
});
