import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/messaging.dart';
import '../services/messaging_service.dart';

import 'provider.dart';

// Provider for MessagingService
final messagingServiceProvider = Provider<MessagingService>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return MessagingService(apiService);
});

// State class for messaging
class MessagingState {
  final List<Conversation> conversations;
  final Map<int, List<Message>> conversationMessages;
  final bool isLoading;
  final String? error;
  final int unreadCount;

  const MessagingState({
    this.conversations = const [],
    this.conversationMessages = const {},
    this.isLoading = false,
    this.error,
    this.unreadCount = 0,
  });

  MessagingState copyWith({
    List<Conversation>? conversations,
    Map<int, List<Message>>? conversationMessages,
    bool? isLoading,
    String? error,
    int? unreadCount,
  }) {
    return MessagingState(
      conversations: conversations ?? this.conversations,
      conversationMessages: conversationMessages ?? this.conversationMessages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

// Messaging provider
class MessagingNotifier extends StateNotifier<MessagingState> {
  final MessagingService _messagingService;

  MessagingNotifier(this._messagingService) : super(const MessagingState());

  /// Load conversations
  Future<void> loadConversations() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _messagingService.getConversations();
      state = state.copyWith(
        conversations: response.results,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load messages for a conversation
  Future<void> loadMessages(int conversationId) async {
    try {
      final response = await _messagingService.getMessages(conversationId);
      final updatedMessages =
          Map<int, List<Message>>.from(state.conversationMessages);
      updatedMessages[conversationId] = response.results;

      state = state.copyWith(conversationMessages: updatedMessages);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Send a message
  Future<void> sendMessage(int conversationId, String content) async {
    try {
      final message =
          await _messagingService.sendMessage(conversationId, content);

      // Add message to local state
      final updatedMessages =
          Map<int, List<Message>>.from(state.conversationMessages);
      final currentMessages = updatedMessages[conversationId] ?? [];
      updatedMessages[conversationId] = [...currentMessages, message];

      // Update conversation's last message
      final updatedConversations = state.conversations.map((conv) {
        if (conv.id == conversationId) {
          return conv.copyWith(lastMessage: message);
        }
        return conv;
      }).toList();

      state = state.copyWith(
        conversationMessages: updatedMessages,
        conversations: updatedConversations,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Start a new conversation
  Future<Conversation?> startConversation(String username,
      {String? initialMessage}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _messagingService.startConversation(username,
          initialMessage: initialMessage);

      // Add conversation to local state if it's new
      if (response.created) {
        final updatedConversations = [
          response.conversation,
          ...state.conversations
        ];
        state = state.copyWith(conversations: updatedConversations);
      }

      state = state.copyWith(isLoading: false);
      return response.conversation;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Mark conversation as read
  Future<void> markConversationAsRead(int conversationId) async {
    try {
      await _messagingService.markConversationAsRead(conversationId);

      // Update local state
      final updatedConversations = state.conversations.map((conv) {
        if (conv.id == conversationId) {
          return conv.copyWith(unreadCount: 0);
        }
        return conv;
      }).toList();

      state = state.copyWith(conversations: updatedConversations);
      await loadUnreadCount(); // Refresh total unread count
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Load unread count
  Future<void> loadUnreadCount() async {
    try {
      final count = await _messagingService.getUnreadCount();
      state = state.copyWith(unreadCount: count);
    } catch (e) {
      // Don't update error state for unread count failures
      print('Error loading unread count: $e');
    }
  }

  /// Delete conversation
  Future<void> deleteConversation(int conversationId) async {
    try {
      await _messagingService.deleteConversation(conversationId);

      // Remove from local state
      final updatedConversations = state.conversations
          .where((conv) => conv.id != conversationId)
          .toList();
      final updatedMessages =
          Map<int, List<Message>>.from(state.conversationMessages);
      updatedMessages.remove(conversationId);

      state = state.copyWith(
        conversations: updatedConversations,
        conversationMessages: updatedMessages,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh conversations
  Future<void> refresh() async {
    await loadConversations();
    await loadUnreadCount();
  }

  /// Get messages for a conversation
  List<Message> getMessagesForConversation(int conversationId) {
    return state.conversationMessages[conversationId] ?? [];
  }
}

// Provider for messaging state
final messagingProvider =
    StateNotifierProvider<MessagingNotifier, MessagingState>((ref) {
  final messagingService = ref.watch(messagingServiceProvider);
  return MessagingNotifier(messagingService);
});

// Provider for unread count (for use in app bar badge)
final unreadCountProvider = Provider<int>((ref) {
  return ref.watch(messagingProvider).unreadCount;
});
