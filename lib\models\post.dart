import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:trendy/models/category.dart';
import 'package:trendy/models/comment.dart';
import 'package:trendy/models/country.dart';
import 'package:trendy/models/post_media.dart';
import 'package:trendy/models/tag.dart';
import 'package:trendy/models/user.dart';

part 'post.freezed.dart';
part 'post.g.dart';

@freezed
class Post with _$Post {
  const factory Post({
    required int id,
    required String title,
    required String slug,
    required String content,
    required User author,
    required Category category,
    @Default([]) List<Tag> tags,
    @Json<PERSON>ey(name: 'created_at') required DateTime createdAt,
    @<PERSON>son<PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    @Default(0) int views,
    @Json<PERSON>ey(name: 'is_featured') @Default(false) bool isFeatured,
    @Default('draft') String status,
    @Default([]) List<dynamic> reference,
    @JsonKey(name: 'comment_count') @Default(0) int commentCount,
    @<PERSON>son<PERSON>ey(name: 'like_count') @Default(0) int likeCount,
    @Json<PERSON>ey(name: 'is_liked') @Default(false) bool isLiked,
    @Json<PERSON>ey(name: 'media_items') @Default([]) List<PostMedia> mediaItems,
    // Regional targeting fields
    @JsonKey(name: 'is_global') @Default(false) bool isGlobal,
    @JsonKey(name: 'target_countries')
    @Default([])
    List<Country> targetCountries,
    @JsonKey(name: 'regional_priority') @Default(0) int regionalPriority,
  }) = _Post;

  factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);
}
