import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../screens/home_screen.dart';
import '../screens/community_screen.dart';
import '../screens/wallet_screen.dart';
import '../screens/store_screen.dart';
import '../screens/profile_screen.dart';
import '../screens/advertising_screen.dart';
import '../providers/auth_provider.dart';
import '../providers/role_provider.dart';
import '../providers/maintenance_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/auth_guard.dart';
import '../widgets/role_based_widget.dart';
import '../widgets/maintenance_banner.dart';
import '../utils/data_loader.dart';

class MainNavigation extends ConsumerStatefulWidget {
  const MainNavigation({Key? key}) : super(key: key);

  @override
  ConsumerState<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends ConsumerState<MainNavigation>
    with WidgetsBindingObserver {
  int _currentIndex = 0;
  bool _dataLoaded = false;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const WalletScreen(),
    const StoreScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // Load data when navigation initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _refreshAuthState();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Refresh authentication when app becomes active
    if (state == AppLifecycleState.resumed) {
      _refreshAuthState();
    }
  }

  Future<void> _refreshAuthState() async {
    try {
      await ref.read(enhancedAuthProvider.notifier).refreshAuthState();
      print('✅ Authentication state refreshed');
    } catch (e) {
      print('❌ Error refreshing auth state: $e');
    }
  }

  Future<void> _loadInitialData() async {
    if (!_dataLoaded) {
      print('🚀 Loading initial app data...');
      // Load data asynchronously without blocking UI
      DataLoader.loadAllData(ref).then((_) {
        if (mounted) {
          setState(() {
            _dataLoaded = true;
          });
          print('✅ Initial data loading complete!');
        }
      }).catchError((error) {
        print('❌ Error loading initial data: $error');
        if (mounted) {
          setState(() {
            _dataLoaded =
                true; // Still mark as loaded to prevent infinite loading
          });
        }
      });
    }
  }

  void _loadScreenData(int index) {
    final screenNames = ['home', 'community', 'wallet', 'store', 'profile'];
    if (index < screenNames.length) {
      DataLoader.loadScreenData(ref, screenNames[index]);
    }
  }

  void _showLoginPrompt() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.lock_outline, color: AppTheme.primaryColor, size: 28),
              const SizedBox(width: 12),
              const Text(
                'Login Required',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              ),
            ],
          ),
          content: const Text(
            'You need to log in to access this feature. Would you like to log in now?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushNamed(context, '/auth');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Login',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final isLoggedIn = authState.isAuthenticated;

    // Reset to home tab if user logs out and is on a restricted tab
    if (!isLoggedIn && _currentIndex > 0) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _currentIndex = 0;
        });
      });
    }

    return Scaffold(
      body: Column(
        children: [
          const MaintenanceBanner(),
          Expanded(
            child: IndexedStack(index: _currentIndex, children: _screens),
          ),
        ],
      ),
      floatingActionButton: ContentCreatorFAB(
        onPressed: () {
          // Navigate to create post screen
          Navigator.pushNamed(context, '/create-post');
        },
        icon: const Icon(Icons.add),
        tooltip: 'Create Post',
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: isLoggedIn
                  ? MainAxisAlignment.spaceAround
                  : MainAxisAlignment.center,
              children: [
                // Home tab - always visible
                _buildNavItem(
                  icon: Icons.home_rounded,
                  label: 'Home',
                  index: 0,
                  isActive: _currentIndex == 0,
                ),

                // Other tabs - only visible when logged in
                if (isLoggedIn) ...[
                  _buildNavItem(
                    icon: Icons.people_rounded,
                    label: 'Community',
                    index: 1,
                    isActive: _currentIndex == 1,
                  ),
                  _buildNavItem(
                    icon: Icons.account_balance_wallet_rounded,
                    label: 'Wallet',
                    index: 2,
                    isActive: _currentIndex == 2,
                    showBadge: authState.isAuthenticated,
                  ),
                  _buildNavItem(
                    icon: Icons.store_rounded,
                    label: 'Shop',
                    index: 3,
                    isActive: _currentIndex == 3,
                  ),
                  _buildNavItem(
                    icon: Icons.person_rounded,
                    label: 'Profile',
                    index: 4,
                    isActive: _currentIndex == 4,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isActive,
    bool showBadge = false,
  }) {
    final authState = ref.watch(enhancedAuthProvider);
    final isLoggedIn = authState.isAuthenticated;

    return GestureDetector(
      onTap: () {
        // If user is not logged in and trying to access restricted tabs (index > 0)
        if (!isLoggedIn && index > 0) {
          _showLoginPrompt();
          return;
        }

        setState(() {
          _currentIndex = index;
        });
        _loadScreenData(index);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive
              ? AppTheme.primaryColor.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                Icon(
                  icon,
                  color: isActive ? AppTheme.primaryColor : Colors.grey[600],
                  size: 24,
                ),
                if (showBadge)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isActive ? AppTheme.primaryColor : Colors.grey[600],
                fontSize: 12,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
