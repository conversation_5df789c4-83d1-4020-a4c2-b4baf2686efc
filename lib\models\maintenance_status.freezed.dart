// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'maintenance_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MaintenanceStatus _$MaintenanceStatusFromJson(Map<String, dynamic> json) {
  return _MaintenanceStatus.fromJson(json);
}

/// @nodoc
mixin _$MaintenanceStatus {
  bool get isActive => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String get maintenanceType => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  DateTime? get scheduledStart => throw _privateConstructorUsedError;
  DateTime? get scheduledEnd => throw _privateConstructorUsedError;
  DateTime? get actualStart => throw _privateConstructorUsedError;
  DateTime? get actualEnd => throw _privateConstructorUsedError;
  bool get allowAdminAccess => throw _privateConstructorUsedError;
  bool get allowStaffAccess => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MaintenanceStatusCopyWith<MaintenanceStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MaintenanceStatusCopyWith<$Res> {
  factory $MaintenanceStatusCopyWith(
          MaintenanceStatus value, $Res Function(MaintenanceStatus) then) =
      _$MaintenanceStatusCopyWithImpl<$Res, MaintenanceStatus>;
  @useResult
  $Res call(
      {bool isActive,
      String title,
      String message,
      String maintenanceType,
      String status,
      DateTime? scheduledStart,
      DateTime? scheduledEnd,
      DateTime? actualStart,
      DateTime? actualEnd,
      bool allowAdminAccess,
      bool allowStaffAccess});
}

/// @nodoc
class _$MaintenanceStatusCopyWithImpl<$Res, $Val extends MaintenanceStatus>
    implements $MaintenanceStatusCopyWith<$Res> {
  _$MaintenanceStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = null,
    Object? title = null,
    Object? message = null,
    Object? maintenanceType = null,
    Object? status = null,
    Object? scheduledStart = freezed,
    Object? scheduledEnd = freezed,
    Object? actualStart = freezed,
    Object? actualEnd = freezed,
    Object? allowAdminAccess = null,
    Object? allowStaffAccess = null,
  }) {
    return _then(_value.copyWith(
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      maintenanceType: null == maintenanceType
          ? _value.maintenanceType
          : maintenanceType // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledStart: freezed == scheduledStart
          ? _value.scheduledStart
          : scheduledStart // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      scheduledEnd: freezed == scheduledEnd
          ? _value.scheduledEnd
          : scheduledEnd // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      actualStart: freezed == actualStart
          ? _value.actualStart
          : actualStart // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      actualEnd: freezed == actualEnd
          ? _value.actualEnd
          : actualEnd // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      allowAdminAccess: null == allowAdminAccess
          ? _value.allowAdminAccess
          : allowAdminAccess // ignore: cast_nullable_to_non_nullable
              as bool,
      allowStaffAccess: null == allowStaffAccess
          ? _value.allowStaffAccess
          : allowStaffAccess // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MaintenanceStatusImplCopyWith<$Res>
    implements $MaintenanceStatusCopyWith<$Res> {
  factory _$$MaintenanceStatusImplCopyWith(_$MaintenanceStatusImpl value,
          $Res Function(_$MaintenanceStatusImpl) then) =
      __$$MaintenanceStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isActive,
      String title,
      String message,
      String maintenanceType,
      String status,
      DateTime? scheduledStart,
      DateTime? scheduledEnd,
      DateTime? actualStart,
      DateTime? actualEnd,
      bool allowAdminAccess,
      bool allowStaffAccess});
}

/// @nodoc
class __$$MaintenanceStatusImplCopyWithImpl<$Res>
    extends _$MaintenanceStatusCopyWithImpl<$Res, _$MaintenanceStatusImpl>
    implements _$$MaintenanceStatusImplCopyWith<$Res> {
  __$$MaintenanceStatusImplCopyWithImpl(_$MaintenanceStatusImpl _value,
      $Res Function(_$MaintenanceStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isActive = null,
    Object? title = null,
    Object? message = null,
    Object? maintenanceType = null,
    Object? status = null,
    Object? scheduledStart = freezed,
    Object? scheduledEnd = freezed,
    Object? actualStart = freezed,
    Object? actualEnd = freezed,
    Object? allowAdminAccess = null,
    Object? allowStaffAccess = null,
  }) {
    return _then(_$MaintenanceStatusImpl(
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      maintenanceType: null == maintenanceType
          ? _value.maintenanceType
          : maintenanceType // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledStart: freezed == scheduledStart
          ? _value.scheduledStart
          : scheduledStart // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      scheduledEnd: freezed == scheduledEnd
          ? _value.scheduledEnd
          : scheduledEnd // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      actualStart: freezed == actualStart
          ? _value.actualStart
          : actualStart // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      actualEnd: freezed == actualEnd
          ? _value.actualEnd
          : actualEnd // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      allowAdminAccess: null == allowAdminAccess
          ? _value.allowAdminAccess
          : allowAdminAccess // ignore: cast_nullable_to_non_nullable
              as bool,
      allowStaffAccess: null == allowStaffAccess
          ? _value.allowStaffAccess
          : allowStaffAccess // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MaintenanceStatusImpl implements _MaintenanceStatus {
  const _$MaintenanceStatusImpl(
      {this.isActive = false,
      this.title = '',
      this.message = '',
      this.maintenanceType = '',
      this.status = '',
      this.scheduledStart,
      this.scheduledEnd,
      this.actualStart,
      this.actualEnd,
      this.allowAdminAccess = true,
      this.allowStaffAccess = false});

  factory _$MaintenanceStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$MaintenanceStatusImplFromJson(json);

  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String message;
  @override
  @JsonKey()
  final String maintenanceType;
  @override
  @JsonKey()
  final String status;
  @override
  final DateTime? scheduledStart;
  @override
  final DateTime? scheduledEnd;
  @override
  final DateTime? actualStart;
  @override
  final DateTime? actualEnd;
  @override
  @JsonKey()
  final bool allowAdminAccess;
  @override
  @JsonKey()
  final bool allowStaffAccess;

  @override
  String toString() {
    return 'MaintenanceStatus(isActive: $isActive, title: $title, message: $message, maintenanceType: $maintenanceType, status: $status, scheduledStart: $scheduledStart, scheduledEnd: $scheduledEnd, actualStart: $actualStart, actualEnd: $actualEnd, allowAdminAccess: $allowAdminAccess, allowStaffAccess: $allowStaffAccess)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MaintenanceStatusImpl &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.maintenanceType, maintenanceType) ||
                other.maintenanceType == maintenanceType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.scheduledStart, scheduledStart) ||
                other.scheduledStart == scheduledStart) &&
            (identical(other.scheduledEnd, scheduledEnd) ||
                other.scheduledEnd == scheduledEnd) &&
            (identical(other.actualStart, actualStart) ||
                other.actualStart == actualStart) &&
            (identical(other.actualEnd, actualEnd) ||
                other.actualEnd == actualEnd) &&
            (identical(other.allowAdminAccess, allowAdminAccess) ||
                other.allowAdminAccess == allowAdminAccess) &&
            (identical(other.allowStaffAccess, allowStaffAccess) ||
                other.allowStaffAccess == allowStaffAccess));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isActive,
      title,
      message,
      maintenanceType,
      status,
      scheduledStart,
      scheduledEnd,
      actualStart,
      actualEnd,
      allowAdminAccess,
      allowStaffAccess);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MaintenanceStatusImplCopyWith<_$MaintenanceStatusImpl> get copyWith =>
      __$$MaintenanceStatusImplCopyWithImpl<_$MaintenanceStatusImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MaintenanceStatusImplToJson(
      this,
    );
  }
}

abstract class _MaintenanceStatus implements MaintenanceStatus {
  const factory _MaintenanceStatus(
      {final bool isActive,
      final String title,
      final String message,
      final String maintenanceType,
      final String status,
      final DateTime? scheduledStart,
      final DateTime? scheduledEnd,
      final DateTime? actualStart,
      final DateTime? actualEnd,
      final bool allowAdminAccess,
      final bool allowStaffAccess}) = _$MaintenanceStatusImpl;

  factory _MaintenanceStatus.fromJson(Map<String, dynamic> json) =
      _$MaintenanceStatusImpl.fromJson;

  @override
  bool get isActive;
  @override
  String get title;
  @override
  String get message;
  @override
  String get maintenanceType;
  @override
  String get status;
  @override
  DateTime? get scheduledStart;
  @override
  DateTime? get scheduledEnd;
  @override
  DateTime? get actualStart;
  @override
  DateTime? get actualEnd;
  @override
  bool get allowAdminAccess;
  @override
  bool get allowStaffAccess;
  @override
  @JsonKey(ignore: true)
  _$$MaintenanceStatusImplCopyWith<_$MaintenanceStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FeatureToggle _$FeatureToggleFromJson(Map<String, dynamic> json) {
  return _FeatureToggle.fromJson(json);
}

/// @nodoc
mixin _$FeatureToggle {
  String get name => throw _privateConstructorUsedError;
  String get displayName => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get featureType => throw _privateConstructorUsedError;
  bool get isEnabled => throw _privateConstructorUsedError;
  bool get isGlobal => throw _privateConstructorUsedError;
  bool get enabledForAdmins => throw _privateConstructorUsedError;
  bool get enabledForStaff => throw _privateConstructorUsedError;
  bool get enabledForUsers => throw _privateConstructorUsedError;
  DateTime? get disableStart => throw _privateConstructorUsedError;
  DateTime? get disableEnd => throw _privateConstructorUsedError;
  String get disabledMessage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FeatureToggleCopyWith<FeatureToggle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeatureToggleCopyWith<$Res> {
  factory $FeatureToggleCopyWith(
          FeatureToggle value, $Res Function(FeatureToggle) then) =
      _$FeatureToggleCopyWithImpl<$Res, FeatureToggle>;
  @useResult
  $Res call(
      {String name,
      String displayName,
      String description,
      String featureType,
      bool isEnabled,
      bool isGlobal,
      bool enabledForAdmins,
      bool enabledForStaff,
      bool enabledForUsers,
      DateTime? disableStart,
      DateTime? disableEnd,
      String disabledMessage});
}

/// @nodoc
class _$FeatureToggleCopyWithImpl<$Res, $Val extends FeatureToggle>
    implements $FeatureToggleCopyWith<$Res> {
  _$FeatureToggleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? displayName = null,
    Object? description = null,
    Object? featureType = null,
    Object? isEnabled = null,
    Object? isGlobal = null,
    Object? enabledForAdmins = null,
    Object? enabledForStaff = null,
    Object? enabledForUsers = null,
    Object? disableStart = freezed,
    Object? disableEnd = freezed,
    Object? disabledMessage = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      featureType: null == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as String,
      isEnabled: null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
      enabledForAdmins: null == enabledForAdmins
          ? _value.enabledForAdmins
          : enabledForAdmins // ignore: cast_nullable_to_non_nullable
              as bool,
      enabledForStaff: null == enabledForStaff
          ? _value.enabledForStaff
          : enabledForStaff // ignore: cast_nullable_to_non_nullable
              as bool,
      enabledForUsers: null == enabledForUsers
          ? _value.enabledForUsers
          : enabledForUsers // ignore: cast_nullable_to_non_nullable
              as bool,
      disableStart: freezed == disableStart
          ? _value.disableStart
          : disableStart // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      disableEnd: freezed == disableEnd
          ? _value.disableEnd
          : disableEnd // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      disabledMessage: null == disabledMessage
          ? _value.disabledMessage
          : disabledMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FeatureToggleImplCopyWith<$Res>
    implements $FeatureToggleCopyWith<$Res> {
  factory _$$FeatureToggleImplCopyWith(
          _$FeatureToggleImpl value, $Res Function(_$FeatureToggleImpl) then) =
      __$$FeatureToggleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String displayName,
      String description,
      String featureType,
      bool isEnabled,
      bool isGlobal,
      bool enabledForAdmins,
      bool enabledForStaff,
      bool enabledForUsers,
      DateTime? disableStart,
      DateTime? disableEnd,
      String disabledMessage});
}

/// @nodoc
class __$$FeatureToggleImplCopyWithImpl<$Res>
    extends _$FeatureToggleCopyWithImpl<$Res, _$FeatureToggleImpl>
    implements _$$FeatureToggleImplCopyWith<$Res> {
  __$$FeatureToggleImplCopyWithImpl(
      _$FeatureToggleImpl _value, $Res Function(_$FeatureToggleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? displayName = null,
    Object? description = null,
    Object? featureType = null,
    Object? isEnabled = null,
    Object? isGlobal = null,
    Object? enabledForAdmins = null,
    Object? enabledForStaff = null,
    Object? enabledForUsers = null,
    Object? disableStart = freezed,
    Object? disableEnd = freezed,
    Object? disabledMessage = null,
  }) {
    return _then(_$FeatureToggleImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      featureType: null == featureType
          ? _value.featureType
          : featureType // ignore: cast_nullable_to_non_nullable
              as String,
      isEnabled: null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
      enabledForAdmins: null == enabledForAdmins
          ? _value.enabledForAdmins
          : enabledForAdmins // ignore: cast_nullable_to_non_nullable
              as bool,
      enabledForStaff: null == enabledForStaff
          ? _value.enabledForStaff
          : enabledForStaff // ignore: cast_nullable_to_non_nullable
              as bool,
      enabledForUsers: null == enabledForUsers
          ? _value.enabledForUsers
          : enabledForUsers // ignore: cast_nullable_to_non_nullable
              as bool,
      disableStart: freezed == disableStart
          ? _value.disableStart
          : disableStart // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      disableEnd: freezed == disableEnd
          ? _value.disableEnd
          : disableEnd // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      disabledMessage: null == disabledMessage
          ? _value.disabledMessage
          : disabledMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FeatureToggleImpl implements _FeatureToggle {
  const _$FeatureToggleImpl(
      {this.name = '',
      this.displayName = '',
      this.description = '',
      this.featureType = '',
      this.isEnabled = true,
      this.isGlobal = true,
      this.enabledForAdmins = true,
      this.enabledForStaff = true,
      this.enabledForUsers = true,
      this.disableStart,
      this.disableEnd,
      this.disabledMessage = 'This feature is temporarily unavailable.'});

  factory _$FeatureToggleImpl.fromJson(Map<String, dynamic> json) =>
      _$$FeatureToggleImplFromJson(json);

  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey()
  final String displayName;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String featureType;
  @override
  @JsonKey()
  final bool isEnabled;
  @override
  @JsonKey()
  final bool isGlobal;
  @override
  @JsonKey()
  final bool enabledForAdmins;
  @override
  @JsonKey()
  final bool enabledForStaff;
  @override
  @JsonKey()
  final bool enabledForUsers;
  @override
  final DateTime? disableStart;
  @override
  final DateTime? disableEnd;
  @override
  @JsonKey()
  final String disabledMessage;

  @override
  String toString() {
    return 'FeatureToggle(name: $name, displayName: $displayName, description: $description, featureType: $featureType, isEnabled: $isEnabled, isGlobal: $isGlobal, enabledForAdmins: $enabledForAdmins, enabledForStaff: $enabledForStaff, enabledForUsers: $enabledForUsers, disableStart: $disableStart, disableEnd: $disableEnd, disabledMessage: $disabledMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FeatureToggleImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.featureType, featureType) ||
                other.featureType == featureType) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled) &&
            (identical(other.isGlobal, isGlobal) ||
                other.isGlobal == isGlobal) &&
            (identical(other.enabledForAdmins, enabledForAdmins) ||
                other.enabledForAdmins == enabledForAdmins) &&
            (identical(other.enabledForStaff, enabledForStaff) ||
                other.enabledForStaff == enabledForStaff) &&
            (identical(other.enabledForUsers, enabledForUsers) ||
                other.enabledForUsers == enabledForUsers) &&
            (identical(other.disableStart, disableStart) ||
                other.disableStart == disableStart) &&
            (identical(other.disableEnd, disableEnd) ||
                other.disableEnd == disableEnd) &&
            (identical(other.disabledMessage, disabledMessage) ||
                other.disabledMessage == disabledMessage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      displayName,
      description,
      featureType,
      isEnabled,
      isGlobal,
      enabledForAdmins,
      enabledForStaff,
      enabledForUsers,
      disableStart,
      disableEnd,
      disabledMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FeatureToggleImplCopyWith<_$FeatureToggleImpl> get copyWith =>
      __$$FeatureToggleImplCopyWithImpl<_$FeatureToggleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FeatureToggleImplToJson(
      this,
    );
  }
}

abstract class _FeatureToggle implements FeatureToggle {
  const factory _FeatureToggle(
      {final String name,
      final String displayName,
      final String description,
      final String featureType,
      final bool isEnabled,
      final bool isGlobal,
      final bool enabledForAdmins,
      final bool enabledForStaff,
      final bool enabledForUsers,
      final DateTime? disableStart,
      final DateTime? disableEnd,
      final String disabledMessage}) = _$FeatureToggleImpl;

  factory _FeatureToggle.fromJson(Map<String, dynamic> json) =
      _$FeatureToggleImpl.fromJson;

  @override
  String get name;
  @override
  String get displayName;
  @override
  String get description;
  @override
  String get featureType;
  @override
  bool get isEnabled;
  @override
  bool get isGlobal;
  @override
  bool get enabledForAdmins;
  @override
  bool get enabledForStaff;
  @override
  bool get enabledForUsers;
  @override
  DateTime? get disableStart;
  @override
  DateTime? get disableEnd;
  @override
  String get disabledMessage;
  @override
  @JsonKey(ignore: true)
  _$$FeatureToggleImplCopyWith<_$FeatureToggleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SystemStatus _$SystemStatusFromJson(Map<String, dynamic> json) {
  return _SystemStatus.fromJson(json);
}

/// @nodoc
mixin _$SystemStatus {
  bool get maintenanceActive => throw _privateConstructorUsedError;
  MaintenanceStatus? get activeMaintenance =>
      throw _privateConstructorUsedError;
  Map<String, FeatureToggle> get featureToggles =>
      throw _privateConstructorUsedError;
  Map<String, bool> get featureStatus => throw _privateConstructorUsedError;
  DateTime? get lastChecked => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SystemStatusCopyWith<SystemStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SystemStatusCopyWith<$Res> {
  factory $SystemStatusCopyWith(
          SystemStatus value, $Res Function(SystemStatus) then) =
      _$SystemStatusCopyWithImpl<$Res, SystemStatus>;
  @useResult
  $Res call(
      {bool maintenanceActive,
      MaintenanceStatus? activeMaintenance,
      Map<String, FeatureToggle> featureToggles,
      Map<String, bool> featureStatus,
      DateTime? lastChecked});

  $MaintenanceStatusCopyWith<$Res>? get activeMaintenance;
}

/// @nodoc
class _$SystemStatusCopyWithImpl<$Res, $Val extends SystemStatus>
    implements $SystemStatusCopyWith<$Res> {
  _$SystemStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maintenanceActive = null,
    Object? activeMaintenance = freezed,
    Object? featureToggles = null,
    Object? featureStatus = null,
    Object? lastChecked = freezed,
  }) {
    return _then(_value.copyWith(
      maintenanceActive: null == maintenanceActive
          ? _value.maintenanceActive
          : maintenanceActive // ignore: cast_nullable_to_non_nullable
              as bool,
      activeMaintenance: freezed == activeMaintenance
          ? _value.activeMaintenance
          : activeMaintenance // ignore: cast_nullable_to_non_nullable
              as MaintenanceStatus?,
      featureToggles: null == featureToggles
          ? _value.featureToggles
          : featureToggles // ignore: cast_nullable_to_non_nullable
              as Map<String, FeatureToggle>,
      featureStatus: null == featureStatus
          ? _value.featureStatus
          : featureStatus // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      lastChecked: freezed == lastChecked
          ? _value.lastChecked
          : lastChecked // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MaintenanceStatusCopyWith<$Res>? get activeMaintenance {
    if (_value.activeMaintenance == null) {
      return null;
    }

    return $MaintenanceStatusCopyWith<$Res>(_value.activeMaintenance!, (value) {
      return _then(_value.copyWith(activeMaintenance: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SystemStatusImplCopyWith<$Res>
    implements $SystemStatusCopyWith<$Res> {
  factory _$$SystemStatusImplCopyWith(
          _$SystemStatusImpl value, $Res Function(_$SystemStatusImpl) then) =
      __$$SystemStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool maintenanceActive,
      MaintenanceStatus? activeMaintenance,
      Map<String, FeatureToggle> featureToggles,
      Map<String, bool> featureStatus,
      DateTime? lastChecked});

  @override
  $MaintenanceStatusCopyWith<$Res>? get activeMaintenance;
}

/// @nodoc
class __$$SystemStatusImplCopyWithImpl<$Res>
    extends _$SystemStatusCopyWithImpl<$Res, _$SystemStatusImpl>
    implements _$$SystemStatusImplCopyWith<$Res> {
  __$$SystemStatusImplCopyWithImpl(
      _$SystemStatusImpl _value, $Res Function(_$SystemStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maintenanceActive = null,
    Object? activeMaintenance = freezed,
    Object? featureToggles = null,
    Object? featureStatus = null,
    Object? lastChecked = freezed,
  }) {
    return _then(_$SystemStatusImpl(
      maintenanceActive: null == maintenanceActive
          ? _value.maintenanceActive
          : maintenanceActive // ignore: cast_nullable_to_non_nullable
              as bool,
      activeMaintenance: freezed == activeMaintenance
          ? _value.activeMaintenance
          : activeMaintenance // ignore: cast_nullable_to_non_nullable
              as MaintenanceStatus?,
      featureToggles: null == featureToggles
          ? _value._featureToggles
          : featureToggles // ignore: cast_nullable_to_non_nullable
              as Map<String, FeatureToggle>,
      featureStatus: null == featureStatus
          ? _value._featureStatus
          : featureStatus // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      lastChecked: freezed == lastChecked
          ? _value.lastChecked
          : lastChecked // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SystemStatusImpl implements _SystemStatus {
  const _$SystemStatusImpl(
      {this.maintenanceActive = false,
      this.activeMaintenance,
      final Map<String, FeatureToggle> featureToggles = const {},
      final Map<String, bool> featureStatus = const {},
      this.lastChecked})
      : _featureToggles = featureToggles,
        _featureStatus = featureStatus;

  factory _$SystemStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$SystemStatusImplFromJson(json);

  @override
  @JsonKey()
  final bool maintenanceActive;
  @override
  final MaintenanceStatus? activeMaintenance;
  final Map<String, FeatureToggle> _featureToggles;
  @override
  @JsonKey()
  Map<String, FeatureToggle> get featureToggles {
    if (_featureToggles is EqualUnmodifiableMapView) return _featureToggles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_featureToggles);
  }

  final Map<String, bool> _featureStatus;
  @override
  @JsonKey()
  Map<String, bool> get featureStatus {
    if (_featureStatus is EqualUnmodifiableMapView) return _featureStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_featureStatus);
  }

  @override
  final DateTime? lastChecked;

  @override
  String toString() {
    return 'SystemStatus(maintenanceActive: $maintenanceActive, activeMaintenance: $activeMaintenance, featureToggles: $featureToggles, featureStatus: $featureStatus, lastChecked: $lastChecked)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SystemStatusImpl &&
            (identical(other.maintenanceActive, maintenanceActive) ||
                other.maintenanceActive == maintenanceActive) &&
            (identical(other.activeMaintenance, activeMaintenance) ||
                other.activeMaintenance == activeMaintenance) &&
            const DeepCollectionEquality()
                .equals(other._featureToggles, _featureToggles) &&
            const DeepCollectionEquality()
                .equals(other._featureStatus, _featureStatus) &&
            (identical(other.lastChecked, lastChecked) ||
                other.lastChecked == lastChecked));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      maintenanceActive,
      activeMaintenance,
      const DeepCollectionEquality().hash(_featureToggles),
      const DeepCollectionEquality().hash(_featureStatus),
      lastChecked);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SystemStatusImplCopyWith<_$SystemStatusImpl> get copyWith =>
      __$$SystemStatusImplCopyWithImpl<_$SystemStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SystemStatusImplToJson(
      this,
    );
  }
}

abstract class _SystemStatus implements SystemStatus {
  const factory _SystemStatus(
      {final bool maintenanceActive,
      final MaintenanceStatus? activeMaintenance,
      final Map<String, FeatureToggle> featureToggles,
      final Map<String, bool> featureStatus,
      final DateTime? lastChecked}) = _$SystemStatusImpl;

  factory _SystemStatus.fromJson(Map<String, dynamic> json) =
      _$SystemStatusImpl.fromJson;

  @override
  bool get maintenanceActive;
  @override
  MaintenanceStatus? get activeMaintenance;
  @override
  Map<String, FeatureToggle> get featureToggles;
  @override
  Map<String, bool> get featureStatus;
  @override
  DateTime? get lastChecked;
  @override
  @JsonKey(ignore: true)
  _$$SystemStatusImplCopyWith<_$SystemStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
