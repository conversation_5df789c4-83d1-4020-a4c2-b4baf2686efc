import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'post.dart';
import 'user.dart';

part 'search_results.freezed.dart';
part 'search_results.g.dart';

@freezed
class SearchResults with _$SearchResults {
  const factory SearchResults({
    required List<Post> posts,
    required List<User> users,
    @Default(0) int totalCount,
  }) = _SearchResults;

  factory SearchResults.fromJson(Map<String, dynamic> json) =>
      _$SearchResultsFromJson(json);
}
