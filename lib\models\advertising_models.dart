import 'package:freezed_annotation/freezed_annotation.dart';

part 'advertising_models.freezed.dart';
part 'advertising_models.g.dart';

@freezed
class AdPlacement with _$AdPlacement {
  const factory AdPlacement({
    required String id,
    required String name,
    @J<PERSON><PERSON>ey(name: 'placement_type') required String placementType,
    required String location,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') required bool isActive,
    required int frequency,
    @<PERSON>son<PERSON>ey(name: 'max_per_session') required int maxPerSession,
    @<PERSON>son<PERSON>ey(name: 'points_reward') required int pointsReward,
    @J<PERSON><PERSON>ey(name: 'min_user_level') required int minUserLevel,
    @<PERSON>son<PERSON>ey(name: 'premium_users_only') required bool premiumUsersOnly,
    @<PERSON>son<PERSON><PERSON>(name: 'free_users_only') required bool freeUsersOnly,
  }) = _AdPlacement;

  factory AdPlacement.fromJson(Map<String, dynamic> json) =>
      _$AdPlacementFromJson(json);
}

@freezed
class AdNetwork with _$AdNetwork {
  const factory AdNetwork({
    required String id,
    required String name,
    @J<PERSON><PERSON>ey(name: 'display_name') required String displayName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') required bool isActive,
    required int priority,
    @<PERSON>sonKey(name: 'revenue_share_percentage') required double revenueSharePercentage,
  }) = _AdNetwork;

  factory AdNetwork.fromJson(Map<String, dynamic> json) =>
      _$AdNetworkFromJson(json);
}

@freezed
class RewardedAdSession with _$RewardedAdSession {
  const factory RewardedAdSession({
    required String id,
    @JsonKey(name: 'session_id') required String sessionId,
    required AdPlacement placement,
    @JsonKey(name: 'ad_network') required AdNetwork adNetwork,
    required String status,
    @JsonKey(name: 'points_offered') required int pointsOffered,
    @JsonKey(name: 'points_awarded') required int pointsAwarded,
    @JsonKey(name: 'reward_claimed') required bool rewardClaimed,
    @JsonKey(name: 'started_at') required DateTime startedAt,
    @JsonKey(name: 'completed_at') DateTime? completedAt,
  }) = _RewardedAdSession;

  factory RewardedAdSession.fromJson(Map<String, dynamic> json) =>
      _$RewardedAdSessionFromJson(json);
}

@freezed
class SponsoredContent with _$SponsoredContent {
  const factory SponsoredContent({
    required String id,
    required String title,
    required String content,
    @JsonKey(name: 'sponsor_name') required String sponsorName,
    @JsonKey(name: 'sponsor_type') required String sponsorType,
    @JsonKey(name: 'sponsor_logo') String? sponsorLogo,
    @JsonKey(name: 'image_url') String? imageUrl,
    @JsonKey(name: 'video_url') String? videoUrl,
    @JsonKey(name: 'call_to_action') required String callToAction,
    @JsonKey(name: 'target_url') required String targetUrl,
    required String status,
    @JsonKey(name: 'start_date') required DateTime startDate,
    @JsonKey(name: 'end_date') required DateTime endDate,
    @JsonKey(name: 'total_impressions') required int totalImpressions,
    @JsonKey(name: 'total_clicks') required int totalClicks,
  }) = _SponsoredContent;

  factory SponsoredContent.fromJson(Map<String, dynamic> json) =>
      _$SponsoredContentFromJson(json);
}

@freezed
class AdSettings with _$AdSettings {
  const factory AdSettings({
    @JsonKey(name: 'ads_enabled') required bool adsEnabled,
    @JsonKey(name: 'rewarded_ads_enabled') required bool rewardedAdsEnabled,
    @JsonKey(name: 'sponsored_content_enabled') required bool sponsoredContentEnabled,
    @JsonKey(name: 'max_ads_per_session') required int maxAdsPerSession,
    @JsonKey(name: 'min_time_between_ads') required int minTimeBetweenAds,
    @JsonKey(name: 'skip_ads_for_premium') required bool skipAdsForPremium,
    @JsonKey(name: 'base_points_per_ad') required int basePointsPerAd,
    @JsonKey(name: 'bonus_points_multiplier') required double bonusPointsMultiplier,
    @JsonKey(name: 'max_daily_ad_points') required int maxDailyAdPoints,
  }) = _AdSettings;

  factory AdSettings.fromJson(Map<String, dynamic> json) =>
      _$AdSettingsFromJson(json);
}

@freezed
class AdImpression with _$AdImpression {
  const factory AdImpression({
    required String id,
    required AdPlacement placement,
    @JsonKey(name: 'ad_network') required AdNetwork adNetwork,
    @JsonKey(name: 'user_id') String? userId,
    @JsonKey(name: 'session_id') String? sessionId,
    @JsonKey(name: 'was_clicked') required bool wasClicked,
    @JsonKey(name: 'points_awarded') required int pointsAwarded,
    @JsonKey(name: 'revenue_generated') required double revenueGenerated,
    @JsonKey(name: 'created_at') required DateTime createdAt,
  }) = _AdImpression;

  factory AdImpression.fromJson(Map<String, dynamic> json) =>
      _$AdImpressionFromJson(json);
}

// Enums for ad types
enum AdPlacementType {
  banner,
  interstitial,
  rewarded,
  native,
  video,
}

enum AdStatus {
  pending,
  active,
  paused,
  completed,
  failed,
}

enum SponsorType {
  brand,
  startup,
  nonprofit,
  government,
  individual,
}

// Helper extensions
extension AdPlacementTypeExtension on AdPlacementType {
  String get displayName {
    switch (this) {
      case AdPlacementType.banner:
        return 'Banner Ad';
      case AdPlacementType.interstitial:
        return 'Interstitial Ad';
      case AdPlacementType.rewarded:
        return 'Rewarded Ad';
      case AdPlacementType.native:
        return 'Native Ad';
      case AdPlacementType.video:
        return 'Video Ad';
    }
  }
}

extension AdStatusExtension on AdStatus {
  String get displayName {
    switch (this) {
      case AdStatus.pending:
        return 'Pending';
      case AdStatus.active:
        return 'Active';
      case AdStatus.paused:
        return 'Paused';
      case AdStatus.completed:
        return 'Completed';
      case AdStatus.failed:
        return 'Failed';
    }
  }
}

extension SponsorTypeExtension on SponsorType {
  String get displayName {
    switch (this) {
      case SponsorType.brand:
        return 'Brand';
      case SponsorType.startup:
        return 'Startup';
      case SponsorType.nonprofit:
        return 'Non-Profit';
      case SponsorType.government:
        return 'Government';
      case SponsorType.individual:
        return 'Individual';
    }
  }
}
