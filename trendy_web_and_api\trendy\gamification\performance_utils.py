"""
Performance optimization utilities for gamification system
"""
from django.core.cache import cache
from django.utils import timezone
from functools import wraps
import hashlib
import json
from datetime import timedelta


class RequestThrottler:
    """Throttle API requests to prevent performance issues"""
    
    @staticmethod
    def get_user_key(user, endpoint):
        """Generate cache key for user-specific throttling"""
        return f"throttle:{user.id}:{endpoint}"
    
    @staticmethod
    def is_throttled(user, endpoint, limit_seconds=5):
        """Check if user is throttled for specific endpoint"""
        key = RequestThrottler.get_user_key(user, endpoint)
        last_request = cache.get(key)
        
        if last_request:
            time_diff = (timezone.now() - last_request).total_seconds()
            return time_diff < limit_seconds
        
        return False
    
    @staticmethod
    def record_request(user, endpoint):
        """Record user request timestamp"""
        key = RequestThrottler.get_user_key(user, endpoint)
        cache.set(key, timezone.now(), timeout=60)  # Cache for 1 minute


class DataCache:
    """Intelligent caching for frequently accessed data"""
    
    @staticmethod
    def get_cache_key(prefix, user_id, **kwargs):
        """Generate consistent cache key"""
        key_data = {'user_id': user_id, **kwargs}
        key_string = json.dumps(key_data, sort_keys=True)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    @staticmethod
    def get_user_points_cache_key(user_id):
        """Get cache key for user points data"""
        return f"user_points:{user_id}"
    
    @staticmethod
    def cache_user_points(user_id, data, timeout=300):  # 5 minutes
        """Cache user points data"""
        key = DataCache.get_user_points_cache_key(user_id)
        cache.set(key, data, timeout=timeout)
    
    @staticmethod
    def get_cached_user_points(user_id):
        """Get cached user points data"""
        key = DataCache.get_user_points_cache_key(user_id)
        return cache.get(key)
    
    @staticmethod
    def invalidate_user_points_cache(user_id):
        """Invalidate user points cache when data changes"""
        key = DataCache.get_user_points_cache_key(user_id)
        cache.delete(key)
    
    @staticmethod
    def cache_leaderboard(data, timeout=600):  # 10 minutes
        """Cache leaderboard data"""
        cache.set("leaderboard:global", data, timeout=timeout)
    
    @staticmethod
    def get_cached_leaderboard():
        """Get cached leaderboard data"""
        return cache.get("leaderboard:global")


def throttle_endpoint(limit_seconds=5):
    """Decorator to throttle API endpoint requests"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return view_func(request, *args, **kwargs)
            
            endpoint = request.resolver_match.url_name
            
            if RequestThrottler.is_throttled(request.user, endpoint, limit_seconds):
                from rest_framework.response import Response
                from rest_framework import status
                return Response({
                    'success': False,
                    'error': f'Too many requests. Please wait {limit_seconds} seconds between requests.',
                    'throttled': True
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)
            
            RequestThrottler.record_request(request.user, endpoint)
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def cache_response(timeout=300, key_prefix='api'):
    """Decorator to cache API responses"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return view_func(request, *args, **kwargs)
            
            # Generate cache key based on user, endpoint, and parameters
            cache_key = DataCache.get_cache_key(
                key_prefix,
                request.user.id,
                endpoint=request.resolver_match.url_name,
                method=request.method,
                params=dict(request.GET.items()) if request.method == 'GET' else {}
            )
            
            # Try to get cached response
            cached_response = cache.get(cache_key)
            if cached_response:
                from rest_framework.response import Response
                cached_response['cached'] = True
                cached_response['cache_timestamp'] = timezone.now().isoformat()
                return Response(cached_response)
            
            # Get fresh response
            response = view_func(request, *args, **kwargs)
            
            # Cache successful responses
            if hasattr(response, 'data') and response.status_code == 200:
                cache.set(cache_key, response.data, timeout=timeout)
            
            return response
        
        return wrapper
    return decorator


class PerformanceMonitor:
    """Monitor and log performance metrics"""
    
    @staticmethod
    def log_slow_request(endpoint, duration, user_id=None):
        """Log slow requests for analysis"""
        if duration > 2.0:  # Log requests taking more than 2 seconds
            print(f"⚠️ Slow request: {endpoint} took {duration:.2f}s for user {user_id}")
    
    @staticmethod
    def monitor_endpoint(view_func):
        """Decorator to monitor endpoint performance"""
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            start_time = timezone.now()
            
            try:
                response = view_func(request, *args, **kwargs)
                return response
            finally:
                end_time = timezone.now()
                duration = (end_time - start_time).total_seconds()
                
                endpoint = request.resolver_match.url_name if request.resolver_match else 'unknown'
                user_id = request.user.id if request.user.is_authenticated else None
                
                PerformanceMonitor.log_slow_request(endpoint, duration, user_id)
        
        return wrapper


def batch_database_operations(operations):
    """Execute multiple database operations in a single transaction"""
    from django.db import transaction
    
    with transaction.atomic():
        results = []
        for operation in operations:
            try:
                result = operation()
                results.append(result)
            except Exception as e:
                print(f"Batch operation failed: {e}")
                results.append(None)
        return results


class RequestBatcher:
    """Batch multiple requests to reduce database load"""
    
    def __init__(self, batch_size=10, timeout_seconds=2):
        self.batch_size = batch_size
        self.timeout_seconds = timeout_seconds
        self.pending_requests = []
        self.last_batch_time = timezone.now()
    
    def add_request(self, request_data):
        """Add request to batch"""
        self.pending_requests.append(request_data)
        
        # Process batch if size limit reached or timeout exceeded
        if (len(self.pending_requests) >= self.batch_size or 
            (timezone.now() - self.last_batch_time).total_seconds() >= self.timeout_seconds):
            return self.process_batch()
        
        return None
    
    def process_batch(self):
        """Process all pending requests in batch"""
        if not self.pending_requests:
            return []
        
        requests = self.pending_requests.copy()
        self.pending_requests.clear()
        self.last_batch_time = timezone.now()
        
        # Process requests in batch
        results = []
        for request_data in requests:
            try:
                # Process individual request
                result = self._process_single_request(request_data)
                results.append(result)
            except Exception as e:
                print(f"Batch request failed: {e}")
                results.append({'error': str(e)})
        
        return results
    
    def _process_single_request(self, request_data):
        """Process a single request (override in subclasses)"""
        return request_data


# Global request batcher instance
points_update_batcher = RequestBatcher(batch_size=5, timeout_seconds=3)


def optimize_queryset(queryset, select_related=None, prefetch_related=None):
    """Optimize queryset with select_related and prefetch_related"""
    if select_related:
        queryset = queryset.select_related(*select_related)
    
    if prefetch_related:
        queryset = queryset.prefetch_related(*prefetch_related)
    
    return queryset


def get_optimized_user_level(user):
    """Get user level with optimized query"""
    from .models import UserLevel
    
    try:
        return UserLevel.objects.select_related('user').get(user=user)
    except UserLevel.DoesNotExist:
        # Create with minimal database hits
        return UserLevel.objects.create(
            user=user,
            total_points=0,
            current_level=1,
            points_to_next_level=100
        )
