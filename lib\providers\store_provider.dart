import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../models/reward_models.dart';

// Store state
class StoreState {
  final List<VirtualItem> virtualItems;
  final List<PointBoostPackage> pointBoosts;
  final List<UserVirtualItem> userItems;
  final PremiumSubscription? premiumSubscription;
  final bool isLoading;
  final String? error;

  const StoreState({
    this.virtualItems = const [],
    this.pointBoosts = const [],
    this.userItems = const [],
    this.premiumSubscription,
    this.isLoading = false,
    this.error,
  });

  StoreState copyWith({
    List<VirtualItem>? virtualItems,
    List<PointBoostPackage>? pointBoosts,
    List<UserVirtualItem>? userItems,
    PremiumSubscription? premiumSubscription,
    bool? isLoading,
    String? error,
  }) {
    return StoreState(
      virtualItems: virtualItems ?? this.virtualItems,
      pointBoosts: pointBoosts ?? this.pointBoosts,
      userItems: userItems ?? this.userItems,
      premiumSubscription: premiumSubscription ?? this.premiumSubscription,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Store notifier
class StoreNotifier extends StateNotifier<StoreState> {
  final ApiService _apiService;

  StoreNotifier(this._apiService) : super(const StoreState());

  // Load virtual items
  Future<void> loadVirtualItems() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final response = await _apiService.getVirtualItems();
      final items = (response['items'] as List)
          .map((json) => VirtualItem.fromJson(json))
          .toList();
      
      state = state.copyWith(
        virtualItems: items,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  // Load point boost packages
  Future<void> loadPointBoosts() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the actual backend API
      final response = await _apiService.getPointBoostPackages();

      if (response['success'] == true && response['packages'] != null) {
        final packagesData = response['packages'] as List;

        final packages = packagesData.map((json) {
          return PointBoostPackage(
            id: json['id']?.toString() ?? '',
            name: json['name']?.toString() ?? '',
            description: json['description']?.toString() ?? '',
            basePoints: json['base_points'] ?? 0,
            bonusPoints: json['bonus_points'] ?? 0,
            totalPoints: json['total_points'] ?? 0,
            price: double.tryParse(json['price']?.toString() ?? '0') ?? 0.0,
            currency: json['currency']?.toString() ?? 'USD',
            isActive: json['is_active'] ?? true,
            isPopular: json['is_popular'] ?? false,
          );
        }).toList();

        state = state.copyWith(
          pointBoosts: packages,
          isLoading: false,
        );
      } else {
        // Fallback to empty list if API fails
        state = state.copyWith(
          pointBoosts: [],
          isLoading: false,
          error: response['message'] ?? 'Failed to load point boost packages from server',
        );
      }
    } catch (e) {
      print('Error loading point boosts: $e');
      state = state.copyWith(
        isLoading: false,
        pointBoosts: [],
        error: 'Failed to load point boosts: ${e.toString()}',
      );
    }
  }

  // Load premium subscription status
  Future<void> loadPremiumStatus() async {
    try {
      final response = await _apiService.getPremiumStatus();
      
      if (response['is_premium'] == true && response['subscription'] != null) {
        final subscription = PremiumSubscription.fromJson(response['subscription']);
        state = state.copyWith(premiumSubscription: subscription);
      } else {
        state = state.copyWith(premiumSubscription: null);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  // Purchase virtual item
  Future<Map<String, dynamic>> purchaseVirtualItem(String itemId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.purchaseVirtualItem(itemId);

      if (response['success'] == true) {
        // Reload items to reflect purchase
        await loadVirtualItems();
        state = state.copyWith(isLoading: false);
        return {'success': true, 'message': response['message']};
      } else if (response['payment_required'] == true) {
        // Payment required - return payment info
        state = state.copyWith(isLoading: false);
        return {
          'success': false,
          'payment_required': true,
          'item_id': response['item_id'],
          'item_name': response['item_name'],
          'price': response['price'],
          'message': response['message'],
        };
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['error'] ?? response['message'] ?? 'Failed to purchase item',
        );
        return {'success': false, 'message': response['error'] ?? response['message'] ?? 'Failed to purchase item'};
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return {'success': false, 'message': e.toString()};
    }
  }

  // Purchase point boost
  Future<Map<String, dynamic>> purchasePointBoost(String packageId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.purchasePointBoost(packageId);

      if (response['success'] == true) {
        // Reload point boosts to reflect purchase
        await loadPointBoosts();
        state = state.copyWith(isLoading: false);
        return {'success': true, 'message': response['message']};
      } else if (response['payment_required'] == true) {
        // Payment required - return payment info with order details
        state = state.copyWith(isLoading: false);
        return {
          'success': false,
          'payment_required': true,
          'package_id': response['package_id'],
          'package_name': response['package_name'],
          'price': response['price'],
          'points': response['points'],
          'order_id': response['order_id'],
          'approval_url': response['approval_url'],
          'transaction_id': response['transaction_id'],
          'message': response['message'],
        };
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['error'] ?? response['message'] ?? 'Failed to purchase point boost',
        );
        return {'success': false, 'message': response['error'] ?? response['message'] ?? 'Failed to purchase point boost'};
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to purchase point boost: ${e.toString()}',
      );
      return {'success': false, 'message': 'Failed to purchase point boost: ${e.toString()}'};
    }
  }

  // Confirm point boost payment
  Future<Map<String, dynamic>> confirmPointBoostPayment(String transactionId, String packageId, String status) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.confirmPointBoostPayment(transactionId, packageId, status);

      if (response['success'] == true) {
        // Reload point boosts and user data
        await loadPointBoosts();
        state = state.copyWith(isLoading: false);
        return {
          'success': true,
          'message': response['message'],
          'points_awarded': response['points_awarded']
        };
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? 'Failed to confirm payment',
        );
        return {'success': false, 'message': response['message'] ?? 'Failed to confirm payment'};
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to confirm payment: ${e.toString()}',
      );
      return {'success': false, 'message': 'Failed to confirm payment: ${e.toString()}'};
    }
  }

  // Create premium subscription payment
  Future<Map<String, dynamic>> createPremiumSubscription(String plan) async {
    try {
      print('🛒 Creating premium subscription: $plan');
      state = state.copyWith(isLoading: true, error: null);

      // Try wallet payment first
      final response = await _apiService.subscribeToPremium(plan, paymentMethod: 'wallet');
      print('🛒 Subscription API response: $response');

      state = state.copyWith(isLoading: false);

      if (response['success'] == true) {
        print('✅ Subscription created successfully');
        // Reload premium status to reflect changes
        await loadPremiumStatus();
        return response;
      } else {
        print('❌ Subscription creation failed: ${response['message']}');
        return response;
      }
    } catch (e) {
      print('❌ Error creating premium subscription: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create subscription: ${e.toString()}',
      );
      return {
        'success': false,
        'message': 'Failed to create subscription: ${e.toString()}',
      };
    }
  }

  // Create premium subscription payment (legacy method for compatibility)
  Future<String?> createPremiumPayment(String plan, double amount) async {
    final result = await createPremiumSubscription(plan);

    if (result['success'] == true) {
      // Return success indicator instead of fake URL
      return 'subscription_created_successfully';
    } else {
      return null;
    }
  }

  // Capture premium payment
  Future<bool> capturePremiumPayment(String orderId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final response = await _apiService.capturePaymentOrder(orderId);
      
      if (response['success'] == true) {
        // Reload premium status
        await loadPremiumStatus();
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['error'] ?? 'Failed to capture payment',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // Get items by category
  List<VirtualItem> getItemsByCategory(String category) {
    return state.virtualItems
        .where((item) => item.category == category && item.isActive)
        .toList();
  }

  // Check if user is premium
  bool isPremium() {
    return state.premiumSubscription != null &&
           state.premiumSubscription!.status == 'active';
  }

  // Get premium benefits
  Map<String, dynamic> getPremiumBenefits() {
    if (state.premiumSubscription != null) {
      return {
        'pointMultiplier': state.premiumSubscription!.pointMultiplier,
        'dailyStreakBonus': state.premiumSubscription!.dailyStreakBonus,
        'voiceCommentsLimit': state.premiumSubscription!.voiceCommentsLimit,
        'plan': state.premiumSubscription!.plan,
        'endDate': state.premiumSubscription!.endDate,
      };
    }
    return {};
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final storeProvider = StateNotifierProvider<StoreNotifier, StoreState>((ref) {
  return StoreNotifier(ApiService());
});

// Convenience providers
final virtualItemsProvider = Provider<List<VirtualItem>>((ref) {
  return ref.watch(storeProvider).virtualItems;
});

final pointBoostsProvider = Provider<List<PointBoostPackage>>((ref) {
  return ref.watch(storeProvider).pointBoosts;
});

final premiumSubscriptionProvider = Provider<PremiumSubscription?>((ref) {
  return ref.watch(storeProvider).premiumSubscription;
});

final isPremiumProvider = Provider<bool>((ref) {
  final notifier = ref.read(storeProvider.notifier);
  return notifier.isPremium();
});

final premiumBenefitsProvider = Provider<Map<String, dynamic>>((ref) {
  final notifier = ref.read(storeProvider.notifier);
  return notifier.getPremiumBenefits();
});

// Category-specific providers
final cosmeticItemsProvider = Provider<List<VirtualItem>>((ref) {
  final notifier = ref.read(storeProvider.notifier);
  return notifier.getItemsByCategory('cosmetic');
});

final functionalItemsProvider = Provider<List<VirtualItem>>((ref) {
  final notifier = ref.read(storeProvider.notifier);
  return notifier.getItemsByCategory('functional');
});

final boostItemsProvider = Provider<List<VirtualItem>>((ref) {
  final notifier = ref.read(storeProvider.notifier);
  return notifier.getItemsByCategory('boost');
});
