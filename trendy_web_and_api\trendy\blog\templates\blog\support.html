{% extends 'blog/base.html' %}
{% load crispy_forms_tags %}

{% block title %}Support Center - Trendy Blog{% endblock %}

{% block content %}
<div class="support-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="support-title">How can we help you?</h1>
                <p class="support-subtitle">Find answers to common questions or get in touch with our support team</p>

                <!-- Search Bar -->
                <div class="support-search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search for help articles..." id="supportSearch">
                        <button class="btn btn-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container mt-5">
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="support-action-card text-center">
                <div class="action-icon">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h5>Submit Ticket</h5>
                <p>Get personalized help from our support team</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ticketModal">
                    Create Ticket
                </button>
            </div>
        </div>
        <div class="col-md-3">
            <div class="support-action-card text-center">
                <div class="action-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <h5>Live Chat</h5>
                <p>Chat with our support team in real-time</p>
                <button class="btn btn-success" onclick="startLiveChat()">
                    Start Chat
                </button>
            </div>
        </div>
        <div class="col-md-3">
            <div class="support-action-card text-center">
                <div class="action-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <h5>Call Support</h5>
                <p>Speak directly with our support team</p>
                <a href="tel:******-TRENDY" class="btn btn-info">
                    Call Now
                </a>
            </div>
        </div>
        <div class="col-md-3">
            <div class="support-action-card text-center">
                <div class="action-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h5>Email Support</h5>
                <p>Send us an email for detailed inquiries</p>
                <a href="mailto:<EMAIL>" class="btn btn-secondary">
                    Send Email
                </a>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <h2 class="text-center mb-5">Frequently Asked Questions</h2>
            <div class="accordion" id="faqAccordion">
                <!-- Account & Registration -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            How do I create an account?
                        </button>
                    </h2>
                    <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Creating an account is easy! Click the "Sign Up" button in the top right corner, fill in your details, and verify your email address. You'll be ready to start blogging in minutes.
                        </div>
                    </div>
                </div>

                <!-- Writing & Publishing -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            How do I publish my first post?
                        </button>
                    </h2>
                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            After logging in, click "New Story" from the homepage or your dashboard. Use our rich text editor to write your content, add images, and format your post. When ready, click "Publish" to share it with the world.
                        </div>
                    </div>
                </div>

                <!-- Monetization -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                            How can I earn money from my content?
                        </button>
                    </h2>
                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Trendy offers multiple monetization options: earn points through our gamification system, unlock achievements for rewards, participate in our referral program, and access premium features. Check your wallet for current earnings and available rewards.
                        </div>
                    </div>
                </div>

                <!-- Technical Issues -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                            I'm having technical issues. What should I do?
                        </button>
                    </h2>
                    <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            First, try refreshing your browser or clearing your cache. If the issue persists, submit a support ticket with details about the problem, including your browser type and any error messages you see.
                        </div>
                    </div>
                </div>

                <!-- Privacy & Security -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                            How is my data protected?
                        </button>
                    </h2>
                    <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            We take your privacy seriously. All data is encrypted, we never share personal information with third parties, and you have full control over your content. Read our <a href="{% url 'privacy-policy' %}">Privacy Policy</a> for complete details.
                        </div>
                    </div>
                </div>

                <!-- Blockchain & Wallet -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq6">
                            What is the blockchain wallet feature?
                        </button>
                    </h2>
                    <div id="faq6" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Your blockchain wallet allows you to earn TRD tokens and collect achievement NFTs. It's automatically created when you sign up but needs activation. Check your profile for activation instructions and start earning crypto rewards!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Support Ticket Modal -->
<div class="modal fade" id="ticketModal" tabindex="-1" aria-labelledby="ticketModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ticketModalLabel">
                    <i class="fas fa-ticket-alt me-2"></i>Submit Support Ticket
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="supportTicketForm" method="post" action="{% url 'support' %}">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ticketName" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="ticketName" name="name" required
                                       value="{% if user.is_authenticated %}{{ user.get_full_name }}{% endif %}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ticketEmail" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="ticketEmail" name="email" required
                                       value="{% if user.is_authenticated %}{{ user.email }}{% endif %}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ticketCategory" class="form-label">Issue Category *</label>
                                <select class="form-select" id="ticketCategory" name="category" required>
                                    <option value="">Select a category</option>
                                    <option value="account">Account & Login Issues</option>
                                    <option value="technical">Technical Problems</option>
                                    <option value="billing">Billing & Payments</option>
                                    <option value="content">Content & Publishing</option>
                                    <option value="blockchain">Blockchain & Wallet</option>
                                    <option value="feature">Feature Request</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ticketPriority" class="form-label">Priority Level</label>
                                <select class="form-select" id="ticketPriority" name="priority">
                                    <option value="low">Low - General inquiry</option>
                                    <option value="medium" selected>Medium - Standard issue</option>
                                    <option value="high">High - Urgent problem</option>
                                    <option value="critical">Critical - Service down</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="ticketSubject" class="form-label">Subject *</label>
                        <input type="text" class="form-control" id="ticketSubject" name="subject" required
                               placeholder="Brief description of your issue">
                    </div>

                    <div class="mb-3">
                        <label for="ticketMessage" class="form-label">Detailed Description *</label>
                        <textarea class="form-control" id="ticketMessage" name="message" rows="5" required
                                  placeholder="Please provide as much detail as possible about your issue, including steps to reproduce if applicable."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="ticketAttachment" class="form-label">Attachment (Optional)</label>
                        <input type="file" class="form-control" id="ticketAttachment" name="attachment"
                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                        <div class="form-text">Supported formats: JPG, PNG, GIF, PDF, DOC, DOCX (Max 10MB)</div>
                    </div>

                    {% if user.is_authenticated %}
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="ticketUpdates" name="email_updates" checked>
                            <label class="form-check-label" for="ticketUpdates">
                                Send me email updates about this ticket
                            </label>
                        </div>
                    </div>
                    {% endif %}
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="supportTicketForm" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>Submit Ticket
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Support Ticket Modal -->
<div class="modal fade" id="ticketModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Submit Support Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="supportTicketForm" method="post" action="{% url 'support' %}">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ticketName" class="form-label">Your Name *</label>
                                <input type="text" class="form-control" id="ticketName" name="name" required
                                       value="{% if user.is_authenticated %}{{ user.get_full_name|default:user.username }}{% endif %}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="ticketEmail" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="ticketEmail" name="email" required
                                       value="{% if user.is_authenticated %}{{ user.email }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="ticketCategory" class="form-label">Category *</label>
                        <select class="form-select" id="ticketCategory" name="category" required>
                            <option value="">Select a category</option>
                            <option value="technical">Technical Issue</option>
                            <option value="account">Account & Login</option>
                            <option value="billing">Billing & Payments</option>
                            <option value="content">Content & Publishing</option>
                            <option value="blockchain">Blockchain & Wallet</option>
                            <option value="feature">Feature Request</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="ticketPriority" class="form-label">Priority *</label>
                        <select class="form-select" id="ticketPriority" name="priority" required>
                            <option value="low">Low - General inquiry</option>
                            <option value="medium" selected>Medium - Standard issue</option>
                            <option value="high">High - Urgent issue</option>
                            <option value="critical">Critical - System down</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="ticketSubject" class="form-label">Subject *</label>
                        <input type="text" class="form-control" id="ticketSubject" name="subject" required
                               placeholder="Brief description of your issue">
                    </div>
                    <div class="mb-3">
                        <label for="ticketDescription" class="form-label">Description *</label>
                        <textarea class="form-control" id="ticketDescription" name="description" rows="5" required
                                  placeholder="Please provide detailed information about your issue, including steps to reproduce if applicable"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="ticketAttachment" class="form-label">Attachment (optional)</label>
                        <input type="file" class="form-control" id="ticketAttachment" name="attachment"
                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                        <div class="form-text">Supported formats: JPG, PNG, GIF, PDF, DOC, DOCX (Max 10MB)</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="supportTicketForm" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>Submit Ticket
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container mt-5">
    <div class="row">
        <!-- Quick Help Cards -->
        <div class="col-12 mb-5">
            <h2 class="section-title">Quick Help</h2>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="help-card">
                        <div class="help-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <h5>Account & Profile</h5>
                        <p>Manage your account settings, profile information, and privacy preferences.</p>
                        <a href="#account-faq" class="help-link">Learn more <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="help-card">
                        <div class="help-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h5>Creating Posts</h5>
                        <p>Learn how to create, edit, and manage your blog posts effectively.</p>
                        <a href="#posting-faq" class="help-link">Learn more <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="help-card">
                        <div class="help-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5>Security & Privacy</h5>
                        <p>Keep your account secure and control your privacy settings.</p>
                        <a href="#security-faq" class="help-link">Learn more <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="help-card">
                        <div class="help-icon">
                            <i class="fas fa-bug"></i>
                        </div>
                        <h5>Report Issues</h5>
                        <p>Found a bug or experiencing technical difficulties? Let us know.</p>
                        <a href="#contact" class="help-link">Report issue <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="col-lg-8 mb-5">
            <h2 class="section-title">Frequently Asked Questions</h2>
            
            <!-- Account FAQ -->
            <div class="faq-section" id="account-faq">
                <h4 class="faq-category">Account & Profile</h4>
                
                <div class="accordion" id="accountAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#account1">
                                How do I change my password?
                            </button>
                        </h2>
                        <div id="account1" class="accordion-collapse collapse" data-bs-parent="#accountAccordion">
                            <div class="accordion-body">
                                <p>To change your password:</p>
                                <ol>
                                    <li>Go to your <a href="{% url 'profile' %}">Profile Settings</a></li>
                                    <li>Click on "Account & Security" tab</li>
                                    <li>Click "Change Password" button</li>
                                    <li>Enter your current password and new password</li>
                                    <li>Click "Save Changes"</li>
                                </ol>
                                <p>Make sure your new password is at least 8 characters long and includes a mix of letters, numbers, and symbols.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#account2">
                                How do I delete my account?
                            </button>
                        </h2>
                        <div id="account2" class="accordion-collapse collapse" data-bs-parent="#accountAccordion">
                            <div class="accordion-body">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> Account deletion is permanent and cannot be undone.
                                </div>
                                <p>To delete your account:</p>
                                <ol>
                                    <li>Go to <a href="{% url 'profile' %}">Settings</a></li>
                                    <li>Navigate to "Danger Zone" tab</li>
                                    <li>Click "Delete Account"</li>
                                    <li>Type "DELETE" to confirm</li>
                                    <li>Enter your password</li>
                                    <li>Click "Delete My Account"</li>
                                </ol>
                                <p>This will permanently delete all your posts, comments, and personal data.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#account3">
                                How do I update my profile information?
                            </button>
                        </h2>
                        <div id="account3" class="accordion-collapse collapse" data-bs-parent="#accountAccordion">
                            <div class="accordion-body">
                                <p>You can update your profile information by:</p>
                                <ol>
                                    <li>Going to your <a href="{% url 'profile' %}">Profile Settings</a></li>
                                    <li>Editing the fields you want to change (name, bio, location, etc.)</li>
                                    <li>Uploading a new profile picture if desired</li>
                                    <li>Clicking "Save Changes"</li>
                                </ol>
                                <p>Your updated information will be visible to other users based on your privacy settings.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Posting FAQ -->
            <div class="faq-section mt-4" id="posting-faq">
                <h4 class="faq-category">Creating Posts</h4>
                
                <div class="accordion" id="postingAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#posting1">
                                How do I create a new post?
                            </button>
                        </h2>
                        <div id="posting1" class="accordion-collapse collapse" data-bs-parent="#postingAccordion">
                            <div class="accordion-body">
                                <p>To create a new post:</p>
                                <ol>
                                    <li>Click the "Create Post" button in the navigation</li>
                                    <li>Choose a category for your post</li>
                                    <li>Write an engaging title</li>
                                    <li>Add your content using the rich text editor</li>
                                    <li>Upload images or media if desired</li>
                                    <li>Add relevant tags</li>
                                    <li>Click "Publish" to make it live</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#posting2">
                                Can I edit my posts after publishing?
                            </button>
                        </h2>
                        <div id="posting2" class="accordion-collapse collapse" data-bs-parent="#postingAccordion">
                            <div class="accordion-body">
                                <p>Yes! You can edit your posts anytime:</p>
                                <ol>
                                    <li>Go to your post</li>
                                    <li>Click the "Edit" button (visible only to you)</li>
                                    <li>Make your changes</li>
                                    <li>Click "Update Post"</li>
                                </ol>
                                <p>Edited posts will show an "edited" indicator to other users.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security FAQ -->
            <div class="faq-section mt-4" id="security-faq">
                <h4 class="faq-category">Security & Privacy</h4>
                
                <div class="accordion" id="securityAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#security1">
                                How do I enable two-factor authentication?
                            </button>
                        </h2>
                        <div id="security1" class="accordion-collapse collapse" data-bs-parent="#securityAccordion">
                            <div class="accordion-body">
                                <p>Two-factor authentication adds an extra layer of security:</p>
                                <ol>
                                    <li>Go to Settings → Account & Security</li>
                                    <li>Toggle "Two-Factor Authentication" to enabled</li>
                                    <li>Download an authenticator app (Google Authenticator, Authy, etc.)</li>
                                    <li>Scan the QR code with your app</li>
                                    <li>Enter the verification code</li>
                                </ol>
                                <p>You'll now need both your password and a code from your phone to log in.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#security2">
                                How do I control who can see my posts?
                            </button>
                        </h2>
                        <div id="security2" class="accordion-collapse collapse" data-bs-parent="#securityAccordion">
                            <div class="accordion-body">
                                <p>You can control your privacy in Settings:</p>
                                <ol>
                                    <li>Go to Settings → Privacy</li>
                                    <li>Choose your profile visibility:
                                        <ul>
                                            <li><strong>Public:</strong> Anyone can see your posts</li>
                                            <li><strong>Friends Only:</strong> Only people you follow</li>
                                            <li><strong>Private:</strong> Only you can see your posts</li>
                                        </ul>
                                    </li>
                                    <li>Save your settings</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support Sidebar -->
        <div class="col-lg-4">
            <div class="support-sidebar">
                <div class="contact-card" id="contact">
                    <h4>Still need help?</h4>
                    <p>Can't find what you're looking for? Our support team is here to help.</p>
                    
                    <div class="contact-options">
                        <div class="contact-option">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-info">
                                <h6>Email Support</h6>
                                <p>Get help via email</p>
                                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#contactModal">
                                    Send Message
                                </button>
                            </div>
                        </div>
                        
                        <div class="contact-option">
                            <div class="contact-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="contact-info">
                                <h6>Live Chat</h6>
                                <p>Chat with our team</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="startLiveChat()">
                                    Start Chat
                                </button>
                            </div>
                        </div>
                        
                        <div class="contact-option">
                            <div class="contact-icon">
                                <i class="fab fa-twitter"></i>
                            </div>
                            <div class="contact-info">
                                <h6>Twitter Support</h6>
                                <p>@TrendySupport</p>
                                <a href="https://twitter.com/TrendySupport" class="btn btn-outline-primary btn-sm" target="_blank">
                                    Follow Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="status-card mt-4">
                    <h5>System Status</h5>
                    <div class="status-item">
                        <div class="status-indicator status-operational"></div>
                        <span>All systems operational</span>
                    </div>
                    <a href="#" class="status-link">View status page</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Contact Support</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="contactForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-control" name="name" value="{% if user.is_authenticated %}{{ user.get_full_name }}{% endif %}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="{% if user.is_authenticated %}{{ user.email }}{% endif %}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label class="form-label">Subject</label>
                        <select class="form-select" name="subject" required>
                            <option value="">Select a topic</option>
                            <option value="account">Account Issues</option>
                            <option value="technical">Technical Problems</option>
                            <option value="billing">Billing Questions</option>
                            <option value="feature">Feature Request</option>
                            <option value="bug">Bug Report</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label class="form-label">Priority</label>
                        <select class="form-select" name="priority">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label class="form-label">Message</label>
                        <textarea class="form-control" name="message" rows="5" placeholder="Please describe your issue or question in detail..." required></textarea>
                    </div>
                    
                    <div class="form-group mb-3">
                        <label class="form-label">Attachments (optional)</label>
                        <input type="file" class="form-control" name="attachments" multiple accept="image/*,.pdf,.doc,.docx">
                        <div class="form-text">You can attach screenshots or documents to help us understand your issue.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="contactForm" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>Send Message
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.support-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    margin-top: -80px;
}

.support-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.support-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.support-search {
    max-width: 500px;
    margin: 0 auto;
}

.support-search .form-control {
    border: none;
    padding: 15px 20px;
    font-size: 16px;
    border-radius: 50px 0 0 50px;
}

.support-search .btn {
    border-radius: 0 50px 50px 0;
    padding: 15px 25px;
}

.section-title {
    color: var(--text-color);
    margin-bottom: 2rem;
    font-weight: 600;
}

.help-card {
    background: white;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.help-card:hover {
    transform: translateY(-5px);
}

.help-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.5rem;
    color: white;
}

.help-card h5 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.help-card p {
    color: #6c757d;
    margin-bottom: 20px;
}

.help-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.help-link:hover {
    color: var(--secondary-color);
}

.faq-section {
    margin-bottom: 2rem;
}

.faq-category {
    color: var(--text-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.accordion-button {
    font-weight: 500;
}

.accordion-button:not(.collapsed) {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.support-sidebar {
    position: sticky;
    top: 100px;
}

.contact-card,
.status-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.contact-card h4 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.contact-option {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.contact-option:last-child {
    border-bottom: none;
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: rgba(79, 70, 229, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--primary-color);
}

.contact-info h6 {
    margin-bottom: 5px;
    color: var(--text-color);
}

.contact-info p {
    margin-bottom: 10px;
    color: #6c757d;
    font-size: 14px;
}

.status-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
}

.status-operational {
    background: #28a745;
}

.status-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
}

@media (max-width: 768px) {
    .support-title {
        font-size: 2rem;
    }
    
    .support-hero {
        padding: 60px 0;
    }
    
    .help-card {
        margin-bottom: 20px;
    }
}
</style>

<script>
// Search functionality
document.getElementById('supportSearch').addEventListener('input', function() {
    const query = this.value.toLowerCase();
    const faqItems = document.querySelectorAll('.accordion-item');
    
    faqItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(query)) {
            item.style.display = 'block';
        } else {
            item.style.display = query ? 'none' : 'block';
        }
    });
});

// Contact form submission
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show success message
    const modal = bootstrap.Modal.getInstance(document.getElementById('contactModal'));
    modal.hide();
    
    // Show success alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show';
    alert.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        <strong>Message sent!</strong> We'll get back to you within 24 hours.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container').insertBefore(alert, document.querySelector('.container').firstChild);
    
    // Reset form
    this.reset();
});

// Live chat function
function startLiveChat() {
    // This would integrate with your live chat service
    alert('Live chat feature coming soon! Please use email support for now.');
}
</script>
{% endblock %}
