from rest_framework import serializers
from .models import ReadingSession, ContentAnalytics
from blog.serializers import PostSerializer
from accounts.serializers import UserSerializer

class ReadingSessionSerializer(serializers.ModelSerializer):
    post = PostSerializer(read_only=True)
    user = UserSerializer(read_only=True)
    
    class Meta:
        model = ReadingSession
        fields = [
            'id', 'user', 'post', 'start_time', 'end_time',
            'progress_percentage', 'reading_speed_wpm', 'session_duration',
            'scroll_depth', 'is_completed', 'device_type'
        ]
        read_only_fields = ['id', 'start_time']

class ContentAnalyticsSerializer(serializers.ModelSerializer):
    post_title = serializers.CharField(source='post.title', read_only=True)
    post_slug = serializers.CharField(source='post.slug', read_only=True)
    
    class Meta:
        model = ContentAnalytics
        fields = [
            'id', 'post_title', 'post_slug', 'estimated_reading_time',
            'complexity_score', 'word_count', 'sentence_count', 'paragraph_count',
            'readability_score', 'reading_level', 'average_words_per_sentence',
            'average_syllables_per_word', 'total_reading_time', 'completion_rate',
            'average_session_duration', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class ReadingStatsSerializer(serializers.Serializer):
    """Serializer for user reading statistics"""
    total_sessions = serializers.IntegerField()
    completed_sessions = serializers.IntegerField()
    total_reading_time = serializers.IntegerField()
    average_reading_speed = serializers.FloatField()
    completion_rate = serializers.FloatField()
    recent_activity = ReadingSessionSerializer(many=True, read_only=True)
