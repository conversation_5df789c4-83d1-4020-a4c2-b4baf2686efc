[{"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add category", "content_type": 6, "codename": "add_category"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change category", "content_type": 6, "codename": "change_category"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete category", "content_type": 6, "codename": "delete_category"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view category", "content_type": 6, "codename": "view_category"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add hero image", "content_type": 7, "codename": "add_heroimage"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change hero image", "content_type": 7, "codename": "change_heroimage"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete hero image", "content_type": 7, "codename": "delete_heroimage"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view hero image", "content_type": 7, "codename": "view_heroimage"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add newsletter", "content_type": 8, "codename": "add_newsletter"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change newsletter", "content_type": 8, "codename": "change_newsletter"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete newsletter", "content_type": 8, "codename": "delete_newsletter"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view newsletter", "content_type": 8, "codename": "view_newsletter"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add tag", "content_type": 9, "codename": "add_tag"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change tag", "content_type": 9, "codename": "change_tag"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete tag", "content_type": 9, "codename": "delete_tag"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view tag", "content_type": 9, "codename": "view_tag"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add post", "content_type": 10, "codename": "add_post"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change post", "content_type": 10, "codename": "change_post"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete post", "content_type": 10, "codename": "delete_post"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view post", "content_type": 10, "codename": "view_post"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add post media", "content_type": 11, "codename": "add_postmedia"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change post media", "content_type": 11, "codename": "change_postmedia"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete post media", "content_type": 11, "codename": "delete_postmedia"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view post media", "content_type": 11, "codename": "view_postmedia"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add comment", "content_type": 12, "codename": "add_comment"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change comment", "content_type": 12, "codename": "change_comment"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete comment", "content_type": 12, "codename": "delete_comment"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view comment", "content_type": 12, "codename": "view_comment"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add User", "content_type": 13, "codename": "add_customuser"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change User", "content_type": 13, "codename": "change_customuser"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete User", "content_type": 13, "codename": "delete_customuser"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view User", "content_type": 13, "codename": "view_customuser"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add password reset token", "content_type": 14, "codename": "add_passwordresettoken"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change password reset token", "content_type": 14, "codename": "change_passwordresettoken"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete password reset token", "content_type": 14, "codename": "delete_passwordresettoken"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view password reset token", "content_type": 14, "codename": "view_passwordresettoken"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add user settings", "content_type": 15, "codename": "add_usersettings"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change user settings", "content_type": 15, "codename": "change_usersettings"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete user settings", "content_type": 15, "codename": "delete_usersettings"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view user settings", "content_type": 15, "codename": "view_usersettings"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add notification", "content_type": 16, "codename": "add_notification"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change notification", "content_type": 16, "codename": "change_notification"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete notification", "content_type": 16, "codename": "delete_notification"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view notification", "content_type": 16, "codename": "view_notification"}}, {"model": "auth.permission", "pk": 65, "fields": {"name": "Can add content analytics", "content_type": 17, "codename": "add_contentanalytics"}}, {"model": "auth.permission", "pk": 66, "fields": {"name": "Can change content analytics", "content_type": 17, "codename": "change_contentanalytics"}}, {"model": "auth.permission", "pk": 67, "fields": {"name": "Can delete content analytics", "content_type": 17, "codename": "delete_contentanalytics"}}, {"model": "auth.permission", "pk": 68, "fields": {"name": "Can view content analytics", "content_type": 17, "codename": "view_contentanalytics"}}, {"model": "auth.permission", "pk": 69, "fields": {"name": "Can add reading session", "content_type": 18, "codename": "add_readingsession"}}, {"model": "auth.permission", "pk": 70, "fields": {"name": "Can change reading session", "content_type": 18, "codename": "change_readingsession"}}, {"model": "auth.permission", "pk": 71, "fields": {"name": "Can delete reading session", "content_type": 18, "codename": "delete_readingsession"}}, {"model": "auth.permission", "pk": 72, "fields": {"name": "Can view reading session", "content_type": 18, "codename": "view_readingsession"}}, {"model": "auth.permission", "pk": 73, "fields": {"name": "Can add interactive block", "content_type": 19, "codename": "add_interactiveblock"}}, {"model": "auth.permission", "pk": 74, "fields": {"name": "Can change interactive block", "content_type": 19, "codename": "change_interactiveblock"}}, {"model": "auth.permission", "pk": 75, "fields": {"name": "Can delete interactive block", "content_type": 19, "codename": "delete_interactiveblock"}}, {"model": "auth.permission", "pk": 76, "fields": {"name": "Can view interactive block", "content_type": 19, "codename": "view_interactiveblock"}}, {"model": "auth.permission", "pk": 77, "fields": {"name": "Can add code playground", "content_type": 20, "codename": "add_codeplayground"}}, {"model": "auth.permission", "pk": 78, "fields": {"name": "Can change code playground", "content_type": 20, "codename": "change_codeplayground"}}, {"model": "auth.permission", "pk": 79, "fields": {"name": "Can delete code playground", "content_type": 20, "codename": "delete_codeplayground"}}, {"model": "auth.permission", "pk": 80, "fields": {"name": "Can view code playground", "content_type": 20, "codename": "view_codeplayground"}}, {"model": "auth.permission", "pk": 81, "fields": {"name": "Can add poll", "content_type": 21, "codename": "add_poll"}}, {"model": "auth.permission", "pk": 82, "fields": {"name": "Can change poll", "content_type": 21, "codename": "change_poll"}}, {"model": "auth.permission", "pk": 83, "fields": {"name": "Can delete poll", "content_type": 21, "codename": "delete_poll"}}, {"model": "auth.permission", "pk": 84, "fields": {"name": "Can view poll", "content_type": 21, "codename": "view_poll"}}, {"model": "auth.permission", "pk": 85, "fields": {"name": "Can add poll option", "content_type": 22, "codename": "add_polloption"}}, {"model": "auth.permission", "pk": 86, "fields": {"name": "Can change poll option", "content_type": 22, "codename": "change_polloption"}}, {"model": "auth.permission", "pk": 87, "fields": {"name": "Can delete poll option", "content_type": 22, "codename": "delete_polloption"}}, {"model": "auth.permission", "pk": 88, "fields": {"name": "Can view poll option", "content_type": 22, "codename": "view_polloption"}}, {"model": "auth.permission", "pk": 89, "fields": {"name": "Can add poll vote", "content_type": 23, "codename": "add_pollvote"}}, {"model": "auth.permission", "pk": 90, "fields": {"name": "Can change poll vote", "content_type": 23, "codename": "change_pollvote"}}, {"model": "auth.permission", "pk": 91, "fields": {"name": "Can delete poll vote", "content_type": 23, "codename": "delete_pollvote"}}, {"model": "auth.permission", "pk": 92, "fields": {"name": "Can view poll vote", "content_type": 23, "codename": "view_pollvote"}}, {"model": "auth.permission", "pk": 93, "fields": {"name": "Can add quiz", "content_type": 24, "codename": "add_quiz"}}, {"model": "auth.permission", "pk": 94, "fields": {"name": "Can change quiz", "content_type": 24, "codename": "change_quiz"}}, {"model": "auth.permission", "pk": 95, "fields": {"name": "Can delete quiz", "content_type": 24, "codename": "delete_quiz"}}, {"model": "auth.permission", "pk": 96, "fields": {"name": "Can view quiz", "content_type": 24, "codename": "view_quiz"}}, {"model": "auth.permission", "pk": 97, "fields": {"name": "Can add quiz attempt", "content_type": 25, "codename": "add_quizattempt"}}, {"model": "auth.permission", "pk": 98, "fields": {"name": "Can change quiz attempt", "content_type": 25, "codename": "change_quizattempt"}}, {"model": "auth.permission", "pk": 99, "fields": {"name": "Can delete quiz attempt", "content_type": 25, "codename": "delete_quizattempt"}}, {"model": "auth.permission", "pk": 100, "fields": {"name": "Can view quiz attempt", "content_type": 25, "codename": "view_quizattempt"}}, {"model": "auth.permission", "pk": 101, "fields": {"name": "Can add quiz question", "content_type": 26, "codename": "add_quizquestion"}}, {"model": "auth.permission", "pk": 102, "fields": {"name": "Can change quiz question", "content_type": 26, "codename": "change_quizquestion"}}, {"model": "auth.permission", "pk": 103, "fields": {"name": "Can delete quiz question", "content_type": 26, "codename": "delete_quizquestion"}}, {"model": "auth.permission", "pk": 104, "fields": {"name": "Can view quiz question", "content_type": 26, "codename": "view_quizquestion"}}, {"model": "auth.permission", "pk": 105, "fields": {"name": "Can add quiz answer", "content_type": 27, "codename": "add_quizanswer"}}, {"model": "auth.permission", "pk": 106, "fields": {"name": "Can change quiz answer", "content_type": 27, "codename": "change_quizanswer"}}, {"model": "auth.permission", "pk": 107, "fields": {"name": "Can delete quiz answer", "content_type": 27, "codename": "delete_quizanswer"}}, {"model": "auth.permission", "pk": 108, "fields": {"name": "Can view quiz answer", "content_type": 27, "codename": "view_quizanswer"}}, {"model": "auth.permission", "pk": 109, "fields": {"name": "Can add voice comment", "content_type": 28, "codename": "add_voicecomment"}}, {"model": "auth.permission", "pk": 110, "fields": {"name": "Can change voice comment", "content_type": 28, "codename": "change_voicecomment"}}, {"model": "auth.permission", "pk": 111, "fields": {"name": "Can delete voice comment", "content_type": 28, "codename": "delete_voicecomment"}}, {"model": "auth.permission", "pk": 112, "fields": {"name": "Can view voice comment", "content_type": 28, "codename": "view_voicecomment"}}, {"model": "auth.permission", "pk": 113, "fields": {"name": "Can add speech to text session", "content_type": 29, "codename": "add_speechtotextsession"}}, {"model": "auth.permission", "pk": 114, "fields": {"name": "Can change speech to text session", "content_type": 29, "codename": "change_speechtotextsession"}}, {"model": "auth.permission", "pk": 115, "fields": {"name": "Can delete speech to text session", "content_type": 29, "codename": "delete_speechtotextsession"}}, {"model": "auth.permission", "pk": 116, "fields": {"name": "Can view speech to text session", "content_type": 29, "codename": "view_speechtotextsession"}}, {"model": "auth.permission", "pk": 117, "fields": {"name": "Can add voice comment like", "content_type": 30, "codename": "add_voicecommentlike"}}, {"model": "auth.permission", "pk": 118, "fields": {"name": "Can change voice comment like", "content_type": 30, "codename": "change_voicecommentlike"}}, {"model": "auth.permission", "pk": 119, "fields": {"name": "Can delete voice comment like", "content_type": 30, "codename": "delete_voicecommentlike"}}, {"model": "auth.permission", "pk": 120, "fields": {"name": "Can view voice comment like", "content_type": 30, "codename": "view_voicecommentlike"}}, {"model": "auth.permission", "pk": 121, "fields": {"name": "Can add ai writing session", "content_type": 31, "codename": "add_aiwritingsession"}}, {"model": "auth.permission", "pk": 122, "fields": {"name": "Can change ai writing session", "content_type": 31, "codename": "change_aiwritingsession"}}, {"model": "auth.permission", "pk": 123, "fields": {"name": "Can delete ai writing session", "content_type": 31, "codename": "delete_aiwritingsession"}}, {"model": "auth.permission", "pk": 124, "fields": {"name": "Can view ai writing session", "content_type": 31, "codename": "view_aiwritingsession"}}, {"model": "auth.permission", "pk": 125, "fields": {"name": "Can add text to speech request", "content_type": 32, "codename": "add_texttospeechrequest"}}, {"model": "auth.permission", "pk": 126, "fields": {"name": "Can change text to speech request", "content_type": 32, "codename": "change_texttospeechrequest"}}, {"model": "auth.permission", "pk": 127, "fields": {"name": "Can delete text to speech request", "content_type": 32, "codename": "delete_texttospeechrequest"}}, {"model": "auth.permission", "pk": 128, "fields": {"name": "Can view text to speech request", "content_type": 32, "codename": "view_texttospeechrequest"}}, {"model": "auth.permission", "pk": 129, "fields": {"name": "Can add PayPal Settings", "content_type": 33, "codename": "add_paypalsettings"}}, {"model": "auth.permission", "pk": 130, "fields": {"name": "Can change PayPal Settings", "content_type": 33, "codename": "change_paypalsettings"}}, {"model": "auth.permission", "pk": 131, "fields": {"name": "Can delete PayPal Settings", "content_type": 33, "codename": "delete_paypalsettings"}}, {"model": "auth.permission", "pk": 132, "fields": {"name": "Can view PayPal Settings", "content_type": 33, "codename": "view_paypalsettings"}}, {"model": "auth.permission", "pk": 133, "fields": {"name": "Can add badge", "content_type": 34, "codename": "add_badge"}}, {"model": "auth.permission", "pk": 134, "fields": {"name": "Can change badge", "content_type": 34, "codename": "change_badge"}}, {"model": "auth.permission", "pk": 135, "fields": {"name": "Can delete badge", "content_type": 34, "codename": "delete_badge"}}, {"model": "auth.permission", "pk": 136, "fields": {"name": "Can view badge", "content_type": 34, "codename": "view_badge"}}, {"model": "auth.permission", "pk": 137, "fields": {"name": "Can add challenge", "content_type": 35, "codename": "add_challenge"}}, {"model": "auth.permission", "pk": 138, "fields": {"name": "Can change challenge", "content_type": 35, "codename": "change_challenge"}}, {"model": "auth.permission", "pk": 139, "fields": {"name": "Can delete challenge", "content_type": 35, "codename": "delete_challenge"}}, {"model": "auth.permission", "pk": 140, "fields": {"name": "Can view challenge", "content_type": 35, "codename": "view_challenge"}}, {"model": "auth.permission", "pk": 141, "fields": {"name": "Can add challenge participation", "content_type": 36, "codename": "add_challengeparticipation"}}, {"model": "auth.permission", "pk": 142, "fields": {"name": "Can change challenge participation", "content_type": 36, "codename": "change_challengeparticipation"}}, {"model": "auth.permission", "pk": 143, "fields": {"name": "Can delete challenge participation", "content_type": 36, "codename": "delete_challengeparticipation"}}, {"model": "auth.permission", "pk": 144, "fields": {"name": "Can view challenge participation", "content_type": 36, "codename": "view_challengeparticipation"}}, {"model": "auth.permission", "pk": 145, "fields": {"name": "Can add pay pal reward", "content_type": 37, "codename": "add_paypalreward"}}, {"model": "auth.permission", "pk": 146, "fields": {"name": "Can change pay pal reward", "content_type": 37, "codename": "change_paypalreward"}}, {"model": "auth.permission", "pk": 147, "fields": {"name": "Can delete pay pal reward", "content_type": 37, "codename": "delete_paypalreward"}}, {"model": "auth.permission", "pk": 148, "fields": {"name": "Can view pay pal reward", "content_type": 37, "codename": "view_paypalreward"}}, {"model": "auth.permission", "pk": 149, "fields": {"name": "Can add point transaction", "content_type": 38, "codename": "add_pointtransaction"}}, {"model": "auth.permission", "pk": 150, "fields": {"name": "Can change point transaction", "content_type": 38, "codename": "change_pointtransaction"}}, {"model": "auth.permission", "pk": 151, "fields": {"name": "Can delete point transaction", "content_type": 38, "codename": "delete_pointtransaction"}}, {"model": "auth.permission", "pk": 152, "fields": {"name": "Can view point transaction", "content_type": 38, "codename": "view_pointtransaction"}}, {"model": "auth.permission", "pk": 153, "fields": {"name": "Can add user badge", "content_type": 39, "codename": "add_userbadge"}}, {"model": "auth.permission", "pk": 154, "fields": {"name": "Can change user badge", "content_type": 39, "codename": "change_userbadge"}}, {"model": "auth.permission", "pk": 155, "fields": {"name": "Can delete user badge", "content_type": 39, "codename": "delete_userbadge"}}, {"model": "auth.permission", "pk": 156, "fields": {"name": "Can view user badge", "content_type": 39, "codename": "view_userbadge"}}, {"model": "auth.permission", "pk": 157, "fields": {"name": "Can add user level", "content_type": 40, "codename": "add_userlevel"}}, {"model": "auth.permission", "pk": 158, "fields": {"name": "Can change user level", "content_type": 40, "codename": "change_userlevel"}}, {"model": "auth.permission", "pk": 159, "fields": {"name": "Can delete user level", "content_type": 40, "codename": "delete_userlevel"}}, {"model": "auth.permission", "pk": 160, "fields": {"name": "Can view user level", "content_type": 40, "codename": "view_userlevel"}}, {"model": "auth.permission", "pk": 161, "fields": {"name": "Can add user pay pal reward", "content_type": 41, "codename": "add_userpaypalreward"}}, {"model": "auth.permission", "pk": 162, "fields": {"name": "Can change user pay pal reward", "content_type": 41, "codename": "change_userpaypalreward"}}, {"model": "auth.permission", "pk": 163, "fields": {"name": "Can delete user pay pal reward", "content_type": 41, "codename": "delete_userpaypalreward"}}, {"model": "auth.permission", "pk": 164, "fields": {"name": "Can view user pay pal reward", "content_type": 41, "codename": "view_userpaypalreward"}}, {"model": "auth.permission", "pk": 165, "fields": {"name": "Can add Monetization Settings", "content_type": 42, "codename": "add_monetizationsettings"}}, {"model": "auth.permission", "pk": 166, "fields": {"name": "Can change Monetization Settings", "content_type": 42, "codename": "change_monetizationsettings"}}, {"model": "auth.permission", "pk": 167, "fields": {"name": "Can delete Monetization Settings", "content_type": 42, "codename": "delete_monetizationsettings"}}, {"model": "auth.permission", "pk": 168, "fields": {"name": "Can view Monetization Settings", "content_type": 42, "codename": "view_monetizationsettings"}}, {"model": "auth.permission", "pk": 169, "fields": {"name": "Can add purchase transaction", "content_type": 43, "codename": "add_purchasetransaction"}}, {"model": "auth.permission", "pk": 170, "fields": {"name": "Can change purchase transaction", "content_type": 43, "codename": "change_purchasetransaction"}}, {"model": "auth.permission", "pk": 171, "fields": {"name": "Can delete purchase transaction", "content_type": 43, "codename": "delete_purchasetransaction"}}, {"model": "auth.permission", "pk": 172, "fields": {"name": "Can view purchase transaction", "content_type": 43, "codename": "view_purchasetransaction"}}, {"model": "auth.permission", "pk": 173, "fields": {"name": "Can add point boost purchase", "content_type": 44, "codename": "add_pointboostpurchase"}}, {"model": "auth.permission", "pk": 174, "fields": {"name": "Can change point boost purchase", "content_type": 44, "codename": "change_pointboostpurchase"}}, {"model": "auth.permission", "pk": 175, "fields": {"name": "Can delete point boost purchase", "content_type": 44, "codename": "delete_pointboostpurchase"}}, {"model": "auth.permission", "pk": 176, "fields": {"name": "Can view point boost purchase", "content_type": 44, "codename": "view_pointboostpurchase"}}, {"model": "auth.permission", "pk": 177, "fields": {"name": "Can add referral program", "content_type": 45, "codename": "add_referralprogram"}}, {"model": "auth.permission", "pk": 178, "fields": {"name": "Can change referral program", "content_type": 45, "codename": "change_referralprogram"}}, {"model": "auth.permission", "pk": 179, "fields": {"name": "Can delete referral program", "content_type": 45, "codename": "delete_referralprogram"}}, {"model": "auth.permission", "pk": 180, "fields": {"name": "Can view referral program", "content_type": 45, "codename": "view_referralprogram"}}, {"model": "auth.permission", "pk": 181, "fields": {"name": "Can add reward tier unlock", "content_type": 46, "codename": "add_reward<PERSON>unlock"}}, {"model": "auth.permission", "pk": 182, "fields": {"name": "Can change reward tier unlock", "content_type": 46, "codename": "change_reward<PERSON><PERSON>lock"}}, {"model": "auth.permission", "pk": 183, "fields": {"name": "Can delete reward tier unlock", "content_type": 46, "codename": "delete_reward<PERSON>unlock"}}, {"model": "auth.permission", "pk": 184, "fields": {"name": "Can view reward tier unlock", "content_type": 46, "codename": "view_reward<PERSON><PERSON>lock"}}, {"model": "auth.permission", "pk": 185, "fields": {"name": "Can add streak protection", "content_type": 47, "codename": "add_streakprotection"}}, {"model": "auth.permission", "pk": 186, "fields": {"name": "Can change streak protection", "content_type": 47, "codename": "change_streakprotection"}}, {"model": "auth.permission", "pk": 187, "fields": {"name": "Can delete streak protection", "content_type": 47, "codename": "delete_streakprotection"}}, {"model": "auth.permission", "pk": 188, "fields": {"name": "Can view streak protection", "content_type": 47, "codename": "view_streakprotection"}}, {"model": "auth.permission", "pk": 189, "fields": {"name": "Can add virtual item", "content_type": 48, "codename": "add_virtualitem"}}, {"model": "auth.permission", "pk": 190, "fields": {"name": "Can change virtual item", "content_type": 48, "codename": "change_virtualitem"}}, {"model": "auth.permission", "pk": 191, "fields": {"name": "Can delete virtual item", "content_type": 48, "codename": "delete_virtualitem"}}, {"model": "auth.permission", "pk": 192, "fields": {"name": "Can view virtual item", "content_type": 48, "codename": "view_virtualitem"}}, {"model": "auth.permission", "pk": 193, "fields": {"name": "Can add user virtual item", "content_type": 49, "codename": "add_uservirtualitem"}}, {"model": "auth.permission", "pk": 194, "fields": {"name": "Can change user virtual item", "content_type": 49, "codename": "change_uservirtualitem"}}, {"model": "auth.permission", "pk": 195, "fields": {"name": "Can delete user virtual item", "content_type": 49, "codename": "delete_uservirtualitem"}}, {"model": "auth.permission", "pk": 196, "fields": {"name": "Can view user virtual item", "content_type": 49, "codename": "view_uservirtualitem"}}, {"model": "auth.permission", "pk": 197, "fields": {"name": "Can add premium subscription", "content_type": 50, "codename": "add_premiumsubscription"}}, {"model": "auth.permission", "pk": 198, "fields": {"name": "Can change premium subscription", "content_type": 50, "codename": "change_premiumsubscription"}}, {"model": "auth.permission", "pk": 199, "fields": {"name": "Can delete premium subscription", "content_type": 50, "codename": "delete_premiumsubscription"}}, {"model": "auth.permission", "pk": 200, "fields": {"name": "Can view premium subscription", "content_type": 50, "codename": "view_premiumsubscription"}}, {"model": "auth.permission", "pk": 201, "fields": {"name": "Can add Payment Settings", "content_type": 51, "codename": "add_paymentsettings"}}, {"model": "auth.permission", "pk": 202, "fields": {"name": "Can change Payment Settings", "content_type": 51, "codename": "change_paymentsettings"}}, {"model": "auth.permission", "pk": 203, "fields": {"name": "Can delete Payment Settings", "content_type": 51, "codename": "delete_paymentsettings"}}, {"model": "auth.permission", "pk": 204, "fields": {"name": "Can view Payment Settings", "content_type": 51, "codename": "view_paymentsettings"}}, {"model": "auth.permission", "pk": 205, "fields": {"name": "Can add PayPal Account", "content_type": 52, "codename": "add_paypalaccount"}}, {"model": "auth.permission", "pk": 206, "fields": {"name": "Can change PayPal Account", "content_type": 52, "codename": "change_paypalaccount"}}, {"model": "auth.permission", "pk": 207, "fields": {"name": "Can delete PayPal Account", "content_type": 52, "codename": "delete_paypalaccount"}}, {"model": "auth.permission", "pk": 208, "fields": {"name": "Can view PayPal Account", "content_type": 52, "codename": "view_paypalaccount"}}, {"model": "auth.permission", "pk": 209, "fields": {"name": "Can add payment transaction", "content_type": 53, "codename": "add_paymenttransaction"}}, {"model": "auth.permission", "pk": 210, "fields": {"name": "Can change payment transaction", "content_type": 53, "codename": "change_paymenttransaction"}}, {"model": "auth.permission", "pk": 211, "fields": {"name": "Can delete payment transaction", "content_type": 53, "codename": "delete_paymenttransaction"}}, {"model": "auth.permission", "pk": 212, "fields": {"name": "Can view payment transaction", "content_type": 53, "codename": "view_paymenttransaction"}}, {"model": "auth.permission", "pk": 213, "fields": {"name": "Can add pay pal payout batch", "content_type": 54, "codename": "add_paypalpayoutbatch"}}, {"model": "auth.permission", "pk": 214, "fields": {"name": "Can change pay pal payout batch", "content_type": 54, "codename": "change_paypalpayoutbatch"}}, {"model": "auth.permission", "pk": 215, "fields": {"name": "Can delete pay pal payout batch", "content_type": 54, "codename": "delete_paypalpayoutbatch"}}, {"model": "auth.permission", "pk": 216, "fields": {"name": "Can view pay pal payout batch", "content_type": 54, "codename": "view_paypalpayoutbatch"}}, {"model": "auth.permission", "pk": 217, "fields": {"name": "Can add pay pal webhook", "content_type": 55, "codename": "add_paypalwebhook"}}, {"model": "auth.permission", "pk": 218, "fields": {"name": "Can change pay pal webhook", "content_type": 55, "codename": "change_paypalwebhook"}}, {"model": "auth.permission", "pk": 219, "fields": {"name": "Can delete pay pal webhook", "content_type": 55, "codename": "delete_paypalwebhook"}}, {"model": "auth.permission", "pk": 220, "fields": {"name": "Can view pay pal webhook", "content_type": 55, "codename": "view_paypalwebhook"}}, {"model": "auth.permission", "pk": 221, "fields": {"name": "Can add user pay pal profile", "content_type": 56, "codename": "add_userpaypalprofile"}}, {"model": "auth.permission", "pk": 222, "fields": {"name": "Can change user pay pal profile", "content_type": 56, "codename": "change_userpaypalprofile"}}, {"model": "auth.permission", "pk": 223, "fields": {"name": "Can delete user pay pal profile", "content_type": 56, "codename": "delete_userpaypalprofile"}}, {"model": "auth.permission", "pk": 224, "fields": {"name": "Can view user pay pal profile", "content_type": 56, "codename": "view_userpaypalprofile"}}, {"model": "auth.permission", "pk": 225, "fields": {"name": "Can add Wallet Settings", "content_type": 57, "codename": "add_walletsettings"}}, {"model": "auth.permission", "pk": 226, "fields": {"name": "Can change Wallet Settings", "content_type": 57, "codename": "change_walletsettings"}}, {"model": "auth.permission", "pk": 227, "fields": {"name": "Can delete Wallet Settings", "content_type": 57, "codename": "delete_walletsettings"}}, {"model": "auth.permission", "pk": 228, "fields": {"name": "Can view Wallet Settings", "content_type": 57, "codename": "view_walletsettings"}}, {"model": "auth.permission", "pk": 229, "fields": {"name": "Can add user wallet", "content_type": 58, "codename": "add_userwallet"}}, {"model": "auth.permission", "pk": 230, "fields": {"name": "Can change user wallet", "content_type": 58, "codename": "change_userwallet"}}, {"model": "auth.permission", "pk": 231, "fields": {"name": "Can delete user wallet", "content_type": 58, "codename": "delete_userwallet"}}, {"model": "auth.permission", "pk": 232, "fields": {"name": "Can view user wallet", "content_type": 58, "codename": "view_userwallet"}}, {"model": "auth.permission", "pk": 233, "fields": {"name": "Can add wallet deposit request", "content_type": 59, "codename": "add_walletdepositrequest"}}, {"model": "auth.permission", "pk": 234, "fields": {"name": "Can change wallet deposit request", "content_type": 59, "codename": "change_walletdepositrequest"}}, {"model": "auth.permission", "pk": 235, "fields": {"name": "Can delete wallet deposit request", "content_type": 59, "codename": "delete_walletdepositrequest"}}, {"model": "auth.permission", "pk": 236, "fields": {"name": "Can view wallet deposit request", "content_type": 59, "codename": "view_walletdepositrequest"}}, {"model": "auth.permission", "pk": 237, "fields": {"name": "Can add wallet transaction", "content_type": 60, "codename": "add_wallettransaction"}}, {"model": "auth.permission", "pk": 238, "fields": {"name": "Can change wallet transaction", "content_type": 60, "codename": "change_wallettransaction"}}, {"model": "auth.permission", "pk": 239, "fields": {"name": "Can delete wallet transaction", "content_type": 60, "codename": "delete_wallettransaction"}}, {"model": "auth.permission", "pk": 240, "fields": {"name": "Can view wallet transaction", "content_type": 60, "codename": "view_wallettransaction"}}, {"model": "auth.permission", "pk": 241, "fields": {"name": "Can add wallet withdrawal request", "content_type": 61, "codename": "add_walletwithdrawalrequest"}}, {"model": "auth.permission", "pk": 242, "fields": {"name": "Can change wallet withdrawal request", "content_type": 61, "codename": "change_walletwithdrawalrequest"}}, {"model": "auth.permission", "pk": 243, "fields": {"name": "Can delete wallet withdrawal request", "content_type": 61, "codename": "delete_walletwithdrawalrequest"}}, {"model": "auth.permission", "pk": 244, "fields": {"name": "Can view wallet withdrawal request", "content_type": 61, "codename": "view_walletwithdrawalrequest"}}, {"model": "auth.permission", "pk": 245, "fields": {"name": "Can add bookmark collection", "content_type": 62, "codename": "add_bookmarkcollection"}}, {"model": "auth.permission", "pk": 246, "fields": {"name": "Can change bookmark collection", "content_type": 62, "codename": "change_bookmarkcollection"}}, {"model": "auth.permission", "pk": 247, "fields": {"name": "Can delete bookmark collection", "content_type": 62, "codename": "delete_bookmarkcollection"}}, {"model": "auth.permission", "pk": 248, "fields": {"name": "Can view bookmark collection", "content_type": 62, "codename": "view_bookmarkcollection"}}, {"model": "auth.permission", "pk": 249, "fields": {"name": "Can add reading list", "content_type": 63, "codename": "add_readinglist"}}, {"model": "auth.permission", "pk": 250, "fields": {"name": "Can change reading list", "content_type": 63, "codename": "change_readinglist"}}, {"model": "auth.permission", "pk": 251, "fields": {"name": "Can delete reading list", "content_type": 63, "codename": "delete_readinglist"}}, {"model": "auth.permission", "pk": 252, "fields": {"name": "Can view reading list", "content_type": 63, "codename": "view_readinglist"}}, {"model": "auth.permission", "pk": 253, "fields": {"name": "Can add reading list item", "content_type": 64, "codename": "add_readinglistitem"}}, {"model": "auth.permission", "pk": 254, "fields": {"name": "Can change reading list item", "content_type": 64, "codename": "change_readinglistitem"}}, {"model": "auth.permission", "pk": 255, "fields": {"name": "Can delete reading list item", "content_type": 64, "codename": "delete_readinglistitem"}}, {"model": "auth.permission", "pk": 256, "fields": {"name": "Can view reading list item", "content_type": 64, "codename": "view_readinglistitem"}}, {"model": "auth.permission", "pk": 257, "fields": {"name": "Can add bookmark", "content_type": 65, "codename": "add_bookmark"}}, {"model": "auth.permission", "pk": 258, "fields": {"name": "Can change bookmark", "content_type": 65, "codename": "change_bookmark"}}, {"model": "auth.permission", "pk": 259, "fields": {"name": "Can delete bookmark", "content_type": 65, "codename": "delete_bookmark"}}, {"model": "auth.permission", "pk": 260, "fields": {"name": "Can view bookmark", "content_type": 65, "codename": "view_bookmark"}}, {"model": "auth.permission", "pk": 261, "fields": {"name": "Can add follow", "content_type": 66, "codename": "add_follow"}}, {"model": "auth.permission", "pk": 262, "fields": {"name": "Can change follow", "content_type": 66, "codename": "change_follow"}}, {"model": "auth.permission", "pk": 263, "fields": {"name": "Can delete follow", "content_type": 66, "codename": "delete_follow"}}, {"model": "auth.permission", "pk": 264, "fields": {"name": "Can view follow", "content_type": 66, "codename": "view_follow"}}, {"model": "auth.permission", "pk": 265, "fields": {"name": "Can add notification", "content_type": 67, "codename": "add_notification"}}, {"model": "auth.permission", "pk": 266, "fields": {"name": "Can change notification", "content_type": 67, "codename": "change_notification"}}, {"model": "auth.permission", "pk": 267, "fields": {"name": "Can delete notification", "content_type": 67, "codename": "delete_notification"}}, {"model": "auth.permission", "pk": 268, "fields": {"name": "Can view notification", "content_type": 67, "codename": "view_notification"}}, {"model": "auth.permission", "pk": 269, "fields": {"name": "Can add report", "content_type": 68, "codename": "add_report"}}, {"model": "auth.permission", "pk": 270, "fields": {"name": "Can change report", "content_type": 68, "codename": "change_report"}}, {"model": "auth.permission", "pk": 271, "fields": {"name": "Can delete report", "content_type": 68, "codename": "delete_report"}}, {"model": "auth.permission", "pk": 272, "fields": {"name": "Can view report", "content_type": 68, "codename": "view_report"}}, {"model": "auth.permission", "pk": 273, "fields": {"name": "Can add user activity", "content_type": 69, "codename": "add_useractivity"}}, {"model": "auth.permission", "pk": 274, "fields": {"name": "Can change user activity", "content_type": 69, "codename": "change_useractivity"}}, {"model": "auth.permission", "pk": 275, "fields": {"name": "Can delete user activity", "content_type": 69, "codename": "delete_useractivity"}}, {"model": "auth.permission", "pk": 276, "fields": {"name": "Can view user activity", "content_type": 69, "codename": "view_useractivity"}}, {"model": "auth.permission", "pk": 277, "fields": {"name": "Can add user profile", "content_type": 70, "codename": "add_userprofile"}}, {"model": "auth.permission", "pk": 278, "fields": {"name": "Can change user profile", "content_type": 70, "codename": "change_userprofile"}}, {"model": "auth.permission", "pk": 279, "fields": {"name": "Can delete user profile", "content_type": 70, "codename": "delete_userprofile"}}, {"model": "auth.permission", "pk": 280, "fields": {"name": "Can view user profile", "content_type": 70, "codename": "view_userprofile"}}, {"model": "auth.permission", "pk": 281, "fields": {"name": "Can add Token", "content_type": 71, "codename": "add_token"}}, {"model": "auth.permission", "pk": 282, "fields": {"name": "Can change Token", "content_type": 71, "codename": "change_token"}}, {"model": "auth.permission", "pk": 283, "fields": {"name": "Can delete Token", "content_type": 71, "codename": "delete_token"}}, {"model": "auth.permission", "pk": 284, "fields": {"name": "Can view Token", "content_type": 71, "codename": "view_token"}}, {"model": "auth.permission", "pk": 285, "fields": {"name": "Can add Token", "content_type": 72, "codename": "add_tokenproxy"}}, {"model": "auth.permission", "pk": 286, "fields": {"name": "Can change Token", "content_type": 72, "codename": "change_tokenproxy"}}, {"model": "auth.permission", "pk": 287, "fields": {"name": "Can delete Token", "content_type": 72, "codename": "delete_tokenproxy"}}, {"model": "auth.permission", "pk": 288, "fields": {"name": "Can view Token", "content_type": 72, "codename": "view_tokenproxy"}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "blog", "model": "category"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "blog", "model": "heroimage"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "blog", "model": "newsletter"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "blog", "model": "tag"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "blog", "model": "post"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "blog", "model": "postmedia"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "blog", "model": "comment"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "accounts", "model": "customuser"}}, {"model": "contenttypes.contenttype", "pk": 14, "fields": {"app_label": "accounts", "model": "passwordresettoken"}}, {"model": "contenttypes.contenttype", "pk": 15, "fields": {"app_label": "accounts", "model": "usersettings"}}, {"model": "contenttypes.contenttype", "pk": 16, "fields": {"app_label": "accounts", "model": "notification"}}, {"model": "contenttypes.contenttype", "pk": 17, "fields": {"app_label": "analytics", "model": "contentanalytics"}}, {"model": "contenttypes.contenttype", "pk": 18, "fields": {"app_label": "analytics", "model": "readingsession"}}, {"model": "contenttypes.contenttype", "pk": 19, "fields": {"app_label": "interactive", "model": "interactiveblock"}}, {"model": "contenttypes.contenttype", "pk": 20, "fields": {"app_label": "interactive", "model": "codeplayground"}}, {"model": "contenttypes.contenttype", "pk": 21, "fields": {"app_label": "interactive", "model": "poll"}}, {"model": "contenttypes.contenttype", "pk": 22, "fields": {"app_label": "interactive", "model": "polloption"}}, {"model": "contenttypes.contenttype", "pk": 23, "fields": {"app_label": "interactive", "model": "pollvote"}}, {"model": "contenttypes.contenttype", "pk": 24, "fields": {"app_label": "interactive", "model": "quiz"}}, {"model": "contenttypes.contenttype", "pk": 25, "fields": {"app_label": "interactive", "model": "quizattempt"}}, {"model": "contenttypes.contenttype", "pk": 26, "fields": {"app_label": "interactive", "model": "quizquestion"}}, {"model": "contenttypes.contenttype", "pk": 27, "fields": {"app_label": "interactive", "model": "quizanswer"}}, {"model": "contenttypes.contenttype", "pk": 28, "fields": {"app_label": "voice_features", "model": "voicecomment"}}, {"model": "contenttypes.contenttype", "pk": 29, "fields": {"app_label": "voice_features", "model": "speechtotextsession"}}, {"model": "contenttypes.contenttype", "pk": 30, "fields": {"app_label": "voice_features", "model": "voicecommentlike"}}, {"model": "contenttypes.contenttype", "pk": 31, "fields": {"app_label": "voice_features", "model": "aiwritingsession"}}, {"model": "contenttypes.contenttype", "pk": 32, "fields": {"app_label": "voice_features", "model": "texttospeechrequest"}}, {"model": "contenttypes.contenttype", "pk": 33, "fields": {"app_label": "gamification", "model": "paypalsettings"}}, {"model": "contenttypes.contenttype", "pk": 34, "fields": {"app_label": "gamification", "model": "badge"}}, {"model": "contenttypes.contenttype", "pk": 35, "fields": {"app_label": "gamification", "model": "challenge"}}, {"model": "contenttypes.contenttype", "pk": 36, "fields": {"app_label": "gamification", "model": "challengeparticipation"}}, {"model": "contenttypes.contenttype", "pk": 37, "fields": {"app_label": "gamification", "model": "paypalreward"}}, {"model": "contenttypes.contenttype", "pk": 38, "fields": {"app_label": "gamification", "model": "pointtransaction"}}, {"model": "contenttypes.contenttype", "pk": 39, "fields": {"app_label": "gamification", "model": "userbadge"}}, {"model": "contenttypes.contenttype", "pk": 40, "fields": {"app_label": "gamification", "model": "userlevel"}}, {"model": "contenttypes.contenttype", "pk": 41, "fields": {"app_label": "gamification", "model": "userpaypalreward"}}, {"model": "contenttypes.contenttype", "pk": 42, "fields": {"app_label": "monetization", "model": "monetizationsettings"}}, {"model": "contenttypes.contenttype", "pk": 43, "fields": {"app_label": "monetization", "model": "purchasetransaction"}}, {"model": "contenttypes.contenttype", "pk": 44, "fields": {"app_label": "monetization", "model": "pointboostpurchase"}}, {"model": "contenttypes.contenttype", "pk": 45, "fields": {"app_label": "monetization", "model": "referralprogram"}}, {"model": "contenttypes.contenttype", "pk": 46, "fields": {"app_label": "monetization", "model": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "contenttypes.contenttype", "pk": 47, "fields": {"app_label": "monetization", "model": "streakprotection"}}, {"model": "contenttypes.contenttype", "pk": 48, "fields": {"app_label": "monetization", "model": "virtualitem"}}, {"model": "contenttypes.contenttype", "pk": 49, "fields": {"app_label": "monetization", "model": "uservirtualitem"}}, {"model": "contenttypes.contenttype", "pk": 50, "fields": {"app_label": "monetization", "model": "premiumsubscription"}}, {"model": "contenttypes.contenttype", "pk": 51, "fields": {"app_label": "payments", "model": "paymentsettings"}}, {"model": "contenttypes.contenttype", "pk": 52, "fields": {"app_label": "payments", "model": "paypalaccount"}}, {"model": "contenttypes.contenttype", "pk": 53, "fields": {"app_label": "payments", "model": "paymenttransaction"}}, {"model": "contenttypes.contenttype", "pk": 54, "fields": {"app_label": "payments", "model": "paypalpayoutbatch"}}, {"model": "contenttypes.contenttype", "pk": 55, "fields": {"app_label": "payments", "model": "paypalwebhook"}}, {"model": "contenttypes.contenttype", "pk": 56, "fields": {"app_label": "payments", "model": "userpaypalprofile"}}, {"model": "contenttypes.contenttype", "pk": 57, "fields": {"app_label": "wallet", "model": "walletsettings"}}, {"model": "contenttypes.contenttype", "pk": 58, "fields": {"app_label": "wallet", "model": "userwallet"}}, {"model": "contenttypes.contenttype", "pk": 59, "fields": {"app_label": "wallet", "model": "walletdepositrequest"}}, {"model": "contenttypes.contenttype", "pk": 60, "fields": {"app_label": "wallet", "model": "wallettransaction"}}, {"model": "contenttypes.contenttype", "pk": 61, "fields": {"app_label": "wallet", "model": "walletwithdrawalrequest"}}, {"model": "contenttypes.contenttype", "pk": 62, "fields": {"app_label": "social", "model": "bookmarkcollection"}}, {"model": "contenttypes.contenttype", "pk": 63, "fields": {"app_label": "social", "model": "readinglist"}}, {"model": "contenttypes.contenttype", "pk": 64, "fields": {"app_label": "social", "model": "readinglistitem"}}, {"model": "contenttypes.contenttype", "pk": 65, "fields": {"app_label": "social", "model": "bookmark"}}, {"model": "contenttypes.contenttype", "pk": 66, "fields": {"app_label": "social", "model": "follow"}}, {"model": "contenttypes.contenttype", "pk": 67, "fields": {"app_label": "social", "model": "notification"}}, {"model": "contenttypes.contenttype", "pk": 68, "fields": {"app_label": "social", "model": "report"}}, {"model": "contenttypes.contenttype", "pk": 69, "fields": {"app_label": "social", "model": "useractivity"}}, {"model": "contenttypes.contenttype", "pk": 70, "fields": {"app_label": "social", "model": "userprofile"}}, {"model": "contenttypes.contenttype", "pk": 71, "fields": {"app_label": "authtoken", "model": "token"}}, {"model": "contenttypes.contenttype", "pk": 72, "fields": {"app_label": "authtoken", "model": "tokenproxy"}}, {"model": "blog.category", "pk": 1, "fields": {"name": "Technology", "slug": "technology", "description": "Latest tech trends and innovations", "created_at": "2025-06-23T13:32:06.145Z", "updated_at": "2025-06-23T13:32:06.145Z"}}, {"model": "blog.category", "pk": 2, "fields": {"name": "Lifestyle", "slug": "lifestyle", "description": "Tips for better living", "created_at": "2025-06-23T13:32:06.151Z", "updated_at": "2025-06-23T13:32:06.151Z"}}, {"model": "blog.category", "pk": 3, "fields": {"name": "Business", "slug": "business", "description": "Business insights and strategies", "created_at": "2025-06-23T13:32:06.161Z", "updated_at": "2025-06-23T13:32:06.161Z"}}, {"model": "blog.category", "pk": 4, "fields": {"name": "Health", "slug": "health", "description": "Health and wellness tips", "created_at": "2025-06-23T13:32:06.165Z", "updated_at": "2025-06-23T13:32:06.165Z"}}, {"model": "blog.category", "pk": 5, "fields": {"name": "Travel", "slug": "travel", "description": "Travel guides and experiences", "created_at": "2025-06-23T13:32:06.171Z", "updated_at": "2025-06-23T13:32:06.171Z"}}, {"model": "blog.post", "pk": 1, "fields": {"title": "Welcome to Trendy App", "slug": "welcome-to-trendy-app", "content": "Welcome to our amazing social platform! Here you can share posts, earn points, and connect with others. Explore all the features and start your journey today!", "author": 1, "category": 1, "created_at": "2025-06-23T13:32:06.185Z", "updated_at": "2025-06-23T13:32:06.185Z", "views": 0, "is_featured": false, "sanitized_content": "", "is_sanitized": false, "status": "published", "reference": [], "tags": [], "likes": []}}, {"model": "blog.post", "pk": 2, "fields": {"title": "How to Earn Points", "slug": "how-to-earn-points", "content": "Earn points by reading posts, commenting, and engaging with the community. Use points to unlock rewards and boost your profile!", "author": 1, "category": 2, "created_at": "2025-06-23T13:32:06.931Z", "updated_at": "2025-06-23T13:32:06.931Z", "views": 0, "is_featured": false, "sanitized_content": "", "is_sanitized": false, "status": "published", "reference": [], "tags": [], "likes": []}}, {"model": "blog.post", "pk": 3, "fields": {"title": "Wallet Features Guide", "slug": "wallet-features-guide", "content": "Your wallet allows you to store money, make purchases, and receive rewards. Keep your balance topped up for premium features!", "author": 1, "category": 1, "created_at": "2025-06-23T13:32:06.943Z", "updated_at": "2025-06-23T13:32:06.943Z", "views": 0, "is_featured": false, "sanitized_content": "", "is_sanitized": false, "status": "published", "reference": [], "tags": [], "likes": []}}, {"model": "blog.post", "pk": 4, "fields": {"title": "Premium Subscription Benefits", "slug": "premium-subscription-benefits", "content": "Upgrade to premium for exclusive features, 2x point multiplier, and priority support. Only $9.99/month!", "author": 1, "category": 2, "created_at": "2025-06-23T13:32:06.955Z", "updated_at": "2025-06-23T13:32:06.955Z", "views": 0, "is_featured": false, "sanitized_content": "", "is_sanitized": false, "status": "published", "reference": [], "tags": [], "likes": []}}, {"model": "accounts.customuser", "pk": 1, "fields": {"password": "pbkdf2_sha256$1000000$nhjRUEaAjgCoZNwvnGHlW5$U2FB9jMwZDKlcXD5PWHsoJYEyD0K2AUn8NptS0QJnB0=", "last_login": null, "is_superuser": true, "username": "admin", "first_name": "Admin", "last_name": "User", "is_staff": true, "is_active": true, "date_joined": "2025-06-23T13:29:03.016Z", "email": "<EMAIL>", "bio": "", "avatar": "", "phone_number": "", "date_of_birth": null, "location": "", "website": "", "is_email_verified": true, "email_verification_token": "f73c80ee-02ba-490e-af5a-d95aa88fa08e", "email_verification_sent_at": null, "receive_email_notifications": true, "receive_push_notifications": true, "is_profile_public": true, "twitter_url": "", "linkedin_url": "", "github_url": "", "updated_at": "2025-06-23T13:29:03.018Z", "groups": [], "user_permissions": []}}, {"model": "accounts.customuser", "pk": 2, "fields": {"password": "pbkdf2_sha256$1000000$PR4bu1SeGhpYkhsENqJcaU$Hyq0gPD68TycaumVt+W5vIoN4OJC5DxbsqaAjA8HluY=", "last_login": null, "is_superuser": false, "username": "sarah_johnson", "first_name": "<PERSON>", "last_name": "<PERSON>", "is_staff": false, "is_active": true, "date_joined": "2025-06-23T13:30:54.312Z", "email": "<EMAIL>", "bio": "", "avatar": "", "phone_number": "", "date_of_birth": null, "location": "", "website": "", "is_email_verified": false, "email_verification_token": "879da548-5498-4288-b185-f14821e42eac", "email_verification_sent_at": null, "receive_email_notifications": true, "receive_push_notifications": true, "is_profile_public": true, "twitter_url": "", "linkedin_url": "", "github_url": "", "updated_at": "2025-06-23T13:30:54.315Z", "groups": [], "user_permissions": []}}, {"model": "accounts.customuser", "pk": 3, "fields": {"password": "pbkdf2_sha256$1000000$e1C994gs8sZWtYs1Fu8x9B$fvGSk6fRGJbGkcX7/BGGT+onMtamxcSMhELA+Fty8Ig=", "last_login": null, "is_superuser": false, "username": "mike_chen", "first_name": "<PERSON>", "last_name": "<PERSON>", "is_staff": false, "is_active": true, "date_joined": "2025-06-23T13:30:54.946Z", "email": "<EMAIL>", "bio": "", "avatar": "", "phone_number": "", "date_of_birth": null, "location": "", "website": "", "is_email_verified": false, "email_verification_token": "65f77a1b-f825-4bfe-9103-5375814a99f9", "email_verification_sent_at": null, "receive_email_notifications": true, "receive_push_notifications": true, "is_profile_public": true, "twitter_url": "", "linkedin_url": "", "github_url": "", "updated_at": "2025-06-23T13:30:54.946Z", "groups": [], "user_permissions": []}}, {"model": "accounts.customuser", "pk": 4, "fields": {"password": "pbkdf2_sha256$1000000$7iu2569c6rAeWnDqpgZ0zS$E577K1/LbDBUugXLpo7g99mxgkgapqDYKe04F9I6mQ4=", "last_login": null, "is_superuser": false, "username": "alex_rivera", "first_name": "<PERSON>", "last_name": "<PERSON>", "is_staff": false, "is_active": true, "date_joined": "2025-06-23T13:30:55.578Z", "email": "<EMAIL>", "bio": "", "avatar": "", "phone_number": "", "date_of_birth": null, "location": "", "website": "", "is_email_verified": false, "email_verification_token": "ec7877cf-1564-4e98-9456-fe8bb5bf989d", "email_verification_sent_at": null, "receive_email_notifications": true, "receive_push_notifications": true, "is_profile_public": true, "twitter_url": "", "linkedin_url": "", "github_url": "", "updated_at": "2025-06-23T13:30:55.579Z", "groups": [], "user_permissions": []}}, {"model": "accounts.customuser", "pk": 5, "fields": {"password": "pbkdf2_sha256$1000000$P1MElbzWnyV4robDHGtrk0$sjHcKyHH2zb95+TzV/bBHIhn6rtYnWuuLDFKtqIRcLA=", "last_login": null, "is_superuser": false, "username": "testuser", "first_name": "Test", "last_name": "User", "is_staff": false, "is_active": true, "date_joined": "2025-06-23T13:30:56.321Z", "email": "<EMAIL>", "bio": "", "avatar": "", "phone_number": "", "date_of_birth": null, "location": "", "website": "", "is_email_verified": false, "email_verification_token": "34ee996d-2eff-4e60-bdec-608f9c6879ba", "email_verification_sent_at": null, "receive_email_notifications": true, "receive_push_notifications": true, "is_profile_public": true, "twitter_url": "", "linkedin_url": "", "github_url": "", "updated_at": "2025-06-23T13:30:56.322Z", "groups": [], "user_permissions": []}}, {"model": "accounts.usersettings", "pk": 1, "fields": {"user": 1, "email_notifications": true, "push_notifications": true, "comment_notifications": true, "like_notifications": true, "follow_notifications": true, "profile_visibility": "public", "show_email": false, "show_phone": false, "content_language": "en", "posts_per_page": 10, "auto_play_videos": true, "theme": "auto", "created_at": "2025-06-23T13:29:03.043Z", "updated_at": "2025-06-23T13:29:03.043Z"}}, {"model": "accounts.usersettings", "pk": 2, "fields": {"user": 2, "email_notifications": true, "push_notifications": true, "comment_notifications": true, "like_notifications": true, "follow_notifications": true, "profile_visibility": "public", "show_email": false, "show_phone": false, "content_language": "en", "posts_per_page": 10, "auto_play_videos": true, "theme": "auto", "created_at": "2025-06-23T13:30:54.331Z", "updated_at": "2025-06-23T13:30:54.331Z"}}, {"model": "accounts.usersettings", "pk": 3, "fields": {"user": 3, "email_notifications": true, "push_notifications": true, "comment_notifications": true, "like_notifications": true, "follow_notifications": true, "profile_visibility": "public", "show_email": false, "show_phone": false, "content_language": "en", "posts_per_page": 10, "auto_play_videos": true, "theme": "auto", "created_at": "2025-06-23T13:30:54.958Z", "updated_at": "2025-06-23T13:30:54.958Z"}}, {"model": "accounts.usersettings", "pk": 4, "fields": {"user": 4, "email_notifications": true, "push_notifications": true, "comment_notifications": true, "like_notifications": true, "follow_notifications": true, "profile_visibility": "public", "show_email": false, "show_phone": false, "content_language": "en", "posts_per_page": 10, "auto_play_videos": true, "theme": "auto", "created_at": "2025-06-23T13:30:55.590Z", "updated_at": "2025-06-23T13:30:55.590Z"}}, {"model": "accounts.usersettings", "pk": 5, "fields": {"user": 5, "email_notifications": true, "push_notifications": true, "comment_notifications": true, "like_notifications": true, "follow_notifications": true, "profile_visibility": "public", "show_email": false, "show_phone": false, "content_language": "en", "posts_per_page": 10, "auto_play_videos": true, "theme": "auto", "created_at": "2025-06-23T13:30:56.333Z", "updated_at": "2025-06-23T13:30:56.333Z"}}, {"model": "analytics.contentanalytics", "pk": 1, "fields": {"post": 1, "estimated_reading_time": 7, "complexity_score": 68.*************, "word_count": 26, "sentence_count": 3, "paragraph_count": 1, "readability_score": 74.**************, "reading_level": "Fairly Easy", "average_words_per_sentence": 8.***************, "average_syllables_per_word": 1.****************, "total_reading_time": 0, "completion_rate": 0.0, "average_session_duration": 0, "created_at": "2025-06-23T13:32:06.926Z", "updated_at": "2025-06-23T13:32:06.926Z"}}, {"model": "analytics.contentanalytics", "pk": 2, "fields": {"post": 2, "estimated_reading_time": 6, "complexity_score": 71.889, "word_count": 20, "sentence_count": 2, "paragraph_count": 1, "readability_score": 65.555, "reading_level": "Standard", "average_words_per_sentence": 10.0, "average_syllables_per_word": 1.55, "total_reading_time": 0, "completion_rate": 0.0, "average_session_duration": 0, "created_at": "2025-06-23T13:32:06.936Z", "updated_at": "2025-06-23T13:32:06.937Z"}}, {"model": "analytics.contentanalytics", "pk": 3, "fields": {"post": 3, "estimated_reading_time": 6, "complexity_score": 71.889, "word_count": 20, "sentence_count": 2, "paragraph_count": 1, "readability_score": 65.555, "reading_level": "Standard", "average_words_per_sentence": 10.0, "average_syllables_per_word": 1.55, "total_reading_time": 0, "completion_rate": 0.0, "average_session_duration": 0, "created_at": "2025-06-23T13:32:06.950Z", "updated_at": "2025-06-23T13:32:06.950Z"}}, {"model": "analytics.contentanalytics", "pk": 4, "fields": {"post": 4, "estimated_reading_time": 4, "complexity_score": 73.52357142857143, "word_count": 14, "sentence_count": 3, "paragraph_count": 1, "readability_score": 17.382142857142867, "reading_level": "Very Difficult", "average_words_per_sentence": 4.666666666666667, "average_syllables_per_word": 2.0714285714285716, "total_reading_time": 0, "completion_rate": 0.0, "average_session_duration": 0, "created_at": "2025-06-23T13:32:06.963Z", "updated_at": "2025-06-23T13:32:06.963Z"}}, {"model": "gamification.userlevel", "pk": 1, "fields": {"user": 1, "total_points": 100, "current_level": 1, "points_to_next_level": 100, "reading_streak": 1, "writing_streak": 0, "engagement_streak": 0, "last_reading_date": null, "last_writing_date": null, "last_engagement_date": null, "total_posts_read": 0, "total_posts_written": 0, "total_comments_made": 0, "total_likes_given": 0, "total_voice_comments": 0, "created_at": "2025-06-23T13:32:29.037Z", "updated_at": "2025-06-23T13:32:29.037Z"}}, {"model": "gamification.userlevel", "pk": 2, "fields": {"user": 2, "total_points": 100, "current_level": 1, "points_to_next_level": 100, "reading_streak": 1, "writing_streak": 0, "engagement_streak": 0, "last_reading_date": null, "last_writing_date": null, "last_engagement_date": null, "total_posts_read": 0, "total_posts_written": 0, "total_comments_made": 0, "total_likes_given": 0, "total_voice_comments": 0, "created_at": "2025-06-23T13:32:29.060Z", "updated_at": "2025-06-23T13:32:29.060Z"}}, {"model": "gamification.userlevel", "pk": 3, "fields": {"user": 3, "total_points": 100, "current_level": 1, "points_to_next_level": 100, "reading_streak": 1, "writing_streak": 0, "engagement_streak": 0, "last_reading_date": null, "last_writing_date": null, "last_engagement_date": null, "total_posts_read": 0, "total_posts_written": 0, "total_comments_made": 0, "total_likes_given": 0, "total_voice_comments": 0, "created_at": "2025-06-23T13:32:29.078Z", "updated_at": "2025-06-23T13:32:29.078Z"}}, {"model": "gamification.userlevel", "pk": 4, "fields": {"user": 4, "total_points": 100, "current_level": 1, "points_to_next_level": 100, "reading_streak": 1, "writing_streak": 0, "engagement_streak": 0, "last_reading_date": null, "last_writing_date": null, "last_engagement_date": null, "total_posts_read": 0, "total_posts_written": 0, "total_comments_made": 0, "total_likes_given": 0, "total_voice_comments": 0, "created_at": "2025-06-23T13:32:29.092Z", "updated_at": "2025-06-23T13:32:29.092Z"}}, {"model": "gamification.userlevel", "pk": 5, "fields": {"user": 5, "total_points": 100, "current_level": 1, "points_to_next_level": 100, "reading_streak": 1, "writing_streak": 0, "engagement_streak": 0, "last_reading_date": null, "last_writing_date": null, "last_engagement_date": null, "total_posts_read": 0, "total_posts_written": 0, "total_comments_made": 0, "total_likes_given": 0, "total_voice_comments": 0, "created_at": "2025-06-23T13:32:29.113Z", "updated_at": "2025-06-23T13:32:29.113Z"}}, {"model": "gamification.pointtransaction", "pk": 1, "fields": {"user": 1, "transaction_type": "earned", "points": 100, "description": "Welcome bonus points", "related_object_type": "", "related_object_id": null, "created_at": "2025-06-23T13:32:29.047Z"}}, {"model": "gamification.pointtransaction", "pk": 2, "fields": {"user": 2, "transaction_type": "earned", "points": 100, "description": "Welcome bonus points", "related_object_type": "", "related_object_id": null, "created_at": "2025-06-23T13:32:29.066Z"}}, {"model": "gamification.pointtransaction", "pk": 3, "fields": {"user": 3, "transaction_type": "earned", "points": 100, "description": "Welcome bonus points", "related_object_type": "", "related_object_id": null, "created_at": "2025-06-23T13:32:29.083Z"}}, {"model": "gamification.pointtransaction", "pk": 4, "fields": {"user": 4, "transaction_type": "earned", "points": 100, "description": "Welcome bonus points", "related_object_type": "", "related_object_id": null, "created_at": "2025-06-23T13:32:29.102Z"}}, {"model": "gamification.pointtransaction", "pk": 5, "fields": {"user": 5, "transaction_type": "earned", "points": 100, "description": "Welcome bonus points", "related_object_type": "", "related_object_id": null, "created_at": "2025-06-23T13:32:29.123Z"}}, {"model": "monetization.monetizationsettings", "pk": 1, "fields": {"premium_monthly_price": "9.99", "premium_point_multiplier": "2.0", "premium_daily_bonus": 15, "engagement_tier_price": "2.99", "achievement_tier_price": "4.99", "elite_tier_price": "9.99", "referral_join_points": 100, "referral_level_5_reward": "2.00", "referral_premium_reward": "5.00", "monetization_enabled": true, "premium_enabled": true, "tier_unlocks_enabled": true, "created_at": "2025-06-23T13:33:06.882Z", "updated_at": "2025-06-23T13:33:06.882Z"}}, {"model": "wallet.userwallet", "pk": 1, "fields": {"user": 1, "balance": "50.00", "is_active": true, "is_verified": false, "verification_date": null, "daily_spend_limit": "100.00", "monthly_spend_limit": "500.00", "created_at": "2025-06-23T13:29:03.034Z", "updated_at": "2025-06-23T13:31:43.180Z"}}, {"model": "wallet.userwallet", "pk": 2, "fields": {"user": 2, "balance": "50.00", "is_active": true, "is_verified": false, "verification_date": null, "daily_spend_limit": "100.00", "monthly_spend_limit": "500.00", "created_at": "2025-06-23T13:30:54.334Z", "updated_at": "2025-06-23T13:31:43.209Z"}}, {"model": "wallet.userwallet", "pk": 3, "fields": {"user": 3, "balance": "50.00", "is_active": true, "is_verified": false, "verification_date": null, "daily_spend_limit": "100.00", "monthly_spend_limit": "500.00", "created_at": "2025-06-23T13:30:54.964Z", "updated_at": "2025-06-23T13:31:43.222Z"}}, {"model": "wallet.userwallet", "pk": 4, "fields": {"user": 4, "balance": "50.00", "is_active": true, "is_verified": false, "verification_date": null, "daily_spend_limit": "100.00", "monthly_spend_limit": "500.00", "created_at": "2025-06-23T13:30:55.595Z", "updated_at": "2025-06-23T13:31:43.238Z"}}, {"model": "wallet.userwallet", "pk": 5, "fields": {"user": 5, "balance": "50.00", "is_active": true, "is_verified": false, "verification_date": null, "daily_spend_limit": "100.00", "monthly_spend_limit": "500.00", "created_at": "2025-06-23T13:30:56.335Z", "updated_at": "2025-06-23T13:31:43.250Z"}}, {"model": "wallet.wallettransaction", "pk": "0bc03251-76fb-4f60-96b7-bbfae319ce65", "fields": {"wallet": 1, "transaction_type": "credit", "purpose": "Initial Balance", "amount": "50.00", "balance_before": "50.00", "balance_after": "50.00", "status": "completed", "description": "Welcome bonus - starting balance", "reference_id": "", "payment_method": "", "external_transaction_id": "", "created_at": "2025-06-23T13:31:43.197Z", "processed_at": null, "completed_at": null}}, {"model": "wallet.wallettransaction", "pk": "47fd5cd1-feee-449f-b2e0-8ff011bfbbdb", "fields": {"wallet": 5, "transaction_type": "credit", "purpose": "Initial Balance", "amount": "50.00", "balance_before": "0.00", "balance_after": "50.00", "status": "completed", "description": "Welcome bonus - starting balance", "reference_id": "", "payment_method": "", "external_transaction_id": "", "created_at": "2025-06-23T13:31:43.256Z", "processed_at": null, "completed_at": null}}, {"model": "wallet.wallettransaction", "pk": "7ac11cd3-b824-4858-a3c9-49d7b213f5d3", "fields": {"wallet": 3, "transaction_type": "credit", "purpose": "Initial Balance", "amount": "50.00", "balance_before": "0.00", "balance_after": "50.00", "status": "completed", "description": "Welcome bonus - starting balance", "reference_id": "", "payment_method": "", "external_transaction_id": "", "created_at": "2025-06-23T13:31:43.226Z", "processed_at": null, "completed_at": null}}, {"model": "wallet.wallettransaction", "pk": "a74183df-a200-4fb4-b1fd-fdf07ac50a2b", "fields": {"wallet": 4, "transaction_type": "credit", "purpose": "Initial Balance", "amount": "50.00", "balance_before": "0.00", "balance_after": "50.00", "status": "completed", "description": "Welcome bonus - starting balance", "reference_id": "", "payment_method": "", "external_transaction_id": "", "created_at": "2025-06-23T13:31:43.242Z", "processed_at": null, "completed_at": null}}, {"model": "wallet.wallettransaction", "pk": "af8a9ca0-5d2c-463d-95db-bbf9a973385d", "fields": {"wallet": 2, "transaction_type": "credit", "purpose": "Initial Balance", "amount": "50.00", "balance_before": "0.00", "balance_after": "50.00", "status": "completed", "description": "Welcome bonus - starting balance", "reference_id": "", "payment_method": "", "external_transaction_id": "", "created_at": "2025-06-23T13:31:43.213Z", "processed_at": null, "completed_at": null}}]