import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:confetti/confetti.dart';
import '../theme/app_theme.dart';
import '../providers/auth_provider.dart';
import '../providers/gamification_provider.dart';
import '../services/api_service.dart';
import '../widgets/main_navigation.dart';

class WelcomeScreen extends ConsumerStatefulWidget {
  final bool isNewUser;
  final String? referralCode;

  const WelcomeScreen({Key? key, this.isNewUser = false, this.referralCode})
    : super(key: key);

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _textController;
  late AnimationController _buttonController;
  late ConfettiController _confettiController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _buttonScale;

  // Welcome bonus state
  bool _bonusLoading = true;
  int _bonusAmount = 0;
  String _bonusMessage = '';
  bool _hasBonusError = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startWelcomeSequence();

    // Load welcome bonus info for new users
    if (widget.isNewUser) {
      _loadWelcomeBonusInfo();
    }

    // Load gamification data for dynamic stats
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(gamificationProvider.notifier).loadUserLevel();
      ref.read(gamificationProvider.notifier).loadUserBadges();
    });
  }

  void _initializeAnimations() {
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _mainController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _textController, curve: Curves.easeIn));

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(parent: _textController, curve: Curves.easeOutCubic),
        );

    _buttonScale = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.elasticOut),
    );
  }

  void _startWelcomeSequence() async {
    // Start confetti if new user
    if (widget.isNewUser) {
      await Future.delayed(const Duration(milliseconds: 500));
      _confettiController.play();
    }

    // Start main animation
    _mainController.forward();

    // Start text animation
    await Future.delayed(const Duration(milliseconds: 400));
    _textController.forward();

    // Start button animation
    await Future.delayed(const Duration(milliseconds: 600));
    _buttonController.forward();
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const MainNavigation(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  Future<void> _loadWelcomeBonusInfo() async {
    try {
      final apiService = ApiService();
      final response = await apiService.getWelcomeBonusInfo();

      if (mounted && response['success'] == true) {
        setState(() {
          _bonusLoading = false;
          if (response['has_welcome_bonus'] == true) {
            _bonusAmount = response['bonus_amount'] ?? 0;
            _bonusMessage = response['message'] ?? 'Welcome bonus received!';
          } else {
            _bonusAmount = 100; // Default welcome bonus
            _bonusMessage = 'Welcome bonus of 100 points awarded!';
          }
          _hasBonusError = false;
        });

        // Refresh gamification data to show updated points
        ref.read(gamificationProvider.notifier).loadUserLevel();
        ref.read(gamificationProvider.notifier).loadPointTransactions();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _bonusLoading = false;
          _hasBonusError = true;
          _bonusAmount = 100; // Default fallback
          _bonusMessage = 'Welcome bonus awarded!';
        });
      }
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _textController.dispose();
    _buttonController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(enhancedAuthProvider).user;
    final gamificationState = ref.watch(gamificationProvider);

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF6C5CE7), Color(0xFF00B894), Color(0xFF0984E3)],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Confetti
              if (widget.isNewUser)
                Align(
                  alignment: Alignment.topCenter,
                  child: ConfettiWidget(
                    confettiController: _confettiController,
                    blastDirection: 1.5708, // radians for downward
                    emissionFrequency: 0.05,
                    numberOfParticles: 20,
                    gravity: 0.1,
                    shouldLoop: false,
                    colors: const [
                      Colors.white,
                      Colors.yellow,
                      Colors.pink,
                      Colors.orange,
                      Colors.purple,
                    ],
                  ),
                ),

              // Main Content
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Welcome Icon
                    AnimatedBuilder(
                      animation: _mainController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(40),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 30,
                                  offset: const Offset(0, 15),
                                ),
                              ],
                            ),
                            child: Icon(
                              widget.isNewUser
                                  ? Icons.celebration
                                  : Icons.waving_hand,
                              size: 80,
                              color: const Color(0xFF6C5CE7),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 40),

                    // Welcome Text
                    AnimatedBuilder(
                      animation: _textController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Column(
                              children: [
                                Text(
                                  widget.isNewUser
                                      ? 'Welcome to Trendy!'
                                      : 'Continue Your Journey!',
                                  style: const TextStyle(
                                    fontSize: 36,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    height: 1.2,
                                  ),
                                  textAlign: TextAlign.center,
                                ),

                                const SizedBox(height: 16),

                                if (user != null)
                                  Text(
                                    'Hi ${user.firstName.isNotEmpty ? user.firstName : user.username}! 👋',
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),

                                const SizedBox(height: 24),

                                Text(
                                  widget.isNewUser
                                      ? 'Your journey to earning and creating amazing content starts now!'
                                      : 'Ready to dive back in? Your adventure continues with new content, rewards, and exciting challenges!',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    color: Colors.white,
                                    height: 1.4,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 40),

                    // Bonus Information - only for new users
                    if (widget.isNewUser)
                      AnimatedBuilder(
                        animation: _textController,
                        builder: (context, child) {
                          return FadeTransition(
                            opacity: _fadeAnimation,
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                ),
                              ),
                              child: Column(
                                children: [
                                  const Icon(
                                    Icons.card_giftcard,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    widget.referralCode != null
                                        ? '🎉 Bonus Points Awarded!'
                                        : '🎁 Welcome Bonus Unlocked!',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  if (_bonusLoading)
                                    const CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    )
                                  else
                                    Column(
                                      children: [
                                        Text(
                                          '$_bonusAmount Points Awarded!',
                                          style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.yellow,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          _bonusMessage,
                                          style: const TextStyle(
                                            fontSize: 14,
                                            color: Colors.white,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),

                    const SizedBox(height: 40),

                    // Journey Continuation for returning users
                    if (!widget.isNewUser)
                      AnimatedBuilder(
                        animation: _textController,
                        builder: (context, child) {
                          return FadeTransition(
                            opacity: _fadeAnimation,
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                children: [
                                  const Icon(
                                    Icons.trending_up,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    widget.isNewUser
                                        ? '🎉 Welcome to Trendy!'
                                        : '🚀 Continue Your Journey',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    widget.isNewUser
                                        ? 'Start your adventure! Discover amazing content, connect with creators, and earn points as you explore everything Trendy has to offer.'
                                        : 'Explore trending content, engage with the community, and earn rewards as you continue your creative journey!',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.white,
                                      height: 1.4,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),

                    const SizedBox(height: 40),

                    // Action Buttons
                    AnimatedBuilder(
                      animation: _buttonController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _buttonScale.value,
                          child: Column(
                            children: [
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: _navigateToHome,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.white,
                                    foregroundColor: const Color(0xFF6C5CE7),
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 18,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        widget.isNewUser
                                            ? 'Start Exploring'
                                            : 'Continue Journey',
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(Icons.arrow_forward),
                                    ],
                                  ),
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Quick Stats - Dynamic Data
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem(
                                    '🎯',
                                    'Level ${gamificationState.userLevel?.currentLevel ?? 1}',
                                    'Your Level',
                                  ),
                                  _buildStatItem(
                                    '💰',
                                    '${gamificationState.userLevel?.totalPoints ?? _bonusAmount}',
                                    'Points',
                                  ),
                                  _buildStatItem(
                                    '🏆',
                                    '${gamificationState.badges.length}',
                                    'Achievements',
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String emoji, String value, String label) {
    return Column(
      children: [
        Text(emoji, style: const TextStyle(fontSize: 24)),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.white.withOpacity(0.8)),
        ),
      ],
    );
  }
}
