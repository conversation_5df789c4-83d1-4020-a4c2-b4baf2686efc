# 🌐 Trendy App Network Configuration

## **Current Network Setup**

### **System Information**
- **Host IP Address**: `**************`
- **Django Server**: `http://**************:8000`
- **Flutter API Config**: Updated to use network IP

### **Django Server Configuration**
```bash
# Server is running on all network interfaces
python manage.py runserver 0.0.0.0:8000
```

### **Django Settings**
```python
# ALLOWED_HOSTS includes network IP
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '**************', '*']

# CORS allows all origins in development
CORS_ALLOW_ALL_ORIGINS = True  # When DEBUG = True
```

### **Flutter Configuration**
```dart
// lib/config/api_config.dart
static const String baseUrl = 'http://**************:8000';
```

## **Network Access Instructions**

### **For Mobile Devices on Same Network**
1. **Ensure devices are on same WiFi network**
2. **Flutter app will automatically connect to**: `http://**************:8000`
3. **All API endpoints accessible**:
   - Authentication: `http://**************:8000/api/v1/accounts/`
   - Posts: `http://**************:8000/api/v1/posts/`
   - Wallet: `http://**************:8000/api/v1/wallet/`
   - Shop: `http://**************:8000/api/v1/monetization/`

### **Testing Network Connectivity**
```bash
# Test server accessibility
curl -I http://**************:8000/api/v1/monetization/settings/

# Expected response: HTTP/1.1 200 OK
```

### **Firewall Configuration**
If connection fails, ensure port 8000 is open:
```bash
# Ubuntu/Debian
sudo ufw allow 8000

# Or temporarily disable firewall for testing
sudo ufw disable
```

## **Development Workflow**

### **Starting the Server**
```bash
cd trendy/trendy_web_and_api/trendy
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

### **Building Flutter App**
```bash
cd trendy
flutter pub get
flutter run
```

### **Network Testing**
```bash
# Test from another device on same network
curl http://**************:8000/api/v1/monetization/settings/
```

## **Troubleshooting**

### **Common Issues**

1. **Connection Refused**
   - Check if Django server is running
   - Verify IP address is correct
   - Ensure port 8000 is not blocked

2. **CORS Errors**
   - Verify `CORS_ALLOW_ALL_ORIGINS = True` in settings
   - Check `corsheaders` is in INSTALLED_APPS

3. **404 Errors**
   - Verify API endpoints are correctly configured
   - Check URL patterns in Django

### **IP Address Changes**
If system IP changes, update:
1. `trendy/lib/config/api_config.dart` - Update `baseUrl`
2. `trendy_web_and_api/trendy/trendyblog/settings.py` - Update `ALLOWED_HOSTS`

### **Production Deployment**
For production, update:
1. Set `DEBUG = False`
2. Configure specific `CORS_ALLOWED_ORIGINS`
3. Use proper domain name instead of IP
4. Enable HTTPS

## **Security Notes**

⚠️ **Development Only**: Current configuration allows all origins and uses HTTP
🔒 **Production**: Use HTTPS and restrict CORS origins
🛡️ **Firewall**: Consider restricting access to specific IP ranges

## **Current Status**
✅ Django server accessible on network
✅ Flutter app configured for network access  
✅ CORS properly configured
✅ All API endpoints working
✅ Wallet and shop features functional over network
