import 'package:freezed_annotation/freezed_annotation.dart';
import 'user.dart';

part 'comment.freezed.dart';
part 'comment.g.dart';

@freezed
class Comment with _$Comment {
  const Comment._();

  const factory Comment({
    required int id,
    required int post,
    required User author,
    required String content,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @<PERSON>son<PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    int? parent,
    @Default([]) List<Comment> replies,
    @JsonKey(name: 'like_count') @Default(0) int likeCount,
    @<PERSON>son<PERSON>ey(name: 'is_liked') @Default(false) bool isLiked,
  }) = _Comment;

  factory Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);
}