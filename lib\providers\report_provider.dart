import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../config/api_config.dart';
import 'provider.dart';
import '../services/api_service.dart';

class ReportState {
  final bool isLoading;
  final String? error;
  final List<Map<String, dynamic>> reports;

  ReportState({
    this.isLoading = false,
    this.error,
    this.reports = const [],
  });

  ReportState copyWith({
    bool? isLoading,
    String? error,
    List<Map<String, dynamic>>? reports,
  }) {
    return ReportState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      reports: reports ?? this.reports,
    );
  }
}

class ReportNotifier extends StateNotifier<ReportState> {
  final Dio _dio;
  final ApiService _apiService;

  ReportNotifier(this._dio, this._apiService) : super(ReportState());

  Future<void> createReport({
    required String reportType,
    required String description,
    int? reportedUserId,
    int? reportedPostId,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final token = await _apiService.getStoredToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final data = {
        'report_type': reportType,
        'description': description,
      };

      if (reportedUserId != null) {
        data['reported_user'] = reportedUserId.toString();
      }
      if (reportedPostId != null) {
        data['reported_post'] = reportedPostId.toString();
      }

      final response = await _dio.post(
        '${ApiConfig.baseUrl}/api/v1/social/reports/',
        data: data,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 201) {
        // Refresh user reports after creating a new one
        await loadUserReports();
      } else {
        throw Exception('Failed to create report');
      }
    } on DioException catch (e) {
      String errorMessage = 'Failed to create report';

      if (e.response?.data != null) {
        if (e.response!.data is Map<String, dynamic>) {
          final errorData = e.response!.data as Map<String, dynamic>;
          if (errorData.containsKey('error')) {
            errorMessage = errorData['error'];
          } else if (errorData.containsKey('non_field_errors')) {
            errorMessage = errorData['non_field_errors'][0];
          }
        }
      }

      state = state.copyWith(isLoading: false, error: errorMessage);
      throw Exception(errorMessage);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      throw e;
    }
  }

  Future<void> loadUserReports() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final token = await _apiService.getStoredToken();
      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await _dio.get(
        '${ApiConfig.baseUrl}/api/v1/social/reports/user/',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> reportsData = response.data;
        final reports = reportsData.cast<Map<String, dynamic>>();

        state = state.copyWith(
          isLoading: false,
          reports: reports,
        );
      } else {
        throw Exception('Failed to load reports');
      }
    } on DioException catch (e) {
      String errorMessage = 'Failed to load reports';

      if (e.response?.data != null) {
        if (e.response!.data is Map<String, dynamic>) {
          final errorData = e.response!.data as Map<String, dynamic>;
          if (errorData.containsKey('error')) {
            errorMessage = errorData['error'];
          }
        }
      }

      state = state.copyWith(isLoading: false, error: errorMessage);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

final reportProvider =
    StateNotifierProvider<ReportNotifier, ReportState>((ref) {
  final dio = Dio();
  final apiService = ref.watch(apiServiceProvider);
  return ReportNotifier(dio, apiService);
});
