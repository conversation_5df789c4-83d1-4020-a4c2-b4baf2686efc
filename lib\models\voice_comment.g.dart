// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voice_comment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VoiceCommentImpl _$$VoiceCommentImplFromJson(Map<String, dynamic> json) =>
    _$VoiceCommentImpl(
      id: (json['id'] as num).toInt(),
      postId: (json['post_id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      userName: json['user_name'] as String,
      userAvatar: json['user_avatar'] as String?,
      audioUrl: json['audio_url'] as String,
      transcription: json['transcription'] as String?,
      durationSeconds: (json['duration_seconds'] as num?)?.toInt() ?? 0,
      fileSize: (json['file_size'] as num?)?.toInt() ?? 0,
      isTranscribed: json['is_transcribed'] as bool? ?? false,
      likeCount: (json['like_count'] as num?)?.toInt() ?? 0,
      isLiked: json['is_liked'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$VoiceCommentImplToJson(_$VoiceCommentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'post_id': instance.postId,
      'user_id': instance.userId,
      'user_name': instance.userName,
      'user_avatar': instance.userAvatar,
      'audio_url': instance.audioUrl,
      'transcription': instance.transcription,
      'duration_seconds': instance.durationSeconds,
      'file_size': instance.fileSize,
      'is_transcribed': instance.isTranscribed,
      'like_count': instance.likeCount,
      'is_liked': instance.isLiked,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_$CreateVoiceCommentImpl _$$CreateVoiceCommentImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateVoiceCommentImpl(
      postId: (json['post_id'] as num).toInt(),
      audioFile: json['audio_file'] as String,
      durationSeconds: (json['duration_seconds'] as num).toInt(),
      transcription: json['transcription'] as String?,
    );

Map<String, dynamic> _$$CreateVoiceCommentImplToJson(
        _$CreateVoiceCommentImpl instance) =>
    <String, dynamic>{
      'post_id': instance.postId,
      'audio_file': instance.audioFile,
      'duration_seconds': instance.durationSeconds,
      'transcription': instance.transcription,
    };

_$VoiceCommentResponseImpl _$$VoiceCommentResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VoiceCommentResponseImpl(
      results: (json['results'] as List<dynamic>?)
              ?.map((e) => VoiceComment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      count: (json['count'] as num?)?.toInt() ?? 0,
      next: json['next'] as String?,
      previous: json['previous'] as String?,
    );

Map<String, dynamic> _$$VoiceCommentResponseImplToJson(
        _$VoiceCommentResponseImpl instance) =>
    <String, dynamic>{
      'results': instance.results,
      'count': instance.count,
      'next': instance.next,
      'previous': instance.previous,
    };
