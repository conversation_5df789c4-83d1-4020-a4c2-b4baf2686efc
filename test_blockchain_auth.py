#!/usr/bin/env python3
"""
Test script to verify blockchain authentication issues
"""

import requests
import json

# API Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_authentication_flow():
    """Test the complete authentication flow"""
    print("🔐 Testing Authentication Flow...\n")
    
    # Test data - using the test user we just created
    test_user = {
        "username": "apitest",
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "API",
        "last_name": "Test"
    }
    
    # Step 1: Register user (if not exists)
    print("1️⃣ Testing User Registration...")
    try:
        register_response = requests.post(f"{API_BASE}/accounts/register/", json=test_user)
        print(f"Registration Status: {register_response.status_code}")
        
        if register_response.status_code in [200, 201]:
            register_data = register_response.json()
            print(f"✅ Registration successful!")
            print(f"Token received: {register_data.get('token', 'No token')[:20]}...")
            token = register_data.get('token')
        elif register_response.status_code == 400:
            print("ℹ️ User already exists, proceeding to login...")
            token = None
        else:
            print(f"❌ Registration failed: {register_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False
    
    # Step 2: Login user
    print("\n2️⃣ Testing User Login...")
    try:
        login_data = {
            "email_or_username": test_user["username"],
            "password": test_user["password"]
        }
        
        login_response = requests.post(f"{API_BASE}/accounts/login/", json=login_data)
        print(f"Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            print(f"✅ Login successful!")
            token = login_data.get('token')
            print(f"Token received: {token[:20]}...")
            user_data = login_data.get('user', {})
            print(f"User ID: {user_data.get('id')}")
            print(f"Username: {user_data.get('username')}")
            print(f"Email verified: {user_data.get('is_email_verified')}")
        else:
            print(f"❌ Login failed: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Step 3: Test authenticated endpoints
    print("\n3️⃣ Testing Authenticated Endpoints...")
    headers = {'Authorization': f'Token {token}'}
    
    # Test wallet overview (should work)
    print("Testing wallet overview...")
    try:
        wallet_response = requests.get(f"{API_BASE}/wallet/overview/", headers=headers)
        print(f"Wallet Overview Status: {wallet_response.status_code}")
        if wallet_response.status_code == 200:
            print("✅ Wallet overview working!")
        else:
            print(f"❌ Wallet overview failed: {wallet_response.text}")
    except Exception as e:
        print(f"❌ Wallet overview error: {e}")
    
    # Test blockchain wallet (the problematic endpoint)
    print("\nTesting blockchain wallet...")
    try:
        blockchain_response = requests.get(f"{API_BASE}/blockchain/wallet/", headers=headers)
        print(f"Blockchain Wallet Status: {blockchain_response.status_code}")
        
        if blockchain_response.status_code == 200:
            print("✅ Blockchain wallet working!")
            data = blockchain_response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        elif blockchain_response.status_code == 403:
            print("❌ Blockchain wallet forbidden - checking permissions...")
            print(f"Response: {blockchain_response.text}")
            
            # Check if user has required permissions
            print(f"Token being sent: {token[:20]}...")
            print("Checking if token is valid...")
            
            # Test with a simple authenticated endpoint first
            profile_response = requests.get(f"{API_BASE}/accounts/profile/", headers=headers)
            print(f"Profile endpoint status: {profile_response.status_code}")
            
        else:
            print(f"❌ Blockchain wallet failed: {blockchain_response.text}")
            
    except Exception as e:
        print(f"❌ Blockchain wallet error: {e}")
    
    # Step 4: Test blockchain wallet creation
    print("\n4️⃣ Testing Blockchain Wallet Creation...")
    try:
        create_response = requests.post(f"{API_BASE}/blockchain/wallet/create/", headers=headers)
        print(f"Wallet Creation Status: {create_response.status_code}")
        
        if create_response.status_code == 200:
            print("✅ Blockchain wallet creation working!")
            data = create_response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        elif create_response.status_code == 403:
            print("❌ Blockchain wallet creation forbidden")
            print(f"Response: {create_response.text}")
        else:
            print(f"❌ Blockchain wallet creation failed: {create_response.text}")
            
    except Exception as e:
        print(f"❌ Blockchain wallet creation error: {e}")
    
    return True

def test_blockchain_models():
    """Test if blockchain models exist in database"""
    print("\n🗄️ Testing Blockchain Database Models...")
    
    try:
        # Test if we can access Django shell
        import os
        import sys
        
        # Add Django project to path
        django_path = "/home/<USER>/Desktop/Trendy App/trendy/trendy_web_and_api/trendy"
        sys.path.append(django_path)
        
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
        
        import django
        django.setup()
        
        # Import blockchain models
        from blockchain.models import BlockchainNetwork, UserWalletAddress
        
        # Check if blockchain networks exist
        networks = BlockchainNetwork.objects.all()
        print(f"Blockchain networks in database: {networks.count()}")
        
        for network in networks:
            print(f"- {network.name}: {network.rpc_url}")
        
        # Check if any wallets exist
        wallets = UserWalletAddress.objects.all()
        print(f"User wallets in database: {wallets.count()}")
        
        if networks.count() == 0:
            print("⚠️ No blockchain networks found - this might be the issue!")
            return False
        
        print("✅ Blockchain models accessible!")
        return True
        
    except Exception as e:
        print(f"❌ Error accessing blockchain models: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Blockchain Authentication Tests...\n")
    
    # Test authentication flow
    auth_success = test_authentication_flow()
    
    # Test blockchain models
    models_success = test_blockchain_models()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    if auth_success and models_success:
        print("🎉 All tests passed! The issue might be in Flutter app token handling.")
        print("\n💡 Recommendations:")
        print("1. Check if Flutter app is sending the Authorization header correctly")
        print("2. Verify the token format: 'Token <token_value>'")
        print("3. Ensure the user is logged in before accessing blockchain features")
        print("4. Check if blockchain networks are properly initialized in database")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        
        if not models_success:
            print("\n🔧 To fix blockchain models issue:")
            print("1. Run Django migrations: python manage.py migrate")
            print("2. Initialize blockchain networks: python manage.py shell")
            print("3. Create test blockchain networks in admin panel")

if __name__ == "__main__":
    main()
