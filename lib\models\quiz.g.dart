// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quiz.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$QuizImpl _$$QuizImplFromJson(Map<String, dynamic> json) => _$QuizImpl(
      id: (json['id'] as num).toInt(),
      instructions: json['instructions'] as String? ?? '',
      timeLimit: (json['time_limit'] as num?)?.toInt(),
      showCorrectAnswers: json['show_correct_answers'] as bool? ?? true,
      randomizeQuestions: json['randomize_questions'] as bool? ?? false,
      passingScore: (json['passing_score'] as num?)?.toInt() ?? 70,
      totalAttempts: (json['total_attempts'] as num?)?.toInt() ?? 0,
      averageScore: (json['average_score'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['created_at'] as String),
      questions: (json['questions'] as List<dynamic>?)
              ?.map((e) => QuizQuestion.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      userAttempts: (json['user_attempts'] as List<dynamic>?)
              ?.map((e) => QuizAttempt.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      bestAttempt: json['best_attempt'] == null
          ? null
          : QuizAttempt.fromJson(json['best_attempt'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$QuizImplToJson(_$QuizImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'instructions': instance.instructions,
      'time_limit': instance.timeLimit,
      'show_correct_answers': instance.showCorrectAnswers,
      'randomize_questions': instance.randomizeQuestions,
      'passing_score': instance.passingScore,
      'total_attempts': instance.totalAttempts,
      'average_score': instance.averageScore,
      'created_at': instance.createdAt.toIso8601String(),
      'questions': instance.questions,
      'user_attempts': instance.userAttempts,
      'best_attempt': instance.bestAttempt,
    };

_$QuizQuestionImpl _$$QuizQuestionImplFromJson(Map<String, dynamic> json) =>
    _$QuizQuestionImpl(
      id: (json['id'] as num).toInt(),
      questionType: json['question_type'] as String? ?? 'multiple_choice',
      questionText: json['question_text'] as String,
      explanation: json['explanation'] as String? ?? '',
      points: (json['points'] as num?)?.toInt() ?? 1,
      position: (json['position'] as num?)?.toInt() ?? 0,
      answers: (json['answers'] as List<dynamic>?)
              ?.map((e) => QuizAnswer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$QuizQuestionImplToJson(_$QuizQuestionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'question_type': instance.questionType,
      'question_text': instance.questionText,
      'explanation': instance.explanation,
      'points': instance.points,
      'position': instance.position,
      'answers': instance.answers,
    };

_$QuizAnswerImpl _$$QuizAnswerImplFromJson(Map<String, dynamic> json) =>
    _$QuizAnswerImpl(
      id: (json['id'] as num).toInt(),
      answerText: json['answer_text'] as String,
      isCorrect: json['is_correct'] as bool? ?? false,
      position: (json['position'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$QuizAnswerImplToJson(_$QuizAnswerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'answer_text': instance.answerText,
      'is_correct': instance.isCorrect,
      'position': instance.position,
    };

_$QuizAttemptImpl _$$QuizAttemptImplFromJson(Map<String, dynamic> json) =>
    _$QuizAttemptImpl(
      id: (json['id'] as num).toInt(),
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
      totalPoints: (json['total_points'] as num?)?.toInt() ?? 0,
      earnedPoints: (json['earned_points'] as num?)?.toInt() ?? 0,
      timeTaken: (json['time_taken'] as num?)?.toInt() ?? 0,
      completedAt: DateTime.parse(json['completed_at'] as String),
      answers: json['answers'] as Map<String, dynamic>? ?? const {},
      passed: json['passed'] as bool? ?? false,
    );

Map<String, dynamic> _$$QuizAttemptImplToJson(_$QuizAttemptImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'score': instance.score,
      'total_points': instance.totalPoints,
      'earned_points': instance.earnedPoints,
      'time_taken': instance.timeTaken,
      'completed_at': instance.completedAt.toIso8601String(),
      'answers': instance.answers,
      'passed': instance.passed,
    };
