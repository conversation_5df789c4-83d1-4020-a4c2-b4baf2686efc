// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_analytics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContentAnalyticsImpl _$$ContentAnalyticsImplFromJson(
        Map<String, dynamic> json) =>
    _$ContentAnalyticsImpl(
      id: (json['id'] as num).toInt(),
      postTitle: json['post_title'] as String,
      postSlug: json['post_slug'] as String,
      estimatedReadingTime: (json['estimated_reading_time'] as num).toInt(),
      complexityScore: (json['complexity_score'] as num?)?.toDouble() ?? 0.0,
      wordCount: (json['word_count'] as num?)?.toInt() ?? 0,
      sentenceCount: (json['sentence_count'] as num?)?.toInt() ?? 0,
      paragraphCount: (json['paragraph_count'] as num?)?.toInt() ?? 0,
      readabilityScore: (json['readability_score'] as num?)?.toDouble() ?? 0.0,
      readingLevel: json['reading_level'] as String? ?? 'Unknown',
      averageWordsPerSentence:
          (json['average_words_per_sentence'] as num?)?.toDouble() ?? 0.0,
      averageSyllablesPerWord:
          (json['average_syllables_per_word'] as num?)?.toDouble() ?? 0.0,
      totalReadingTime: (json['total_reading_time'] as num?)?.toInt() ?? 0,
      completionRate: (json['completion_rate'] as num?)?.toDouble() ?? 0.0,
      averageSessionDuration:
          (json['average_session_duration'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$ContentAnalyticsImplToJson(
        _$ContentAnalyticsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'post_title': instance.postTitle,
      'post_slug': instance.postSlug,
      'estimated_reading_time': instance.estimatedReadingTime,
      'complexity_score': instance.complexityScore,
      'word_count': instance.wordCount,
      'sentence_count': instance.sentenceCount,
      'paragraph_count': instance.paragraphCount,
      'readability_score': instance.readabilityScore,
      'reading_level': instance.readingLevel,
      'average_words_per_sentence': instance.averageWordsPerSentence,
      'average_syllables_per_word': instance.averageSyllablesPerWord,
      'total_reading_time': instance.totalReadingTime,
      'completion_rate': instance.completionRate,
      'average_session_duration': instance.averageSessionDuration,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };
