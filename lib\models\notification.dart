import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification.freezed.dart';
part 'notification.g.dart';

@freezed
class AppNotification with _$AppNotification {
  const AppNotification._();

  const factory AppNotification({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_type') required String notificationType,
    required String title,
    required String message,
    @J<PERSON><PERSON><PERSON>(name: 'post_id') int? postId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'comment_id') int? commentId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read') @Default(false) bool isRead,
    @<PERSON><PERSON><PERSON>ey(name: 'created_at') required String createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'read_at') String? readAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'sender_name') String? senderName,
    @Json<PERSON>ey(name: 'sender_avatar') String? senderAvatar,
  }) = _AppNotification;

  factory AppNotification.fromJson(Map<String, dynamic> json) => _$AppNotificationFromJson(json);

  String get timeAgo {
    final now = DateTime.now();
    final createdTime = DateTime.parse(createdAt);
    final difference = now.difference(createdTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get typeDisplayName {
    switch (notificationType) {
      case 'like':
        return 'Like';
      case 'comment':
        return 'Comment';
      case 'follow':
        return 'Follow';
      case 'mention':
        return 'Mention';
      case 'post':
        return 'New Post';
      case 'system':
        return 'System';
      default:
        return 'Notification';
    }
  }
}
