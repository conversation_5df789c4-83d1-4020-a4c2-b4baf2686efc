from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    UserProfile, Follow, Bookmark, BookmarkCollection,
    ReadingList, ReadingListItem, Notification, UserActivity, Report
)
from blog.models import Post

User = get_user_model()

class UserProfileSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)
    date_joined = serializers.DateTimeField(source='user.date_joined', read_only=True)
    
    # Social stats
    followers_count = serializers.SerializerMethodField()
    following_count = serializers.SerializerMethodField()
    posts_count = serializers.SerializerMethodField()
    is_following = serializers.SerializerMethodField()
    
    class Meta:
        model = UserProfile
        fields = [
            'username', 'email', 'first_name', 'last_name', 'date_joined',
            'bio', 'location', 'website', 'birth_date', 'avatar', 'cover_image',
            'twitter_handle', 'linkedin_url', 'github_username',
            'is_public', 'show_email', 'show_reading_activity',
            'is_verified', 'verification_date',
            'followers_count', 'following_count', 'posts_count', 'is_following',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['is_verified', 'verification_date', 'created_at', 'updated_at']
    
    def get_followers_count(self, obj):
        return obj.user.followers.count()
    
    def get_following_count(self, obj):
        return obj.user.following.count()
    
    def get_posts_count(self, obj):
        return obj.user.posts.filter(status='published').count()
    
    def get_is_following(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated and request.user != obj.user:
            return Follow.objects.filter(
                follower=request.user,
                following=obj.user
            ).exists()
        return False

class FollowSerializer(serializers.ModelSerializer):
    follower_username = serializers.CharField(source='follower.username', read_only=True)
    following_username = serializers.CharField(source='following.username', read_only=True)
    follower_profile = serializers.SerializerMethodField()
    following_profile = serializers.SerializerMethodField()
    
    class Meta:
        model = Follow
        fields = [
            'id', 'follower_username', 'following_username',
            'follower_profile', 'following_profile', 'created_at'
        ]
    
    def get_follower_profile(self, obj):
        try:
            profile = obj.follower.profile
            return {
                'avatar': profile.avatar.url if profile.avatar else None,
                'bio': profile.bio,
                'is_verified': profile.is_verified,
            }
        except UserProfile.DoesNotExist:
            return None
    
    def get_following_profile(self, obj):
        try:
            profile = obj.following.profile
            return {
                'avatar': profile.avatar.url if profile.avatar else None,
                'bio': profile.bio,
                'is_verified': profile.is_verified,
            }
        except UserProfile.DoesNotExist:
            return None

class BookmarkCollectionSerializer(serializers.ModelSerializer):
    bookmarks_count = serializers.SerializerMethodField()
    
    class Meta:
        model = BookmarkCollection
        fields = [
            'id', 'name', 'description', 'is_public',
            'bookmarks_count', 'created_at', 'updated_at'
        ]
    
    def get_bookmarks_count(self, obj):
        return obj.bookmarks.count()

class BookmarkSerializer(serializers.ModelSerializer):
    post_title = serializers.CharField(source='post.title', read_only=True)
    post_author = serializers.CharField(source='post.author.username', read_only=True)
    post_excerpt = serializers.CharField(source='post.excerpt', read_only=True)
    post_featured_image = serializers.SerializerMethodField()
    collection_name = serializers.CharField(source='collection.name', read_only=True)
    
    class Meta:
        model = Bookmark
        fields = [
            'id', 'post', 'post_title', 'post_author', 'post_excerpt',
            'post_featured_image', 'collection', 'collection_name',
            'notes', 'created_at'
        ]
    
    def get_post_featured_image(self, obj):
        if obj.post.featured_image:
            return obj.post.featured_image.url
        return None

class ReadingListSerializer(serializers.ModelSerializer):
    posts_count = serializers.SerializerMethodField()
    read_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ReadingList
        fields = [
            'id', 'name', 'description', 'is_public',
            'posts_count', 'read_count', 'created_at', 'updated_at'
        ]
    
    def get_posts_count(self, obj):
        return obj.readinglistitem_set.count()
    
    def get_read_count(self, obj):
        return obj.readinglistitem_set.filter(is_read=True).count()

class ReadingListItemSerializer(serializers.ModelSerializer):
    post_title = serializers.CharField(source='post.title', read_only=True)
    post_author = serializers.CharField(source='post.author.username', read_only=True)
    post_excerpt = serializers.CharField(source='post.excerpt', read_only=True)
    post_reading_time = serializers.IntegerField(source='post.reading_time', read_only=True)
    
    class Meta:
        model = ReadingListItem
        fields = [
            'id', 'post', 'post_title', 'post_author', 'post_excerpt',
            'post_reading_time', 'added_at', 'is_read', 'read_at'
        ]

class NotificationSerializer(serializers.ModelSerializer):
    sender_username = serializers.CharField(source='sender.username', read_only=True)
    sender_avatar = serializers.SerializerMethodField()
    type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    time_ago = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'type_display', 'title', 'message',
            'sender_username', 'sender_avatar', 'related_post', 'is_read',
            'is_seen', 'created_at', 'time_ago'
        ]
    
    def get_sender_avatar(self, obj):
        if obj.sender:
            try:
                profile = obj.sender.profile
                return profile.avatar.url if profile.avatar else None
            except UserProfile.DoesNotExist:
                pass
        return None
    
    def get_time_ago(self, obj):
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff < timedelta(minutes=1):
            return 'Just now'
        elif diff < timedelta(hours=1):
            return f'{diff.seconds // 60}m ago'
        elif diff < timedelta(days=1):
            return f'{diff.seconds // 3600}h ago'
        elif diff < timedelta(days=7):
            return f'{diff.days}d ago'
        else:
            return obj.created_at.strftime('%b %d')

class UserActivitySerializer(serializers.ModelSerializer):
    username = serializers.CharField(source='user.username', read_only=True)
    user_avatar = serializers.SerializerMethodField()
    type_display = serializers.CharField(source='get_activity_type_display', read_only=True)
    related_post_title = serializers.CharField(source='related_post.title', read_only=True)
    related_user_username = serializers.CharField(source='related_user.username', read_only=True)
    time_ago = serializers.SerializerMethodField()
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'username', 'user_avatar', 'activity_type', 'type_display',
            'description', 'related_post', 'related_post_title',
            'related_user', 'related_user_username', 'metadata',
            'created_at', 'time_ago'
        ]
    
    def get_user_avatar(self, obj):
        try:
            profile = obj.user.profile
            return profile.avatar.url if profile.avatar else None
        except UserProfile.DoesNotExist:
            return None
    
    def get_time_ago(self, obj):
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - obj.created_at
        
        if diff < timedelta(minutes=1):
            return 'Just now'
        elif diff < timedelta(hours=1):
            return f'{diff.seconds // 60}m ago'
        elif diff < timedelta(days=1):
            return f'{diff.seconds // 3600}h ago'
        else:
            return f'{diff.days}d ago'

class ReportSerializer(serializers.ModelSerializer):
    reporter_username = serializers.CharField(source='reporter.username', read_only=True)
    type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reported_user_username = serializers.CharField(source='reported_user.username', read_only=True)
    reported_post_title = serializers.CharField(source='reported_post.title', read_only=True)

    class Meta:
        model = Report
        fields = [
            'id', 'reporter', 'reporter_username', 'report_type', 'type_display',
            'description', 'reported_user', 'reported_user_username',
            'reported_post', 'reported_post_title', 'status', 'status_display',
            'created_at', 'reviewed_at', 'resolution_notes'
        ]
        read_only_fields = ['id', 'created_at', 'reviewed_at']

# Simplified serializers for API responses
class UserSummarySerializer(serializers.ModelSerializer):
    profile = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'profile']
    
    def get_profile(self, obj):
        try:
            profile = obj.profile
            return {
                'avatar': profile.avatar.url if profile.avatar else None,
                'bio': profile.bio,
                'is_verified': profile.is_verified,
            }
        except UserProfile.DoesNotExist:
            return None

class SearchResultSerializer(serializers.Serializer):
    """Serializer for search results"""
    posts = serializers.ListField(child=serializers.DictField())
    users = serializers.ListField(child=serializers.DictField())
    total_posts = serializers.IntegerField()
    total_users = serializers.IntegerField()
    query = serializers.CharField()
    filters = serializers.DictField()

class SearchSuggestionSerializer(serializers.Serializer):
    """Serializer for search suggestions"""
    type = serializers.CharField()
    text = serializers.CharField()
    query = serializers.CharField()
