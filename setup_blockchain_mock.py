#!/usr/bin/env python3
"""
Mock blockchain setup script for Trendy app development
This creates all the database records needed for blockchain functionality without actual deployment
"""

import os
import sys
import django
from decimal import Decimal

# Add the Django project to the Python path
import pathlib
script_dir = pathlib.Path(__file__).parent.absolute()
django_dir = script_dir / 'trendy_web_and_api' / 'trendy'
sys.path.append(str(django_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')

# Setup Django
django.setup()

from django.conf import settings
from django.utils import timezone
from datetime import timedelta
from blockchain.models import BlockchainNetwork, SmartContract, StakingPool

# Contract ABIs (simplified for demo)
TRENDY_TOKEN_ABI = [
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol", 
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "transfer",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    }
]

TRENDY_ACHIEVEMENTS_ABI = [
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "string", "name": "achievementName", "type": "string"},
            {"internalType": "uint256", "name": "rarity", "type": "uint256"}
        ],
        "name": "mintAchievement",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    }
]

class MockBlockchainSetup:
    def __init__(self):
        self.network_name = 'polygon_testnet'
    
    def setup_blockchain_network(self):
        """Setup blockchain network record"""
        print("🌐 Setting up blockchain network...")
        
        network_config = settings.BLOCKCHAIN_NETWORKS[self.network_name]
        
        network, created = BlockchainNetwork.objects.get_or_create(
            name=self.network_name,
            defaults={
                'chain_id': network_config['chain_id'],
                'rpc_url': network_config['rpc_url'],
                'explorer_url': network_config['explorer_url'],
                'native_token': network_config['native_token'],
                'gas_price_gwei': network_config['gas_price_gwei'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created blockchain network: {network.get_name_display()}")
        else:
            print(f"ℹ️  Blockchain network already exists: {network.get_name_display()}")
        
        return network
    
    def setup_token_contract(self, network):
        """Setup mock TRD token contract"""
        print("🪙 Setting up TRD Token contract...")
        
        # Mock contract address (will be replaced with real address when deployed)
        mock_address = "0x1111111111111111111111111111111111111111"
        
        contract, created = SmartContract.objects.get_or_create(
            network=network,
            contract_type='token',
            name='Trendy Token',
            defaults={
                'address': mock_address,
                'abi': TRENDY_TOKEN_ABI,
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created TRD token contract: {mock_address}")
            print("💡 This is a mock address - replace with real deployment address")
        else:
            print(f"ℹ️  TRD token contract already exists: {contract.address}")
        
        return contract
    
    def setup_nft_contract(self, network):
        """Setup mock NFT achievements contract"""
        print("🖼️  Setting up NFT Achievements contract...")
        
        # Mock contract address (will be replaced with real address when deployed)
        mock_address = "0x2222222222222222222222222222222222222222"
        
        contract, created = SmartContract.objects.get_or_create(
            network=network,
            contract_type='nft',
            name='Trendy Achievements',
            defaults={
                'address': mock_address,
                'abi': TRENDY_ACHIEVEMENTS_ABI,
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created NFT contract: {mock_address}")
            print("💡 This is a mock address - replace with real deployment address")
        else:
            print(f"ℹ️  NFT contract already exists: {contract.address}")
        
        return contract
    
    def setup_staking_pool(self, network, token_contract):
        """Setup default staking pool"""
        print("🏊 Setting up staking pool...")
        
        pool, created = StakingPool.objects.get_or_create(
            network=network,
            name='TRD Staking Pool',
            defaults={
                'contract': token_contract,  # Using token contract as mock staking contract
                'token_contract': token_contract,
                'description': 'Stake your TRD tokens to earn 15% APY rewards',
                'apy_percentage': Decimal('15.00'),  # 15% APY
                'minimum_stake': Decimal('10.00'),   # 10 TRD minimum
                'maximum_stake': Decimal('10000.00'), # 10,000 TRD maximum
                'is_active': True,
                'start_date': timezone.now(),
                'end_date': timezone.now() + timedelta(days=365)  # 1 year
            }
        )
        
        if created:
            print(f"✅ Created staking pool: {pool.name}")
            print(f"📊 APY: {pool.apy_percentage}%")
            print(f"💰 Min Stake: {pool.minimum_stake} TRD")
            print(f"💰 Max Stake: {pool.maximum_stake} TRD")
        else:
            print(f"ℹ️  Staking pool already exists: {pool.name}")
        
        return pool
    
    def setup_all(self):
        """Setup all blockchain components"""
        print("🚀 Setting up Trendy Blockchain (Mock Mode)")
        print("=" * 60)
        print("📝 Note: This creates database records for development")
        print("🔧 Replace mock addresses with real ones after deployment")
        print("=" * 60)
        
        try:
            # Setup network
            network = self.setup_blockchain_network()
            
            # Setup contracts
            token_contract = self.setup_token_contract(network)
            nft_contract = self.setup_nft_contract(network)
            
            # Setup staking pool
            staking_pool = self.setup_staking_pool(network, token_contract)
            
            print("\n" + "=" * 60)
            print("🎉 Mock Blockchain Setup Complete!")
            print("\n📋 Summary:")
            print(f"🌐 Network: {network.get_name_display()}")
            print(f"🪙 Token Contract: {token_contract.address}")
            print(f"🖼️  NFT Contract: {nft_contract.address}")
            print(f"🏊 Staking Pool: {staking_pool.name} ({staking_pool.apy_percentage}% APY)")
            
            print("\n🔧 Next Steps:")
            print("1. Test the blockchain API endpoints")
            print("2. Integrate with automated transaction system")
            print("3. Test with Flutter app")
            print("4. When ready, deploy real contracts and update addresses")
            
            print("\n📚 API Endpoints Available:")
            print("• GET  /api/v1/blockchain/wallet/ - Get user wallet")
            print("• POST /api/v1/blockchain/wallet/create/ - Create wallet")
            print("• GET  /api/v1/blockchain/nfts/ - Get user NFTs")
            print("• GET  /api/v1/blockchain/staking/pools/ - Get staking pools")
            print("• POST /api/v1/blockchain/staking/stake/ - Stake tokens")
            
            print("\n🧪 Test Commands:")
            print("python test_blockchain_integration.py")
            
            return True
            
        except Exception as e:
            print(f"\n❌ Setup failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main setup function"""
    setup = MockBlockchainSetup()
    success = setup.setup_all()
    
    if success:
        print("\n✅ Blockchain setup completed successfully!")
        print("🚀 You can now test blockchain features in development mode")
    else:
        print("\n❌ Setup failed!")

if __name__ == '__main__':
    main()
