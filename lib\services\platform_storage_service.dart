import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Platform-aware storage service that handles secure storage gracefully
class PlatformStorageService {
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    lOptions: LinuxOptions(),
    wOptions: WindowsOptions(),
    mOptions: MacOsOptions(),
  );

  /// Check if secure storage is available on the current platform
  static bool get isSecureStorageAvailable {
    // Secure storage is generally available on mobile platforms
    if (Platform.isAndroid || Platform.isIOS) return true;
    
    // On desktop, it depends on system dependencies
    if (Platform.isMacOS) return true;
    
    // Linux and Windows may have dependency issues
    return false;
  }

  /// Store sensitive data with platform-appropriate security
  static Future<void> setSecureData(String key, String value) async {
    try {
      if (isSecureStorageAvailable) {
        await _secureStorage.write(key: key, value: value);
        if (kDebugMode) {
          print('✅ Stored $key securely using FlutterSecureStorage');
        }
      } else {
        // Fallback to SharedPreferences with warning
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('secure_$key', value);
        if (kDebugMode) {
          print('⚠️ WARNING: Stored $key using SharedPreferences (not secure) on ${Platform.operatingSystem}');
          print('   Consider installing system dependencies for secure storage');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Secure storage failed for $key: $e');
        print('   Falling back to SharedPreferences');
      }
      
      // Fallback to SharedPreferences if secure storage fails
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('secure_$key', value);
      
      if (kDebugMode) {
        print('⚠️ WARNING: Using insecure storage for $key due to system limitations');
      }
    }
  }

  /// Retrieve sensitive data with platform-appropriate security
  static Future<String?> getSecureData(String key) async {
    try {
      if (isSecureStorageAvailable) {
        final value = await _secureStorage.read(key: key);
        if (value != null && kDebugMode) {
          print('✅ Retrieved $key securely from FlutterSecureStorage');
        }
        return value;
      } else {
        // Fallback to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        final value = prefs.getString('secure_$key');
        if (value != null && kDebugMode) {
          print('⚠️ Retrieved $key from SharedPreferences (not secure)');
        }
        return value;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Secure storage read failed for $key: $e');
        print('   Trying SharedPreferences fallback');
      }
      
      // Fallback to SharedPreferences if secure storage fails
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('secure_$key');
    }
  }

  /// Delete sensitive data with platform-appropriate security
  static Future<void> deleteSecureData(String key) async {
    try {
      if (isSecureStorageAvailable) {
        await _secureStorage.delete(key: key);
        if (kDebugMode) {
          print('✅ Deleted $key securely from FlutterSecureStorage');
        }
      } else {
        // Fallback to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('secure_$key');
        if (kDebugMode) {
          print('⚠️ Deleted $key from SharedPreferences');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Secure storage delete failed for $key: $e');
        print('   Trying SharedPreferences fallback');
      }
      
      // Fallback to SharedPreferences if secure storage fails
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('secure_$key');
    }
  }

  /// Store complex objects securely
  static Future<void> setSecureObject(String key, Map<String, dynamic> object) async {
    final jsonString = jsonEncode(object);
    await setSecureData(key, jsonString);
  }

  /// Retrieve complex objects securely
  static Future<Map<String, dynamic>?> getSecureObject(String key) async {
    try {
      final jsonString = await getSecureData(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error parsing secure object for $key: $e');
      }
      return null;
    }
  }

  /// Clear all secure data
  static Future<void> clearAllSecureData() async {
    try {
      if (isSecureStorageAvailable) {
        await _secureStorage.deleteAll();
        if (kDebugMode) {
          print('✅ Cleared all secure data from FlutterSecureStorage');
        }
      } else {
        // Clear SharedPreferences entries with 'secure_' prefix
        final prefs = await SharedPreferences.getInstance();
        final keys = prefs.getKeys().where((key) => key.startsWith('secure_')).toList();
        for (final key in keys) {
          await prefs.remove(key);
        }
        if (kDebugMode) {
          print('⚠️ Cleared ${keys.length} secure entries from SharedPreferences');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing secure data: $e');
      }
      
      // Fallback to clearing SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('secure_')).toList();
      for (final key in keys) {
        await prefs.remove(key);
      }
    }
  }

  /// Get storage type being used
  static String get storageType {
    if (isSecureStorageAvailable) {
      return 'FlutterSecureStorage (Secure)';
    } else {
      return 'SharedPreferences (Fallback - Not Secure)';
    }
  }

  /// Get platform-specific security recommendations
  static String get securityRecommendations {
    if (Platform.isLinux) {
      return 'Install libsecret-1-dev for secure storage: sudo apt-get install libsecret-1-dev';
    } else if (Platform.isWindows) {
      return 'Ensure Windows Credential Manager is available for secure storage';
    } else if (Platform.isAndroid || Platform.isIOS) {
      return 'Secure storage is fully supported on mobile platforms';
    } else {
      return 'Consider platform-specific secure storage solutions';
    }
  }
}
