{% extends 'blog/base.html' %}

{% block title %}Account Settings - Trendy Blog{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Settings Sidebar -->
        <div class="col-lg-3 col-md-4">
            <div class="settings-sidebar">
                <div class="settings-header">
                    <h4><i class="fas fa-cog me-2"></i>Settings</h4>
                </div>
                
                <nav class="settings-nav">
                    <a href="#profile" class="settings-nav-link active" data-tab="profile">
                        <i class="fas fa-user"></i>
                        <span>Profile</span>
                    </a>
                    <a href="#account" class="settings-nav-link" data-tab="account">
                        <i class="fas fa-shield-alt"></i>
                        <span>Account & Security</span>
                    </a>
                    <a href="#notifications" class="settings-nav-link" data-tab="notifications">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                    </a>
                    <a href="#privacy" class="settings-nav-link" data-tab="privacy">
                        <i class="fas fa-lock"></i>
                        <span>Privacy</span>
                    </a>
                    <a href="#preferences" class="settings-nav-link" data-tab="preferences">
                        <i class="fas fa-palette"></i>
                        <span>Preferences</span>
                    </a>
                    <a href="#danger" class="settings-nav-link text-danger" data-tab="danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Danger Zone</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-lg-9 col-md-8">
            <div class="settings-content">
                
                <!-- Profile Tab -->
                <div class="settings-tab active" id="profile-tab">
                    <div class="settings-section">
                        <h3>Profile Information</h3>
                        <p class="text-muted">Update your profile information and how others see you on Trendy.</p>
                        
                        <form method="post" enctype="multipart/form-data" class="settings-form">
                            {% csrf_token %}
                            
                            <!-- Profile Picture -->
                            <div class="profile-picture-section mb-4">
                                <div class="d-flex align-items-center">
                                    <div class="profile-picture-wrapper">
                                        {% if user.avatar %}
                                            <img src="{{ user.avatar.url }}" alt="Profile Picture" class="profile-picture">
                                        {% else %}
                                            <div class="profile-picture-placeholder">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        {% endif %}
                                        <div class="profile-picture-overlay">
                                            <i class="fas fa-camera"></i>
                                        </div>
                                    </div>
                                    <div class="ms-3">
                                        <h5>Profile Picture</h5>
                                        <p class="text-muted mb-2">JPG, PNG or GIF. Max size 2MB.</p>
                                        <input type="file" class="form-control" name="avatar" accept="image/*">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control" name="first_name" value="{{ user.first_name }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control" name="last_name" value="{{ user.last_name }}">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" name="username" value="{{ user.username }}">
                                <div class="form-text">Your unique identifier on Trendy.</div>
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="{{ user.email }}">
                                {% if not user.is_email_verified %}
                                <div class="alert alert-warning mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Your email is not verified. <a href="#" class="alert-link">Resend verification</a>
                                </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label">Bio</label>
                                <textarea class="form-control" name="bio" rows="3" placeholder="Tell us about yourself...">{{ user.bio }}</textarea>
                                <div class="form-text">Brief description for your profile.</div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Location</label>
                                        <input type="text" class="form-control" name="location" value="{{ user.location }}" placeholder="City, Country">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Website</label>
                                        <input type="url" class="form-control" name="website" value="{{ user.website }}" placeholder="https://yourwebsite.com">
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                                <button type="button" class="btn btn-outline-secondary ms-2">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Account & Security Tab -->
                <div class="settings-tab" id="account-tab">
                    <div class="settings-section">
                        <h3>Account & Security</h3>
                        <p class="text-muted">Manage your account security and login preferences.</p>
                        
                        <!-- Change Password -->
                        <div class="security-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>Password</h5>
                                    <p class="text-muted mb-0">Last changed 3 months ago</p>
                                </div>
                                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                    Change Password
                                </button>
                            </div>
                        </div>

                        <!-- Two-Factor Authentication -->
                        <div class="security-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>Two-Factor Authentication</h5>
                                    <p class="text-muted mb-0">Add an extra layer of security to your account</p>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="twoFactorSwitch">
                                    <label class="form-check-label" for="twoFactorSwitch">Enable</label>
                                </div>
                            </div>
                        </div>

                        <!-- Login Sessions -->
                        <div class="security-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>Active Sessions</h5>
                                    <p class="text-muted mb-0">Manage devices that are signed into your account</p>
                                </div>
                                <button class="btn btn-outline-secondary">View Sessions</button>
                            </div>
                        </div>

                        <!-- Download Data -->
                        <div class="security-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>Download Your Data</h5>
                                    <p class="text-muted mb-0">Get a copy of your data in a machine readable format</p>
                                </div>
                                <button class="btn btn-outline-info">Request Download</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div class="settings-tab" id="notifications-tab">
                    <div class="settings-section">
                        <h3>Notification Preferences</h3>
                        <p class="text-muted">Choose what notifications you want to receive and how.</p>
                        
                        <form class="settings-form">
                            {% csrf_token %}
                            
                            <div class="notification-group">
                                <h5>Email Notifications</h5>
                                
                                <div class="notification-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>New Comments</strong>
                                            <p class="text-muted mb-0">When someone comments on your posts</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="emailComments" checked>
                                        </div>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>New Likes</strong>
                                            <p class="text-muted mb-0">When someone likes your posts or comments</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="emailLikes" checked>
                                        </div>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>New Followers</strong>
                                            <p class="text-muted mb-0">When someone starts following you</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="emailFollowers" checked>
                                        </div>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Weekly Digest</strong>
                                            <p class="text-muted mb-0">Summary of your activity and trending posts</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="emailDigest">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="notification-group">
                                <h5>Push Notifications</h5>
                                
                                <div class="notification-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Real-time Notifications</strong>
                                            <p class="text-muted mb-0">Instant notifications for comments and likes</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="pushRealtime">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Preferences
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Privacy Tab -->
                <div class="settings-tab" id="privacy-tab">
                    <div class="settings-section">
                        <h3>Privacy Settings</h3>
                        <p class="text-muted">Control who can see your content and interact with you.</p>
                        
                        <form class="settings-form">
                            {% csrf_token %}
                            
                            <div class="privacy-group">
                                <h5>Profile Visibility</h5>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="profileVisibility" id="publicProfile" value="public" checked>
                                    <label class="form-check-label" for="publicProfile">
                                        <strong>Public</strong> - Anyone can see your profile and posts
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="profileVisibility" id="friendsProfile" value="friends">
                                    <label class="form-check-label" for="friendsProfile">
                                        <strong>Friends Only</strong> - Only people you follow can see your profile
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="profileVisibility" id="privateProfile" value="private">
                                    <label class="form-check-label" for="privateProfile">
                                        <strong>Private</strong> - Only you can see your profile
                                    </label>
                                </div>
                            </div>

                            <div class="privacy-group">
                                <h5>Contact Information</h5>
                                
                                <div class="privacy-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Show Email Address</strong>
                                            <p class="text-muted mb-0">Allow others to see your email on your profile</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="showEmail">
                                        </div>
                                    </div>
                                </div>

                                <div class="privacy-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Allow Direct Messages</strong>
                                            <p class="text-muted mb-0">Let others send you private messages</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="allowDM" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Preferences Tab -->
                <div class="settings-tab" id="preferences-tab">
                    <div class="settings-section">
                        <h3>Preferences</h3>
                        <p class="text-muted">Customize your Trendy experience.</p>
                        
                        <form class="settings-form">
                            {% csrf_token %}
                            
                            <div class="preference-group">
                                <h5>Appearance</h5>
                                
                                <div class="form-group mb-3">
                                    <label class="form-label">Theme</label>
                                    <select class="form-select">
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="auto">Auto (System)</option>
                                    </select>
                                </div>

                                <div class="form-group mb-3">
                                    <label class="form-label">Language</label>
                                    <select class="form-select">
                                        <option value="en">English</option>
                                        <option value="es">Español</option>
                                        <option value="fr">Français</option>
                                        <option value="de">Deutsch</option>
                                    </select>
                                </div>
                            </div>

                            <div class="preference-group">
                                <h5>Content</h5>
                                
                                <div class="form-group mb-3">
                                    <label class="form-label">Posts per page</label>
                                    <select class="form-select">
                                        <option value="10">10 posts</option>
                                        <option value="20" selected>20 posts</option>
                                        <option value="50">50 posts</option>
                                    </select>
                                </div>

                                <div class="preference-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>Auto-play Videos</strong>
                                            <p class="text-muted mb-0">Automatically play videos in your feed</p>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="autoplayVideos" checked>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Preferences
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Danger Zone Tab -->
                <div class="settings-tab" id="danger-tab">
                    <div class="settings-section">
                        <h3 class="text-danger">Danger Zone</h3>
                        <p class="text-muted">Irreversible and destructive actions.</p>
                        
                        <div class="danger-zone">
                            <!-- Deactivate Account -->
                            <div class="danger-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5>Deactivate Account</h5>
                                        <p class="text-muted mb-0">Temporarily disable your account. You can reactivate it anytime.</p>
                                    </div>
                                    <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#deactivateModal">
                                        Deactivate
                                    </button>
                                </div>
                            </div>

                            <!-- Delete Account -->
                            <div class="danger-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="text-danger">Delete Account</h5>
                                        <p class="text-muted mb-0">Permanently delete your account and all associated data. This cannot be undone.</p>
                                    </div>
                                    <button class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                                        Delete Account
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    {% csrf_token %}
                    <div class="form-group mb-3">
                        <label class="form-label">Current Password</label>
                        <input type="password" class="form-control" name="current_password" required>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">New Password</label>
                        <input type="password" class="form-control" name="new_password" required>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" name="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="changePasswordForm" class="btn btn-primary">Change Password</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title text-danger">Delete Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>Deleting your account will:</p>
                <ul>
                    <li>Permanently delete all your posts and comments</li>
                    <li>Remove all your likes and interactions</li>
                    <li>Delete your profile and personal information</li>
                    <li>Cancel any active subscriptions</li>
                </ul>
                
                <form id="deleteAccountForm">
                    {% csrf_token %}
                    <div class="form-group mb-3">
                        <label class="form-label">Type "DELETE" to confirm:</label>
                        <input type="text" class="form-control" name="confirmation" placeholder="DELETE" required>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Enter your password:</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger">Delete My Account</button>
            </div>
        </div>
    </div>
</div>

<style>
.settings-sidebar {
    background: white;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 100px;
}

.settings-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.settings-header h4 {
    margin: 0;
    color: var(--text-color);
}

.settings-nav {
    padding: 10px 0;
}

.settings-nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.settings-nav-link:hover {
    background: #f8f9fa;
    color: var(--primary-color);
}

.settings-nav-link.active {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.settings-nav-link i {
    width: 20px;
    margin-right: 12px;
}

.settings-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.settings-section h3 {
    color: var(--text-color);
    margin-bottom: 8px;
}

.profile-picture-wrapper {
    position: relative;
    width: 80px;
    height: 80px;
}

.profile-picture {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-picture-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #6c757d;
}

.profile-picture-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.profile-picture-wrapper:hover .profile-picture-overlay {
    opacity: 1;
}

.security-item,
.notification-item,
.privacy-item,
.preference-item,
.danger-item {
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 16px;
}

.notification-group,
.privacy-group,
.preference-group {
    margin-bottom: 30px;
}

.notification-group h5,
.privacy-group h5,
.preference-group h5 {
    color: var(--text-color);
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.danger-zone {
    border: 2px solid #dc3545;
    border-radius: 8px;
    padding: 20px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .settings-sidebar {
        position: static;
        margin-bottom: 20px;
    }
    
    .settings-nav {
        display: flex;
        overflow-x: auto;
        padding: 10px;
    }
    
    .settings-nav-link {
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
    }
    
    .settings-nav-link.active {
        border-left: none;
        border-bottom-color: var(--primary-color);
    }
}
</style>

<script>
// Tab switching
document.querySelectorAll('.settings-nav-link').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all links and tabs
        document.querySelectorAll('.settings-nav-link').forEach(l => l.classList.remove('active'));
        document.querySelectorAll('.settings-tab').forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked link
        this.classList.add('active');
        
        // Show corresponding tab
        const tabId = this.getAttribute('data-tab') + '-tab';
        document.getElementById(tabId).classList.add('active');
    });
});

// Delete account form validation
document.getElementById('deleteAccountForm').addEventListener('submit', function(e) {
    const confirmation = this.querySelector('input[name="confirmation"]').value;
    if (confirmation !== 'DELETE') {
        e.preventDefault();
        alert('Please type "DELETE" to confirm account deletion.');
    }
});
</script>
{% endblock %}
