import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';
import '../providers/community_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/enhanced_login_dialog.dart';
import 'user_profile_screen.dart';

class CommunityScreen extends ConsumerStatefulWidget {
  const CommunityScreen({super.key});

  @override
  ConsumerState<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends ConsumerState<CommunityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Add listener to refresh data when tab changes
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _refreshCurrentTabData();
      }
    });

    // Load community data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(communityProvider.notifier).refreshAllData();
      }
    });
  }

  void _refreshCurrentTabData() {
    if (!mounted) return;

    switch (_tabController.index) {
      case 0: // Discover
        ref.read(communityProvider.notifier).loadDiscoverUsers();
        ref.read(communityProvider.notifier).loadTrendingUsers();
        break;
      case 1: // Following
        ref.read(communityProvider.notifier).loadFollowing();
        break;
      case 2: // Followers
        ref.read(communityProvider.notifier).loadFollowers();
        break;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final communityState = ref.watch(communityProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          '👥 Community',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        bottom: authState.isAuthenticated ? TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondary,
          indicatorColor: AppTheme.primaryColor,
          tabs: const [
            Tab(text: 'Discover'),
            Tab(text: 'Following'),
            Tab(text: 'Followers'),
          ],
        ) : null,
      ),
      body: authState.isAuthenticated
          ? _buildAuthenticatedContent(communityState)
          : _buildUnauthenticatedContent(),
    );
  }

  Widget _buildAuthenticatedContent(CommunityState communityState) {
    return Column(
      children: [
        // Search bar
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search users...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: AppTheme.backgroundColor,
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                ref.read(communityProvider.notifier).searchUsers(value);
              }
            },
          ),
        ),
        
        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildDiscoverTab(communityState),
              _buildFollowingTab(communityState),
              _buildFollowersTab(communityState),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUnauthenticatedContent() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.people_rounded,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Join the Community',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Connect with other readers, follow your favorite writers, and discover amazing content together.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: ElevatedButton(
                onPressed: () {
                  showEnhancedLoginDialog(
                    context,
                    action: 'join the community',
                    title: 'Join Trendy Community',
                    subtitle: 'Connect with readers and writers worldwide',
                    icon: Icons.people_rounded,
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Sign In to Join',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoverTab(CommunityState communityState) {
    if (communityState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: () async {
        await Future.wait([
          ref.read(communityProvider.notifier).loadDiscoverUsers(),
          ref.read(communityProvider.notifier).loadTrendingUsers(),
        ]);
      },
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          if (communityState.trendingUsers.isNotEmpty) ...[
            _buildSectionHeader('🔥 Trending Users'),
            const SizedBox(height: 12),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: communityState.trendingUsers.length,
                itemBuilder: (context, index) {
                  final user = communityState.trendingUsers[index];
                  return _buildTrendingUserCard(user);
                },
              ),
            ),
            const SizedBox(height: 24),
          ],

          _buildSectionHeader('💫 Discover New People'),
          const SizedBox(height: 12),
          ...communityState.discoverUsers.map((user) => _buildUserCard(user)),
        ],
      ),
    );
  }

  Widget _buildFollowingTab(CommunityState communityState) {
    if (communityState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (communityState.following.isEmpty) {
      return _buildEmptyState(
        icon: Icons.person_add_rounded,
        title: 'No Following Yet',
        subtitle: 'Start following users to see them here',
        actionText: 'Discover Users',
        onAction: () => _tabController.animateTo(0),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(communityProvider.notifier).loadFollowing(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: communityState.following.length,
        itemBuilder: (context, index) {
          final user = communityState.following[index];
          return _buildUserCard(user);
        },
      ),
    );
  }

  Widget _buildFollowersTab(CommunityState communityState) {
    if (communityState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (communityState.followers.isEmpty) {
      return _buildEmptyState(
        icon: Icons.people_outline_rounded,
        title: 'No Followers Yet',
        subtitle: 'Share great content to attract followers',
        actionText: 'View Profile',
        onAction: () => Navigator.pushNamed(context, '/profile'),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(communityProvider.notifier).loadFollowers(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: communityState.followers.length,
        itemBuilder: (context, index) {
          final user = communityState.followers[index];
          return _buildUserCard(user);
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppTheme.textPrimary,
      ),
    );
  }

  Widget _buildTrendingUserCard(dynamic user) {
    return Container(
      width: 100,
      margin: const EdgeInsets.only(right: 12),
      child: Column(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: AppTheme.primaryColor,
            child: Text(
              (user['username'] ?? '')[0].toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            user['username'] ?? '',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            '${user['followers_count'] ?? 0} followers',
            style: TextStyle(
              color: AppTheme.textTertiary,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(dynamic user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: AppTheme.primaryColor,
            child: Text(
              (user['username'] ?? '')[0].toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user['username'] ?? '',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                if (user['bio'] != null && (user['bio'] as String).isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    user['bio'] ?? '',
                    style: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '${user['followers_count'] ?? 0} followers',
                      style: TextStyle(
                        color: AppTheme.textTertiary,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${user['posts_count'] ?? 0} posts',
                      style: TextStyle(
                        color: AppTheme.textTertiary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Column(
            children: [
              ElevatedButton(
                onPressed: () {
                  ref.read(communityProvider.notifier).toggleFollow(user['username'] ?? '');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: (user['is_following'] ?? false) == true
                      ? Colors.grey[200]
                      : AppTheme.primaryColor,
                  foregroundColor: (user['is_following'] ?? false) == true
                      ? AppTheme.textSecondary
                      : Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Text(
                  (user['is_following'] ?? false) == true ? 'Following' : 'Follow',
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => UserProfileScreen(username: user['username'] ?? ''),
                    ),
                  );
                },
                child: const Text(
                  'View Profile',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required String actionText,
    required VoidCallback onAction,
  }) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppTheme.primaryColor.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onAction,
              child: Text(actionText),
            ),
          ],
        ),
      ),
    );
  }
}
