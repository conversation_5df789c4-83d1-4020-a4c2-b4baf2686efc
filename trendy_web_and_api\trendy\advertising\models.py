from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class AdNetwork(models.Model):
    """Ad networks integrated with the app"""
    NETWORK_TYPES = [
        ('google_admob', 'Google AdMob'),
        ('facebook_audience', 'Facebook Audience Network'),
        ('unity_ads', 'Unity Ads'),
        ('applovin', 'AppLovin'),
        ('ironSource', 'ironSource'),
        ('custom', 'Custom Network'),
    ]
    
    name = models.CharField(max_length=100)
    network_type = models.CharField(max_length=20, choices=NETWORK_TYPES)
    
    # API credentials
    app_id = models.CharField(max_length=200, blank=True)
    api_key = models.CharField(max_length=200, blank=True)
    secret_key = models.CharField(max_length=200, blank=True)
    
    # Revenue sharing
    revenue_share_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('70.00'))
    
    # Settings
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=1)  # 1 = highest priority
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['priority', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.network_type})"


class AdPlacement(models.Model):
    """Define where ads are placed in the app"""
    PLACEMENT_TYPES = [
        ('banner', 'Banner Ad'),
        ('interstitial', 'Interstitial (Full Screen)'),
        ('rewarded_video', 'Rewarded Video'),
        ('native', 'Native Ad'),
        ('sponsored_post', 'Sponsored Post'),
    ]
    
    PLACEMENT_LOCATIONS = [
        ('home_feed_top', 'Home Feed - Top'),
        ('home_feed_middle', 'Home Feed - Between Posts'),
        ('home_feed_bottom', 'Home Feed - Bottom'),
        ('post_detail_top', 'Post Detail - Top'),
        ('post_detail_bottom', 'Post Detail - Bottom'),
        ('comments_section', 'Comments Section'),
        ('profile_page', 'Profile Page'),
        ('rewards_page', 'Rewards Page'),
        ('before_reward_claim', 'Before Reward Claim'),
        ('app_launch', 'App Launch'),
        ('level_up', 'Level Up Screen'),
        ('daily_bonus', 'Daily Bonus Screen'),
    ]
    
    name = models.CharField(max_length=100)
    placement_type = models.CharField(max_length=20, choices=PLACEMENT_TYPES)
    location = models.CharField(max_length=30, choices=PLACEMENT_LOCATIONS)
    
    # Ad network configuration
    ad_networks = models.ManyToManyField(AdNetwork, through='AdNetworkPlacement')
    
    # Display settings
    is_active = models.BooleanField(default=True)
    show_to_premium_users = models.BooleanField(default=False)
    frequency_cap = models.PositiveIntegerField(default=3)  # Max shows per session
    
    # Revenue settings
    estimated_cpm = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('2.00'))
    estimated_ctr = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('1.50'))
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['placement_type', 'location']
        ordering = ['location', 'placement_type']
    
    def __str__(self):
        return f"{self.name} - {self.get_location_display()}"


class AdNetworkPlacement(models.Model):
    """Configuration for specific ad network in specific placement"""
    ad_network = models.ForeignKey(AdNetwork, on_delete=models.CASCADE)
    ad_placement = models.ForeignKey(AdPlacement, on_delete=models.CASCADE)
    
    # Network-specific settings
    ad_unit_id = models.CharField(max_length=200)
    weight = models.PositiveIntegerField(default=100)  # For ad mediation
    
    # Performance tracking
    total_requests = models.PositiveIntegerField(default=0)
    total_impressions = models.PositiveIntegerField(default=0)
    total_clicks = models.PositiveIntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Settings
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['ad_network', 'ad_placement']
    
    def __str__(self):
        return f"{self.ad_network.name} - {self.ad_placement.name}"
    
    @property
    def fill_rate(self):
        """Calculate fill rate percentage"""
        if self.total_requests == 0:
            return 0
        return (self.total_impressions / self.total_requests) * 100
    
    @property
    def ctr(self):
        """Calculate click-through rate"""
        if self.total_impressions == 0:
            return 0
        return (self.total_clicks / self.total_impressions) * 100


class RewardedAd(models.Model):
    """Special rewarded ads that give users points"""
    REWARD_TYPES = [
        ('points', 'Points Reward'),
        ('streak_protection', 'Streak Protection'),
        ('premium_trial', 'Premium Trial'),
        ('tier_unlock_discount', 'Tier Unlock Discount'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    reward_type = models.CharField(max_length=30, choices=REWARD_TYPES)
    
    # Reward details
    points_reward = models.PositiveIntegerField(default=10)
    premium_trial_days = models.PositiveIntegerField(default=0)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    
    # Ad configuration
    ad_placement = models.ForeignKey(AdPlacement, on_delete=models.CASCADE)
    video_duration_seconds = models.PositiveIntegerField(default=30)
    
    # Limits
    max_views_per_user_per_day = models.PositiveIntegerField(default=5)
    max_total_views_per_day = models.PositiveIntegerField(default=1000)
    
    # Revenue
    revenue_per_view = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.05'))
    
    # Status
    is_active = models.BooleanField(default=True)
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-points_reward']
    
    def __str__(self):
        return f"{self.name} - {self.points_reward} points"


class AdImpression(models.Model):
    """Track individual ad impressions"""
    IMPRESSION_TYPES = [
        ('shown', 'Ad Shown'),
        ('clicked', 'Ad Clicked'),
        ('completed', 'Video Completed'),
        ('rewarded', 'Reward Given'),
        ('failed', 'Ad Failed'),
    ]
    
    # Basic info
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ad_impressions', null=True, blank=True)
    ad_placement = models.ForeignKey(AdPlacement, on_delete=models.CASCADE)
    ad_network = models.ForeignKey(AdNetwork, on_delete=models.CASCADE)

    # For anonymous users
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    # Impression details
    impression_type = models.CharField(max_length=20, choices=IMPRESSION_TYPES)
    impression_id = models.CharField(max_length=100, unique=True, default=uuid.uuid4)
    
    # Revenue tracking
    revenue_amount = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.00'))
    currency = models.CharField(max_length=3, default='USD')
    
    # Rewarded ad specific
    rewarded_ad = models.ForeignKey(RewardedAd, on_delete=models.SET_NULL, null=True, blank=True)
    points_awarded = models.PositiveIntegerField(default=0)
    
    # Context
    session_id = models.CharField(max_length=100, blank=True)
    device_info = models.JSONField(default=dict)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['ad_placement', 'impression_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.impression_type} - {self.ad_placement.name}"


class SponsoredPost(models.Model):
    """Sponsored content that appears in the feed"""
    SPONSOR_TYPES = [
        ('brand', 'Brand Sponsorship'),
        ('app', 'App Promotion'),
        ('service', 'Service Promotion'),
        ('affiliate', 'Affiliate Marketing'),
    ]
    
    POST_STATUS = [
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('expired', 'Expired'),
    ]
    
    # Sponsor details
    sponsor_name = models.CharField(max_length=200)
    sponsor_type = models.CharField(max_length=20, choices=SPONSOR_TYPES)
    sponsor_logo = models.URLField(blank=True)
    
    # Content
    title = models.CharField(max_length=200)
    content = models.TextField()
    image_url = models.URLField(blank=True)
    video_url = models.URLField(blank=True)
    call_to_action = models.CharField(max_length=100, default='Learn More')
    target_url = models.URLField()
    
    # Targeting
    target_user_levels = models.JSONField(default=list)  # [1, 2, 3] for levels 1-3
    target_premium_users = models.BooleanField(default=False)
    target_locations = models.JSONField(default=list)  # ['US', 'CA', 'UK']
    
    # Campaign settings
    status = models.CharField(max_length=20, choices=POST_STATUS, default='draft')
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField()
    
    # Budget and pricing
    total_budget = models.DecimalField(max_digits=10, decimal_places=2)
    cost_per_impression = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.01'))
    cost_per_click = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.50'))
    
    # Performance tracking
    total_impressions = models.PositiveIntegerField(default=0)
    total_clicks = models.PositiveIntegerField(default=0)
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Frequency capping
    max_impressions_per_user = models.PositiveIntegerField(default=3)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'start_date']),
            models.Index(fields=['sponsor_type']),
        ]
    
    def __str__(self):
        return f"{self.sponsor_name} - {self.title}"
    
    @property
    def is_active(self):
        """Check if sponsored post is currently active"""
        now = timezone.now()
        return (
            self.status == 'active' and
            self.start_date <= now <= self.end_date and
            self.total_spent < self.total_budget
        )
    
    @property
    def ctr(self):
        """Calculate click-through rate"""
        if self.total_impressions == 0:
            return 0
        return (self.total_clicks / self.total_impressions) * 100


class AdSettings(models.Model):
    """Global advertising settings"""
    # General settings
    ads_enabled = models.BooleanField(default=True)
    show_ads_to_premium = models.BooleanField(default=False)
    
    # Frequency settings
    max_ads_per_session = models.PositiveIntegerField(default=10)
    min_time_between_ads = models.PositiveIntegerField(default=30)  # seconds
    
    # Rewarded ads settings
    rewarded_ads_enabled = models.BooleanField(default=True)
    max_rewarded_ads_per_day = models.PositiveIntegerField(default=5)
    rewarded_ad_cooldown = models.PositiveIntegerField(default=300)  # seconds
    
    # Revenue settings
    revenue_share_with_users = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('10.00'))
    minimum_payout_threshold = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('1.00'))
    
    # Sponsored content settings
    sponsored_posts_enabled = models.BooleanField(default=True)
    sponsored_post_frequency = models.PositiveIntegerField(default=5)  # Every N posts
    
    # Targeting settings
    enable_user_targeting = models.BooleanField(default=True)
    enable_behavioral_targeting = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Ad Settings"
        verbose_name_plural = "Ad Settings"
    
    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and AdSettings.objects.exists():
            raise ValueError("Only one AdSettings instance is allowed")
        super().save(*args, **kwargs)
    
    @classmethod
    def get_settings(cls):
        """Get or create the single settings instance"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings
