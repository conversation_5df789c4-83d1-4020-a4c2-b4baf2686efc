import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/maintenance_provider.dart';
import '../screens/maintenance_screen.dart';

/// Wrapper widget that checks maintenance status and shows maintenance screen if needed
class MaintenanceWrapper extends ConsumerWidget {
  final Widget child;
  final bool checkOnBuild;

  const MaintenanceWrapper({
    super.key,
    required this.child,
    this.checkOnBuild = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!checkOnBuild) {
      return child;
    }

    final isMaintenanceActive = ref.watch(maintenanceStatusProvider);
    
    if (isMaintenanceActive) {
      return const MaintenanceScreen();
    }
    
    return child;
  }
}

/// Feature-aware widget that checks if a feature is enabled
class FeatureWrapper extends ConsumerWidget {
  final String featureName;
  final Widget child;
  final Widget? disabledChild;
  final String? customDisabledMessage;

  const FeatureWrapper({
    super.key,
    required this.featureName,
    required this.child,
    this.disabledChild,
    this.customDisabledMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isFeatureEnabled = ref.watch(featureStatusProvider(featureName));
    
    if (!isFeatureEnabled) {
      return disabledChild ?? _buildDisabledWidget(context);
    }
    
    return child;
  }

  Widget _buildDisabledWidget(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.block_rounded,
            color: Colors.grey,
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            'Feature Unavailable',
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.grey,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            customDisabledMessage ?? 
            'This feature is temporarily unavailable.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Button that checks feature status before allowing action
class FeatureAwareButton extends ConsumerWidget {
  final String featureName;
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final String? disabledMessage;

  const FeatureAwareButton({
    super.key,
    required this.featureName,
    required this.onPressed,
    required this.child,
    this.style,
    this.disabledMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isFeatureEnabled = ref.watch(featureStatusProvider(featureName));
    
    return ElevatedButton(
      onPressed: isFeatureEnabled ? onPressed : () => _showDisabledDialog(context),
      style: style,
      child: child,
    );
  }

  void _showDisabledDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.block_rounded, color: Colors.orange),
        title: const Text('Feature Unavailable'),
        content: Text(
          disabledMessage ?? 
          'This feature is temporarily unavailable. Please try again later.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// FloatingActionButton that checks feature status
class FeatureAwareFloatingActionButton extends ConsumerWidget {
  final String featureName;
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final String? disabledMessage;

  const FeatureAwareFloatingActionButton({
    super.key,
    required this.featureName,
    required this.onPressed,
    this.child,
    this.tooltip,
    this.disabledMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isFeatureEnabled = ref.watch(featureStatusProvider(featureName));
    
    return FloatingActionButton(
      onPressed: isFeatureEnabled ? onPressed : () => _showDisabledSnackBar(context),
      tooltip: tooltip,
      child: child,
    );
  }

  void _showDisabledSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          disabledMessage ?? 
          'This feature is temporarily unavailable.',
        ),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}

/// Mixin for screens that need maintenance checking
mixin MaintenanceAware<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  
  @override
  void initState() {
    super.initState();
    // Check maintenance status when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkMaintenanceStatus();
    });
  }

  void _checkMaintenanceStatus() {
    final isMaintenanceActive = ref.read(maintenanceStatusProvider);
    if (isMaintenanceActive) {
      // Navigate to maintenance screen or show dialog
      _handleMaintenanceMode();
    }
  }

  void _handleMaintenanceMode() {
    // Override this method in implementing classes
    // Default behavior: show dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.build_rounded, color: Colors.orange),
        title: const Text('System Maintenance'),
        content: const Text(
          'The system is currently under maintenance. Some features may be unavailable.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Check if a feature is enabled
  bool isFeatureEnabled(String featureName) {
    return ref.read(featureStatusProvider(featureName));
  }

  /// Show feature disabled message
  void showFeatureDisabled(String featureName, [String? customMessage]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          customMessage ?? 'This feature is temporarily unavailable.',
        ),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
