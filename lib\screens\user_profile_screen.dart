import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/auth_provider.dart';
import '../providers/community_provider.dart';
import '../providers/messaging_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/post_preview_card.dart';
import '../models/user.dart';
import 'report_user_screen.dart';
import 'chat_screen.dart';

class UserProfileScreen extends ConsumerStatefulWidget {
  final String username;

  const UserProfileScreen({
    super.key,
    required this.username,
  });

  @override
  ConsumerState<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends ConsumerState<UserProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load user profile data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ref.read(communityProvider.notifier).loadUserProfile(widget.username);
        ref.read(communityProvider.notifier).loadUserPosts(widget.username);
        ref.read(communityProvider.notifier).loadUserFollowers(widget.username);
        ref.read(communityProvider.notifier).loadUserFollowing(widget.username);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final communityState = ref.watch(communityProvider);
    final userProfile = communityState.selectedUserProfile;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: userProfile == null
          ? const Center(child: CircularProgressIndicator())
          : CustomScrollView(
              slivers: [
                _buildSliverAppBar(userProfile, authState),
                SliverToBoxAdapter(
                  child: _buildProfileInfo(userProfile),
                ),
                SliverToBoxAdapter(
                  child: _buildTabBar(),
                ),
                SliverFillRemaining(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildPostsTab(communityState),
                      _buildFollowersTab(communityState),
                      _buildFollowingTab(communityState),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildSliverAppBar(UserProfile? userProfile, AuthState authState) {
    final isOwnProfile = authState.user?.username == widget.username;

    // Extract data from UserProfile
    final String? avatarUrl = userProfile?.effectiveAvatarUrl;
    final bool hasValidAvatar =
        avatarUrl != null && avatarUrl.isNotEmpty && avatarUrl != 'file:///';
    final String username = userProfile?.username ?? 'Unknown';

    return SliverAppBar(
      expandedHeight: 220, // Increased from 200 to accommodate content
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 24), // Further reduced from 32
                  CircleAvatar(
                    radius: 44, // Further reduced from 48
                    backgroundColor: Colors.white,
                    child: CircleAvatar(
                      radius: 41, // Adjusted accordingly
                      backgroundColor: AppTheme.primaryColor,
                      backgroundImage:
                          hasValidAvatar ? NetworkImage(avatarUrl) : null,
                      child: !hasValidAvatar
                          ? Text(
                              username.isNotEmpty
                                  ? username[0].toUpperCase()
                                  : 'U',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 28, // Further reduced from 30
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                  ),
                  const SizedBox(height: 8), // Further reduced from 10
                  Flexible(
                    child: Text(
                      username,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20, // Further reduced from 22
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (userProfile?.isEmailVerified ?? false) ...[
                    const SizedBox(height: 2), // Reduced from 4
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.verified,
                            color: Colors.white,
                            size: 12, // Further reduced from 14
                          ),
                          const SizedBox(width: 3), // Reduced from 4
                          Flexible(
                            child: Text(
                              'Verified',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10, // Further reduced from 11
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  const SizedBox(height: 4), // Reduced from 8
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        if (!isOwnProfile && authState.isAuthenticated)
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () {
              if (userProfile != null) {
                _showUserActions(userProfile);
              }
            },
          ),
      ],
    );
  }

  Widget _buildProfileInfo(UserProfile? userProfile) {
    final authState = ref.watch(enhancedAuthProvider);
    final isOwnProfile = authState.user?.username == widget.username;

    // Extract data from UserProfile
    final String? bio = userProfile?.bio;
    final int postsCount = userProfile?.postsCount ?? 0;
    final int followersCount = userProfile?.followersCount ?? 0;
    final int followingCount = userProfile?.followingCount ?? 0;

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bio
          if (bio != null && bio.isNotEmpty) ...[
            Text(
              bio,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.textSecondary,
                    height: 1.4,
                  ),
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),
          ],

          // Stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(child: _buildStatItem('Posts', postsCount)),
              Expanded(child: _buildStatItem('Followers', followersCount)),
              Expanded(child: _buildStatItem('Following', followingCount)),
            ],
          ),
          const SizedBox(height: 20),

          // Action buttons
          if (!isOwnProfile && authState.isAuthenticated) ...[
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      ref
                          .read(communityProvider.notifier)
                          .toggleFollow(widget.username);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: (userProfile?.isFollowing ?? false)
                          ? Colors.grey[200]
                          : AppTheme.primaryColor,
                      foregroundColor: (userProfile?.isFollowing ?? false)
                          ? AppTheme.textSecondary
                          : Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      (userProfile?.isFollowing ?? false)
                          ? 'Following'
                          : 'Follow',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: OutlinedButton(
                    onPressed: () => _startConversation(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Message',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
          ] else if (isOwnProfile) ...[
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/profile/edit');
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Edit Profile'),
              ),
            ),
          ],

          // Additional info
          if (userProfile?.location != null &&
              userProfile!.location!.isNotEmpty) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(
                  Icons.location_on_outlined,
                  size: 16,
                  color: AppTheme.textTertiary,
                ),
                const SizedBox(width: 8),
                Text(
                  userProfile.location!,
                  style: const TextStyle(
                    color: AppTheme.textTertiary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],

          if (userProfile?.website != null &&
              userProfile!.website!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.link,
                  size: 16,
                  color: AppTheme.textTertiary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    userProfile.website!,
                    style: const TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: 14,
                      decoration: TextDecoration.underline,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],

          if (userProfile?.dateJoined != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.calendar_today_outlined,
                  size: 16,
                  color: AppTheme.textTertiary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Joined ${_formatDate(userProfile!.dateJoined)}',
                  style: const TextStyle(
                    color: AppTheme.textTertiary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textTertiary,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: AppTheme.textSecondary,
        indicatorColor: AppTheme.primaryColor,
        tabs: const [
          Tab(text: 'Posts'),
          Tab(text: 'Followers'),
          Tab(text: 'Following'),
        ],
      ),
    );
  }

  Widget _buildPostsTab(CommunityState communityState) {
    if (communityState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (communityState.userPosts.isEmpty) {
      return _buildEmptyState(
        icon: Icons.article_outlined,
        title: 'No Posts Yet',
        subtitle: 'This user hasn\'t shared any posts yet.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: communityState.userPosts.length,
      itemBuilder: (context, index) {
        final post = communityState.userPosts[index];
        return PostPreviewCard(post: post);
      },
    );
  }

  Widget _buildFollowersTab(CommunityState communityState) {
    if (communityState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (communityState.userFollowers.isEmpty) {
      return _buildEmptyState(
        icon: Icons.people_outline,
        title: 'No Followers',
        subtitle: 'This user doesn\'t have any followers yet.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: communityState.userFollowers.length,
      itemBuilder: (context, index) {
        final user = communityState.userFollowers[index];
        return _buildUserListItem(user);
      },
    );
  }

  Widget _buildFollowingTab(CommunityState communityState) {
    if (communityState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (communityState.userFollowing.isEmpty) {
      return _buildEmptyState(
        icon: Icons.person_add_outlined,
        title: 'Not Following Anyone',
        subtitle: 'This user isn\'t following anyone yet.',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: communityState.userFollowing.length,
      itemBuilder: (context, index) {
        final user = communityState.userFollowing[index];
        return _buildUserListItem(user);
      },
    );
  }

  Widget _buildUserListItem(dynamic user) {
    // Handle both Map<String, dynamic> and UserProfile objects
    String username = '';
    String? bio;

    if (user is Map<String, dynamic>) {
      username = user['username'] ??
          user['follower_username'] ??
          user['following_username'] ??
          'Unknown';
      bio = user['bio'];
    } else if (user is UserProfile) {
      username = user.username;
      bio = user.bio;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppTheme.primaryColor,
            child: Text(
              username.isNotEmpty ? username[0].toUpperCase() : 'U',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  username,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (bio != null && bio.isNotEmpty)
                  Text(
                    bio,
                    style: const TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          SizedBox(
            width: 60,
            child: TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => UserProfileScreen(username: username),
                  ),
                );
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              child: const Text(
                'View',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        margin: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppTheme.primaryColor.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimary,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startConversation() async {
    try {
      final userProfile = ref.read(communityProvider).selectedUserProfile;
      if (userProfile == null) return;

      final username = userProfile.username;
      if (username.isEmpty) return;

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final conversation = await ref
          .read(messagingProvider.notifier)
          .startConversation(username);

      // Hide loading indicator
      if (mounted) {
        Navigator.of(context).pop();

        if (conversation != null) {
          // Navigate to chat screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(conversation: conversation),
            ),
          );
        } else {
          // Get the actual error from the messaging provider
          final messagingState = ref.read(messagingProvider);
          final errorMessage =
              messagingState.error ?? 'Failed to start conversation';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to start conversation: $errorMessage'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Hide loading indicator
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting conversation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareProfile(UserProfile userProfile) {
    final username = userProfile.username;
    final fullName = '${userProfile.firstName} ${userProfile.lastName}'.trim();
    final bio = userProfile.bio ?? '';

    String shareText = 'Check out $username\'s profile on Trendy!';
    if (fullName.isNotEmpty) {
      shareText = 'Check out $fullName (@$username)\'s profile on Trendy!';
    }
    if (bio.isNotEmpty) {
      shareText += '\n\n"$bio"';
    }

    // For now, we'll share a generic link. In a real app, you'd have deep linking
    shareText +=
        '\n\nDownload Trendy to connect with amazing content creators!';

    Share.share(
      shareText,
      subject: '$username\'s Profile - Trendy',
    );
  }

  void _showUserActions(UserProfile userProfile) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Profile'),
              onTap: () {
                Navigator.pop(context);
                _shareProfile(userProfile);
              },
            ),
            ListTile(
              leading: const Icon(Icons.report),
              title: const Text('Report User'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ReportUserScreen(
                      user: User(
                        id: userProfile.id,
                        username: userProfile.username,
                        email: userProfile.email ?? '',
                        firstName: userProfile.firstName,
                        lastName: userProfile.lastName,
                        bio: userProfile.bio,
                        avatarUrl: userProfile.avatarUrl,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return '';
    try {
      final date = DateTime.parse(dateString);
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ];
      return '${months[date.month - 1]} ${date.year}';
    } catch (e) {
      return '';
    }
  }
}
