# =============================================================================
# TRENDY BLOG PLATFORM - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and update the values for your environment

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Environment (development/production)
DEBUG=True

# Secret key for Django (generate a new one for production)
SECRET_KEY=django-insecure-your-secret-key-here-change-this-in-production

# Allowed hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1,.onrender.com,.vercel.app,.herokuapp.com,**************,*

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# For Development (SQLite) - leave DATABASE_URL empty
# DATABASE_URL=

# For Production (Neon PostgreSQL) - uncomment and update
# DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Example Neon PostgreSQL URL:
# DATABASE_URL=postgresql://trendy_user:<EMAIL>/trendy_db?sslmode=require

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Email backend
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# SMTP settings (for production)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Default email addresses
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Frontend URL for email links and CORS
FRONTEND_URL=http://localhost:3000

# =============================================================================
# AI WRITING ASSISTANT CONFIGURATION
# =============================================================================

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic Claude API Configuration (alternative)
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# =============================================================================
# BLOCKCHAIN CONFIGURATION
# =============================================================================

# Blockchain admin private key (for smart contract interactions)
BLOCKCHAIN_ADMIN_PRIVATE_KEY=0x1111111111111111111111111111111111111111111111111111111111111111

# Wallet encryption key (base64 encoded)
WALLET_ENCRYPTION_KEY=ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg=

# Default blockchain network
DEFAULT_BLOCKCHAIN_NETWORK=polygon_testnet

# =============================================================================
# PAYPAL CONFIGURATION
# =============================================================================

# PayPal API Credentials (Sandbox)
PAYPAL_CLIENT_ID=your-paypal-sandbox-client-id
PAYPAL_CLIENT_SECRET=your-paypal-sandbox-client-secret
PAYPAL_MODE=sandbox

# PayPal Business Account
PAYPAL_BUSINESS_EMAIL=<EMAIL>
PAYPAL_BUSINESS_NAME=Trendy App LLC

# PayPal Webhook Configuration
PAYPAL_WEBHOOK_ID=your-webhook-id
PAYPAL_WEBHOOK_URL=https://your-domain.com/api/payments/paypal/webhook/

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# SSL/HTTPS settings (set to True in production)
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Redis URL (for production caching)
# REDIS_URL=redis://localhost:6379/0

# Cache backend (db for development, redis for production)
CACHE_BACKEND=django.core.cache.backends.db.DatabaseCache

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API rate limiting
API_THROTTLE_ANON=100/hour
API_THROTTLE_USER=1000/hour

# API pagination
API_PAGE_SIZE=10

# =============================================================================
# THIRD-PARTY SERVICES
# =============================================================================

# AWS S3 (for production media files)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=us-east-1

# Google Analytics (optional)
# GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# =============================================================================
# GAMIFICATION & REWARDS CONFIGURATION
# =============================================================================

# PayPal Rewards Settings
PAYPAL_REWARDS_ENABLED=True
PAYPAL_MINIMUM_PAYOUT=5.00
PAYPAL_MAXIMUM_MONTHLY_PAYOUT=150.00

# Engagement Rewards
ENGAGEMENT_REWARDS_ENABLED=True
MINIMUM_ACCOUNT_AGE_DAYS=14
MINIMUM_ACTIVITY_SCORE=50

# =============================================================================
# VOICE FEATURES CONFIGURATION
# =============================================================================

# Text-to-Speech Settings
TTS_DEFAULT_VOICE_TYPE=female
TTS_DEFAULT_LANGUAGE=en-US
TTS_DEFAULT_SPEECH_RATE=0.5

# Voice Comments Settings
VOICE_COMMENTS_ENABLED=True
VOICE_COMMENTS_MAX_DURATION=300
VOICE_COMMENTS_AUTO_TRANSCRIBE=True

# =============================================================================
# REGIONAL CONTENT CONFIGURATION
# =============================================================================

# Default region settings
DEFAULT_REGION=global
ENABLE_REGIONAL_FILTERING=True
AUTO_DETECT_LOCATION=True

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Django Debug Toolbar (development only)
ENABLE_DEBUG_TOOLBAR=False

# Django Extensions (development only)
ENABLE_DJANGO_EXTENSIONS=False

# Development server settings
DEV_SERVER_HOST=**************
DEV_SERVER_PORT=8000

# =============================================================================
# MEDIA & STATIC FILES CONFIGURATION
# =============================================================================

# Media files settings
MEDIA_URL=/media/
MEDIA_ROOT=media

# Static files settings
STATIC_URL=/static/
STATIC_ROOT=static

# =============================================================================
# SOCIAL FEATURES CONFIGURATION
# =============================================================================

# Social authentication (optional)
# GOOGLE_OAUTH2_CLIENT_ID=your-google-client-id
# GOOGLE_OAUTH2_CLIENT_SECRET=your-google-client-secret
# FACEBOOK_APP_ID=your-facebook-app-id
# FACEBOOK_APP_SECRET=your-facebook-app-secret

# Social sharing settings
ENABLE_SOCIAL_SHARING=True
SOCIAL_SHARE_PLATFORMS=facebook,twitter,linkedin,whatsapp

# =============================================================================
# ANALYTICS & TRACKING CONFIGURATION
# =============================================================================

# User analytics
ENABLE_USER_ANALYTICS=True
TRACK_USER_ENGAGEMENT=True
TRACK_READING_TIME=True

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=True

# =============================================================================
# CONTENT MODERATION CONFIGURATION
# =============================================================================

# Auto-moderation settings
ENABLE_AUTO_MODERATION=True
AUTO_APPROVE_POSTS=True
AUTO_APPROVE_COMMENTS=True

# Content filtering
ENABLE_PROFANITY_FILTER=False
ENABLE_SPAM_DETECTION=True

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Email notifications
ENABLE_EMAIL_NOTIFICATIONS=True
NOTIFICATION_EMAIL_BATCH_SIZE=50

# Push notifications (for mobile app)
# FIREBASE_SERVER_KEY=your-firebase-server-key
# FIREBASE_SENDER_ID=your-firebase-sender-id

# =============================================================================
# BACKUP & MAINTENANCE CONFIGURATION
# =============================================================================

# Database backup settings
ENABLE_AUTO_BACKUP=False
BACKUP_RETENTION_DAYS=30

# Maintenance mode
MAINTENANCE_MODE=False
MAINTENANCE_MESSAGE=The site is currently under maintenance. Please check back later.

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature toggles
ENABLE_AI_WRITING=True
ENABLE_BLOCKCHAIN_FEATURES=True
ENABLE_VOICE_FEATURES=True
ENABLE_GAMIFICATION=True
ENABLE_PAYMENTS=True
ENABLE_MESSAGING=True
ENABLE_ADVERTISING=True
ENABLE_REGIONAL_CONTENT=True

# Experimental features
ENABLE_EXPERIMENTAL_FEATURES=False
