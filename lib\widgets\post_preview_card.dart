import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:share_plus/share_plus.dart';
import 'package:trendy/models/post.dart';
import 'package:trendy/models/post_media.dart';
import 'package:trendy/providers/provider.dart' as provider;
import 'package:trendy/providers/auth_provider.dart';
import 'package:trendy/screens/post_detail_screen.dart';
import 'package:trendy/theme/app_theme.dart';
import 'package:trendy/widgets/enhanced_login_dialog.dart';
import 'package:trendy/widgets/post_media_preview.dart';


class PostPreviewCard extends ConsumerWidget {
  final Post post;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final int maxLines;
  final bool showFullContent;

  const PostPreviewCard({
    super.key,
    required this.post,
    this.onLike,
    this.onComment,
    this.maxLines = 3,
    this.showFullContent = false,
  });

  void _showLoginPrompt(BuildContext context, String action) {
    IconData icon;
    String title;
    String subtitle;

    // Customize dialog based on action
    switch (action.toLowerCase()) {
      case 'like this post':
        icon = Icons.favorite_rounded;
        title = 'Like Posts';
        subtitle = 'Sign in to like posts and show your appreciation to authors';
        break;
      case 'comment on this post':
        icon = Icons.chat_bubble_rounded;
        title = 'Join the Conversation';
        subtitle = 'Sign in to comment and engage with the community';
        break;
      default:
        icon = Icons.login_rounded;
        title = 'Login Required';
        subtitle = 'Please sign in to $action';
    }

    showEnhancedLoginDialog(
      context,
      action: action,
      title: title,
      subtitle: subtitle,
      icon: icon,
    );
  }

  void _sharePost(Post post) {
    final shareText = '''
${post.title}

${post.content.length > 200 ? '${post.content.substring(0, 200)}...' : post.content}

Check out this post on Trendy!
''';

    Share.share(
      shareText,
      subject: post.title,
    );
  }

  Widget _buildReadMoreButton(BuildContext context) {
    return Container(
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PostDetailScreen(pk: post.id.toString()),
            ),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Read More',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 6),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 12,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
    bool isActive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: isActive ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 18,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(enhancedAuthProvider);
    final isLoggedIn = authState.isAuthenticated;

    // Determine if content should be truncated
    final shouldTruncate = !showFullContent && post.content.length > 150;
    final displayContent = shouldTruncate 
        ? '${post.content.substring(0, 150)}...'
        : post.content;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            // View count will be tracked in the detail screen
            if (context.mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PostDetailScreen(pk: post.id.toString()),
                ),
              );
            }
          },
          borderRadius: BorderRadius.circular(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
          // Enhanced Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: AppTheme.primaryGradient,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 22,
                    backgroundColor: Colors.transparent,
                    child: Text(
                      post.author.username.isNotEmpty
                          ? post.author.username[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.author.username,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        timeago.format(post.createdAt),
                        style: const TextStyle(
                          color: AppTheme.textTertiary,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: AppTheme.accentGradient,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.accentColor.withOpacity(0.2),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    post.category.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Enhanced Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              post.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimary,
                height: 1.3,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Enhanced Content Preview
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayContent,
                  style: const TextStyle(
                    fontSize: 15,
                    height: 1.6,
                    color: AppTheme.textSecondary,
                  ),
                  maxLines: showFullContent ? null : maxLines,
                  overflow: showFullContent ? null : TextOverflow.ellipsis,
                ),
                if (shouldTruncate)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: _buildReadMoreButton(context),
                  ),
              ],
            ),
          ),

          // Enhanced Media Display
          if (post.mediaItems.isNotEmpty)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: PostMediaPreview(media: post.mediaItems.first),
                ),
              ),
            ),

          // Enhanced Actions
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppTheme.dividerColor,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Like button
                _buildActionButton(
                  icon: post.isLiked ? Icons.favorite : Icons.favorite_border,
                  label: post.likeCount.toString(),
                  color: post.isLiked ? Colors.red : AppTheme.textTertiary,
                  onTap: isLoggedIn ? onLike : () => _showLoginPrompt(context, 'like this post'),
                  isActive: post.isLiked,
                ),
                const SizedBox(width: 20),
                // Comment button
                _buildActionButton(
                  icon: Icons.chat_bubble_outline,
                  label: post.commentCount.toString(),
                  color: AppTheme.textTertiary,
                  onTap: isLoggedIn ? onComment : () => _showLoginPrompt(context, 'comment on this post'),
                ),
                const SizedBox(width: 20),
                // Views
                _buildActionButton(
                  icon: Icons.visibility_outlined,
                  label: post.views.toString(),
                  color: AppTheme.textTertiary,
                  onTap: null,
                ),
                const Spacer(),
                // Share button
                Container(
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.share_outlined,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                    onPressed: () => _sharePost(post),
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 36,
                      minHeight: 36,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
          ),
        ),
      ),
    );
  }
}
