# 🔑 Test User Credentials for Trendy App

## **🚨 IMPORTANT: Default Passwords for Development**

Since the database fixtures contain hashed passwords that can't be retrieved, here are the **recommended default passwords** for development:

## **👤 Admin User**
```
Username: admin
Password: admin123
Email: <EMAIL>
Access: Full admin access + all features
```

## **👥 Test Users**
All test users use the same password for simplicity:

```
Username: sarah_johnson    | Password: password123
Username: mike_chen        | Password: password123  
Username: alex_rivera      | Password: password123
Username: testuser         | Password: password123
```

## **🔧 Setting Up Test Users on New Machines**

### **Option 1: Create Fresh Users (Recommended)**
```bash
cd trendy_web_and_api/trendy
source venv/bin/activate

# Create admin user
python manage.py createsuperuser
# Enter: admin / <EMAIL> / admin123

# Create test users via Django shell
python manage.py shell -c "
from accounts.models import CustomUser

# Create test users with known passwords
test_users = [
    ('sarah_johnson', '<EMAIL>', '<PERSON>', '<PERSON>'),
    ('mike_chen', '<EMAIL>', '<PERSON>', 'Chen'),
    ('alex_rivera', '<EMAIL>', 'Alex', 'Rivera'),
    ('testuser', '<EMAIL>', 'Test', 'User')
]

for username, email, first_name, last_name in test_users:
    user, created = CustomUser.objects.get_or_create(
        username=username,
        defaults={
            'email': email,
            'first_name': first_name,
            'last_name': last_name
        }
    )
    user.set_password('password123')
    user.save()
    print(f'✅ {username} - password123')
"
```

### **Option 2: Reset Existing User Passwords**
```bash
cd trendy_web_and_api/trendy
source venv/bin/activate

python manage.py shell -c "
from accounts.models import CustomUser

# Reset admin password
try:
    admin = CustomUser.objects.get(username='admin')
    admin.set_password('admin123')
    admin.save()
    print('✅ Admin password reset to: admin123')
except CustomUser.DoesNotExist:
    print('❌ Admin user not found')

# Reset test user passwords
test_usernames = ['sarah_johnson', 'mike_chen', 'alex_rivera', 'testuser']
for username in test_usernames:
    try:
        user = CustomUser.objects.get(username=username)
        user.set_password('password123')
        user.save()
        print(f'✅ {username} password reset to: password123')
    except CustomUser.DoesNotExist:
        print(f'❌ {username} not found')
"
```

### **Option 3: Use Django Admin**
1. **Access admin panel**: `http://localhost:8000/admin/`
2. **Login with admin credentials**
3. **Go to Users section**
4. **Click on any user**
5. **Click "Change password"**
6. **Set new password**

## **💰 Wallet Information**

All test users automatically get:
- **Starting balance**: $50.00
- **Daily limit**: $100.00
- **Monthly limit**: $500.00
- **Wallet status**: Active

## **🧪 Testing Features**

### **Login Testing**
```bash
# Test login via API
curl -X POST http://localhost:8000/api/v1/accounts/login/ \
  -H "Content-Type: application/json" \
  -d '{"email_or_username": "admin", "password": "admin123"}'
```

### **Wallet Testing**
```bash
# Check wallet balance (requires auth token)
curl -X GET http://localhost:8000/api/v1/wallet/balance/ \
  -H "Authorization: Token YOUR_TOKEN_HERE"
```

### **Shop Testing**
- **Premium subscription**: $9.99/month
- **Point boost packages**: $1.99 - $19.99
- **All payments**: Wallet-first, PayPal fallback

## **🔄 Quick Reset Script**

Save this as `reset_passwords.py` in the Django directory:

```python
#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from accounts.models import CustomUser

def reset_all_passwords():
    # Admin user
    try:
        admin = CustomUser.objects.get(username='admin')
        admin.set_password('admin123')
        admin.save()
        print('✅ Admin: admin123')
    except CustomUser.DoesNotExist:
        print('❌ Admin user not found')
    
    # Test users
    test_users = ['sarah_johnson', 'mike_chen', 'alex_rivera', 'testuser']
    for username in test_users:
        try:
            user = CustomUser.objects.get(username=username)
            user.set_password('password123')
            user.save()
            print(f'✅ {username}: password123')
        except CustomUser.DoesNotExist:
            print(f'❌ {username} not found')

if __name__ == '__main__':
    reset_all_passwords()
```

Run with: `python reset_passwords.py`

## **🌐 Network Access**

### **Current Configuration**
- **Django Server**: `http://**************:8000`
- **Admin Panel**: `http://**************:8000/admin/`
- **API Base**: `http://**************:8000/api/v1/`

### **Mobile Testing**
- **Same WiFi network**: Required for mobile device access
- **Flutter app**: Configured for network IP
- **All features**: Work over network connection

## **🎯 Summary**

**For quick development setup:**

1. **Admin access**: `admin` / `admin123`
2. **Test users**: All use `password123`
3. **Wallet balance**: $50 for all users
4. **Network access**: Available on `**************:8000`
5. **Reset passwords**: Use the scripts above if needed

**All users have full access to:**
- ✅ Wallet operations
- ✅ Shop features (premium, point boosts)
- ✅ Social features (posts, comments, likes)
- ✅ Gamification (points, achievements)
- ✅ Mobile app via network connection

Happy testing! 🚀
