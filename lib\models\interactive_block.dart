import 'package:freezed_annotation/freezed_annotation.dart';
import 'poll.dart';
import 'quiz.dart';
import 'code_playground.dart';

part 'interactive_block.freezed.dart';
part 'interactive_block.g.dart';

@freezed
class InteractiveBlock with _$InteractiveBlock {
  const factory InteractiveBlock({
    required int id,
    @J<PERSON><PERSON><PERSON>(name: 'block_type') required String blockType,
    required String title,
    @Default('') String description,
    @Default(0) int position,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active') @Default(true) bool isActive,
    @Default({}) Map<String, dynamic> metadata,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,

    // Related content (only one will be populated based on block_type)
    Poll? poll,
    Quiz? quiz,
    // @<PERSON><PERSON><PERSON><PERSON>(name: 'code_playground') CodePlayground? codePlayground,
  }) = _InteractiveBlock;

  factory InteractiveBlock.fromJson(Map<String, dynamic> json) =>
      _$Interactive<PERSON>(json);
}

enum InteractiveBlockType {
  poll,
  quiz,
  code,
  chart,
  timeline,
  comparison,
  embed,
}

extension InteractiveBlockTypeExtension on InteractiveBlockType {
  String get value {
    switch (this) {
      case InteractiveBlockType.poll:
        return 'poll';
      case InteractiveBlockType.quiz:
        return 'quiz';
      case InteractiveBlockType.code:
        return 'code';
      case InteractiveBlockType.chart:
        return 'chart';
      case InteractiveBlockType.timeline:
        return 'timeline';
      case InteractiveBlockType.comparison:
        return 'comparison';
      case InteractiveBlockType.embed:
        return 'embed';
    }
  }

  String get displayName {
    switch (this) {
      case InteractiveBlockType.poll:
        return 'Poll';
      case InteractiveBlockType.quiz:
        return 'Quiz';
      case InteractiveBlockType.code:
        return 'Code Playground';
      case InteractiveBlockType.chart:
        return 'Data Visualization';
      case InteractiveBlockType.timeline:
        return 'Timeline';
      case InteractiveBlockType.comparison:
        return 'Comparison';
      case InteractiveBlockType.embed:
        return 'Rich Embed';
    }
  }

  static InteractiveBlockType fromString(String value) {
    switch (value) {
      case 'poll':
        return InteractiveBlockType.poll;
      case 'quiz':
        return InteractiveBlockType.quiz;
      case 'code':
        return InteractiveBlockType.code;
      case 'chart':
        return InteractiveBlockType.chart;
      case 'timeline':
        return InteractiveBlockType.timeline;
      case 'comparison':
        return InteractiveBlockType.comparison;
      case 'embed':
        return InteractiveBlockType.embed;
      default:
        throw ArgumentError('Unknown interactive block type: $value');
    }
  }
}
