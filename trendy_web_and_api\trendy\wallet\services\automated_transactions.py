"""
Automated Transaction Processing Service
Handles automated PayPal transactions, email notifications, and verification codes
"""

import random
import string
import logging
from decimal import Decimal
from datetime import datetime, timedelta
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings

# Blockchain integration
try:
    from blockchain.services import BlockchainService, RewardService
    BLOCKCHAIN_ENABLED = True
except ImportError:
    BLOCKCHAIN_ENABLED = False
from django.utils import timezone
from django.contrib.auth import get_user_model
from ..models import (
    WalletTransaction, WalletDepositRequest, WalletWithdrawalRequest,
    UserWallet, TransactionVerificationCode
)

User = get_user_model()
logger = logging.getLogger(__name__)

class AutomatedTransactionService:
    """Service for handling automated transactions with email verification"""
    
    @staticmethod
    def generate_verification_code():
        """Generate a 6-digit verification code"""
        return ''.join(random.choices(string.digits, k=6))
    
    @staticmethod
    def generate_transaction_id():
        """Generate a unique transaction ID"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        random_suffix = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        return f"TXN{timestamp}{random_suffix}"
    
    @classmethod
    def process_automated_deposit(cls, user, amount, payment_method='paypal'):
        """
        Process an automated deposit with email verification
        """
        try:
            # Create deposit request
            deposit_request = WalletDepositRequest.objects.create(
                wallet=user.wallet,
                amount=amount,
                payment_method=payment_method,
                status='processing',
                external_transaction_id=cls.generate_transaction_id()
            )
            
            # Generate verification code
            verification_code = cls.generate_verification_code()
            
            # Store verification code
            TransactionVerificationCode.objects.create(
                user=user,
                transaction_type='deposit',
                transaction_id=str(deposit_request.id),
                verification_code=verification_code,
                expires_at=timezone.now() + timedelta(minutes=15)
            )
            
            # Simulate PayPal processing (90% success rate)
            is_successful = random.random() < 0.9
            
            if is_successful:
                # Send success email with verification code
                cls.send_deposit_success_email(user, amount, verification_code, deposit_request.id)
                
                # Auto-complete after 2 minutes (simulate processing time)
                cls.schedule_auto_completion(deposit_request.id, 'deposit')
                
                return {
                    'success': True,
                    'message': 'Deposit initiated successfully. Check your email for verification code.',
                    'deposit_request_id': str(deposit_request.id),
                    'requires_verification': True
                }
            else:
                # Mark as failed
                deposit_request.status = 'failed'
                deposit_request.save()
                
                # Send failure email
                cls.send_deposit_failure_email(user, amount)
                
                return {
                    'success': False,
                    'message': 'Payment processing failed. Please try again or contact support.'
                }
                
        except Exception as e:
            logger.error(f"Error processing automated deposit for user {user.id}: {str(e)}")
            return {
                'success': False,
                'message': 'An error occurred while processing your deposit.'
            }
    
    @classmethod
    def verify_transaction_code(cls, user, transaction_id, verification_code, transaction_type):
        """
        Verify the transaction code and complete the transaction
        """
        try:
            # Find verification code
            verification = TransactionVerificationCode.objects.filter(
                user=user,
                transaction_id=transaction_id,
                verification_code=verification_code,
                transaction_type=transaction_type,
                is_used=False,
                expires_at__gt=timezone.now()
            ).first()
            
            if not verification:
                return {
                    'success': False,
                    'message': 'Invalid or expired verification code.'
                }
            
            # Mark verification as used
            verification.is_used = True
            verification.used_at = timezone.now()
            verification.save()
            
            # Complete the transaction based on type
            if transaction_type == 'deposit':
                return cls.complete_deposit(transaction_id)
            elif transaction_type == 'withdrawal':
                return cls.complete_withdrawal(transaction_id)
            
        except Exception as e:
            logger.error(f"Error verifying transaction code: {str(e)}")
            return {
                'success': False,
                'message': 'An error occurred while verifying your code.'
            }
    
    @classmethod
    def complete_deposit(cls, deposit_request_id):
        """Complete a verified deposit"""
        try:
            deposit_request = WalletDepositRequest.objects.get(id=deposit_request_id)
            
            if deposit_request.status != 'processing':
                return {
                    'success': False,
                    'message': 'Deposit request is not in processing status.'
                }
            
            # Update wallet balance
            wallet = deposit_request.wallet
            old_balance = wallet.balance
            wallet.balance += deposit_request.amount
            wallet.save()
            
            # Create transaction record
            transaction = WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='credit',
                purpose='deposit',
                amount=deposit_request.amount,
                balance_before=old_balance,
                balance_after=wallet.balance,
                status='completed',
                description=f'PayPal deposit - ${deposit_request.amount}',
                payment_method='paypal',
                external_transaction_id=deposit_request.external_transaction_id
            )
            
            # Update deposit request
            deposit_request.status = 'completed'
            deposit_request.completed_at = timezone.now()
            deposit_request.save()
            
            # Send completion email
            cls.send_deposit_completion_email(wallet.user, deposit_request.amount, wallet.balance)

            # Blockchain reward for deposit
            if BLOCKCHAIN_ENABLED:
                try:
                    blockchain_service = BlockchainService()
                    reward_service = RewardService(blockchain_service)

                    # Give bonus tokens for deposits
                    bonus_amount = float(deposit_request.amount) * 0.1  # 10% bonus in TRD tokens
                    reward_service.process_engagement_reward(
                        user=wallet.user,
                        engagement_type='deposit_bonus',
                        amount=bonus_amount
                    )
                    logger.info(f"Awarded {bonus_amount} TRD tokens as deposit bonus to {wallet.user.username}")
                except Exception as e:
                    logger.warning(f"Blockchain reward failed for deposit: {str(e)}")

            # Check for achievement milestones
            if BLOCKCHAIN_ENABLED:
                cls._check_deposit_achievements(wallet.user, wallet.balance)

            return {
                'success': True,
                'message': f'Deposit of ${deposit_request.amount} completed successfully.',
                'new_balance': str(wallet.balance),
                'transaction_id': str(transaction.id)
            }
            
        except Exception as e:
            logger.error(f"Error completing deposit {deposit_request_id}: {str(e)}")
            return {
                'success': False,
                'message': 'An error occurred while completing your deposit.'
            }
    
    @classmethod
    def process_automated_withdrawal(cls, user, amount, paypal_email):
        """
        Process an automated withdrawal with email verification
        """
        try:
            # Check if user has sufficient balance
            if user.wallet.balance < amount:
                return {
                    'success': False,
                    'message': 'Insufficient wallet balance.'
                }
            
            # Create withdrawal request
            withdrawal_request = WalletWithdrawalRequest.objects.create(
                wallet=user.wallet,
                amount=amount,
                paypal_email=paypal_email,
                status='processing',
                external_transaction_id=cls.generate_transaction_id()
            )
            
            # Generate verification code
            verification_code = cls.generate_verification_code()
            
            # Store verification code
            TransactionVerificationCode.objects.create(
                user=user,
                transaction_type='withdrawal',
                transaction_id=str(withdrawal_request.id),
                verification_code=verification_code,
                expires_at=timezone.now() + timedelta(minutes=15)
            )
            
            # Send verification email
            cls.send_withdrawal_verification_email(user, amount, paypal_email, verification_code, withdrawal_request.id)
            
            return {
                'success': True,
                'message': 'Withdrawal initiated. Check your email for verification code.',
                'withdrawal_request_id': str(withdrawal_request.id),
                'requires_verification': True
            }
            
        except Exception as e:
            logger.error(f"Error processing automated withdrawal for user {user.id}: {str(e)}")
            return {
                'success': False,
                'message': 'An error occurred while processing your withdrawal.'
            }
    
    @classmethod
    def complete_withdrawal(cls, withdrawal_request_id):
        """Complete a verified withdrawal"""
        try:
            withdrawal_request = WalletWithdrawalRequest.objects.get(id=withdrawal_request_id)
            
            if withdrawal_request.status != 'processing':
                return {
                    'success': False,
                    'message': 'Withdrawal request is not in processing status.'
                }
            
            # Update wallet balance
            wallet = withdrawal_request.wallet
            old_balance = wallet.balance
            wallet.balance -= withdrawal_request.amount
            wallet.save()
            
            # Create transaction record
            transaction = WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='debit',
                purpose='withdrawal',
                amount=withdrawal_request.amount,
                balance_before=old_balance,
                balance_after=wallet.balance,
                status='completed',
                description=f'PayPal withdrawal - ${withdrawal_request.amount}',
                payment_method='paypal',
                external_transaction_id=withdrawal_request.external_transaction_id
            )
            
            # Update withdrawal request
            withdrawal_request.status = 'completed'
            withdrawal_request.completed_at = timezone.now()
            withdrawal_request.save()
            
            # Simulate PayPal payout (95% success rate)
            payout_successful = random.random() < 0.95
            
            if payout_successful:
                # Send success email
                cls.send_withdrawal_success_email(
                    wallet.user, 
                    withdrawal_request.amount, 
                    withdrawal_request.paypal_email,
                    wallet.balance
                )
            else:
                # Reverse the transaction if payout fails
                wallet.balance += withdrawal_request.amount
                wallet.save()
                
                withdrawal_request.status = 'failed'
                withdrawal_request.save()
                
                transaction.status = 'failed'
                transaction.save()
                
                cls.send_withdrawal_failure_email(wallet.user, withdrawal_request.amount)
                
                return {
                    'success': False,
                    'message': 'PayPal payout failed. Amount has been refunded to your wallet.'
                }
            
            return {
                'success': True,
                'message': f'Withdrawal of ${withdrawal_request.amount} completed successfully.',
                'new_balance': str(wallet.balance),
                'transaction_id': str(transaction.id)
            }
            
        except Exception as e:
            logger.error(f"Error completing withdrawal {withdrawal_request_id}: {str(e)}")
            return {
                'success': False,
                'message': 'An error occurred while completing your withdrawal.'
            }

    @classmethod
    def send_deposit_success_email(cls, user, amount, verification_code, deposit_id):
        """Send deposit success email with verification code"""
        subject = f'Trendy - Deposit Verification Required (${amount})'

        context = {
            'user': user,
            'amount': amount,
            'verification_code': verification_code,
            'deposit_id': deposit_id,
            'expires_in': 15  # minutes
        }

        html_message = render_to_string('emails/deposit_verification.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def send_deposit_failure_email(cls, user, amount):
        """Send deposit failure email"""
        subject = f'Trendy - Deposit Failed (${amount})'

        context = {
            'user': user,
            'amount': amount
        }

        html_message = render_to_string('emails/deposit_failure.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def send_deposit_completion_email(cls, user, amount, new_balance):
        """Send deposit completion email"""
        subject = f'Trendy - Deposit Completed (${amount})'

        context = {
            'user': user,
            'amount': amount,
            'new_balance': new_balance
        }

        html_message = render_to_string('emails/deposit_completion.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def send_withdrawal_verification_email(cls, user, amount, paypal_email, verification_code, withdrawal_id):
        """Send withdrawal verification email"""
        subject = f'Trendy - Withdrawal Verification Required (${amount})'

        context = {
            'user': user,
            'amount': amount,
            'paypal_email': paypal_email,
            'verification_code': verification_code,
            'withdrawal_id': withdrawal_id,
            'expires_in': 15  # minutes
        }

        html_message = render_to_string('emails/withdrawal_verification.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def send_withdrawal_success_email(cls, user, amount, paypal_email, new_balance):
        """Send withdrawal success email"""
        subject = f'Trendy - Withdrawal Completed (${amount})'

        context = {
            'user': user,
            'amount': amount,
            'paypal_email': paypal_email,
            'new_balance': new_balance
        }

        html_message = render_to_string('emails/withdrawal_success.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def send_withdrawal_failure_email(cls, user, amount):
        """Send withdrawal failure email"""
        subject = f'Trendy - Withdrawal Failed (${amount})'

        context = {
            'user': user,
            'amount': amount
        }

        html_message = render_to_string('emails/withdrawal_failure.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def schedule_auto_completion(cls, transaction_id, transaction_type):
        """Schedule automatic completion of transaction (simulate processing time)"""
        # In a real application, you would use Celery or similar for this
        # For now, we'll just log it
        logger.info(f"Scheduled auto-completion for {transaction_type} {transaction_id}")

    @classmethod
    def process_automated_purchase(cls, user, amount, item_name, description):
        """Process automated purchase from wallet"""
        try:
            wallet = user.wallet

            if wallet.balance < amount:
                return {
                    'success': False,
                    'message': 'Insufficient wallet balance.'
                }

            # Generate verification code for large purchases (>$10)
            if amount > Decimal('10.00'):
                verification_code = cls.generate_verification_code()

                TransactionVerificationCode.objects.create(
                    user=user,
                    transaction_type='purchase',
                    transaction_id=f"purchase_{cls.generate_transaction_id()}",
                    verification_code=verification_code,
                    expires_at=timezone.now() + timedelta(minutes=10)
                )

                cls.send_purchase_verification_email(user, amount, item_name, verification_code)

                return {
                    'success': True,
                    'message': 'Purchase requires verification. Check your email.',
                    'requires_verification': True
                }

            # Process small purchases automatically
            old_balance = wallet.balance
            wallet.balance -= amount
            wallet.save()

            # Create transaction record
            transaction = WalletTransaction.objects.create(
                wallet=wallet,
                transaction_type='debit',
                purpose='purchase',
                amount=amount,
                balance_before=old_balance,
                balance_after=wallet.balance,
                status='completed',
                description=f'Purchase: {item_name}',
                external_transaction_id=cls.generate_transaction_id()
            )

            # Send purchase confirmation
            cls.send_purchase_confirmation_email(user, amount, item_name, wallet.balance)

            return {
                'success': True,
                'message': f'Purchase of {item_name} completed successfully.',
                'new_balance': str(wallet.balance),
                'transaction_id': str(transaction.id)
            }

        except Exception as e:
            logger.error(f"Error processing automated purchase for user {user.id}: {str(e)}")
            return {
                'success': False,
                'message': 'An error occurred while processing your purchase.'
            }

    @classmethod
    def send_purchase_verification_email(cls, user, amount, item_name, verification_code):
        """Send purchase verification email"""
        subject = f'Trendy - Purchase Verification Required (${amount})'

        context = {
            'user': user,
            'amount': amount,
            'item_name': item_name,
            'verification_code': verification_code,
            'expires_in': 10  # minutes
        }

        html_message = render_to_string('emails/purchase_verification.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def send_purchase_confirmation_email(cls, user, amount, item_name, new_balance):
        """Send purchase confirmation email"""
        subject = f'Trendy - Purchase Confirmed (${amount})'

        context = {
            'user': user,
            'amount': amount,
            'item_name': item_name,
            'new_balance': new_balance
        }

        html_message = render_to_string('emails/purchase_confirmation.html', context)
        plain_message = strip_tags(html_message)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False
        )

    @classmethod
    def _check_deposit_achievements(cls, user, current_balance):
        """Check and award blockchain achievements for deposit milestones"""
        if not BLOCKCHAIN_ENABLED:
            return

        try:
            blockchain_service = BlockchainService()
            reward_service = RewardService(blockchain_service)

            # Define achievement milestones
            milestones = [
                {'balance': 100, 'name': 'First Century', 'rarity': 2, 'description': 'Reached $100 wallet balance'},
                {'balance': 500, 'name': 'High Roller', 'rarity': 3, 'description': 'Reached $500 wallet balance'},
                {'balance': 1000, 'name': 'Wallet Warrior', 'rarity': 4, 'description': 'Reached $1000 wallet balance'},
                {'balance': 5000, 'name': 'Money Master', 'rarity': 5, 'description': 'Reached $5000 wallet balance'},
            ]

            for milestone in milestones:
                if current_balance >= milestone['balance']:
                    # Check if user already has this achievement
                    if BLOCKCHAIN_ENABLED:
                        from blockchain.models import NFTAsset
                        existing = NFTAsset.objects.filter(
                            user=user,
                            name=milestone['name']
                        ).exists()

                        if not existing:
                            achievement_data = {
                                'name': milestone['name'],
                                'description': milestone['description'],
                                'rarity': milestone['rarity'],
                                'image_url': f"https://trendy.app/achievements/{milestone['name'].lower().replace(' ', '_')}.png",
                                'attributes': {
                                    'category': 'financial',
                                    'milestone_amount': str(milestone['balance']),
                                    'earned_date': timezone.now().isoformat()
                                }
                            }

                            result = reward_service.process_achievement_reward(
                                user=user,
                                achievement_type='wallet_milestone',
                                achievement_data=achievement_data
                            )

                            if result['success']:
                                logger.info(f"Awarded achievement '{milestone['name']}' to {user.username}")

                                # Send achievement notification email
                                cls._send_achievement_email(user, milestone['name'], milestone['description'])

        except Exception as e:
            logger.error(f"Error checking deposit achievements: {str(e)}")

    @classmethod
    def _send_achievement_email(cls, user, achievement_name, description):
        """Send achievement notification email"""
        try:
            subject = f'🏆 Trendy - New Achievement Unlocked: {achievement_name}'

            context = {
                'user': user,
                'achievement_name': achievement_name,
                'description': description,
                'blockchain_enabled': BLOCKCHAIN_ENABLED
            }

            html_message = render_to_string('emails/achievement_notification.html', context)
            plain_message = strip_tags(html_message)

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False
            )

        except Exception as e:
            logger.error(f"Error sending achievement email: {str(e)}")

    @classmethod
    def _check_deposit_achievements(cls, user, current_balance):
        """Check and award blockchain achievements for deposit milestones"""
        if not BLOCKCHAIN_ENABLED:
            return

        try:
            blockchain_service = BlockchainService()
            reward_service = RewardService(blockchain_service)

            # Define achievement milestones
            milestones = [
                {'balance': 100, 'name': 'First Century', 'rarity': 2, 'description': 'Reached $100 wallet balance'},
                {'balance': 500, 'name': 'High Roller', 'rarity': 3, 'description': 'Reached $500 wallet balance'},
                {'balance': 1000, 'name': 'Millionaire Mindset', 'rarity': 4, 'description': 'Reached $1000 wallet balance'},
                {'balance': 5000, 'name': 'Whale Status', 'rarity': 5, 'description': 'Reached $5000 wallet balance'},
            ]

            for milestone in milestones:
                if current_balance >= milestone['balance']:
                    # Check if user already has this achievement
                    from blockchain.models import NFTAsset
                    existing = NFTAsset.objects.filter(
                        user=user,
                        name=milestone['name']
                    ).exists()

                    if not existing:
                        # Award achievement
                        achievement_data = {
                            'name': milestone['name'],
                            'description': milestone['description'],
                            'rarity': milestone['rarity'],
                            'image_url': f"https://trendy.app/achievements/{milestone['name'].lower().replace(' ', '_')}.png",
                            'attributes': {
                                'category': 'financial',
                                'milestone_amount': str(milestone['balance']),
                                'earned_date': timezone.now().isoformat()
                            }
                        }

                        result = reward_service.process_achievement_reward(
                            user=user,
                            achievement_type='wallet_milestone',
                            achievement_data=achievement_data
                        )

                        if result['success']:
                            logger.info(f"Awarded achievement '{milestone['name']}' to {user.username}")

        except Exception as e:
            logger.warning(f"Error checking deposit achievements: {str(e)}")
