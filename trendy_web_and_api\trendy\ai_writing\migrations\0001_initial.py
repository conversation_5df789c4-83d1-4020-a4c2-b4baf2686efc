# Generated by Django 5.0.4 on 2025-08-03 17:26

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("blog", "0002_post_is_global_post_regional_priority_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AIWritingPreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "preferred_tone",
                    models.CharField(
                        choices=[
                            ("professional", "Professional"),
                            ("casual", "Casual"),
                            ("friendly", "Friendly"),
                            ("formal", "Formal"),
                            ("creative", "Creative"),
                            ("technical", "Technical"),
                            ("conversational", "Conversational"),
                        ],
                        default="professional",
                        max_length=20,
                    ),
                ),
                (
                    "preferred_style",
                    models.Char<PERSON><PERSON>(
                        choices=[
                            ("blog", "Blog Post"),
                            ("news", "News Article"),
                            ("tutorial", "Tutorial"),
                            ("review", "Review"),
                            ("opinion", "Opinion Piece"),
                            ("guide", "How-to Guide"),
                            ("listicle", "List Article"),
                        ],
                        default="blog",
                        max_length=20,
                    ),
                ),
                (
                    "target_audience",
                    models.CharField(default="general audience", max_length=100),
                ),
                ("enable_grammar_suggestions", models.BooleanField(default=True)),
                ("enable_seo_suggestions", models.BooleanField(default=True)),
                ("enable_content_generation", models.BooleanField(default=True)),
                ("enable_readability_analysis", models.BooleanField(default=True)),
                ("enable_auto_complete", models.BooleanField(default=True)),
                ("preferred_word_count", models.PositiveIntegerField(default=800)),
                ("include_references", models.BooleanField(default=True)),
                ("include_images_suggestions", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_writing_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="AIWritingSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "session_data",
                    models.JSONField(
                        default=dict, help_text="Store session state and context"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("abandoned", "Abandoned"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("suggestions_generated", models.PositiveIntegerField(default=0)),
                ("suggestions_accepted", models.PositiveIntegerField(default=0)),
                ("words_generated", models.PositiveIntegerField(default=0)),
                ("time_saved_minutes", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "post",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_content_sessions",
                        to="blog.post",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_content_writing_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContentPrompt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "prompt_type",
                    models.CharField(
                        choices=[
                            ("introduction", "Introduction"),
                            ("conclusion", "Conclusion"),
                            ("outline", "Content Outline"),
                            ("title", "Title Generation"),
                            ("meta_description", "Meta Description"),
                            ("social_media", "Social Media Post"),
                            ("email", "Email Content"),
                            ("summary", "Content Summary"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "template",
                    models.TextField(
                        help_text="Use {topic}, {tone}, {audience} as placeholders"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("usage_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="blog.category",
                    ),
                ),
            ],
            options={
                "ordering": ["-usage_count", "name"],
            },
        ),
        migrations.CreateModel(
            name="ContentSuggestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "suggestion_type",
                    models.CharField(
                        choices=[
                            ("grammar", "Grammar Correction"),
                            ("style", "Style Improvement"),
                            ("seo", "SEO Optimization"),
                            ("readability", "Readability Enhancement"),
                            ("expansion", "Content Expansion"),
                            ("completion", "Text Completion"),
                            ("title", "Title Suggestion"),
                            ("meta", "Meta Description"),
                            ("tags", "Tag Suggestions"),
                        ],
                        max_length=20,
                    ),
                ),
                ("original_text", models.TextField()),
                ("suggested_text", models.TextField()),
                ("explanation", models.TextField(blank=True)),
                (
                    "confidence_score",
                    models.FloatField(default=0.0, help_text="AI confidence score 0-1"),
                ),
                ("start_position", models.PositiveIntegerField(default=0)),
                ("end_position", models.PositiveIntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("rejected", "Rejected"),
                            ("modified", "Modified"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("user_feedback", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="suggestions",
                        to="ai_writing.aiwritingsession",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContentTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "template_content",
                    models.TextField(
                        help_text="Use placeholders like {title}, {topic}, {audience}"
                    ),
                ),
                ("estimated_word_count", models.PositiveIntegerField(default=500)),
                (
                    "difficulty_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                        ],
                        default="intermediate",
                        max_length=20,
                    ),
                ),
                ("is_public", models.BooleanField(default=True)),
                ("usage_count", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="blog.category",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_content_templates",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-usage_count", "name"],
            },
        ),
        migrations.CreateModel(
            name="AIUsageAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("feature_used", models.CharField(max_length=50)),
                ("usage_count", models.PositiveIntegerField(default=1)),
                ("total_time_saved_minutes", models.PositiveIntegerField(default=0)),
                ("total_words_generated", models.PositiveIntegerField(default=0)),
                ("year", models.PositiveIntegerField()),
                ("month", models.PositiveIntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_content_usage_analytics",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-year", "-month"],
                "unique_together": {("user", "feature_used", "year", "month")},
            },
        ),
    ]
