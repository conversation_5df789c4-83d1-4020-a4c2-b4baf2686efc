// Add filter parameters class
class PostFilter {
  final String? search;
  final int? categoryId;
  final int? page;

  PostFilter({
    this.search,
    this.categoryId,
    this.page,
  });

  PostFilter copyWith({
    String? search,
    int? categoryId,
    int? page,
  }) {
    return PostFilter(
      search: search ?? this.search,
      categoryId: categoryId ?? this.categoryId,
      page: page ?? this.page,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'search': search,
      'category_id': categoryId,
      'page': page,
    };
  }
}