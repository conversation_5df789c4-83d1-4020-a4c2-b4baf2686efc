/// Media grid widget for displaying optimized media in posts
/// Supports progressive loading, tap to full-screen, and responsive layouts

import 'package:flutter/material.dart';
import '../models/media_models.dart';
import '../services/media_service.dart';
import '../widgets/progressive_image.dart';
import '../widgets/media_gallery.dart';

class MediaGrid extends StatefulWidget {
  final int postId;
  final List<OptimizedMedia>? initialMedia;
  final int maxItemsToShow;
  final double aspectRatio;
  final double spacing;
  final bool enableFullScreen;

  const MediaGrid({
    Key? key,
    required this.postId,
    this.initialMedia,
    this.maxItemsToShow = 4,
    this.aspectRatio = 1.0,
    this.spacing = 8.0,
    this.enableFullScreen = true,
  }) : super(key: key);

  @override
  State<MediaGrid> createState() => _MediaGridState();
}

class _MediaGridState extends State<MediaGrid> {
  List<OptimizedMedia> _mediaItems = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    if (widget.initialMedia != null) {
      _mediaItems = widget.initialMedia!;
    } else {
      _loadMedia();
    }
  }

  Future<void> _loadMedia() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final media = await MediaService.getOptimizedMedia(widget.postId);
      setState(() {
        _mediaItems = media;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _openFullScreenGallery(int initialIndex) async {
    if (!widget.enableFullScreen) return;

    try {
      final galleryData = await MediaService.getMediaGallery(widget.postId);
      
      if (mounted) {
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                MediaGalleryScreen(
              galleryData: galleryData,
              initialIndex: initialIndex,
            ),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 300),
            reverseTransitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load gallery: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildSingleImage(OptimizedMedia media, int index) {
    return GestureDetector(
      onTap: () => _openFullScreenGallery(index),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: HeroProgressiveImage(
          media: media,
          heroTag: 'media_${media.id}',
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildTwoImages() {
    return Row(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: widget.aspectRatio,
            child: _buildSingleImage(_mediaItems[0], 0),
          ),
        ),
        SizedBox(width: widget.spacing),
        Expanded(
          child: AspectRatio(
            aspectRatio: widget.aspectRatio,
            child: _buildSingleImage(_mediaItems[1], 1),
          ),
        ),
      ],
    );
  }

  Widget _buildThreeImages() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: AspectRatio(
            aspectRatio: widget.aspectRatio,
            child: _buildSingleImage(_mediaItems[0], 0),
          ),
        ),
        SizedBox(width: widget.spacing),
        Expanded(
          child: Column(
            children: [
              Expanded(
                child: _buildSingleImage(_mediaItems[1], 1),
              ),
              SizedBox(height: widget.spacing),
              Expanded(
                child: _buildSingleImage(_mediaItems[2], 2),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFourOrMoreImages() {
    final hasMore = _mediaItems.length > widget.maxItemsToShow;
    final remainingCount = _mediaItems.length - widget.maxItemsToShow;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: AspectRatio(
                aspectRatio: widget.aspectRatio,
                child: _buildSingleImage(_mediaItems[0], 0),
              ),
            ),
            SizedBox(width: widget.spacing),
            Expanded(
              child: AspectRatio(
                aspectRatio: widget.aspectRatio,
                child: _buildSingleImage(_mediaItems[1], 1),
              ),
            ),
          ],
        ),
        SizedBox(height: widget.spacing),
        Row(
          children: [
            Expanded(
              child: AspectRatio(
                aspectRatio: widget.aspectRatio,
                child: _buildSingleImage(_mediaItems[2], 2),
              ),
            ),
            SizedBox(width: widget.spacing),
            Expanded(
              child: AspectRatio(
                aspectRatio: widget.aspectRatio,
                child: Stack(
                  children: [
                    _buildSingleImage(_mediaItems[3], 3),
                    if (hasMore)
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            '+$remainingCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMediaGrid() {
    if (_mediaItems.isEmpty) {
      return const SizedBox.shrink();
    }

    switch (_mediaItems.length) {
      case 1:
        return AspectRatio(
          aspectRatio: 16 / 9,
          child: _buildSingleImage(_mediaItems[0], 0),
        );
      case 2:
        return _buildTwoImages();
      case 3:
        return _buildThreeImages();
      default:
        return _buildFourOrMoreImages();
    }
  }

  Widget _buildLoadingState() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.grey,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              'Failed to load media',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadMedia,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_error != null) {
      return _buildErrorState();
    }

    if (_mediaItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return _buildMediaGrid();
  }
}

/// Compact media preview for post cards
class MediaPreview extends StatelessWidget {
  final List<OptimizedMedia> mediaItems;
  final double height;
  final VoidCallback? onTap;

  const MediaPreview({
    Key? key,
    required this.mediaItems,
    this.height = 200,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (mediaItems.isEmpty) {
      return const SizedBox.shrink();
    }

    final firstMedia = mediaItems.first;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: height,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey[300],
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: ResponsiveImage(
                media: firstMedia,
                fit: BoxFit.cover,
              ),
            ),
            
            // Media count indicator
            if (mediaItems.length > 1)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.photo_library,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${mediaItems.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // Video play indicator
            if (firstMedia.type == 'video')
              const Center(
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(12),
                    child: Icon(
                      Icons.play_arrow,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
