// 4. Profile Screen
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/rewards_provider.dart';
import '../providers/store_provider.dart';
import '../providers/points_provider.dart' as points;
import '../theme/app_theme.dart';
import '../screens/point_conversion_screen.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Load user data when screen initializes using optimized method
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && ref.read(enhancedAuthProvider).isAuthenticated) {
        ref.read(gamificationProvider.notifier).loadAllDataOptimized();
        ref.read(rewardsProvider.notifier).loadUserRewards();
        ref.read(storeProvider.notifier).loadPremiumStatus();
        ref.read(points.unifiedPointsProvider.notifier).refreshAll();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final gamificationState = ref.watch(gamificationProvider);
    final rewardsState = ref.watch(rewardsProvider);
    final storeState = ref.watch(storeProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: authState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : authState.error != null
              ? Center(child: Text('Error: ${authState.error}'))
              : authState.user == null
                  ? const Center(child: Text('No user data'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // User Avatar and Basic Info
                          CircleAvatar(
                            radius: 50,
                            backgroundImage: authState.user!.avatarUrl != null
                                ? NetworkImage(authState.user!.avatarUrl!)
                                : null,
                            child: authState.user!.avatarUrl == null
                                ? Text(
                                    authState.user!.username[0].toUpperCase(),
                                    style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold),
                                  )
                                : null,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            authState.user!.fullName,
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            authState.user!.email,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          if (authState.user!.bio != null) ...[
                            const SizedBox(height: 8),
                            Text(
                              authState.user!.bio!,
                              style: Theme.of(context).textTheme.bodySmall,
                              textAlign: TextAlign.center,
                            ),
                          ],

                          const SizedBox(height: 24),

                          // Premium Status
                          if (storeState.premiumSubscription?.status ==
                              'active')
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFF667EEA),
                                    Color(0xFF764BA2)
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.star, color: Colors.white),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Premium Member',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    '2x Points',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          if (storeState.premiumSubscription?.status ==
                              'active')
                            const SizedBox(height: 16),

                          // Stats Cards
                          Row(
                            children: [
                              Expanded(
                                child: _buildStatCard(
                                  'Level',
                                  '${gamificationState.userLevel?.currentLevel ?? 1}',
                                  Icons.trending_up,
                                  AppTheme.primaryColor,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Consumer(
                                  builder: (context, ref, child) {
                                    final pointsState =
                                        ref.watch(points.unifiedPointsProvider);
                                    return _buildStatCard(
                                      'Gamification',
                                      '${pointsState.gamificationPoints}',
                                      Icons.stars,
                                      AppTheme.accentColor,
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 12),

                          // Store Points and Conversion
                          Row(
                            children: [
                              Expanded(
                                child: Consumer(
                                  builder: (context, ref, child) {
                                    final pointsState =
                                        ref.watch(points.unifiedPointsProvider);
                                    return _buildStatCard(
                                      'Store Points',
                                      '${pointsState.storePoints}',
                                      Icons.shopping_cart,
                                      Colors.green,
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 10,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.swap_horiz,
                                        color: AppTheme.primaryColor,
                                        size: 32,
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'Convert',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: AppTheme.textSecondary,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      ElevatedButton(
                                        onPressed: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  const PointConversionScreen(),
                                            ),
                                          );
                                        },
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              AppTheme.primaryColor,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 6),
                                          minimumSize: Size.zero,
                                        ),
                                        child: const Text(
                                          'Convert',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 12),

                          Row(
                            children: [
                              Expanded(
                                child: _buildStatCard(
                                  'Streak',
                                  '${gamificationState.userLevel?.readingStreak ?? 0} days',
                                  Icons.local_fire_department,
                                  AppTheme.warningColor,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildStatCard(
                                  'Earned',
                                  '\$${rewardsState.userRewards.fold(0.0, (sum, reward) => sum + (reward.status == 'completed' ? reward.amount : 0.0)).toStringAsFixed(2)}',
                                  Icons.attach_money,
                                  AppTheme.successColor,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          // Recent Badges
                          if (gamificationState.badges.isNotEmpty) ...[
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'Recent Badges',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              height:
                                  105, // Increased from 80 to accommodate content + padding
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: gamificationState.badges.length,
                                itemBuilder: (context, index) {
                                  final userBadge =
                                      gamificationState.badges[index];
                                  final badge = userBadge.badge;

                                  return Container(
                                    width: 80,
                                    margin: const EdgeInsets.only(right: 12),
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: AppTheme.surfaceColor,
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                          color: AppTheme.dividerColor),
                                    ),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          badge.icon.isNotEmpty
                                              ? badge.icon
                                              : '🏆',
                                          style: const TextStyle(fontSize: 20),
                                        ),
                                        const SizedBox(height: 2),
                                        Flexible(
                                          child: Text(
                                            badge.name,
                                            style: const TextStyle(
                                              fontSize: 9,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],

                          // Logout Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              key: const ValueKey('profile_logout_button'),
                              onPressed: () async {
                                await ref
                                    .read(enhancedAuthProvider.notifier)
                                    .logout();
                                if (context.mounted) {
                                  Navigator.pushNamedAndRemoveUntil(
                                      context, '/', (route) => false);
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppTheme.errorColor,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                              ),
                              child: const Text('Logout'),
                            ),
                          ),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.dividerColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
