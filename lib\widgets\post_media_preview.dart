import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/post_media.dart';
import '../theme/app_theme.dart';
import '../config/api_config.dart';
import 'video_player_widget.dart';

class PostMediaPreview extends StatelessWidget {
  final PostMedia media;

  const PostMediaPreview({Key? key, required this.media}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (media.mediaType == 'image') {
      return _buildImageWidget();
    } else if (media.mediaType == 'video') {
      return _buildVideoWidget();
    } else {
      return _buildUnsupportedWidget();
    }
  }

  Widget _buildImageWidget() {
    // Try imageUrlFull first (which should have the full URL), then image, then imageUrl
    final imageUrl = media.imageUrlFull ?? media.image ?? media.imageUrl;

    if (imageUrl == null || imageUrl.isEmpty) {
      return _buildPlaceholderWidget(Icons.image_outlined, 'No image available');
    }

    // Use the API config to build the full URL
    final fullImageUrl = ApiConfig.getMediaUrl(imageUrl);

    // Debug info for development
    if (ApiConfig.isDevelopment) {
      debugPrint('Loading image:');
      debugPrint('  Original URL: $imageUrl');
      debugPrint('  Full URL: $fullImageUrl');
    }

    return SizedBox(
      width: double.infinity,
      child: CachedNetworkImage(
        imageUrl: fullImageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: AppTheme.backgroundColor,
          child: const Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
          ),
        ),
        errorWidget: (context, url, error) {
          // Debug info for development
          debugPrint('❌ Image loading error: $error');
          debugPrint('❌ Failed URL: $fullImageUrl');
          debugPrint('❌ Original URL: $imageUrl');
          return _buildErrorWidget('Failed to load image', Icons.broken_image);
        },
      ),
    );
  }

  Widget _buildVideoWidget() {
    // Try videoUrl first (which should have the full URL), then video field
    final videoUrl = media.videoUrl ?? media.video;

    if (videoUrl == null || videoUrl.isEmpty) {
      return _buildPlaceholderWidget(Icons.videocam_off_outlined, 'No video available');
    }

    // Use the API config to build the full URL
    final fullVideoUrl = ApiConfig.getMediaUrl(videoUrl);

    // Debug info for development
    if (ApiConfig.isDevelopment) {
      debugPrint('Loading video:');
      debugPrint('  Original URL: $videoUrl');
      debugPrint('  Full URL: $fullVideoUrl');
    }

    return SizedBox(
      width: double.infinity,
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: VideoPlayerWidget(videoUrl: fullVideoUrl),
      ),
    );
  }

  Widget _buildPlaceholderWidget(IconData icon, String message) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border.all(
          color: AppTheme.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(
              color: AppTheme.textTertiary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message, IconData icon) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withOpacity(0.1),
        border: Border.all(
          color: AppTheme.errorColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(
              color: AppTheme.errorColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUnsupportedWidget() {
    return _buildPlaceholderWidget(
      Icons.help_outline,
      'Unsupported media type',
    );
  }
}