import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'platform_storage_service.dart';
import '../config/api_config.dart';
import '../models/category.dart';
import '../models/comment.dart';
import '../models/paginated_response.dart';
import '../models/post.dart';
import '../models/post_filter.dart';
import '../models/post_media.dart';
import '../models/tag.dart';
import '../models/user.dart';
import '../models/notification.dart';
import '../models/user_settings.dart';
import '../models/auth_models.dart';
import '../models/search_results.dart';
import '../models/api_response.dart';

class ApiService {
  late final Dio _dio;

  // Getter for Dio instance (needed for other services)
  Dio get dio => _dio;

  ApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: ApiConfig.baseUrl,
        connectTimeout: ApiConfig.connectTimeout,
        receiveTimeout: ApiConfig.receiveTimeout,
        sendTimeout: ApiConfig.sendTimeout,
        validateStatus: (status) {
          return status != null && status < 500;
        },
      ),
    );

    // Add interceptor to automatically include authentication headers
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          final token = await _getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Token $token';
          }
          handler.next(options);
        },
      ),
    );
  }

  Future<String?> _getToken() async {
    return await PlatformStorageService.getSecureData('token');
  }

  Future<void> _setToken(String token) async {
    await PlatformStorageService.setSecureData('token', token);
  }

  Future<void> setToken(String token) async {
    await _setToken(token);
  }

  Future<String?> getStoredToken() async {
    return await _getToken();
  }

  Future<bool> isAuthenticated() async {
    try {
      final token = await _getToken();
      if (token == null) return false;

      // Verify token is still valid by trying to get current user
      final user = await getCurrentUser();
      return user != null;
    } catch (e) {
      print('Error checking authentication: $e');
      return false;
    }
  }

  Future<void> clearToken() async {
    await _deleteToken();
  }

  Future<void> _deleteToken() async {
    await PlatformStorageService.deleteSecureData('token');
    await PlatformStorageService.deleteSecureData(
      'user_data',
    ); // Also clear cached user data
  }

  Future<void> clearUserData() async {
    try {
      await PlatformStorageService.deleteSecureData('user_data');
      await PlatformStorageService.deleteSecureData('user_id');
    } catch (e) {
      print('Error clearing user data: $e');
    }
  }

  Future<void> _setUserData(User user) async {
    await PlatformStorageService.setSecureObject('user_data', user.toJson());
  }

  Future<User?> _getUserData() async {
    try {
      final userData = await PlatformStorageService.getSecureObject(
        'user_data',
      );
      if (userData != null) {
        return User.fromJson(userData);
      }
      return null;
    } catch (e) {
      print('Error reading cached user data: $e');
      return null;
    }
  }

  Future<User> login(String username, String password) async {
    try {
      print('Attempting login with username: $username');
      final response = await _dio.post(
        '/api/v1/accounts/login/',
        data: {'email_or_username': username, 'password': password},
      );
      print('Login response: ${response.data}');
      await _setToken(response.data['token']);
      final user = User.fromJson(response.data['user']);
      await _setUserData(user); // Cache user data
      return user;
    } catch (e) {
      print('Login error: $e');
      if (e is DioException) {
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
          case DioExceptionType.sendTimeout:
          case DioExceptionType.receiveTimeout:
            throw Exception(
              'Connection timeout. Please check your internet connection.',
            );
          case DioExceptionType.badResponse:
            if (e.response?.statusCode == 401) {
              throw Exception('Invalid username or password.');
            } else if (e.response?.statusCode == 500) {
              throw Exception('Server error. Please try again later.');
            }
            throw Exception(
              e.response?.data?['detail'] ?? 'An error occurred during login.',
            );
          case DioExceptionType.unknown:
            if (e.error != null &&
                e.error.toString().contains('SocketException')) {
              throw Exception(
                'Unable to connect to server. Please check your internet connection.',
              );
            }
            throw Exception('An unexpected error occurred. Please try again.');
          default:
            throw Exception('An error occurred during login.');
        }
      }
      rethrow;
    }
  }

  Future<User> register(
    String username,
    String email,
    String password,
    String firstName,
    String lastName,
  ) async {
    try {
      print('Attempting registration with email: $email');
      final response = await _dio.post(
        '/api/v1/accounts/register/',
        data: {
          'username': username,
          'email': email,
          'password': password,
          'first_name': firstName,
          'last_name': lastName,
        },
      );
      print('Registration response: ${response.data}');
      await _setToken(response.data['token']);
      final user = User.fromJson(response.data['user']);
      await _setUserData(user); // Cache user data
      return user;
    } catch (e) {
      print('Registration error: $e');
      if (e is DioException) {
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
          case DioExceptionType.sendTimeout:
          case DioExceptionType.receiveTimeout:
            throw Exception(
              'Connection timeout. Please check your internet connection.',
            );
          case DioExceptionType.badResponse:
            if (e.response?.statusCode == 400) {
              final errors = e.response?.data ?? {};
              if (errors.containsKey('username')) {
                throw Exception('Username is already taken.');
              } else if (errors.containsKey('email')) {
                throw Exception('Email is already registered.');
              }
              throw Exception('Invalid registration data.');
            } else if (e.response?.statusCode == 500) {
              throw Exception('Server error. Please try again later.');
            }
            throw Exception(
              e.response?.data?['detail'] ??
                  'An error occurred during registration.',
            );
          case DioExceptionType.unknown:
            if (e.error != null &&
                e.error.toString().contains('SocketException')) {
              throw Exception(
                'Unable to connect to server. Please check your internet connection.',
              );
            }
            throw Exception('An unexpected error occurred. Please try again.');
          default:
            throw Exception('An error occurred during registration.');
        }
      }
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await _dio.post('/api/v1/accounts/logout/');
    } catch (e) {
      // Continue with logout even if API call fails
      print('Logout API call failed: $e');
    }
    await _deleteToken();
  }

  Future<PaginatedResponse<Post>> getPosts({
    int page = 1,
    int pageSize = 10,
    String? category,
    String? search,
    String? country,
    bool? showGlobal,
  }) async {
    try {
      final response = await _dio.get(
        '/api/v1/posts/',
        queryParameters: {
          'page': page,
          'page_size': pageSize,
          if (category != null) 'category': category,
          if (search != null) 'search': search,
          if (country != null) 'country': country,
          if (showGlobal != null) 'show_global': showGlobal.toString(),
        },
      );
      print('Posts response: ${response.data}');

      // Ensure the response data is not null
      if (response.data == null) {
        throw Exception('Response data is null');
      }

      // Ensure the results array exists
      if (!response.data.containsKey('results')) {
        throw Exception('Response data does not contain results array');
      }

      return PaginatedResponse.fromJson(response.data, (json) {
        if (json == null) {
          throw Exception('Post data is null');
        }
        return Post.fromJson(json as Map<String, dynamic>);
      });
    } catch (e) {
      print('Error fetching posts: $e');
      rethrow;
    }
  }

  Future<Post> getPost(String pk) async {
    try {
      print('Fetching post with pk: $pk');

      final response = await _dio.get('/api/v1/posts/$pk/');
      print('Post response status: ${response.statusCode}');
      print('Post response data: ${response.data}');

      // Debug media items specifically
      if (response.data != null && response.data['media_items'] != null) {
        print('Media items found: ${response.data['media_items']}');
        print('Media items count: ${response.data['media_items'].length}');
      } else {
        print('No media_items field in response or it is null');
      }

      if (response.statusCode == 200 && response.data != null) {
        return Post.fromJson(response.data);
      } else if (response.statusCode == 404) {
        throw Exception('Post not found. Please check the URL and try again.');
      } else {
        throw Exception('Failed to load post: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching post: $e');
      if (e is DioException) {
        print('Dio error type: ${e.type}');
        print('Dio error message: ${e.message}');
        print('Dio error response: ${e.response?.data}');

        switch (e.type) {
          case DioExceptionType.connectionTimeout:
          case DioExceptionType.sendTimeout:
          case DioExceptionType.receiveTimeout:
            throw Exception(
              'Connection timeout. Please check your internet connection.',
            );
          case DioExceptionType.badResponse:
            if (e.response?.statusCode == 404) {
              throw Exception(
                'Post not found. Please check the URL and try again.',
              );
            } else if (e.response?.statusCode == 500) {
              throw Exception('Server error. Please try again later.');
            }
            throw Exception(
              e.response?.data?['detail'] ??
                  'An error occurred while fetching the post.',
            );
          case DioExceptionType.unknown:
            if (e.error != null &&
                e.error.toString().contains('SocketException')) {
              throw Exception(
                'Unable to connect to server. Please check your internet connection.',
              );
            }
            throw Exception('An unexpected error occurred. Please try again.');
          default:
            throw Exception('An error occurred while fetching the post.');
        }
      }
      rethrow;
    }
  }

  Future<Post> createPost({
    required String title,
    required String content,
    int? categoryId,
    List<int>? tagIds,
    List<Map<String, dynamic>>? mediaItems,
    bool? isGlobal,
    List<int>? targetCountryIds,
  }) async {
    final formData = FormData.fromMap({
      'title': title,
      'content': content,
      if (categoryId != null) 'category_id': categoryId,
      if (tagIds != null) 'tag_ids': tagIds,
      if (mediaItems != null) 'media_items': mediaItems,
      if (isGlobal != null) 'is_global': isGlobal,
      if (targetCountryIds != null) 'target_countries': targetCountryIds,
    });

    final response = await _dio.post('/api/v1/posts/', data: formData);
    return Post.fromJson(response.data);
  }

  Future<Post> updatePost({
    required String slug,
    String? title,
    String? content,
    int? categoryId,
    List<int>? tagIds,
    List<Map<String, dynamic>>? mediaItems,
    bool? isGlobal,
    List<int>? targetCountryIds,
  }) async {
    final formData = FormData.fromMap({
      if (title != null) 'title': title,
      if (content != null) 'content': content,
      if (categoryId != null) 'category_id': categoryId,
      if (tagIds != null) 'tag_ids': tagIds,
      if (mediaItems != null) 'media_items': mediaItems,
      if (isGlobal != null) 'is_global': isGlobal,
      if (targetCountryIds != null) 'target_countries': targetCountryIds,
    });

    final response = await _dio.patch('/api/v1/posts/$slug/', data: formData);
    return Post.fromJson(response.data);
  }

  Future<void> deletePost(String slug) async {
    await _dio.delete('/api/v1/posts/$slug/');
  }

  Future<PaginatedResponse<Comment>> getComments(String pk, {int? page}) async {
    try {
      print('Fetching comments for post: $pk');
      // Use the comments endpoint with post filter
      final response = await _dio.get(
        '/api/v1/comments/',
        queryParameters: {'post': pk, if (page != null) 'page': page},
      );
      print('Comments response: ${response.data}');

      // Handle empty response
      if (response.data == null) {
        return PaginatedResponse<Comment>(
          count: 0,
          next: null,
          previous: null,
          results: [],
        );
      }

      // Handle list response
      if (response.data is List) {
        final comments = (response.data as List)
            .map((json) => Comment.fromJson(json as Map<String, dynamic>))
            .toList();
        return PaginatedResponse<Comment>(
          count: comments.length,
          next: null,
          previous: null,
          results: comments,
        );
      }

      // Handle paginated response
      return PaginatedResponse.fromJson(
        response.data,
        (json) => Comment.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      print('Error fetching comments: $e');
      if (e is DioException && e.response?.statusCode == 404) {
        // Return empty response for 404
        return PaginatedResponse<Comment>(
          count: 0,
          next: null,
          previous: null,
          results: [],
        );
      }
      rethrow;
    }
  }

  Future<Comment> createComment(
    String pk,
    String content, {
    int? parentId,
  }) async {
    try {
      // Use the comments endpoint directly
      final response = await _dio.post(
        '/api/v1/comments/',
        data: {
          'post': int.parse(pk),
          'content': content,
          if (parentId != null) 'parent': parentId,
        },
      );
      return Comment.fromJson(response.data);
    } catch (e) {
      print('Error creating comment: $e');
      rethrow;
    }
  }

  Future<void> deleteComment(String pk, int commentId) async {
    await _dio.delete('/api/v1/comments/$commentId/');
  }

  Future<void> toggleCommentLike(int commentId) async {
    try {
      print('Toggling like for comment: $commentId');
      final response = await _dio.post('/api/v1/comments/$commentId/like/');
      print('Comment like response: ${response.data}');
    } catch (e) {
      print('Error toggling comment like: $e');
      rethrow;
    }
  }

  Future<PaginatedResponse<Comment>> getCommentReplies(
    int commentId, {
    int? page,
  }) async {
    try {
      final response = await _dio.get(
        '/api/v1/comments/$commentId/replies/',
        queryParameters: {if (page != null) 'page': page},
      );
      return PaginatedResponse.fromJson(
        response.data,
        (json) => Comment.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      print('Error fetching comment replies: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> toggleLike(String type, String id) async {
    try {
      final response = await _dio.post('/api/v1/likes/$type/$id/');

      // Ensure we return a Map<String, dynamic>
      if (response.data is Map<String, dynamic>) {
        return response.data as Map<String, dynamic>;
      } else {
        // If response.data is not a Map, create a default response
        return {
          'like_count': 0,
          'is_liked': false,
          'message': 'Like toggled successfully',
        };
      }
    } catch (e) {
      print('Error toggling like: $e');
      rethrow;
    }
  }

  Future<void> incrementPostView(String postId) async {
    try {
      final response = await _dio.post('/api/v1/posts/$postId/increment_view/');

      if (response.statusCode == 200) {
        print('View count updated for post $postId');
        // Notify that gamification data might have changed
        _notifyGamificationUpdate();
      } else {
        print('Failed to update view count: ${response.statusCode}');
      }
    } catch (e) {
      print('Error incrementing view count: $e');
      // Don't rethrow - view tracking shouldn't break the app
    }
  }

  Future<Map<String, dynamic>> awardReadingPoints(
    String postId, {
    required int timeSpent,
    required double scrollPercentage,
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/posts/$postId/award_reading_points/',
        data: {'time_spent': timeSpent, 'scroll_percentage': scrollPercentage},
      );

      if (response.statusCode == 200) {
        print('Reading points response for post $postId: ${response.data}');
        // Notify that gamification data might have changed
        _notifyGamificationUpdate();
        return response.data;
      } else {
        throw Exception(
          'Failed to award reading points: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Error awarding reading points: $e');
      rethrow;
    }
  }

  // Notify gamification providers that data might have changed
  void _notifyGamificationUpdate() {
    // This will be used by providers to refresh gamification data
    print('🎮 Gamification data may have been updated');
  }

  Future<PaginatedResponse<Category>> getCategories() async {
    try {
      print('Fetching categories');
      final response = await _dio.get('/api/v1/categories/');
      print('Categories response: ${response.data}');
      return PaginatedResponse.fromJson(
        response.data,
        (json) => Category.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      print('Error fetching categories: $e');
      rethrow;
    }
  }

  Future<PaginatedResponse<Tag>> getTags() async {
    final response = await _dio.get('/api/v1/tags/');
    return PaginatedResponse.fromJson(
      response.data,
      (json) => Tag.fromJson(json as Map<String, dynamic>),
    );
  }

  Future<User?> getCurrentUser() async {
    try {
      final token = await _getToken();
      if (token == null) {
        print('No token found, user not logged in');
        return null;
      }

      // Try to get cached user data first
      final cachedUser = await _getUserData();
      if (cachedUser != null) {
        print('Using cached user data');
        return cachedUser;
      }

      print('Token found, fetching current user profile from API');
      final response = await _dio.get('/api/v1/accounts/profile/');
      print('Current user response: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        final user = User.fromJson(response.data);
        await _setUserData(user); // Cache the user data
        return user;
      }

      return null;
    } catch (e) {
      print('Error fetching current user: $e');
      if (e is DioException && e.response?.statusCode == 401) {
        // Token is invalid, clear it
        await _deleteToken();
        print('Token was invalid, cleared from storage');
      }
      return null;
    }
  }

  Future<User> updateProfile(User updatedUser) async {
    final response = await _dio.patch(
      '/api/v1/auth/me/',
      data: updatedUser.toJson(),
    );
    return User.fromJson(response.data);
  }

  Future<Map<String, dynamic>> getUserProfile(String username) async {
    final response = await _dio.get('/api/v1/users/$username/');
    return response.data;
  }

  Future<Map<String, dynamic>> toggleFollow(String username) async {
    final response = await _dio.post('/api/v1/social/follow/$username/');
    return response.data;
  }

  Future<List<Post>> getUserPosts(String username, {int page = 1}) async {
    try {
      final response = await _dio.get(
        '/api/v1/users/$username/posts/',
        queryParameters: {'page': page},
      );
      final results = response.data['results'] as List<dynamic>;
      return results
          .map((json) => Post.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error fetching user posts: $e');
      rethrow;
    }
  }

  Future<List<Post>> getUserLikedPosts(String username, {int page = 1}) async {
    try {
      final response = await _dio.get(
        '/api/v1/users/$username/liked-posts/',
        queryParameters: {'page': page},
      );
      final results = response.data['results'] as List<dynamic>;
      return results
          .map((json) => Post.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error fetching user liked posts: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getUserFollowers(
    String username, {
    int page = 1,
  }) async {
    final response = await _dio.get(
      '/api/v1/social/users/$username/followers/',
      queryParameters: {'page': page},
    );
    return response.data;
  }

  Future<Map<String, dynamic>> getUserFollowing(
    String username, {
    int page = 1,
  }) async {
    final response = await _dio.get(
      '/api/v1/social/users/$username/following/',
      queryParameters: {'page': page},
    );
    return response.data;
  }

  // Enhanced Authentication Methods
  Future<AuthResponse> loginWithRequest(LoginRequest request) async {
    try {
      print('Attempting login with username: ${request.emailOrUsername}');
      final response = await _dio.post(
        '/api/v1/accounts/login/',
        data: request.toJson(),
      );
      final authResponse = AuthResponse.fromJson(response.data);
      await _setToken(authResponse.token);
      return authResponse;
    } catch (e) {
      print('Login error: $e');
      _handleAuthError(e);
    }
  }

  // Get the isFirstLogin flag from the last login response
  Future<bool> getIsFirstLogin() async {
    try {
      final response = await _dio.get('/api/v1/accounts/welcome-bonus/');
      return response.data['is_new_user'] ?? false;
    } catch (e) {
      print('Error getting first login status: $e');
      return false;
    }
  }

  Future<AuthResponse> registerWithRequest(RegisterRequest request) async {
    try {
      final response = await _dio.post(
        '/api/v1/accounts/register/',
        data: request.toJson(),
      );
      final authResponse = AuthResponse.fromJson(response.data);
      await _setToken(authResponse.token);
      return authResponse;
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<ApiResponse> changePassword(ChangePasswordRequest request) async {
    try {
      final response = await _dio.post(
        '/api/v1/accounts/change-password/',
        data: request.toJson(),
      );
      return ApiResponse.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<ApiResponse> requestPasswordReset(PasswordResetRequest request) async {
    try {
      final response = await _dio.post(
        '/api/v1/accounts/password-reset/',
        data: request.toJson(),
      );
      return ApiResponse.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<ApiResponse> confirmPasswordReset(
    PasswordResetConfirmRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/accounts/password-reset-confirm/',
        data: request.toJson(),
      );
      return ApiResponse.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<ApiResponse> verifyEmail(EmailVerificationRequest request) async {
    try {
      final response = await _dio.post(
        '/api/v1/accounts/verify-email/',
        data: request.toJson(),
      );
      return ApiResponse.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<ApiResponse> resendEmailVerification() async {
    try {
      final response = await _dio.post('/api/v1/accounts/resend-verification/');
      return ApiResponse.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  // User Settings Methods
  Future<UserSettings> getUserSettings() async {
    try {
      final response = await _dio.get('/api/v1/accounts/settings/');
      return UserSettings.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<UserSettings> updateUserSettings(UserSettings settings) async {
    try {
      final response = await _dio.put(
        '/api/v1/accounts/settings/',
        data: settings.toJson(),
      );
      return UserSettings.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  // Notification Methods
  Future<PaginatedResponse<AppNotification>> getNotifications({
    int? page,
  }) async {
    try {
      final response = await _dio.get(
        '/api/v1/accounts/notifications/',
        queryParameters: {if (page != null) 'page': page},
      );
      return PaginatedResponse.fromJson(
        response.data,
        (json) => AppNotification.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<AppNotification> markNotificationAsRead(int notificationId) async {
    try {
      final response = await _dio.patch(
        '/api/v1/accounts/notifications/$notificationId/',
        data: {'is_read': true},
      );
      return AppNotification.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<ApiResponse> markAllNotificationsAsRead() async {
    try {
      final response = await _dio.post(
        '/api/v1/accounts/notifications/mark-all-read/',
      );
      return ApiResponse.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  Future<Map<String, int>> getNotificationCount() async {
    try {
      final response = await _dio.get('/api/v1/accounts/notifications/count/');
      return {'unread_count': response.data['unread_count'] as int};
    } catch (e) {
      _handleAuthError(e);
    }
  }

  // Search functionality
  Future<SearchResults> search(String query) async {
    try {
      final response = await _dio.get(
        '/api/v1/search/',
        queryParameters: {'q': query},
      );
      return SearchResults.fromJson(response.data);
    } catch (e) {
      print('Search error: $e');
      rethrow;
    }
  }

  // Get recommendations
  Future<List<Post>> getRecommendations() async {
    try {
      final response = await _dio.get('/api/v1/recommendations/');
      final results = response.data['results'] as List<dynamic>;
      return results
          .map((json) => Post.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Recommendations error: $e');
      rethrow;
    }
  }

  // Generic GET method for testing
  Future<GenericApiResponse> get(String endpoint) async {
    try {
      final response = await _dio.get(endpoint);
      return GenericApiResponse(
        isSuccess: response.statusCode == 200,
        data: response.data,
        error: null,
      );
    } catch (e) {
      return GenericApiResponse(
        isSuccess: false,
        data: null,
        error: e.toString(),
      );
    }
  }

  // Profile Methods
  Future<User> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _dio.put(
        '/api/v1/accounts/profile/',
        data: profileData,
      );
      return User.fromJson(response.data);
    } catch (e) {
      _handleAuthError(e);
    }
  }

  // Error handling helper
  Never _handleAuthError(dynamic e) {
    if (e is DioException) {
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          throw Exception(
            'Connection timeout. Please check your internet connection.',
          );
        case DioExceptionType.badResponse:
          final statusCode = e.response?.statusCode;
          final errorData = e.response?.data;

          if (statusCode == 400) {
            if (errorData is Map && errorData.containsKey('detail')) {
              throw Exception(errorData['detail']);
            } else if (errorData is Map &&
                errorData.containsKey('non_field_errors')) {
              // Handle non-field errors (like invalid credentials)
              final nonFieldErrors = errorData['non_field_errors'];
              if (nonFieldErrors is List && nonFieldErrors.isNotEmpty) {
                throw Exception(nonFieldErrors.first.toString());
              }
            } else if (errorData is Map) {
              // Handle field-specific errors
              final errors = <String>[];
              errorData.forEach((key, value) {
                if (value is List) {
                  errors.addAll(value.map((e) => '$key: $e'));
                } else {
                  errors.add('$key: $value');
                }
              });
              throw Exception(errors.join('\n'));
            }
            throw Exception('Invalid request. Please check your input.');
          } else if (statusCode == 401) {
            throw Exception('Unauthorized. Please log in again.');
          } else if (statusCode == 403) {
            throw Exception('Access denied.');
          } else if (statusCode == 404) {
            throw Exception('Resource not found.');
          } else if (statusCode == 500) {
            throw Exception('Server error. Please try again later.');
          } else {
            throw Exception('Request failed. Please try again.');
          }
        case DioExceptionType.unknown:
          if (e.error != null &&
              e.error.toString().contains('SocketException')) {
            throw Exception(
              'Unable to connect to server. Please check your internet connection.',
            );
          }
          throw Exception('An unexpected error occurred. Please try again.');
        default:
          throw Exception('Network error occurred.');
      }
    }
    throw Exception('An unexpected error occurred.');
  }

  // Gamification endpoints
  Future<Map<String, dynamic>> getUserLevel() async {
    try {
      final response = await _dio.get('/api/v1/gamification/user/level/');
      return response.data;
    } catch (e) {
      print('Error fetching user level: $e');
      rethrow;
    }
  }

  Future<List<dynamic>> getUserBadges() async {
    try {
      final response = await _dio.get('/api/v1/gamification/user/badges/');
      return response.data as List<dynamic>;
    } catch (e) {
      print('Error fetching user badges: $e');
      rethrow;
    }
  }

  Future<List<dynamic>> getUserTransactions() async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/user/transactions/',
      );
      return response.data;
    } catch (e) {
      print('Error fetching user transactions: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getPointTransactions() async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/user/transactions/',
      );
      return response.data;
    } catch (e) {
      print('Error fetching point transactions: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getChallenges() async {
    try {
      final response = await _dio.get('/api/v1/gamification/challenges/');
      return response.data;
    } catch (e) {
      print('Error fetching challenges: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> joinChallenge(String challengeId) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/challenges/$challengeId/join/',
      );
      return response.data;
    } catch (e) {
      print('Error joining challenge: $e');
      rethrow;
    }
  }

  // PayPal Rewards endpoints
  Future<Map<String, dynamic>> getPayPalRewards() async {
    try {
      final response = await _dio.get('/api/v1/gamification/paypal-rewards/');
      return response.data;
    } catch (e) {
      print('Error fetching PayPal rewards: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getUserPayPalRewards() async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/user-paypal-rewards/',
      );
      return response.data;
    } catch (e) {
      print('Error fetching user PayPal rewards: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> claimPayPalReward(
    String rewardId,
    String paypalEmail,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/paypal-rewards/$rewardId/claim/',
        data: {'paypal_email': paypalEmail},
      );
      return response.data;
    } catch (e) {
      print('Error claiming PayPal reward: $e');
      rethrow;
    }
  }

  // Monetization endpoints
  Future<Map<String, dynamic>> getPremiumStatus() async {
    try {
      final response = await _dio.get('/api/v1/monetization/premium-status/');
      return response.data;
    } catch (e) {
      print('Error fetching premium status: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createPaymentOrder(
    double amount,
    String purpose, {
    String? description,
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/create-order/',
        data: {
          'amount': amount,
          'purpose': purpose,
          'description': description ?? '',
        },
      );
      return response.data;
    } catch (e) {
      print('Error creating payment order: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> capturePaymentOrder(String orderId) async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/capture-order/',
        data: {'order_id': orderId},
      );
      return response.data;
    } catch (e) {
      print('Error capturing payment order: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getPaymentHistory() async {
    try {
      final response = await _dio.get('/api/v1/payments/history/');
      return response.data;
    } catch (e) {
      print('Error fetching payment history: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getPaymentStatus(String transactionId) async {
    try {
      final response =
          await _dio.get('/api/v1/payments/status/$transactionId/');
      return response.data;
    } catch (e) {
      print('Error fetching payment status: $e');
      rethrow;
    }
  }

  // Referral endpoints
  Future<Map<String, dynamic>> getReferralData() async {
    try {
      final response = await _dio.get('/api/v1/monetization/referral-data/');
      return response.data;
    } catch (e) {
      print('Error fetching referral data: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> validateReferralCode(String code) async {
    try {
      final response = await _dio.post(
        '/api/v1/monetization/validate-referral-code/',
        data: {'code': code},
      );
      return response.data;
    } catch (e) {
      print('Error validating referral code: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getReferralCode() async {
    try {
      final response = await _dio.get('/api/v1/monetization/referral-code/');
      return response.data;
    } catch (e) {
      print('Error getting referral code: $e');
      rethrow;
    }
  }

  // Welcome bonus endpoint
  Future<Map<String, dynamic>> getWelcomeBonusInfo() async {
    try {
      final response = await _dio.get('/api/v1/accounts/welcome-bonus/');
      return response.data;
    } catch (e) {
      print('Error getting welcome bonus info: $e');
      rethrow;
    }
  }

  // Unified Points endpoint
  Future<Map<String, dynamic>> getUnifiedUserPoints() async {
    try {
      final response = await _dio.get('/api/v1/gamification/points/unified/');
      return response.data;
    } catch (e) {
      print('Error getting unified user points: $e');
      rethrow;
    }
  }

  // Point Conversion endpoints
  Future<Map<String, dynamic>> getConversionPreview(
    int gamificationPoints,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/conversion/preview/',
        data: {'gamification_points': gamificationPoints},
      );
      return response.data;
    } catch (e) {
      print('Error getting conversion preview: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> convertGamificationToStorePoints(
    int gamificationPoints,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/conversion/convert/',
        data: {'gamification_points': gamificationPoints},
      );
      return response.data;
    } catch (e) {
      print('Error converting points: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getConversionSettings() async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/conversion/settings/',
      );
      return response.data;
    } catch (e) {
      print('Error getting conversion settings: $e');
      rethrow;
    }
  }

  Future<List<dynamic>> getConversionHistory() async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/conversion/history/',
      );
      return response.data['transactions'] ?? [];
    } catch (e) {
      print('Error getting conversion history: $e');
      rethrow;
    }
  }

  // Spend user points (for rewards, purchases, etc.)
  Future<Map<String, dynamic>> spendUserPoints(
    int points,
    String description,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/spend-points/',
        data: {'points': points, 'description': description},
      );
      return response.data;
    } catch (e) {
      print('Error spending user points: $e');
      rethrow;
    }
  }

  // Store endpoints
  Future<Map<String, dynamic>> getVirtualItems() async {
    try {
      final response = await _dio.get('/api/v1/monetization/virtual-items/');
      return response.data;
    } catch (e) {
      print('Error fetching virtual items: $e');
      rethrow;
    }
  }

  // Purchase virtual item with store points
  Future<Map<String, dynamic>> purchaseVirtualItem(
    String itemId, {
    String paymentMethod = 'store_points',
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/monetization/virtual-items/$itemId/purchase/',
        data: {'payment_method': paymentMethod},
      );
      return response.data;
    } catch (e) {
      print('Error purchasing virtual item: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getPointBoostPackages() async {
    try {
      final response = await _dio.get('/api/v1/monetization/point-boosts/');
      return response.data;
    } catch (e) {
      print('Error fetching point boost packages: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> purchasePointBoost(
    String packageId, {
    String paymentMethod = 'wallet',
    String? paypalEmail,
  }) async {
    try {
      final data = {'payment_method': paymentMethod};

      if (paypalEmail != null && paypalEmail.isNotEmpty) {
        data['paypal_email'] = paypalEmail;
      }

      final response = await _dio.post(
        '/api/v1/monetization/point-boosts/$packageId/purchase/',
        data: data,
      );
      return response.data;
    } catch (e) {
      print('Error purchasing point boost: $e');
      if (e is DioException && e.response != null) {
        return e.response!.data;
      }
      return {
        'success': false,
        'message': 'Failed to purchase point boost: ${e.toString()}',
      };
    }
  }

  Future<Map<String, dynamic>> confirmPointBoostPayment(
    String transactionId,
    String packageId,
    String status,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/monetization/point-boosts/confirm-payment/',
        data: {
          'transaction_id': transactionId,
          'package_id': packageId,
          'status': status,
        },
      );
      return response.data;
    } catch (e) {
      print('Error confirming point boost payment: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createPremiumPayment(
    String plan,
    double amount,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/create-order/',
        data: {
          'amount': amount,
          'purpose': 'premium_subscription',
          'description': 'Premium subscription - $plan',
          'plan': plan,
        },
      );
      return response.data;
    } catch (e) {
      print('Error creating premium payment: $e');
      rethrow;
    }
  }

  // Wallet API methods
  Future<Map<String, dynamic>> getWalletOverview() async {
    try {
      final response = await _dio.get('/api/v1/wallet/overview/');
      return response.data;
    } catch (e) {
      print('Error getting wallet overview: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getWalletSettings() async {
    try {
      final response = await _dio.get('/api/v1/wallet/settings/');
      return response.data;
    } catch (e) {
      print('Error getting wallet settings: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createWalletDeposit(
    double amount,
    String paymentMethod, {
    String? paypalEmail,
  }) async {
    try {
      final data = {
        'amount': amount,
        'payment_method': paymentMethod,
      };

      if (paypalEmail != null && paypalEmail.isNotEmpty) {
        data['paypal_email'] = paypalEmail;
      }

      final response = await _dio.post(
        '/api/v1/wallet/deposit/create/',
        data: data,
      );
      return response.data;
    } catch (e) {
      print('Error creating wallet deposit: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> confirmWalletDeposit(
    String depositRequestId,
    String transactionId,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/wallet/deposit/confirm/',
        data: {
          'deposit_request_id': depositRequestId,
          'transaction_id': transactionId,
        },
      );
      return response.data;
    } catch (e) {
      print('Error confirming wallet deposit: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> createWalletWithdrawal(
    double amount,
    String paypalEmail,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/wallet/withdrawal/create/',
        data: {'amount': amount, 'paypal_email': paypalEmail},
      );
      return response.data;
    } catch (e) {
      print('Error creating wallet withdrawal: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> spendFromWallet(
    double amount,
    String purpose,
    String description,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/wallet/spend/',
        data: {
          'amount': amount,
          'purpose': purpose,
          'description': description,
        },
      );
      return response.data;
    } catch (e) {
      print('Error spending from wallet: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getWalletTransactions({
    int limit = 20,
    int offset = 0,
    String? type,
    String? purpose,
  }) async {
    try {
      final queryParams = <String, dynamic>{'limit': limit, 'offset': offset};
      if (type != null) queryParams['type'] = type;
      if (purpose != null) queryParams['purpose'] = purpose;

      final response = await _dio.get(
        '/api/v1/wallet/transactions/',
        queryParameters: queryParams,
      );
      return response.data;
    } catch (e) {
      print('Error getting wallet transactions: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getMonetizationSettings() async {
    try {
      final response = await _dio.get('/api/v1/monetization/settings/');
      return response.data;
    } catch (e) {
      print('Error fetching monetization settings: $e');
      rethrow;
    }
  }

  // Advertising endpoints
  Future<Map<String, dynamic>> getAd(String placement) async {
    try {
      final response = await _dio.get(
        '/api/v1/ads/get-ad/',
        queryParameters: {'placement': placement},
      );
      return response.data;
    } catch (e) {
      print('Error fetching ad: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> recordAdImpression(
    String placementId,
    String adNetworkId, {
    String? adId,
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/ads/record-impression/',
        data: {
          'placement_id': placementId,
          'ad_network_id': adNetworkId,
          'ad_id': adId ?? '',
        },
      );
      return response.data;
    } catch (e) {
      print('Error recording ad impression: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> recordAdClick(String impressionId) async {
    try {
      final response = await _dio.post(
        '/api/v1/ads/record-click/',
        data: {'impression_id': impressionId},
      );
      return response.data;
    } catch (e) {
      print('Error recording ad click: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> startRewardedAd(
    String placementId,
    String adNetworkId,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/ads/start-rewarded-ad/',
        data: {'placement_id': placementId, 'ad_network_id': adNetworkId},
      );
      return response.data;
    } catch (e) {
      print('Error starting rewarded ad: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> completeRewardedAd(
    String sessionId, {
    bool completed = true,
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/ads/complete-rewarded-ad/',
        data: {'session_id': sessionId, 'completed': completed},
      );
      return response.data;
    } catch (e) {
      print('Error completing rewarded ad: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getUserAdStats() async {
    try {
      final response = await _dio.get('/api/v1/ads/user-stats/');
      return response.data;
    } catch (e) {
      print('Error fetching user ad stats: $e');
      rethrow;
    }
  }

  // Tier unlock endpoints
  Future<Map<String, dynamic>> getUnlockedTiers() async {
    try {
      final response = await _dio.get('/api/v1/gamification/unlocked-tiers/');
      return response.data;
    } catch (e) {
      print('Error fetching unlocked tiers: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> unlockTier(String tier) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/unlock-tier/',
        data: {'tier': tier},
      );
      return response.data;
    } catch (e) {
      print('Error unlocking tier: $e');
      rethrow;
    }
  }

  // PayPal profile endpoints
  Future<Map<String, dynamic>> getPayPalProfile() async {
    try {
      final response = await _dio.get('/api/v1/payments/paypal-profile/');
      return response.data;
    } catch (e) {
      print('Error fetching PayPal profile: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> setupPayPalProfile(String paypalEmail) async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/setup-paypal-profile/',
        data: {'paypal_email': paypalEmail},
      );

      // Handle different response types from backend
      if (response.data is Map<String, dynamic>) {
        return response.data;
      } else if (response.data is String) {
        // If backend returns a string, wrap it in a map
        return {
          'success': true,
          'message': response.data,
          'paypal_email': paypalEmail,
        };
      } else {
        // Fallback for any other type
        return {'success': true, 'paypal_email': paypalEmail};
      }
    } catch (e) {
      print('Error setting up PayPal profile: $e');
      // Return a proper error response instead of rethrowing
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> verifyPayPalProfile(
    String verificationCode,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/verify-paypal-profile/',
        data: {'verification_code': verificationCode},
      );
      return response.data;
    } catch (e) {
      print('Error verifying PayPal profile: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> requestPayout(double amount) async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/request-payout/',
        data: {'amount': amount},
      );
      return response.data;
    } catch (e) {
      print('Error requesting payout: $e');
      rethrow;
    }
  }

  // Premium subscription methods
  Future<Map<String, dynamic>> subscribeToPremium(
    String plan, {
    String paymentMethod = 'wallet',
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/monetization/premium-subscribe/',
        data: {'plan': plan, 'payment_method': paymentMethod},
      );
      return response.data;
    } catch (e) {
      print('Error subscribing to premium: $e');
      if (e is DioException && e.response != null) {
        return e.response!.data;
      }
      return {
        'success': false,
        'message': 'Failed to create subscription: ${e.toString()}',
      };
    }
  }

  Future<Map<String, dynamic>> confirmSubscriptionPayment(
    String transactionId,
    String status,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/monetization/confirm-payment/',
        data: {'transaction_id': transactionId, 'status': status},
      );
      return response.data;
    } catch (e) {
      print('Error confirming subscription payment: $e');
      if (e is DioException && e.response != null) {
        return e.response!.data;
      }
      return {
        'success': false,
        'message': 'Failed to confirm payment: ${e.toString()}',
      };
    }
  }

  Future<Map<String, dynamic>> cancelPendingSubscription(
    String subscriptionId,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/monetization/cancel-subscription/',
        data: {'subscription_id': subscriptionId},
      );
      return response.data;
    } catch (e) {
      print('Error cancelling subscription: $e');
      if (e is DioException && e.response != null) {
        return e.response!.data;
      }
      return {
        'success': false,
        'message': 'Failed to cancel subscription: ${e.toString()}',
      };
    }
  }

  // PayPal verification methods
  Future<Map<String, dynamic>> resendPayPalVerification() async {
    try {
      final response = await _dio.post(
        '/api/v1/payments/resend-paypal-verification/',
      );
      return response.data;
    } catch (e) {
      print('Error resending PayPal verification: $e');
      if (e is DioException && e.response != null) {
        return e.response!.data;
      }
      return {
        'success': false,
        'message': 'Failed to resend PayPal verification: ${e.toString()}',
      };
    }
  }

  // Social/Community API methods
  Future<GenericApiResponse> getSocialDiscover() async {
    try {
      final response = await _dio.get('/api/v1/social/discover/');
      return GenericApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error fetching discover users: $e');
      return const GenericApiResponse(
        isSuccess: false,
        error: 'Failed to load discover users',
        data: [],
      );
    }
  }

  Future<GenericApiResponse> getSocialTrending() async {
    try {
      final response = await _dio.get('/api/v1/social/trending/');
      return GenericApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error fetching trending users: $e');
      return const GenericApiResponse(
        isSuccess: false,
        error: 'Failed to load trending users',
        data: [],
      );
    }
  }

  Future<GenericApiResponse> getSocialFollowing() async {
    try {
      final response = await _dio.get('/api/v1/social/following/');
      return GenericApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error fetching following: $e');
      return const GenericApiResponse(
        isSuccess: false,
        error: 'Failed to load following',
        data: [],
      );
    }
  }

  Future<GenericApiResponse> getSocialFollowers() async {
    try {
      final response = await _dio.get('/api/v1/social/followers/');
      return GenericApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error fetching followers: $e');
      return const GenericApiResponse(
        isSuccess: false,
        error: 'Failed to load followers',
        data: [],
      );
    }
  }

  Future<GenericApiResponse> searchSocialUsers(String query) async {
    try {
      final response = await _dio.get(
        '/api/v1/social/search/',
        queryParameters: {'q': query},
      );
      return GenericApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error searching users: $e');
      return const GenericApiResponse(
        isSuccess: false,
        error: 'Failed to search users',
        data: [],
      );
    }
  }
}
