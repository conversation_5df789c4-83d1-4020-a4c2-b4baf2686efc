import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ai_writing_models.dart';
import '../services/ai_writing_service.dart';

// AI Writing Settings Widget
class AIWritingSettings extends ConsumerStatefulWidget {
  const AIWritingSettings({Key? key}) : super(key: key);

  @override
  ConsumerState<AIWritingSettings> createState() => _AIWritingSettingsState();
}

class _AIWritingSettingsState extends ConsumerState<AIWritingSettings> {
  final AIWritingService _aiService = AIWritingService();
  AIWritingPreferences? _preferences;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _aiService.initialize();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    try {
      final prefs = await _aiService.getUserPreferences();
      setState(() {
        _preferences = prefs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _preferences = const AIWritingPreferences();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('AI Writing Settings')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Writing Settings'),
        actions: [
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _savePreferences,
              child: const Text('Save'),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Writing Style'),
            _buildToneSelector(),
            const SizedBox(height: 16),
            _buildStyleSelector(),
            const SizedBox(height: 16),
            _buildTargetAudienceField(),
            const SizedBox(height: 24),
            _buildSectionHeader('AI Features'),
            _buildFeatureToggle(
              'Grammar Suggestions',
              'Get AI-powered grammar and style improvements',
              _preferences!.enableGrammarSuggestions,
              (value) => _updatePreferences(
                  _preferences!.copyWith(enableGrammarSuggestions: value)),
            ),
            _buildFeatureToggle(
              'SEO Suggestions',
              'Receive SEO optimization recommendations',
              _preferences!.enableSeoSuggestions,
              (value) => _updatePreferences(
                  _preferences!.copyWith(enableSeoSuggestions: value)),
            ),
            _buildFeatureToggle(
              'Content Generation',
              'Enable AI content ideas and outlines',
              _preferences!.enableContentGeneration,
              (value) => _updatePreferences(
                  _preferences!.copyWith(enableContentGeneration: value)),
            ),
            _buildFeatureToggle(
              'Readability Analysis',
              'Analyze content readability and complexity',
              _preferences!.enableReadabilityAnalysis,
              (value) => _updatePreferences(
                  _preferences!.copyWith(enableReadabilityAnalysis: value)),
            ),
            _buildFeatureToggle(
              'Auto Complete',
              'AI-powered text completion suggestions',
              _preferences!.enableAutoComplete,
              (value) => _updatePreferences(
                  _preferences!.copyWith(enableAutoComplete: value)),
            ),
            const SizedBox(height: 24),
            _buildSectionHeader('Content Preferences'),
            _buildWordCountSlider(),
            const SizedBox(height: 16),
            _buildFeatureToggle(
              'Include References',
              'Suggest relevant references and citations',
              _preferences!.includeReferences,
              (value) => _updatePreferences(
                  _preferences!.copyWith(includeReferences: value)),
            ),
            _buildFeatureToggle(
              'Image Suggestions',
              'Suggest relevant images for content',
              _preferences!.includeImagesSuggestions,
              (value) => _updatePreferences(
                  _preferences!.copyWith(includeImagesSuggestions: value)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
      ),
    );
  }

  Widget _buildToneSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Preferred Tone',
                style: TextStyle(fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8.0,
              children: [
                'professional',
                'casual',
                'friendly',
                'formal',
                'conversational'
              ].map((tone) {
                final isSelected = _preferences!.preferredTone == tone;
                return FilterChip(
                  label: Text(tone.toUpperCase()),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      _updatePreferences(
                          _preferences!.copyWith(preferredTone: tone));
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStyleSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Content Style',
                style: TextStyle(fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8.0,
              children: ['blog', 'article', 'news', 'tutorial', 'review']
                  .map((style) {
                final isSelected = _preferences!.preferredStyle == style;
                return FilterChip(
                  label: Text(style.toUpperCase()),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      _updatePreferences(
                          _preferences!.copyWith(preferredStyle: style));
                    }
                  },
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetAudienceField() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Target Audience',
                style: TextStyle(fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: _preferences!.targetAudience,
              decoration: const InputDecoration(
                hintText: 'e.g., general audience, professionals, beginners',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                _updatePreferences(
                    _preferences!.copyWith(targetAudience: value));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureToggle(
      String title, String subtitle, bool value, ValueChanged<bool> onChanged) {
    return Card(
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildWordCountSlider() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preferred Word Count: ${_preferences!.preferredWordCount}',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Slider(
              value: _preferences!.preferredWordCount.toDouble(),
              min: 200,
              max: 2000,
              divisions: 18,
              label: '${_preferences!.preferredWordCount} words',
              onChanged: (value) {
                _updatePreferences(
                    _preferences!.copyWith(preferredWordCount: value.toInt()));
              },
            ),
          ],
        ),
      ),
    );
  }

  void _updatePreferences(AIWritingPreferences newPreferences) {
    setState(() {
      _preferences = newPreferences;
    });
  }

  Future<void> _savePreferences() async {
    if (_preferences == null) return;

    setState(() => _isSaving = true);

    try {
      final savedPreferences =
          await _aiService.saveUserPreferences(_preferences!);
      setState(() => _preferences = savedPreferences);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
