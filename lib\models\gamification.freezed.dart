// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gamification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Badge _$BadgeFromJson(Map<String, dynamic> json) {
  return _Badge.fromJson(json);
}

/// @nodoc
mixin _$Badge {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'badge_type')
  String get badgeType => throw _privateConstructorUsedError;
  @JsonKey(name: 'type_display')
  String get typeDisplay => throw _privateConstructorUsedError;
  String get rarity => throw _privateConstructorUsedError;
  @JsonKey(name: 'rarity_display')
  String get rarityDisplay => throw _privateConstructorUsedError;
  String get icon => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  @JsonKey(name: 'image_url')
  String? get imageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_reward')
  int get pointsReward => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_secret')
  bool get isSecret => throw _privateConstructorUsedError;
  Map<String, dynamic> get requirements => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BadgeCopyWith<Badge> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BadgeCopyWith<$Res> {
  factory $BadgeCopyWith(Badge value, $Res Function(Badge) then) =
      _$BadgeCopyWithImpl<$Res, Badge>;
  @useResult
  $Res call(
      {int id,
      String name,
      String description,
      @JsonKey(name: 'badge_type') String badgeType,
      @JsonKey(name: 'type_display') String typeDisplay,
      String rarity,
      @JsonKey(name: 'rarity_display') String rarityDisplay,
      String icon,
      String color,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'points_reward') int pointsReward,
      @JsonKey(name: 'is_secret') bool isSecret,
      Map<String, dynamic> requirements});
}

/// @nodoc
class _$BadgeCopyWithImpl<$Res, $Val extends Badge>
    implements $BadgeCopyWith<$Res> {
  _$BadgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? badgeType = null,
    Object? typeDisplay = null,
    Object? rarity = null,
    Object? rarityDisplay = null,
    Object? icon = null,
    Object? color = null,
    Object? imageUrl = freezed,
    Object? pointsReward = null,
    Object? isSecret = null,
    Object? requirements = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      badgeType: null == badgeType
          ? _value.badgeType
          : badgeType // ignore: cast_nullable_to_non_nullable
              as String,
      typeDisplay: null == typeDisplay
          ? _value.typeDisplay
          : typeDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      rarity: null == rarity
          ? _value.rarity
          : rarity // ignore: cast_nullable_to_non_nullable
              as String,
      rarityDisplay: null == rarityDisplay
          ? _value.rarityDisplay
          : rarityDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pointsReward: null == pointsReward
          ? _value.pointsReward
          : pointsReward // ignore: cast_nullable_to_non_nullable
              as int,
      isSecret: null == isSecret
          ? _value.isSecret
          : isSecret // ignore: cast_nullable_to_non_nullable
              as bool,
      requirements: null == requirements
          ? _value.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BadgeImplCopyWith<$Res> implements $BadgeCopyWith<$Res> {
  factory _$$BadgeImplCopyWith(
          _$BadgeImpl value, $Res Function(_$BadgeImpl) then) =
      __$$BadgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      String description,
      @JsonKey(name: 'badge_type') String badgeType,
      @JsonKey(name: 'type_display') String typeDisplay,
      String rarity,
      @JsonKey(name: 'rarity_display') String rarityDisplay,
      String icon,
      String color,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'points_reward') int pointsReward,
      @JsonKey(name: 'is_secret') bool isSecret,
      Map<String, dynamic> requirements});
}

/// @nodoc
class __$$BadgeImplCopyWithImpl<$Res>
    extends _$BadgeCopyWithImpl<$Res, _$BadgeImpl>
    implements _$$BadgeImplCopyWith<$Res> {
  __$$BadgeImplCopyWithImpl(
      _$BadgeImpl _value, $Res Function(_$BadgeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? badgeType = null,
    Object? typeDisplay = null,
    Object? rarity = null,
    Object? rarityDisplay = null,
    Object? icon = null,
    Object? color = null,
    Object? imageUrl = freezed,
    Object? pointsReward = null,
    Object? isSecret = null,
    Object? requirements = null,
  }) {
    return _then(_$BadgeImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      badgeType: null == badgeType
          ? _value.badgeType
          : badgeType // ignore: cast_nullable_to_non_nullable
              as String,
      typeDisplay: null == typeDisplay
          ? _value.typeDisplay
          : typeDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      rarity: null == rarity
          ? _value.rarity
          : rarity // ignore: cast_nullable_to_non_nullable
              as String,
      rarityDisplay: null == rarityDisplay
          ? _value.rarityDisplay
          : rarityDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pointsReward: null == pointsReward
          ? _value.pointsReward
          : pointsReward // ignore: cast_nullable_to_non_nullable
              as int,
      isSecret: null == isSecret
          ? _value.isSecret
          : isSecret // ignore: cast_nullable_to_non_nullable
              as bool,
      requirements: null == requirements
          ? _value._requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BadgeImpl implements _Badge {
  const _$BadgeImpl(
      {required this.id,
      required this.name,
      required this.description,
      @JsonKey(name: 'badge_type') required this.badgeType,
      @JsonKey(name: 'type_display') required this.typeDisplay,
      required this.rarity,
      @JsonKey(name: 'rarity_display') required this.rarityDisplay,
      required this.icon,
      required this.color,
      @JsonKey(name: 'image_url') this.imageUrl,
      @JsonKey(name: 'points_reward') required this.pointsReward,
      @JsonKey(name: 'is_secret') this.isSecret = false,
      final Map<String, dynamic> requirements = const {}})
      : _requirements = requirements;

  factory _$BadgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$BadgeImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final String description;
  @override
  @JsonKey(name: 'badge_type')
  final String badgeType;
  @override
  @JsonKey(name: 'type_display')
  final String typeDisplay;
  @override
  final String rarity;
  @override
  @JsonKey(name: 'rarity_display')
  final String rarityDisplay;
  @override
  final String icon;
  @override
  final String color;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey(name: 'points_reward')
  final int pointsReward;
  @override
  @JsonKey(name: 'is_secret')
  final bool isSecret;
  final Map<String, dynamic> _requirements;
  @override
  @JsonKey()
  Map<String, dynamic> get requirements {
    if (_requirements is EqualUnmodifiableMapView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_requirements);
  }

  @override
  String toString() {
    return 'Badge(id: $id, name: $name, description: $description, badgeType: $badgeType, typeDisplay: $typeDisplay, rarity: $rarity, rarityDisplay: $rarityDisplay, icon: $icon, color: $color, imageUrl: $imageUrl, pointsReward: $pointsReward, isSecret: $isSecret, requirements: $requirements)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BadgeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.badgeType, badgeType) ||
                other.badgeType == badgeType) &&
            (identical(other.typeDisplay, typeDisplay) ||
                other.typeDisplay == typeDisplay) &&
            (identical(other.rarity, rarity) || other.rarity == rarity) &&
            (identical(other.rarityDisplay, rarityDisplay) ||
                other.rarityDisplay == rarityDisplay) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.pointsReward, pointsReward) ||
                other.pointsReward == pointsReward) &&
            (identical(other.isSecret, isSecret) ||
                other.isSecret == isSecret) &&
            const DeepCollectionEquality()
                .equals(other._requirements, _requirements));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      badgeType,
      typeDisplay,
      rarity,
      rarityDisplay,
      icon,
      color,
      imageUrl,
      pointsReward,
      isSecret,
      const DeepCollectionEquality().hash(_requirements));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BadgeImplCopyWith<_$BadgeImpl> get copyWith =>
      __$$BadgeImplCopyWithImpl<_$BadgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BadgeImplToJson(
      this,
    );
  }
}

abstract class _Badge implements Badge {
  const factory _Badge(
      {required final int id,
      required final String name,
      required final String description,
      @JsonKey(name: 'badge_type') required final String badgeType,
      @JsonKey(name: 'type_display') required final String typeDisplay,
      required final String rarity,
      @JsonKey(name: 'rarity_display') required final String rarityDisplay,
      required final String icon,
      required final String color,
      @JsonKey(name: 'image_url') final String? imageUrl,
      @JsonKey(name: 'points_reward') required final int pointsReward,
      @JsonKey(name: 'is_secret') final bool isSecret,
      final Map<String, dynamic> requirements}) = _$BadgeImpl;

  factory _Badge.fromJson(Map<String, dynamic> json) = _$BadgeImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  String get description;
  @override
  @JsonKey(name: 'badge_type')
  String get badgeType;
  @override
  @JsonKey(name: 'type_display')
  String get typeDisplay;
  @override
  String get rarity;
  @override
  @JsonKey(name: 'rarity_display')
  String get rarityDisplay;
  @override
  String get icon;
  @override
  String get color;
  @override
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @override
  @JsonKey(name: 'points_reward')
  int get pointsReward;
  @override
  @JsonKey(name: 'is_secret')
  bool get isSecret;
  @override
  Map<String, dynamic> get requirements;
  @override
  @JsonKey(ignore: true)
  _$$BadgeImplCopyWith<_$BadgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserBadge _$UserBadgeFromJson(Map<String, dynamic> json) {
  return _UserBadge.fromJson(json);
}

/// @nodoc
mixin _$UserBadge {
  int get id => throw _privateConstructorUsedError;
  Badge get badge => throw _privateConstructorUsedError;
  @JsonKey(name: 'earned_at')
  DateTime get earnedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'progress_data')
  Map<String, dynamic> get progressData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserBadgeCopyWith<UserBadge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserBadgeCopyWith<$Res> {
  factory $UserBadgeCopyWith(UserBadge value, $Res Function(UserBadge) then) =
      _$UserBadgeCopyWithImpl<$Res, UserBadge>;
  @useResult
  $Res call(
      {int id,
      Badge badge,
      @JsonKey(name: 'earned_at') DateTime earnedAt,
      @JsonKey(name: 'progress_data') Map<String, dynamic> progressData});

  $BadgeCopyWith<$Res> get badge;
}

/// @nodoc
class _$UserBadgeCopyWithImpl<$Res, $Val extends UserBadge>
    implements $UserBadgeCopyWith<$Res> {
  _$UserBadgeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? badge = null,
    Object? earnedAt = null,
    Object? progressData = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      badge: null == badge
          ? _value.badge
          : badge // ignore: cast_nullable_to_non_nullable
              as Badge,
      earnedAt: null == earnedAt
          ? _value.earnedAt
          : earnedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      progressData: null == progressData
          ? _value.progressData
          : progressData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BadgeCopyWith<$Res> get badge {
    return $BadgeCopyWith<$Res>(_value.badge, (value) {
      return _then(_value.copyWith(badge: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserBadgeImplCopyWith<$Res>
    implements $UserBadgeCopyWith<$Res> {
  factory _$$UserBadgeImplCopyWith(
          _$UserBadgeImpl value, $Res Function(_$UserBadgeImpl) then) =
      __$$UserBadgeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      Badge badge,
      @JsonKey(name: 'earned_at') DateTime earnedAt,
      @JsonKey(name: 'progress_data') Map<String, dynamic> progressData});

  @override
  $BadgeCopyWith<$Res> get badge;
}

/// @nodoc
class __$$UserBadgeImplCopyWithImpl<$Res>
    extends _$UserBadgeCopyWithImpl<$Res, _$UserBadgeImpl>
    implements _$$UserBadgeImplCopyWith<$Res> {
  __$$UserBadgeImplCopyWithImpl(
      _$UserBadgeImpl _value, $Res Function(_$UserBadgeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? badge = null,
    Object? earnedAt = null,
    Object? progressData = null,
  }) {
    return _then(_$UserBadgeImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      badge: null == badge
          ? _value.badge
          : badge // ignore: cast_nullable_to_non_nullable
              as Badge,
      earnedAt: null == earnedAt
          ? _value.earnedAt
          : earnedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      progressData: null == progressData
          ? _value._progressData
          : progressData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserBadgeImpl implements _UserBadge {
  const _$UserBadgeImpl(
      {required this.id,
      required this.badge,
      @JsonKey(name: 'earned_at') required this.earnedAt,
      @JsonKey(name: 'progress_data')
      final Map<String, dynamic> progressData = const {}})
      : _progressData = progressData;

  factory _$UserBadgeImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserBadgeImplFromJson(json);

  @override
  final int id;
  @override
  final Badge badge;
  @override
  @JsonKey(name: 'earned_at')
  final DateTime earnedAt;
  final Map<String, dynamic> _progressData;
  @override
  @JsonKey(name: 'progress_data')
  Map<String, dynamic> get progressData {
    if (_progressData is EqualUnmodifiableMapView) return _progressData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_progressData);
  }

  @override
  String toString() {
    return 'UserBadge(id: $id, badge: $badge, earnedAt: $earnedAt, progressData: $progressData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserBadgeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.badge, badge) || other.badge == badge) &&
            (identical(other.earnedAt, earnedAt) ||
                other.earnedAt == earnedAt) &&
            const DeepCollectionEquality()
                .equals(other._progressData, _progressData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, badge, earnedAt,
      const DeepCollectionEquality().hash(_progressData));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserBadgeImplCopyWith<_$UserBadgeImpl> get copyWith =>
      __$$UserBadgeImplCopyWithImpl<_$UserBadgeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserBadgeImplToJson(
      this,
    );
  }
}

abstract class _UserBadge implements UserBadge {
  const factory _UserBadge(
      {required final int id,
      required final Badge badge,
      @JsonKey(name: 'earned_at') required final DateTime earnedAt,
      @JsonKey(name: 'progress_data')
      final Map<String, dynamic> progressData}) = _$UserBadgeImpl;

  factory _UserBadge.fromJson(Map<String, dynamic> json) =
      _$UserBadgeImpl.fromJson;

  @override
  int get id;
  @override
  Badge get badge;
  @override
  @JsonKey(name: 'earned_at')
  DateTime get earnedAt;
  @override
  @JsonKey(name: 'progress_data')
  Map<String, dynamic> get progressData;
  @override
  @JsonKey(ignore: true)
  _$$UserBadgeImplCopyWith<_$UserBadgeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Challenge _$ChallengeFromJson(Map<String, dynamic> json) {
  return _Challenge.fromJson(json);
}

/// @nodoc
mixin _$Challenge {
  int get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'challenge_type')
  String get challengeType => throw _privateConstructorUsedError;
  @JsonKey(name: 'type_display')
  String get typeDisplay => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  @JsonKey(name: 'difficulty_display')
  String get difficultyDisplay => throw _privateConstructorUsedError;
  Map<String, dynamic> get requirements => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_reward')
  int get pointsReward => throw _privateConstructorUsedError;
  @JsonKey(name: 'badge_reward')
  Badge? get badgeReward => throw _privateConstructorUsedError;
  @JsonKey(name: 'start_date')
  DateTime get startDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'end_date')
  DateTime get endDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'duration_days')
  int get durationDays => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_participants')
  int? get maxParticipants => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_ongoing')
  bool get isOngoing => throw _privateConstructorUsedError;
  @JsonKey(name: 'participant_count')
  int get participantCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_participation')
  ChallengeParticipation? get userParticipation =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'is_featured')
  bool get isFeatured => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChallengeCopyWith<Challenge> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChallengeCopyWith<$Res> {
  factory $ChallengeCopyWith(Challenge value, $Res Function(Challenge) then) =
      _$ChallengeCopyWithImpl<$Res, Challenge>;
  @useResult
  $Res call(
      {int id,
      String title,
      String description,
      @JsonKey(name: 'challenge_type') String challengeType,
      @JsonKey(name: 'type_display') String typeDisplay,
      String difficulty,
      @JsonKey(name: 'difficulty_display') String difficultyDisplay,
      Map<String, dynamic> requirements,
      @JsonKey(name: 'points_reward') int pointsReward,
      @JsonKey(name: 'badge_reward') Badge? badgeReward,
      @JsonKey(name: 'start_date') DateTime startDate,
      @JsonKey(name: 'end_date') DateTime endDate,
      @JsonKey(name: 'duration_days') int durationDays,
      @JsonKey(name: 'max_participants') int? maxParticipants,
      @JsonKey(name: 'is_ongoing') bool isOngoing,
      @JsonKey(name: 'participant_count') int participantCount,
      @JsonKey(name: 'user_participation')
      ChallengeParticipation? userParticipation,
      @JsonKey(name: 'is_featured') bool isFeatured});

  $BadgeCopyWith<$Res>? get badgeReward;
  $ChallengeParticipationCopyWith<$Res>? get userParticipation;
}

/// @nodoc
class _$ChallengeCopyWithImpl<$Res, $Val extends Challenge>
    implements $ChallengeCopyWith<$Res> {
  _$ChallengeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? challengeType = null,
    Object? typeDisplay = null,
    Object? difficulty = null,
    Object? difficultyDisplay = null,
    Object? requirements = null,
    Object? pointsReward = null,
    Object? badgeReward = freezed,
    Object? startDate = null,
    Object? endDate = null,
    Object? durationDays = null,
    Object? maxParticipants = freezed,
    Object? isOngoing = null,
    Object? participantCount = null,
    Object? userParticipation = freezed,
    Object? isFeatured = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      challengeType: null == challengeType
          ? _value.challengeType
          : challengeType // ignore: cast_nullable_to_non_nullable
              as String,
      typeDisplay: null == typeDisplay
          ? _value.typeDisplay
          : typeDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      difficultyDisplay: null == difficultyDisplay
          ? _value.difficultyDisplay
          : difficultyDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      requirements: null == requirements
          ? _value.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      pointsReward: null == pointsReward
          ? _value.pointsReward
          : pointsReward // ignore: cast_nullable_to_non_nullable
              as int,
      badgeReward: freezed == badgeReward
          ? _value.badgeReward
          : badgeReward // ignore: cast_nullable_to_non_nullable
              as Badge?,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationDays: null == durationDays
          ? _value.durationDays
          : durationDays // ignore: cast_nullable_to_non_nullable
              as int,
      maxParticipants: freezed == maxParticipants
          ? _value.maxParticipants
          : maxParticipants // ignore: cast_nullable_to_non_nullable
              as int?,
      isOngoing: null == isOngoing
          ? _value.isOngoing
          : isOngoing // ignore: cast_nullable_to_non_nullable
              as bool,
      participantCount: null == participantCount
          ? _value.participantCount
          : participantCount // ignore: cast_nullable_to_non_nullable
              as int,
      userParticipation: freezed == userParticipation
          ? _value.userParticipation
          : userParticipation // ignore: cast_nullable_to_non_nullable
              as ChallengeParticipation?,
      isFeatured: null == isFeatured
          ? _value.isFeatured
          : isFeatured // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BadgeCopyWith<$Res>? get badgeReward {
    if (_value.badgeReward == null) {
      return null;
    }

    return $BadgeCopyWith<$Res>(_value.badgeReward!, (value) {
      return _then(_value.copyWith(badgeReward: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ChallengeParticipationCopyWith<$Res>? get userParticipation {
    if (_value.userParticipation == null) {
      return null;
    }

    return $ChallengeParticipationCopyWith<$Res>(_value.userParticipation!,
        (value) {
      return _then(_value.copyWith(userParticipation: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChallengeImplCopyWith<$Res>
    implements $ChallengeCopyWith<$Res> {
  factory _$$ChallengeImplCopyWith(
          _$ChallengeImpl value, $Res Function(_$ChallengeImpl) then) =
      __$$ChallengeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String title,
      String description,
      @JsonKey(name: 'challenge_type') String challengeType,
      @JsonKey(name: 'type_display') String typeDisplay,
      String difficulty,
      @JsonKey(name: 'difficulty_display') String difficultyDisplay,
      Map<String, dynamic> requirements,
      @JsonKey(name: 'points_reward') int pointsReward,
      @JsonKey(name: 'badge_reward') Badge? badgeReward,
      @JsonKey(name: 'start_date') DateTime startDate,
      @JsonKey(name: 'end_date') DateTime endDate,
      @JsonKey(name: 'duration_days') int durationDays,
      @JsonKey(name: 'max_participants') int? maxParticipants,
      @JsonKey(name: 'is_ongoing') bool isOngoing,
      @JsonKey(name: 'participant_count') int participantCount,
      @JsonKey(name: 'user_participation')
      ChallengeParticipation? userParticipation,
      @JsonKey(name: 'is_featured') bool isFeatured});

  @override
  $BadgeCopyWith<$Res>? get badgeReward;
  @override
  $ChallengeParticipationCopyWith<$Res>? get userParticipation;
}

/// @nodoc
class __$$ChallengeImplCopyWithImpl<$Res>
    extends _$ChallengeCopyWithImpl<$Res, _$ChallengeImpl>
    implements _$$ChallengeImplCopyWith<$Res> {
  __$$ChallengeImplCopyWithImpl(
      _$ChallengeImpl _value, $Res Function(_$ChallengeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? challengeType = null,
    Object? typeDisplay = null,
    Object? difficulty = null,
    Object? difficultyDisplay = null,
    Object? requirements = null,
    Object? pointsReward = null,
    Object? badgeReward = freezed,
    Object? startDate = null,
    Object? endDate = null,
    Object? durationDays = null,
    Object? maxParticipants = freezed,
    Object? isOngoing = null,
    Object? participantCount = null,
    Object? userParticipation = freezed,
    Object? isFeatured = null,
  }) {
    return _then(_$ChallengeImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      challengeType: null == challengeType
          ? _value.challengeType
          : challengeType // ignore: cast_nullable_to_non_nullable
              as String,
      typeDisplay: null == typeDisplay
          ? _value.typeDisplay
          : typeDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      difficultyDisplay: null == difficultyDisplay
          ? _value.difficultyDisplay
          : difficultyDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      requirements: null == requirements
          ? _value._requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      pointsReward: null == pointsReward
          ? _value.pointsReward
          : pointsReward // ignore: cast_nullable_to_non_nullable
              as int,
      badgeReward: freezed == badgeReward
          ? _value.badgeReward
          : badgeReward // ignore: cast_nullable_to_non_nullable
              as Badge?,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationDays: null == durationDays
          ? _value.durationDays
          : durationDays // ignore: cast_nullable_to_non_nullable
              as int,
      maxParticipants: freezed == maxParticipants
          ? _value.maxParticipants
          : maxParticipants // ignore: cast_nullable_to_non_nullable
              as int?,
      isOngoing: null == isOngoing
          ? _value.isOngoing
          : isOngoing // ignore: cast_nullable_to_non_nullable
              as bool,
      participantCount: null == participantCount
          ? _value.participantCount
          : participantCount // ignore: cast_nullable_to_non_nullable
              as int,
      userParticipation: freezed == userParticipation
          ? _value.userParticipation
          : userParticipation // ignore: cast_nullable_to_non_nullable
              as ChallengeParticipation?,
      isFeatured: null == isFeatured
          ? _value.isFeatured
          : isFeatured // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChallengeImpl implements _Challenge {
  const _$ChallengeImpl(
      {required this.id,
      required this.title,
      required this.description,
      @JsonKey(name: 'challenge_type') required this.challengeType,
      @JsonKey(name: 'type_display') required this.typeDisplay,
      required this.difficulty,
      @JsonKey(name: 'difficulty_display') required this.difficultyDisplay,
      final Map<String, dynamic> requirements = const {},
      @JsonKey(name: 'points_reward') required this.pointsReward,
      @JsonKey(name: 'badge_reward') this.badgeReward,
      @JsonKey(name: 'start_date') required this.startDate,
      @JsonKey(name: 'end_date') required this.endDate,
      @JsonKey(name: 'duration_days') required this.durationDays,
      @JsonKey(name: 'max_participants') this.maxParticipants,
      @JsonKey(name: 'is_ongoing') this.isOngoing = false,
      @JsonKey(name: 'participant_count') this.participantCount = 0,
      @JsonKey(name: 'user_participation') this.userParticipation,
      @JsonKey(name: 'is_featured') this.isFeatured = false})
      : _requirements = requirements;

  factory _$ChallengeImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChallengeImplFromJson(json);

  @override
  final int id;
  @override
  final String title;
  @override
  final String description;
  @override
  @JsonKey(name: 'challenge_type')
  final String challengeType;
  @override
  @JsonKey(name: 'type_display')
  final String typeDisplay;
  @override
  final String difficulty;
  @override
  @JsonKey(name: 'difficulty_display')
  final String difficultyDisplay;
  final Map<String, dynamic> _requirements;
  @override
  @JsonKey()
  Map<String, dynamic> get requirements {
    if (_requirements is EqualUnmodifiableMapView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_requirements);
  }

  @override
  @JsonKey(name: 'points_reward')
  final int pointsReward;
  @override
  @JsonKey(name: 'badge_reward')
  final Badge? badgeReward;
  @override
  @JsonKey(name: 'start_date')
  final DateTime startDate;
  @override
  @JsonKey(name: 'end_date')
  final DateTime endDate;
  @override
  @JsonKey(name: 'duration_days')
  final int durationDays;
  @override
  @JsonKey(name: 'max_participants')
  final int? maxParticipants;
  @override
  @JsonKey(name: 'is_ongoing')
  final bool isOngoing;
  @override
  @JsonKey(name: 'participant_count')
  final int participantCount;
  @override
  @JsonKey(name: 'user_participation')
  final ChallengeParticipation? userParticipation;
  @override
  @JsonKey(name: 'is_featured')
  final bool isFeatured;

  @override
  String toString() {
    return 'Challenge(id: $id, title: $title, description: $description, challengeType: $challengeType, typeDisplay: $typeDisplay, difficulty: $difficulty, difficultyDisplay: $difficultyDisplay, requirements: $requirements, pointsReward: $pointsReward, badgeReward: $badgeReward, startDate: $startDate, endDate: $endDate, durationDays: $durationDays, maxParticipants: $maxParticipants, isOngoing: $isOngoing, participantCount: $participantCount, userParticipation: $userParticipation, isFeatured: $isFeatured)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChallengeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.challengeType, challengeType) ||
                other.challengeType == challengeType) &&
            (identical(other.typeDisplay, typeDisplay) ||
                other.typeDisplay == typeDisplay) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.difficultyDisplay, difficultyDisplay) ||
                other.difficultyDisplay == difficultyDisplay) &&
            const DeepCollectionEquality()
                .equals(other._requirements, _requirements) &&
            (identical(other.pointsReward, pointsReward) ||
                other.pointsReward == pointsReward) &&
            (identical(other.badgeReward, badgeReward) ||
                other.badgeReward == badgeReward) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.durationDays, durationDays) ||
                other.durationDays == durationDays) &&
            (identical(other.maxParticipants, maxParticipants) ||
                other.maxParticipants == maxParticipants) &&
            (identical(other.isOngoing, isOngoing) ||
                other.isOngoing == isOngoing) &&
            (identical(other.participantCount, participantCount) ||
                other.participantCount == participantCount) &&
            (identical(other.userParticipation, userParticipation) ||
                other.userParticipation == userParticipation) &&
            (identical(other.isFeatured, isFeatured) ||
                other.isFeatured == isFeatured));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      challengeType,
      typeDisplay,
      difficulty,
      difficultyDisplay,
      const DeepCollectionEquality().hash(_requirements),
      pointsReward,
      badgeReward,
      startDate,
      endDate,
      durationDays,
      maxParticipants,
      isOngoing,
      participantCount,
      userParticipation,
      isFeatured);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChallengeImplCopyWith<_$ChallengeImpl> get copyWith =>
      __$$ChallengeImplCopyWithImpl<_$ChallengeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChallengeImplToJson(
      this,
    );
  }
}

abstract class _Challenge implements Challenge {
  const factory _Challenge(
      {required final int id,
      required final String title,
      required final String description,
      @JsonKey(name: 'challenge_type') required final String challengeType,
      @JsonKey(name: 'type_display') required final String typeDisplay,
      required final String difficulty,
      @JsonKey(name: 'difficulty_display')
      required final String difficultyDisplay,
      final Map<String, dynamic> requirements,
      @JsonKey(name: 'points_reward') required final int pointsReward,
      @JsonKey(name: 'badge_reward') final Badge? badgeReward,
      @JsonKey(name: 'start_date') required final DateTime startDate,
      @JsonKey(name: 'end_date') required final DateTime endDate,
      @JsonKey(name: 'duration_days') required final int durationDays,
      @JsonKey(name: 'max_participants') final int? maxParticipants,
      @JsonKey(name: 'is_ongoing') final bool isOngoing,
      @JsonKey(name: 'participant_count') final int participantCount,
      @JsonKey(name: 'user_participation')
      final ChallengeParticipation? userParticipation,
      @JsonKey(name: 'is_featured') final bool isFeatured}) = _$ChallengeImpl;

  factory _Challenge.fromJson(Map<String, dynamic> json) =
      _$ChallengeImpl.fromJson;

  @override
  int get id;
  @override
  String get title;
  @override
  String get description;
  @override
  @JsonKey(name: 'challenge_type')
  String get challengeType;
  @override
  @JsonKey(name: 'type_display')
  String get typeDisplay;
  @override
  String get difficulty;
  @override
  @JsonKey(name: 'difficulty_display')
  String get difficultyDisplay;
  @override
  Map<String, dynamic> get requirements;
  @override
  @JsonKey(name: 'points_reward')
  int get pointsReward;
  @override
  @JsonKey(name: 'badge_reward')
  Badge? get badgeReward;
  @override
  @JsonKey(name: 'start_date')
  DateTime get startDate;
  @override
  @JsonKey(name: 'end_date')
  DateTime get endDate;
  @override
  @JsonKey(name: 'duration_days')
  int get durationDays;
  @override
  @JsonKey(name: 'max_participants')
  int? get maxParticipants;
  @override
  @JsonKey(name: 'is_ongoing')
  bool get isOngoing;
  @override
  @JsonKey(name: 'participant_count')
  int get participantCount;
  @override
  @JsonKey(name: 'user_participation')
  ChallengeParticipation? get userParticipation;
  @override
  @JsonKey(name: 'is_featured')
  bool get isFeatured;
  @override
  @JsonKey(ignore: true)
  _$$ChallengeImplCopyWith<_$ChallengeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChallengeParticipation _$ChallengeParticipationFromJson(
    Map<String, dynamic> json) {
  return _ChallengeParticipation.fromJson(json);
}

/// @nodoc
mixin _$ChallengeParticipation {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'challenge_title')
  String? get challengeTitle => throw _privateConstructorUsedError;
  Map<String, dynamic> get progress => throw _privateConstructorUsedError;
  @JsonKey(name: 'completion_percentage')
  double get completionPercentage => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_completed')
  bool get isCompleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'completed_at')
  DateTime? get completedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'joined_at')
  DateTime get joinedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChallengeParticipationCopyWith<ChallengeParticipation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChallengeParticipationCopyWith<$Res> {
  factory $ChallengeParticipationCopyWith(ChallengeParticipation value,
          $Res Function(ChallengeParticipation) then) =
      _$ChallengeParticipationCopyWithImpl<$Res, ChallengeParticipation>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'challenge_title') String? challengeTitle,
      Map<String, dynamic> progress,
      @JsonKey(name: 'completion_percentage') double completionPercentage,
      @JsonKey(name: 'is_completed') bool isCompleted,
      @JsonKey(name: 'completed_at') DateTime? completedAt,
      @JsonKey(name: 'joined_at') DateTime joinedAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$ChallengeParticipationCopyWithImpl<$Res,
        $Val extends ChallengeParticipation>
    implements $ChallengeParticipationCopyWith<$Res> {
  _$ChallengeParticipationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? challengeTitle = freezed,
    Object? progress = null,
    Object? completionPercentage = null,
    Object? isCompleted = null,
    Object? completedAt = freezed,
    Object? joinedAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      challengeTitle: freezed == challengeTitle
          ? _value.challengeTitle
          : challengeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      completionPercentage: null == completionPercentage
          ? _value.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      joinedAt: null == joinedAt
          ? _value.joinedAt
          : joinedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChallengeParticipationImplCopyWith<$Res>
    implements $ChallengeParticipationCopyWith<$Res> {
  factory _$$ChallengeParticipationImplCopyWith(
          _$ChallengeParticipationImpl value,
          $Res Function(_$ChallengeParticipationImpl) then) =
      __$$ChallengeParticipationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'challenge_title') String? challengeTitle,
      Map<String, dynamic> progress,
      @JsonKey(name: 'completion_percentage') double completionPercentage,
      @JsonKey(name: 'is_completed') bool isCompleted,
      @JsonKey(name: 'completed_at') DateTime? completedAt,
      @JsonKey(name: 'joined_at') DateTime joinedAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$$ChallengeParticipationImplCopyWithImpl<$Res>
    extends _$ChallengeParticipationCopyWithImpl<$Res,
        _$ChallengeParticipationImpl>
    implements _$$ChallengeParticipationImplCopyWith<$Res> {
  __$$ChallengeParticipationImplCopyWithImpl(
      _$ChallengeParticipationImpl _value,
      $Res Function(_$ChallengeParticipationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? challengeTitle = freezed,
    Object? progress = null,
    Object? completionPercentage = null,
    Object? isCompleted = null,
    Object? completedAt = freezed,
    Object? joinedAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$ChallengeParticipationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      challengeTitle: freezed == challengeTitle
          ? _value.challengeTitle
          : challengeTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      progress: null == progress
          ? _value._progress
          : progress // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      completionPercentage: null == completionPercentage
          ? _value.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      joinedAt: null == joinedAt
          ? _value.joinedAt
          : joinedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChallengeParticipationImpl implements _ChallengeParticipation {
  const _$ChallengeParticipationImpl(
      {required this.id,
      @JsonKey(name: 'challenge_title') this.challengeTitle,
      final Map<String, dynamic> progress = const {},
      @JsonKey(name: 'completion_percentage') this.completionPercentage = 0.0,
      @JsonKey(name: 'is_completed') this.isCompleted = false,
      @JsonKey(name: 'completed_at') this.completedAt,
      @JsonKey(name: 'joined_at') required this.joinedAt,
      @JsonKey(name: 'updated_at') required this.updatedAt})
      : _progress = progress;

  factory _$ChallengeParticipationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChallengeParticipationImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'challenge_title')
  final String? challengeTitle;
  final Map<String, dynamic> _progress;
  @override
  @JsonKey()
  Map<String, dynamic> get progress {
    if (_progress is EqualUnmodifiableMapView) return _progress;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_progress);
  }

  @override
  @JsonKey(name: 'completion_percentage')
  final double completionPercentage;
  @override
  @JsonKey(name: 'is_completed')
  final bool isCompleted;
  @override
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;
  @override
  @JsonKey(name: 'joined_at')
  final DateTime joinedAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  String toString() {
    return 'ChallengeParticipation(id: $id, challengeTitle: $challengeTitle, progress: $progress, completionPercentage: $completionPercentage, isCompleted: $isCompleted, completedAt: $completedAt, joinedAt: $joinedAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChallengeParticipationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.challengeTitle, challengeTitle) ||
                other.challengeTitle == challengeTitle) &&
            const DeepCollectionEquality().equals(other._progress, _progress) &&
            (identical(other.completionPercentage, completionPercentage) ||
                other.completionPercentage == completionPercentage) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.joinedAt, joinedAt) ||
                other.joinedAt == joinedAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      challengeTitle,
      const DeepCollectionEquality().hash(_progress),
      completionPercentage,
      isCompleted,
      completedAt,
      joinedAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChallengeParticipationImplCopyWith<_$ChallengeParticipationImpl>
      get copyWith => __$$ChallengeParticipationImplCopyWithImpl<
          _$ChallengeParticipationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChallengeParticipationImplToJson(
      this,
    );
  }
}

abstract class _ChallengeParticipation implements ChallengeParticipation {
  const factory _ChallengeParticipation(
      {required final int id,
      @JsonKey(name: 'challenge_title') final String? challengeTitle,
      final Map<String, dynamic> progress,
      @JsonKey(name: 'completion_percentage') final double completionPercentage,
      @JsonKey(name: 'is_completed') final bool isCompleted,
      @JsonKey(name: 'completed_at') final DateTime? completedAt,
      @JsonKey(name: 'joined_at') required final DateTime joinedAt,
      @JsonKey(name: 'updated_at')
      required final DateTime updatedAt}) = _$ChallengeParticipationImpl;

  factory _ChallengeParticipation.fromJson(Map<String, dynamic> json) =
      _$ChallengeParticipationImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'challenge_title')
  String? get challengeTitle;
  @override
  Map<String, dynamic> get progress;
  @override
  @JsonKey(name: 'completion_percentage')
  double get completionPercentage;
  @override
  @JsonKey(name: 'is_completed')
  bool get isCompleted;
  @override
  @JsonKey(name: 'completed_at')
  DateTime? get completedAt;
  @override
  @JsonKey(name: 'joined_at')
  DateTime get joinedAt;
  @override
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ChallengeParticipationImplCopyWith<_$ChallengeParticipationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UserLevel _$UserLevelFromJson(Map<String, dynamic> json) {
  return _UserLevel.fromJson(json);
}

/// @nodoc
mixin _$UserLevel {
  @JsonKey(name: 'total_points')
  int get totalPoints => throw _privateConstructorUsedError;
  @JsonKey(name: 'current_level')
  int get currentLevel => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_to_next_level')
  int get pointsToNextLevel => throw _privateConstructorUsedError;
  @JsonKey(name: 'level_progress_percentage')
  double get levelProgressPercentage => throw _privateConstructorUsedError;
  @JsonKey(name: 'next_level_points')
  int get nextLevelPoints => throw _privateConstructorUsedError;
  @JsonKey(name: 'reading_streak')
  int get readingStreak => throw _privateConstructorUsedError;
  @JsonKey(name: 'writing_streak')
  int get writingStreak => throw _privateConstructorUsedError;
  @JsonKey(name: 'engagement_streak')
  int get engagementStreak => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_posts_read')
  int get totalPostsRead => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_posts_written')
  int get totalPostsWritten => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_comments_made')
  int get totalCommentsMade => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_likes_given')
  int get totalLikesGiven => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_voice_comments')
  int get totalVoiceComments => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserLevelCopyWith<UserLevel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserLevelCopyWith<$Res> {
  factory $UserLevelCopyWith(UserLevel value, $Res Function(UserLevel) then) =
      _$UserLevelCopyWithImpl<$Res, UserLevel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'total_points') int totalPoints,
      @JsonKey(name: 'current_level') int currentLevel,
      @JsonKey(name: 'points_to_next_level') int pointsToNextLevel,
      @JsonKey(name: 'level_progress_percentage')
      double levelProgressPercentage,
      @JsonKey(name: 'next_level_points') int nextLevelPoints,
      @JsonKey(name: 'reading_streak') int readingStreak,
      @JsonKey(name: 'writing_streak') int writingStreak,
      @JsonKey(name: 'engagement_streak') int engagementStreak,
      @JsonKey(name: 'total_posts_read') int totalPostsRead,
      @JsonKey(name: 'total_posts_written') int totalPostsWritten,
      @JsonKey(name: 'total_comments_made') int totalCommentsMade,
      @JsonKey(name: 'total_likes_given') int totalLikesGiven,
      @JsonKey(name: 'total_voice_comments') int totalVoiceComments});
}

/// @nodoc
class _$UserLevelCopyWithImpl<$Res, $Val extends UserLevel>
    implements $UserLevelCopyWith<$Res> {
  _$UserLevelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalPoints = null,
    Object? currentLevel = null,
    Object? pointsToNextLevel = null,
    Object? levelProgressPercentage = null,
    Object? nextLevelPoints = null,
    Object? readingStreak = null,
    Object? writingStreak = null,
    Object? engagementStreak = null,
    Object? totalPostsRead = null,
    Object? totalPostsWritten = null,
    Object? totalCommentsMade = null,
    Object? totalLikesGiven = null,
    Object? totalVoiceComments = null,
  }) {
    return _then(_value.copyWith(
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      currentLevel: null == currentLevel
          ? _value.currentLevel
          : currentLevel // ignore: cast_nullable_to_non_nullable
              as int,
      pointsToNextLevel: null == pointsToNextLevel
          ? _value.pointsToNextLevel
          : pointsToNextLevel // ignore: cast_nullable_to_non_nullable
              as int,
      levelProgressPercentage: null == levelProgressPercentage
          ? _value.levelProgressPercentage
          : levelProgressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      nextLevelPoints: null == nextLevelPoints
          ? _value.nextLevelPoints
          : nextLevelPoints // ignore: cast_nullable_to_non_nullable
              as int,
      readingStreak: null == readingStreak
          ? _value.readingStreak
          : readingStreak // ignore: cast_nullable_to_non_nullable
              as int,
      writingStreak: null == writingStreak
          ? _value.writingStreak
          : writingStreak // ignore: cast_nullable_to_non_nullable
              as int,
      engagementStreak: null == engagementStreak
          ? _value.engagementStreak
          : engagementStreak // ignore: cast_nullable_to_non_nullable
              as int,
      totalPostsRead: null == totalPostsRead
          ? _value.totalPostsRead
          : totalPostsRead // ignore: cast_nullable_to_non_nullable
              as int,
      totalPostsWritten: null == totalPostsWritten
          ? _value.totalPostsWritten
          : totalPostsWritten // ignore: cast_nullable_to_non_nullable
              as int,
      totalCommentsMade: null == totalCommentsMade
          ? _value.totalCommentsMade
          : totalCommentsMade // ignore: cast_nullable_to_non_nullable
              as int,
      totalLikesGiven: null == totalLikesGiven
          ? _value.totalLikesGiven
          : totalLikesGiven // ignore: cast_nullable_to_non_nullable
              as int,
      totalVoiceComments: null == totalVoiceComments
          ? _value.totalVoiceComments
          : totalVoiceComments // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserLevelImplCopyWith<$Res>
    implements $UserLevelCopyWith<$Res> {
  factory _$$UserLevelImplCopyWith(
          _$UserLevelImpl value, $Res Function(_$UserLevelImpl) then) =
      __$$UserLevelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'total_points') int totalPoints,
      @JsonKey(name: 'current_level') int currentLevel,
      @JsonKey(name: 'points_to_next_level') int pointsToNextLevel,
      @JsonKey(name: 'level_progress_percentage')
      double levelProgressPercentage,
      @JsonKey(name: 'next_level_points') int nextLevelPoints,
      @JsonKey(name: 'reading_streak') int readingStreak,
      @JsonKey(name: 'writing_streak') int writingStreak,
      @JsonKey(name: 'engagement_streak') int engagementStreak,
      @JsonKey(name: 'total_posts_read') int totalPostsRead,
      @JsonKey(name: 'total_posts_written') int totalPostsWritten,
      @JsonKey(name: 'total_comments_made') int totalCommentsMade,
      @JsonKey(name: 'total_likes_given') int totalLikesGiven,
      @JsonKey(name: 'total_voice_comments') int totalVoiceComments});
}

/// @nodoc
class __$$UserLevelImplCopyWithImpl<$Res>
    extends _$UserLevelCopyWithImpl<$Res, _$UserLevelImpl>
    implements _$$UserLevelImplCopyWith<$Res> {
  __$$UserLevelImplCopyWithImpl(
      _$UserLevelImpl _value, $Res Function(_$UserLevelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalPoints = null,
    Object? currentLevel = null,
    Object? pointsToNextLevel = null,
    Object? levelProgressPercentage = null,
    Object? nextLevelPoints = null,
    Object? readingStreak = null,
    Object? writingStreak = null,
    Object? engagementStreak = null,
    Object? totalPostsRead = null,
    Object? totalPostsWritten = null,
    Object? totalCommentsMade = null,
    Object? totalLikesGiven = null,
    Object? totalVoiceComments = null,
  }) {
    return _then(_$UserLevelImpl(
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      currentLevel: null == currentLevel
          ? _value.currentLevel
          : currentLevel // ignore: cast_nullable_to_non_nullable
              as int,
      pointsToNextLevel: null == pointsToNextLevel
          ? _value.pointsToNextLevel
          : pointsToNextLevel // ignore: cast_nullable_to_non_nullable
              as int,
      levelProgressPercentage: null == levelProgressPercentage
          ? _value.levelProgressPercentage
          : levelProgressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      nextLevelPoints: null == nextLevelPoints
          ? _value.nextLevelPoints
          : nextLevelPoints // ignore: cast_nullable_to_non_nullable
              as int,
      readingStreak: null == readingStreak
          ? _value.readingStreak
          : readingStreak // ignore: cast_nullable_to_non_nullable
              as int,
      writingStreak: null == writingStreak
          ? _value.writingStreak
          : writingStreak // ignore: cast_nullable_to_non_nullable
              as int,
      engagementStreak: null == engagementStreak
          ? _value.engagementStreak
          : engagementStreak // ignore: cast_nullable_to_non_nullable
              as int,
      totalPostsRead: null == totalPostsRead
          ? _value.totalPostsRead
          : totalPostsRead // ignore: cast_nullable_to_non_nullable
              as int,
      totalPostsWritten: null == totalPostsWritten
          ? _value.totalPostsWritten
          : totalPostsWritten // ignore: cast_nullable_to_non_nullable
              as int,
      totalCommentsMade: null == totalCommentsMade
          ? _value.totalCommentsMade
          : totalCommentsMade // ignore: cast_nullable_to_non_nullable
              as int,
      totalLikesGiven: null == totalLikesGiven
          ? _value.totalLikesGiven
          : totalLikesGiven // ignore: cast_nullable_to_non_nullable
              as int,
      totalVoiceComments: null == totalVoiceComments
          ? _value.totalVoiceComments
          : totalVoiceComments // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserLevelImpl implements _UserLevel {
  const _$UserLevelImpl(
      {@JsonKey(name: 'total_points') this.totalPoints = 0,
      @JsonKey(name: 'current_level') this.currentLevel = 1,
      @JsonKey(name: 'points_to_next_level') this.pointsToNextLevel = 100,
      @JsonKey(name: 'level_progress_percentage')
      this.levelProgressPercentage = 0.0,
      @JsonKey(name: 'next_level_points') this.nextLevelPoints = 100,
      @JsonKey(name: 'reading_streak') this.readingStreak = 0,
      @JsonKey(name: 'writing_streak') this.writingStreak = 0,
      @JsonKey(name: 'engagement_streak') this.engagementStreak = 0,
      @JsonKey(name: 'total_posts_read') this.totalPostsRead = 0,
      @JsonKey(name: 'total_posts_written') this.totalPostsWritten = 0,
      @JsonKey(name: 'total_comments_made') this.totalCommentsMade = 0,
      @JsonKey(name: 'total_likes_given') this.totalLikesGiven = 0,
      @JsonKey(name: 'total_voice_comments') this.totalVoiceComments = 0});

  factory _$UserLevelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserLevelImplFromJson(json);

  @override
  @JsonKey(name: 'total_points')
  final int totalPoints;
  @override
  @JsonKey(name: 'current_level')
  final int currentLevel;
  @override
  @JsonKey(name: 'points_to_next_level')
  final int pointsToNextLevel;
  @override
  @JsonKey(name: 'level_progress_percentage')
  final double levelProgressPercentage;
  @override
  @JsonKey(name: 'next_level_points')
  final int nextLevelPoints;
  @override
  @JsonKey(name: 'reading_streak')
  final int readingStreak;
  @override
  @JsonKey(name: 'writing_streak')
  final int writingStreak;
  @override
  @JsonKey(name: 'engagement_streak')
  final int engagementStreak;
  @override
  @JsonKey(name: 'total_posts_read')
  final int totalPostsRead;
  @override
  @JsonKey(name: 'total_posts_written')
  final int totalPostsWritten;
  @override
  @JsonKey(name: 'total_comments_made')
  final int totalCommentsMade;
  @override
  @JsonKey(name: 'total_likes_given')
  final int totalLikesGiven;
  @override
  @JsonKey(name: 'total_voice_comments')
  final int totalVoiceComments;

  @override
  String toString() {
    return 'UserLevel(totalPoints: $totalPoints, currentLevel: $currentLevel, pointsToNextLevel: $pointsToNextLevel, levelProgressPercentage: $levelProgressPercentage, nextLevelPoints: $nextLevelPoints, readingStreak: $readingStreak, writingStreak: $writingStreak, engagementStreak: $engagementStreak, totalPostsRead: $totalPostsRead, totalPostsWritten: $totalPostsWritten, totalCommentsMade: $totalCommentsMade, totalLikesGiven: $totalLikesGiven, totalVoiceComments: $totalVoiceComments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserLevelImpl &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints) &&
            (identical(other.currentLevel, currentLevel) ||
                other.currentLevel == currentLevel) &&
            (identical(other.pointsToNextLevel, pointsToNextLevel) ||
                other.pointsToNextLevel == pointsToNextLevel) &&
            (identical(
                    other.levelProgressPercentage, levelProgressPercentage) ||
                other.levelProgressPercentage == levelProgressPercentage) &&
            (identical(other.nextLevelPoints, nextLevelPoints) ||
                other.nextLevelPoints == nextLevelPoints) &&
            (identical(other.readingStreak, readingStreak) ||
                other.readingStreak == readingStreak) &&
            (identical(other.writingStreak, writingStreak) ||
                other.writingStreak == writingStreak) &&
            (identical(other.engagementStreak, engagementStreak) ||
                other.engagementStreak == engagementStreak) &&
            (identical(other.totalPostsRead, totalPostsRead) ||
                other.totalPostsRead == totalPostsRead) &&
            (identical(other.totalPostsWritten, totalPostsWritten) ||
                other.totalPostsWritten == totalPostsWritten) &&
            (identical(other.totalCommentsMade, totalCommentsMade) ||
                other.totalCommentsMade == totalCommentsMade) &&
            (identical(other.totalLikesGiven, totalLikesGiven) ||
                other.totalLikesGiven == totalLikesGiven) &&
            (identical(other.totalVoiceComments, totalVoiceComments) ||
                other.totalVoiceComments == totalVoiceComments));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalPoints,
      currentLevel,
      pointsToNextLevel,
      levelProgressPercentage,
      nextLevelPoints,
      readingStreak,
      writingStreak,
      engagementStreak,
      totalPostsRead,
      totalPostsWritten,
      totalCommentsMade,
      totalLikesGiven,
      totalVoiceComments);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserLevelImplCopyWith<_$UserLevelImpl> get copyWith =>
      __$$UserLevelImplCopyWithImpl<_$UserLevelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserLevelImplToJson(
      this,
    );
  }
}

abstract class _UserLevel implements UserLevel {
  const factory _UserLevel(
      {@JsonKey(name: 'total_points') final int totalPoints,
      @JsonKey(name: 'current_level') final int currentLevel,
      @JsonKey(name: 'points_to_next_level') final int pointsToNextLevel,
      @JsonKey(name: 'level_progress_percentage')
      final double levelProgressPercentage,
      @JsonKey(name: 'next_level_points') final int nextLevelPoints,
      @JsonKey(name: 'reading_streak') final int readingStreak,
      @JsonKey(name: 'writing_streak') final int writingStreak,
      @JsonKey(name: 'engagement_streak') final int engagementStreak,
      @JsonKey(name: 'total_posts_read') final int totalPostsRead,
      @JsonKey(name: 'total_posts_written') final int totalPostsWritten,
      @JsonKey(name: 'total_comments_made') final int totalCommentsMade,
      @JsonKey(name: 'total_likes_given') final int totalLikesGiven,
      @JsonKey(name: 'total_voice_comments')
      final int totalVoiceComments}) = _$UserLevelImpl;

  factory _UserLevel.fromJson(Map<String, dynamic> json) =
      _$UserLevelImpl.fromJson;

  @override
  @JsonKey(name: 'total_points')
  int get totalPoints;
  @override
  @JsonKey(name: 'current_level')
  int get currentLevel;
  @override
  @JsonKey(name: 'points_to_next_level')
  int get pointsToNextLevel;
  @override
  @JsonKey(name: 'level_progress_percentage')
  double get levelProgressPercentage;
  @override
  @JsonKey(name: 'next_level_points')
  int get nextLevelPoints;
  @override
  @JsonKey(name: 'reading_streak')
  int get readingStreak;
  @override
  @JsonKey(name: 'writing_streak')
  int get writingStreak;
  @override
  @JsonKey(name: 'engagement_streak')
  int get engagementStreak;
  @override
  @JsonKey(name: 'total_posts_read')
  int get totalPostsRead;
  @override
  @JsonKey(name: 'total_posts_written')
  int get totalPostsWritten;
  @override
  @JsonKey(name: 'total_comments_made')
  int get totalCommentsMade;
  @override
  @JsonKey(name: 'total_likes_given')
  int get totalLikesGiven;
  @override
  @JsonKey(name: 'total_voice_comments')
  int get totalVoiceComments;
  @override
  @JsonKey(ignore: true)
  _$$UserLevelImplCopyWith<_$UserLevelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PointTransaction _$PointTransactionFromJson(Map<String, dynamic> json) {
  return _PointTransaction.fromJson(json);
}

/// @nodoc
mixin _$PointTransaction {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'transaction_type')
  String get transactionType => throw _privateConstructorUsedError;
  @JsonKey(name: 'type_display')
  String get typeDisplay => throw _privateConstructorUsedError;
  int get points => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PointTransactionCopyWith<PointTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PointTransactionCopyWith<$Res> {
  factory $PointTransactionCopyWith(
          PointTransaction value, $Res Function(PointTransaction) then) =
      _$PointTransactionCopyWithImpl<$Res, PointTransaction>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'transaction_type') String transactionType,
      @JsonKey(name: 'type_display') String typeDisplay,
      int points,
      String description,
      @JsonKey(name: 'created_at') DateTime createdAt});
}

/// @nodoc
class _$PointTransactionCopyWithImpl<$Res, $Val extends PointTransaction>
    implements $PointTransactionCopyWith<$Res> {
  _$PointTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? transactionType = null,
    Object? typeDisplay = null,
    Object? points = null,
    Object? description = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String,
      typeDisplay: null == typeDisplay
          ? _value.typeDisplay
          : typeDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      points: null == points
          ? _value.points
          : points // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PointTransactionImplCopyWith<$Res>
    implements $PointTransactionCopyWith<$Res> {
  factory _$$PointTransactionImplCopyWith(_$PointTransactionImpl value,
          $Res Function(_$PointTransactionImpl) then) =
      __$$PointTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'transaction_type') String transactionType,
      @JsonKey(name: 'type_display') String typeDisplay,
      int points,
      String description,
      @JsonKey(name: 'created_at') DateTime createdAt});
}

/// @nodoc
class __$$PointTransactionImplCopyWithImpl<$Res>
    extends _$PointTransactionCopyWithImpl<$Res, _$PointTransactionImpl>
    implements _$$PointTransactionImplCopyWith<$Res> {
  __$$PointTransactionImplCopyWithImpl(_$PointTransactionImpl _value,
      $Res Function(_$PointTransactionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? transactionType = null,
    Object? typeDisplay = null,
    Object? points = null,
    Object? description = null,
    Object? createdAt = null,
  }) {
    return _then(_$PointTransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String,
      typeDisplay: null == typeDisplay
          ? _value.typeDisplay
          : typeDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      points: null == points
          ? _value.points
          : points // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PointTransactionImpl implements _PointTransaction {
  const _$PointTransactionImpl(
      {required this.id,
      @JsonKey(name: 'transaction_type') required this.transactionType,
      @JsonKey(name: 'type_display') required this.typeDisplay,
      required this.points,
      required this.description,
      @JsonKey(name: 'created_at') required this.createdAt});

  factory _$PointTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PointTransactionImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'transaction_type')
  final String transactionType;
  @override
  @JsonKey(name: 'type_display')
  final String typeDisplay;
  @override
  final int points;
  @override
  final String description;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  String toString() {
    return 'PointTransaction(id: $id, transactionType: $transactionType, typeDisplay: $typeDisplay, points: $points, description: $description, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PointTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.typeDisplay, typeDisplay) ||
                other.typeDisplay == typeDisplay) &&
            (identical(other.points, points) || other.points == points) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, transactionType, typeDisplay,
      points, description, createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PointTransactionImplCopyWith<_$PointTransactionImpl> get copyWith =>
      __$$PointTransactionImplCopyWithImpl<_$PointTransactionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PointTransactionImplToJson(
      this,
    );
  }
}

abstract class _PointTransaction implements PointTransaction {
  const factory _PointTransaction(
      {required final int id,
      @JsonKey(name: 'transaction_type') required final String transactionType,
      @JsonKey(name: 'type_display') required final String typeDisplay,
      required final int points,
      required final String description,
      @JsonKey(name: 'created_at')
      required final DateTime createdAt}) = _$PointTransactionImpl;

  factory _PointTransaction.fromJson(Map<String, dynamic> json) =
      _$PointTransactionImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'transaction_type')
  String get transactionType;
  @override
  @JsonKey(name: 'type_display')
  String get typeDisplay;
  @override
  int get points;
  @override
  String get description;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$PointTransactionImplCopyWith<_$PointTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LeaderboardEntry _$LeaderboardEntryFromJson(Map<String, dynamic> json) {
  return _LeaderboardEntry.fromJson(json);
}

/// @nodoc
mixin _$LeaderboardEntry {
  @JsonKey(name: 'user_id')
  int get userId => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_points')
  int get totalPoints => throw _privateConstructorUsedError;
  @JsonKey(name: 'current_level')
  int get currentLevel => throw _privateConstructorUsedError;
  int get rank => throw _privateConstructorUsedError;
  @JsonKey(name: 'badge_count')
  int get badgeCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LeaderboardEntryCopyWith<LeaderboardEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LeaderboardEntryCopyWith<$Res> {
  factory $LeaderboardEntryCopyWith(
          LeaderboardEntry value, $Res Function(LeaderboardEntry) then) =
      _$LeaderboardEntryCopyWithImpl<$Res, LeaderboardEntry>;
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') int userId,
      String username,
      @JsonKey(name: 'total_points') int totalPoints,
      @JsonKey(name: 'current_level') int currentLevel,
      int rank,
      @JsonKey(name: 'badge_count') int badgeCount});
}

/// @nodoc
class _$LeaderboardEntryCopyWithImpl<$Res, $Val extends LeaderboardEntry>
    implements $LeaderboardEntryCopyWith<$Res> {
  _$LeaderboardEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? totalPoints = null,
    Object? currentLevel = null,
    Object? rank = null,
    Object? badgeCount = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      currentLevel: null == currentLevel
          ? _value.currentLevel
          : currentLevel // ignore: cast_nullable_to_non_nullable
              as int,
      rank: null == rank
          ? _value.rank
          : rank // ignore: cast_nullable_to_non_nullable
              as int,
      badgeCount: null == badgeCount
          ? _value.badgeCount
          : badgeCount // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LeaderboardEntryImplCopyWith<$Res>
    implements $LeaderboardEntryCopyWith<$Res> {
  factory _$$LeaderboardEntryImplCopyWith(_$LeaderboardEntryImpl value,
          $Res Function(_$LeaderboardEntryImpl) then) =
      __$$LeaderboardEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'user_id') int userId,
      String username,
      @JsonKey(name: 'total_points') int totalPoints,
      @JsonKey(name: 'current_level') int currentLevel,
      int rank,
      @JsonKey(name: 'badge_count') int badgeCount});
}

/// @nodoc
class __$$LeaderboardEntryImplCopyWithImpl<$Res>
    extends _$LeaderboardEntryCopyWithImpl<$Res, _$LeaderboardEntryImpl>
    implements _$$LeaderboardEntryImplCopyWith<$Res> {
  __$$LeaderboardEntryImplCopyWithImpl(_$LeaderboardEntryImpl _value,
      $Res Function(_$LeaderboardEntryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? totalPoints = null,
    Object? currentLevel = null,
    Object? rank = null,
    Object? badgeCount = null,
  }) {
    return _then(_$LeaderboardEntryImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      currentLevel: null == currentLevel
          ? _value.currentLevel
          : currentLevel // ignore: cast_nullable_to_non_nullable
              as int,
      rank: null == rank
          ? _value.rank
          : rank // ignore: cast_nullable_to_non_nullable
              as int,
      badgeCount: null == badgeCount
          ? _value.badgeCount
          : badgeCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LeaderboardEntryImpl implements _LeaderboardEntry {
  const _$LeaderboardEntryImpl(
      {@JsonKey(name: 'user_id') required this.userId,
      required this.username,
      @JsonKey(name: 'total_points') required this.totalPoints,
      @JsonKey(name: 'current_level') required this.currentLevel,
      required this.rank,
      @JsonKey(name: 'badge_count') required this.badgeCount});

  factory _$LeaderboardEntryImpl.fromJson(Map<String, dynamic> json) =>
      _$$LeaderboardEntryImplFromJson(json);

  @override
  @JsonKey(name: 'user_id')
  final int userId;
  @override
  final String username;
  @override
  @JsonKey(name: 'total_points')
  final int totalPoints;
  @override
  @JsonKey(name: 'current_level')
  final int currentLevel;
  @override
  final int rank;
  @override
  @JsonKey(name: 'badge_count')
  final int badgeCount;

  @override
  String toString() {
    return 'LeaderboardEntry(userId: $userId, username: $username, totalPoints: $totalPoints, currentLevel: $currentLevel, rank: $rank, badgeCount: $badgeCount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LeaderboardEntryImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints) &&
            (identical(other.currentLevel, currentLevel) ||
                other.currentLevel == currentLevel) &&
            (identical(other.rank, rank) || other.rank == rank) &&
            (identical(other.badgeCount, badgeCount) ||
                other.badgeCount == badgeCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId, username, totalPoints,
      currentLevel, rank, badgeCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LeaderboardEntryImplCopyWith<_$LeaderboardEntryImpl> get copyWith =>
      __$$LeaderboardEntryImplCopyWithImpl<_$LeaderboardEntryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LeaderboardEntryImplToJson(
      this,
    );
  }
}

abstract class _LeaderboardEntry implements LeaderboardEntry {
  const factory _LeaderboardEntry(
          {@JsonKey(name: 'user_id') required final int userId,
          required final String username,
          @JsonKey(name: 'total_points') required final int totalPoints,
          @JsonKey(name: 'current_level') required final int currentLevel,
          required final int rank,
          @JsonKey(name: 'badge_count') required final int badgeCount}) =
      _$LeaderboardEntryImpl;

  factory _LeaderboardEntry.fromJson(Map<String, dynamic> json) =
      _$LeaderboardEntryImpl.fromJson;

  @override
  @JsonKey(name: 'user_id')
  int get userId;
  @override
  String get username;
  @override
  @JsonKey(name: 'total_points')
  int get totalPoints;
  @override
  @JsonKey(name: 'current_level')
  int get currentLevel;
  @override
  int get rank;
  @override
  @JsonKey(name: 'badge_count')
  int get badgeCount;
  @override
  @JsonKey(ignore: true)
  _$$LeaderboardEntryImplCopyWith<_$LeaderboardEntryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GamificationUserProfile _$GamificationUserProfileFromJson(
    Map<String, dynamic> json) {
  return _GamificationUserProfile.fromJson(json);
}

/// @nodoc
mixin _$GamificationUserProfile {
  int get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'date_joined')
  DateTime get dateJoined => throw _privateConstructorUsedError;
  UserLevel? get level => throw _privateConstructorUsedError;
  @JsonKey(name: 'earned_badges')
  List<UserBadge> get earnedBadges => throw _privateConstructorUsedError;
  @JsonKey(name: 'recent_transactions')
  List<PointTransaction> get recentTransactions =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'active_challenges')
  List<ChallengeParticipation> get activeChallenges =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GamificationUserProfileCopyWith<GamificationUserProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GamificationUserProfileCopyWith<$Res> {
  factory $GamificationUserProfileCopyWith(GamificationUserProfile value,
          $Res Function(GamificationUserProfile) then) =
      _$GamificationUserProfileCopyWithImpl<$Res, GamificationUserProfile>;
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'date_joined') DateTime dateJoined,
      UserLevel? level,
      @JsonKey(name: 'earned_badges') List<UserBadge> earnedBadges,
      @JsonKey(name: 'recent_transactions')
      List<PointTransaction> recentTransactions,
      @JsonKey(name: 'active_challenges')
      List<ChallengeParticipation> activeChallenges});

  $UserLevelCopyWith<$Res>? get level;
}

/// @nodoc
class _$GamificationUserProfileCopyWithImpl<$Res,
        $Val extends GamificationUserProfile>
    implements $GamificationUserProfileCopyWith<$Res> {
  _$GamificationUserProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? dateJoined = null,
    Object? level = freezed,
    Object? earnedBadges = null,
    Object? recentTransactions = null,
    Object? activeChallenges = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      dateJoined: null == dateJoined
          ? _value.dateJoined
          : dateJoined // ignore: cast_nullable_to_non_nullable
              as DateTime,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as UserLevel?,
      earnedBadges: null == earnedBadges
          ? _value.earnedBadges
          : earnedBadges // ignore: cast_nullable_to_non_nullable
              as List<UserBadge>,
      recentTransactions: null == recentTransactions
          ? _value.recentTransactions
          : recentTransactions // ignore: cast_nullable_to_non_nullable
              as List<PointTransaction>,
      activeChallenges: null == activeChallenges
          ? _value.activeChallenges
          : activeChallenges // ignore: cast_nullable_to_non_nullable
              as List<ChallengeParticipation>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserLevelCopyWith<$Res>? get level {
    if (_value.level == null) {
      return null;
    }

    return $UserLevelCopyWith<$Res>(_value.level!, (value) {
      return _then(_value.copyWith(level: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$GamificationUserProfileImplCopyWith<$Res>
    implements $GamificationUserProfileCopyWith<$Res> {
  factory _$$GamificationUserProfileImplCopyWith(
          _$GamificationUserProfileImpl value,
          $Res Function(_$GamificationUserProfileImpl) then) =
      __$$GamificationUserProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'date_joined') DateTime dateJoined,
      UserLevel? level,
      @JsonKey(name: 'earned_badges') List<UserBadge> earnedBadges,
      @JsonKey(name: 'recent_transactions')
      List<PointTransaction> recentTransactions,
      @JsonKey(name: 'active_challenges')
      List<ChallengeParticipation> activeChallenges});

  @override
  $UserLevelCopyWith<$Res>? get level;
}

/// @nodoc
class __$$GamificationUserProfileImplCopyWithImpl<$Res>
    extends _$GamificationUserProfileCopyWithImpl<$Res,
        _$GamificationUserProfileImpl>
    implements _$$GamificationUserProfileImplCopyWith<$Res> {
  __$$GamificationUserProfileImplCopyWithImpl(
      _$GamificationUserProfileImpl _value,
      $Res Function(_$GamificationUserProfileImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? dateJoined = null,
    Object? level = freezed,
    Object? earnedBadges = null,
    Object? recentTransactions = null,
    Object? activeChallenges = null,
  }) {
    return _then(_$GamificationUserProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      dateJoined: null == dateJoined
          ? _value.dateJoined
          : dateJoined // ignore: cast_nullable_to_non_nullable
              as DateTime,
      level: freezed == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as UserLevel?,
      earnedBadges: null == earnedBadges
          ? _value._earnedBadges
          : earnedBadges // ignore: cast_nullable_to_non_nullable
              as List<UserBadge>,
      recentTransactions: null == recentTransactions
          ? _value._recentTransactions
          : recentTransactions // ignore: cast_nullable_to_non_nullable
              as List<PointTransaction>,
      activeChallenges: null == activeChallenges
          ? _value._activeChallenges
          : activeChallenges // ignore: cast_nullable_to_non_nullable
              as List<ChallengeParticipation>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GamificationUserProfileImpl implements _GamificationUserProfile {
  const _$GamificationUserProfileImpl(
      {required this.id,
      required this.username,
      required this.email,
      @JsonKey(name: 'date_joined') required this.dateJoined,
      this.level,
      @JsonKey(name: 'earned_badges')
      final List<UserBadge> earnedBadges = const [],
      @JsonKey(name: 'recent_transactions')
      final List<PointTransaction> recentTransactions = const [],
      @JsonKey(name: 'active_challenges')
      final List<ChallengeParticipation> activeChallenges = const []})
      : _earnedBadges = earnedBadges,
        _recentTransactions = recentTransactions,
        _activeChallenges = activeChallenges;

  factory _$GamificationUserProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$GamificationUserProfileImplFromJson(json);

  @override
  final int id;
  @override
  final String username;
  @override
  final String email;
  @override
  @JsonKey(name: 'date_joined')
  final DateTime dateJoined;
  @override
  final UserLevel? level;
  final List<UserBadge> _earnedBadges;
  @override
  @JsonKey(name: 'earned_badges')
  List<UserBadge> get earnedBadges {
    if (_earnedBadges is EqualUnmodifiableListView) return _earnedBadges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_earnedBadges);
  }

  final List<PointTransaction> _recentTransactions;
  @override
  @JsonKey(name: 'recent_transactions')
  List<PointTransaction> get recentTransactions {
    if (_recentTransactions is EqualUnmodifiableListView)
      return _recentTransactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentTransactions);
  }

  final List<ChallengeParticipation> _activeChallenges;
  @override
  @JsonKey(name: 'active_challenges')
  List<ChallengeParticipation> get activeChallenges {
    if (_activeChallenges is EqualUnmodifiableListView)
      return _activeChallenges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_activeChallenges);
  }

  @override
  String toString() {
    return 'GamificationUserProfile(id: $id, username: $username, email: $email, dateJoined: $dateJoined, level: $level, earnedBadges: $earnedBadges, recentTransactions: $recentTransactions, activeChallenges: $activeChallenges)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GamificationUserProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.dateJoined, dateJoined) ||
                other.dateJoined == dateJoined) &&
            (identical(other.level, level) || other.level == level) &&
            const DeepCollectionEquality()
                .equals(other._earnedBadges, _earnedBadges) &&
            const DeepCollectionEquality()
                .equals(other._recentTransactions, _recentTransactions) &&
            const DeepCollectionEquality()
                .equals(other._activeChallenges, _activeChallenges));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      username,
      email,
      dateJoined,
      level,
      const DeepCollectionEquality().hash(_earnedBadges),
      const DeepCollectionEquality().hash(_recentTransactions),
      const DeepCollectionEquality().hash(_activeChallenges));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GamificationUserProfileImplCopyWith<_$GamificationUserProfileImpl>
      get copyWith => __$$GamificationUserProfileImplCopyWithImpl<
          _$GamificationUserProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GamificationUserProfileImplToJson(
      this,
    );
  }
}

abstract class _GamificationUserProfile implements GamificationUserProfile {
  const factory _GamificationUserProfile(
          {required final int id,
          required final String username,
          required final String email,
          @JsonKey(name: 'date_joined') required final DateTime dateJoined,
          final UserLevel? level,
          @JsonKey(name: 'earned_badges') final List<UserBadge> earnedBadges,
          @JsonKey(name: 'recent_transactions')
          final List<PointTransaction> recentTransactions,
          @JsonKey(name: 'active_challenges')
          final List<ChallengeParticipation> activeChallenges}) =
      _$GamificationUserProfileImpl;

  factory _GamificationUserProfile.fromJson(Map<String, dynamic> json) =
      _$GamificationUserProfileImpl.fromJson;

  @override
  int get id;
  @override
  String get username;
  @override
  String get email;
  @override
  @JsonKey(name: 'date_joined')
  DateTime get dateJoined;
  @override
  UserLevel? get level;
  @override
  @JsonKey(name: 'earned_badges')
  List<UserBadge> get earnedBadges;
  @override
  @JsonKey(name: 'recent_transactions')
  List<PointTransaction> get recentTransactions;
  @override
  @JsonKey(name: 'active_challenges')
  List<ChallengeParticipation> get activeChallenges;
  @override
  @JsonKey(ignore: true)
  _$$GamificationUserProfileImplCopyWith<_$GamificationUserProfileImpl>
      get copyWith => throw _privateConstructorUsedError;
}
