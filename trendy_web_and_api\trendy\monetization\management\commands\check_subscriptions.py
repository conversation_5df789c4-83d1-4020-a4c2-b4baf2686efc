from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from monetization.services import MonetizationService
from monetization.models import PremiumSubscription


class Command(BaseCommand):
    help = 'Check and handle subscription renewals and expirations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--auto-renew',
            action='store_true',
            help='Automatically renew subscriptions that are about to expire',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        auto_renew = options['auto_renew']
        
        self.stdout.write('🔍 Checking subscription status...')
        
        # Check expired subscriptions
        success, message = MonetizationService.check_expired_subscriptions()
        if success:
            self.stdout.write(self.style.SUCCESS(f'✅ {message}'))
        else:
            self.stdout.write(self.style.ERROR(f'❌ {message}'))
        
        # Check subscriptions expiring soon (within 3 days)
        soon_expiring = PremiumSubscription.objects.filter(
            status='active',
            end_date__lte=timezone.now() + timedelta(days=3),
            end_date__gt=timezone.now()
        )
        
        if soon_expiring.exists():
            self.stdout.write(f'⚠️  Found {soon_expiring.count()} subscriptions expiring soon:')
            
            for subscription in soon_expiring:
                days_left = (subscription.end_date - timezone.now()).days
                self.stdout.write(f'  - {subscription.user.username}: {days_left} days left')
                
                if auto_renew and not dry_run:
                    # Attempt auto-renewal
                    success, message = MonetizationService.renew_subscription(subscription)
                    if success:
                        self.stdout.write(self.style.SUCCESS(f'    ✅ Auto-renewed: {message}'))
                    else:
                        self.stdout.write(self.style.WARNING(f'    ⚠️  Auto-renewal failed: {message}'))
                elif auto_renew and dry_run:
                    self.stdout.write(f'    🔄 Would auto-renew subscription')
        else:
            self.stdout.write('✅ No subscriptions expiring soon')
        
        # Summary statistics
        active_count = PremiumSubscription.objects.filter(status='active').count()
        expired_count = PremiumSubscription.objects.filter(status='expired').count()
        pending_count = PremiumSubscription.objects.filter(status='pending').count()
        
        self.stdout.write('\n📊 Subscription Summary:')
        self.stdout.write(f'  Active: {active_count}')
        self.stdout.write(f'  Expired: {expired_count}')
        self.stdout.write(f'  Pending: {pending_count}')
        
        if dry_run:
            self.stdout.write('\n🔍 This was a dry run - no changes were made')
        
        self.stdout.write('\n✅ Subscription check complete!')
