"""
Django management command for restoring database backups
Usage: python manage.py restore_database <backup_name> [--force] [--restore-media]
"""
import os
import json
import gzip
import shutil
import subprocess
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.conf import settings
from django.db import connection
import tarfile


class Command(BaseCommand):
    help = 'Restore database from backup'

    def add_arguments(self, parser):
        parser.add_argument(
            'backup_name',
            type=str,
            help='Name of the backup directory to restore'
        )
        parser.add_argument(
            '--backup-dir',
            type=str,
            default='backups',
            help='Directory containing backups (default: backups)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Skip confirmation prompts'
        )
        parser.add_argument(
            '--restore-media',
            action='store_true',
            help='Restore media files (WARNING: will overwrite existing media)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be restored without actually doing it'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔄 Starting Database Restore Process'))
        self.stdout.write('=' * 60)

        # Locate backup
        backup_dir = Path(options['backup_dir']) / options['backup_name']
        if not backup_dir.exists():
            raise CommandError(f'Backup directory not found: {backup_dir}')

        # Load metadata
        metadata_file = backup_dir / 'backup_metadata.json'
        if not metadata_file.exists():
            raise CommandError(f'Backup metadata not found: {metadata_file}')

        with open(metadata_file, 'r') as f:
            metadata = json.load(f)

        self.stdout.write(f'📦 Backup: {options["backup_name"]}')
        self.stdout.write(f'📅 Created: {metadata["backup_created"]}')
        self.stdout.write(f'🗄️  Database: {metadata["database_name"]}')
        self.stdout.write(f'🗜️  Compressed: {metadata["compressed"]}')

        if options['dry_run']:
            self.stdout.write(self.style.WARNING('\n🧪 DRY RUN MODE - No changes will be made'))
            self._show_restore_plan(backup_dir, metadata, options)
            return

        # Confirmation
        if not options['force']:
            self.stdout.write(
                self.style.WARNING('\n⚠️  WARNING: This will REPLACE your current database!')
            )
            if options['restore_media']:
                self.stdout.write(
                    self.style.WARNING('⚠️  WARNING: This will REPLACE your current media files!')
                )
            
            confirm = input('\nAre you sure you want to continue? (yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write('❌ Restore cancelled')
                return

        try:
            # 1. Validate backup
            self.stdout.write('\n1️⃣  Validating backup...')
            self._validate_backup(backup_dir, metadata)

            # 2. Create pre-restore backup
            self.stdout.write('\n2️⃣  Creating pre-restore backup...')
            self._create_pre_restore_backup()

            # 3. Clear current database
            self.stdout.write('\n3️⃣  Clearing current database...')
            self._clear_database()

            # 4. Restore database
            self.stdout.write('\n4️⃣  Restoring database...')
            self._restore_database(backup_dir, metadata)

            # 5. Restore media (if requested)
            if options['restore_media']:
                self.stdout.write('\n5️⃣  Restoring media files...')
                self._restore_media(backup_dir, metadata)

            # 6. Run migrations
            self.stdout.write('\n6️⃣  Running migrations...')
            call_command('migrate', verbosity=0)

            # 7. Collect static files
            self.stdout.write('\n7️⃣  Collecting static files...')
            call_command('collectstatic', '--noinput', verbosity=0)

            self.stdout.write(f'\n✅ Database restore completed successfully!')
            self.stdout.write(f'📊 Restored from: {backup_dir}')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Restore failed: {str(e)}')
            )
            self.stdout.write(
                self.style.WARNING('💡 You can restore from pre-restore backup if needed')
            )
            raise CommandError(f'Restore process failed: {str(e)}')

    def _show_restore_plan(self, backup_dir, metadata, options):
        """Show what would be restored in dry run mode"""
        self.stdout.write('\n📋 RESTORE PLAN:')
        self.stdout.write('-' * 30)
        
        if metadata.get('database_file'):
            db_file = backup_dir / metadata['database_file']
            if db_file.exists():
                size = self._get_file_size(db_file)
                self.stdout.write(f'• Database: {metadata["database_file"]} ({size})')
        
        if metadata.get('fixtures_directory'):
            fixtures_dir = backup_dir / metadata['fixtures_directory']
            if fixtures_dir.exists():
                size = self._get_directory_size(fixtures_dir)
                self.stdout.write(f'• Fixtures: {metadata["fixtures_directory"]} ({size})')
        
        if options['restore_media'] and metadata.get('media_directory'):
            media_path = backup_dir / metadata['media_directory']
            if media_path.exists():
                size = self._get_path_size(media_path)
                self.stdout.write(f'• Media: {metadata["media_directory"]} ({size})')

    def _validate_backup(self, backup_dir, metadata):
        """Validate backup integrity"""
        # Check required files exist
        if metadata.get('database_file'):
            db_file = backup_dir / metadata['database_file']
            if not db_file.exists():
                raise CommandError(f'Database file not found: {db_file}')
        
        if metadata.get('fixtures_directory'):
            fixtures_dir = backup_dir / metadata['fixtures_directory']
            if not fixtures_dir.exists():
                raise CommandError(f'Fixtures directory not found: {fixtures_dir}')
        
        # Validate database engine compatibility
        current_engine = settings.DATABASES['default']['ENGINE']
        backup_engine = metadata.get('database_engine')
        
        if backup_engine and backup_engine != current_engine:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️  Database engine mismatch: backup={backup_engine}, current={current_engine}'
                )
            )
        
        self.stdout.write('  ✅ Backup validation passed')

    def _create_pre_restore_backup(self):
        """Create a backup before restore"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'pre_restore_{timestamp}'
        
        try:
            call_command(
                'backup_database',
                '--output-dir', 'backups/pre_restore',
                '--name', backup_name,
                '--compress',
                verbosity=0
            )
            self.stdout.write(f'  ✅ Pre-restore backup: {backup_name}')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  Pre-restore backup failed: {e}')
            )

    def _clear_database(self):
        """Clear current database"""
        # Use the clear_database command
        call_command('clear_database', '--force', verbosity=0)
        self.stdout.write('  ✅ Database cleared')

    def _restore_database(self, backup_dir, metadata):
        """Restore database from backup"""
        db_config = settings.DATABASES['default']
        engine = db_config['ENGINE']
        
        # Try raw database restore first
        if metadata.get('database_file'):
            db_file = backup_dir / metadata['database_file']
            if db_file.exists():
                if 'sqlite' in engine:
                    self._restore_sqlite(db_file, db_config)
                elif 'postgresql' in engine:
                    self._restore_postgresql(db_file, db_config)
                elif 'mysql' in engine:
                    self._restore_mysql(db_file, db_config)
                return
        
        # Fallback to fixtures
        if metadata.get('fixtures_directory'):
            fixtures_dir = backup_dir / metadata['fixtures_directory']
            fixture_file = fixtures_dir / 'all_data.json'
            if not fixture_file.exists():
                fixture_file = fixtures_dir / 'all_data.json.gz'
            
            if fixture_file.exists():
                self._restore_fixtures(fixture_file, metadata['compressed'])
                return
        
        raise CommandError('No valid database backup found')

    def _restore_sqlite(self, db_file, db_config):
        """Restore SQLite database"""
        target_path = Path(db_config['NAME'])
        
        if db_file.suffix == '.gz':
            with gzip.open(db_file, 'rb') as f_in:
                with open(target_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
        else:
            shutil.copy2(db_file, target_path)
        
        self.stdout.write(f'  ✅ SQLite database restored')

    def _restore_postgresql(self, db_file, db_config):
        """Restore PostgreSQL database"""
        cmd = [
            'psql',
            '--host', db_config.get('HOST', 'localhost'),
            '--port', str(db_config.get('PORT', 5432)),
            '--username', db_config['USER'],
            '--dbname', db_config['NAME'],
            '--quiet',
        ]
        
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['PASSWORD']
        
        try:
            if db_file.suffix == '.gz':
                with gzip.open(db_file, 'rt') as f:
                    subprocess.run(cmd, stdin=f, env=env, check=True)
            else:
                with open(db_file, 'r') as f:
                    subprocess.run(cmd, stdin=f, env=env, check=True)
            
            self.stdout.write(f'  ✅ PostgreSQL database restored')
        except subprocess.CalledProcessError as e:
            raise CommandError(f'PostgreSQL restore failed: {e}')

    def _restore_mysql(self, db_file, db_config):
        """Restore MySQL database"""
        cmd = [
            'mysql',
            '--host', db_config.get('HOST', 'localhost'),
            '--port', str(db_config.get('PORT', 3306)),
            '--user', db_config['USER'],
            f'--password={db_config["PASSWORD"]}',
            db_config['NAME'],
        ]
        
        try:
            if db_file.suffix == '.gz':
                with gzip.open(db_file, 'rt') as f:
                    subprocess.run(cmd, stdin=f, check=True)
            else:
                with open(db_file, 'r') as f:
                    subprocess.run(cmd, stdin=f, check=True)
            
            self.stdout.write(f'  ✅ MySQL database restored')
        except subprocess.CalledProcessError as e:
            raise CommandError(f'MySQL restore failed: {e}')

    def _restore_fixtures(self, fixture_file, compressed):
        """Restore from Django fixtures"""
        try:
            if compressed and fixture_file.suffix == '.gz':
                with gzip.open(fixture_file, 'rt') as f:
                    call_command('loaddata', '-', stdin=f, verbosity=0)
            else:
                call_command('loaddata', str(fixture_file), verbosity=0)
            
            self.stdout.write(f'  ✅ Fixtures restored')
        except Exception as e:
            raise CommandError(f'Fixtures restore failed: {e}')

    def _restore_media(self, backup_dir, metadata):
        """Restore media files"""
        media_root = Path(settings.MEDIA_ROOT)
        
        if metadata.get('media_directory'):
            media_backup = backup_dir / metadata['media_directory']
            
            if media_backup.suffix == '.gz' and tarfile.is_tarfile(media_backup):
                # Extract compressed archive
                with tarfile.open(media_backup, 'r:gz') as tar:
                    tar.extractall(path=media_root.parent)
            elif media_backup.is_dir():
                # Copy directory
                if media_root.exists():
                    shutil.rmtree(media_root)
                shutil.copytree(media_backup, media_root)
            
            self.stdout.write(f'  ✅ Media files restored')

    def _get_file_size(self, file_path):
        """Get human-readable file size"""
        size = file_path.stat().st_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def _get_directory_size(self, dir_path):
        """Get human-readable directory size"""
        total_size = sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())
        for unit in ['B', 'KB', 'MB', 'GB']:
            if total_size < 1024.0:
                return f"{total_size:.1f} {unit}"
            total_size /= 1024.0
        return f"{total_size:.1f} TB"

    def _get_path_size(self, path):
        """Get size of file or directory"""
        if path.is_file():
            return self._get_file_size(path)
        else:
            return self._get_directory_size(path)
