"""
Management command to test the payment system
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from payments.views import create_payment_order, capture_payment_order
from rest_framework.test import APIRequestFactory
from rest_framework.authtoken.models import Token
import json

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the payment system functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-user',
            action='store_true',
            help='Create a test user for payment testing'
        )

    def handle(self, *args, **options):
        if options['create_test_user']:
            self.create_test_user()
        
        self.test_payment_flow()

    def create_test_user(self):
        """Create a test user for payment testing"""
        try:
            # Try to get existing user first
            try:
                user = User.objects.get(email='<EMAIL>')
                created = False
            except User.DoesNotExist:
                # Create new user with unique username
                import uuid
                username = f'testuser_{uuid.uuid4().hex[:8]}'
                user = User.objects.create(
                    email='<EMAIL>',
                    username=username,
                    first_name='Test',
                    last_name='User',
                    is_email_verified=True
                )
                created = True
            
            if created:
                user.set_password('testpass123')
                user.save()
                self.stdout.write(self.style.SUCCESS(f'Created test user: {user.email}'))
            else:
                self.stdout.write(f'Test user already exists: {user.email}')
            
            # Create or get token
            token, created = Token.objects.get_or_create(user=user)
            if created:
                self.stdout.write(f'Created token for test user: {token.key}')
            else:
                self.stdout.write(f'Token for test user: {token.key}')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating test user: {str(e)}'))

    def test_payment_flow(self):
        """Test the complete payment flow"""
        self.stdout.write('Testing payment system...')
        
        try:
            # Get test user
            user = User.objects.get(email='<EMAIL>')
            token = Token.objects.get(user=user)
            
            # Create API request factory
            factory = APIRequestFactory()
            
            # Test 1: Create payment order
            self.stdout.write('\n1. Testing payment order creation...')
            
            request = factory.post('/api/v1/payments/create-order/', {
                'amount': 9.99,
                'purpose': 'premium_subscription',
                'description': 'Premium subscription - monthly'
            }, format='json')
            request.user = user
            
            response = create_payment_order(request)
            
            if response.status_code == 200:
                data = response.data
                if data.get('success'):
                    self.stdout.write(self.style.SUCCESS('✓ Payment order created successfully'))
                    self.stdout.write(f'  Order ID: {data.get("order_id")}')
                    self.stdout.write(f'  Transaction ID: {data.get("transaction_id")}')
                    self.stdout.write(f'  Approval URL: {data.get("approval_url")}')
                    
                    # Test 2: Capture payment order
                    self.stdout.write('\n2. Testing payment capture...')
                    
                    order_id = data.get('order_id')
                    request = factory.post('/api/v1/payments/capture-order/', {
                        'order_id': order_id
                    }, format='json')
                    request.user = user
                    
                    response = capture_payment_order(request)
                    
                    if response.status_code == 200:
                        capture_data = response.data
                        if capture_data.get('success'):
                            self.stdout.write(self.style.SUCCESS('✓ Payment captured successfully'))
                            self.stdout.write(f'  Capture ID: {capture_data.get("capture_id")}')
                            self.stdout.write(f'  Transaction ID: {capture_data.get("transaction_id")}')
                        else:
                            self.stdout.write(self.style.ERROR(f'✗ Payment capture failed: {capture_data.get("error")}'))
                    else:
                        self.stdout.write(self.style.ERROR(f'✗ Payment capture request failed: {response.status_code}'))
                        self.stdout.write(f'  Response: {response.data}')
                        
                else:
                    self.stdout.write(self.style.ERROR(f'✗ Payment order creation failed: {data.get("error")}'))
            else:
                self.stdout.write(self.style.ERROR(f'✗ Payment order request failed: {response.status_code}'))
                self.stdout.write(f'  Response: {response.data}')
                
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('Test user not found. Run with --create-test-user first.'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error testing payment flow: {str(e)}'))
            
        self.stdout.write('\nPayment system test completed.')
