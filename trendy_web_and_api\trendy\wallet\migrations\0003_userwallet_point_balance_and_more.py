# Generated by Django 5.1.7 on 2025-06-25 00:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wallet', '0002_transactionverificationcode'),
    ]

    operations = [
        migrations.AddField(
            model_name='userwallet',
            name='point_balance',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='wallettransaction',
            name='balance_after_points',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='wallettransaction',
            name='points',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='wallettransaction',
            name='purpose',
            field=models.CharField(choices=[('deposit', 'Wallet Deposit'), ('withdrawal', 'Wallet Withdrawal'), ('purchase_subscription', 'Premium Subscription'), ('purchase_points', 'Point Boost Purchase'), ('purchase_virtual_item', 'Virtual Item Purchase'), ('refund', 'Refund'), ('admin_adjustment', 'Admin Adjustment'), ('reward_payout', 'Reward Payout'), ('point_earned', 'Points Earned'), ('point_spent', 'Points Spent'), ('point_bonus', 'Point Bonus'), ('point_purchase', 'Point Purchase')], max_length=30),
        ),
    ]
