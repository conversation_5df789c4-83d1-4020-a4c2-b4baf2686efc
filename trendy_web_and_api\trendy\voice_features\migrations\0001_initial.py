# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.core.validators
import django.db.models.deletion
import voice_features.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('blog', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VoiceComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('audio_file', models.FileField(help_text='Supported formats: MP3, M4A, WAV, OGG', upload_to=voice_features.models.voice_comment_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['mp3', 'm4a', 'wav', 'ogg'])])),
                ('duration_seconds', models.PositiveIntegerField(default=0)),
                ('file_size', models.PositiveIntegerField(default=0)),
                ('transcription', models.TextField(blank=True, null=True)),
                ('is_transcribed', models.BooleanField(default=False)),
                ('transcription_confidence', models.FloatField(default=0.0)),
                ('like_count', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('is_approved', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='voice_comments', to='blog.post')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='voice_comments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SpeechToTextSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('audio_duration', models.PositiveIntegerField()),
                ('transcription_accuracy', models.FloatField(default=0.0)),
                ('language_detected', models.CharField(default='en-US', max_length=10)),
                ('processing_time', models.FloatField(default=0.0)),
                ('success', models.BooleanField(default=False)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stt_sessions', to=settings.AUTH_USER_MODEL)),
                ('voice_comment', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='stt_session', to='voice_features.voicecomment')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VoiceCommentLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('voice_comment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='voice_features.voicecomment')),
            ],
        ),
        migrations.CreateModel(
            name='AIWritingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('content_before', models.TextField()),
                ('content_after', models.TextField()),
                ('suggestions_count', models.PositiveIntegerField(default=0)),
                ('suggestions_accepted', models.PositiveIntegerField(default=0)),
                ('completion_requests', models.PositiveIntegerField(default=0)),
                ('improvement_requests', models.PositiveIntegerField(default=0)),
                ('session_duration', models.PositiveIntegerField(default=0)),
                ('words_added', models.IntegerField(default=0)),
                ('readability_improvement', models.FloatField(default=0.0)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('post', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ai_sessions', to='blog.post')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_writing_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
                'indexes': [models.Index(fields=['user', '-started_at'], name='voice_featu_user_id_744c77_idx'), models.Index(fields=['session_id'], name='voice_featu_session_412033_idx')],
            },
        ),
        migrations.CreateModel(
            name='TextToSpeechRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text_length', models.PositiveIntegerField()),
                ('language', models.CharField(default='en-US', max_length=10)),
                ('voice_settings', models.JSONField(default=dict)),
                ('duration_seconds', models.PositiveIntegerField(default=0)),
                ('completed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tts_requests', to='blog.post')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tts_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['post', '-created_at'], name='voice_featu_post_id_618816_idx'), models.Index(fields=['user', '-created_at'], name='voice_featu_user_id_8647d7_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='voicecomment',
            index=models.Index(fields=['post', '-created_at'], name='voice_featu_post_id_8196d2_idx'),
        ),
        migrations.AddIndex(
            model_name='voicecomment',
            index=models.Index(fields=['user', '-created_at'], name='voice_featu_user_id_39074a_idx'),
        ),
        migrations.AddIndex(
            model_name='voicecomment',
            index=models.Index(fields=['is_active', 'is_approved'], name='voice_featu_is_acti_849f2d_idx'),
        ),
        migrations.AddIndex(
            model_name='speechtotextsession',
            index=models.Index(fields=['user', '-created_at'], name='voice_featu_user_id_6ee1b2_idx'),
        ),
        migrations.AddIndex(
            model_name='speechtotextsession',
            index=models.Index(fields=['success'], name='voice_featu_success_d29443_idx'),
        ),
        migrations.AddIndex(
            model_name='voicecommentlike',
            index=models.Index(fields=['voice_comment', 'user'], name='voice_featu_voice_c_132187_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='voicecommentlike',
            unique_together={('voice_comment', 'user')},
        ),
    ]
