import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Performance optimization service to reduce API calls and improve app responsiveness
class PerformanceService {
  static const String _cachePrefix = 'cache_';
  static const String _lastRequestPrefix = 'last_request_';
  static const Duration _defaultCacheDuration = Duration(minutes: 5);
  static const Duration _defaultThrottleDuration = Duration(seconds: 3);
  
  static final Map<String, Timer> _pendingRequests = {};
  static final Map<String, Completer<Map<String, dynamic>>> _requestCompleters = {};

  /// Check if a request should be throttled
  static Future<bool> shouldThrottle(String endpoint) async {
    final prefs = await SharedPreferences.getInstance();
    final lastRequestKey = '$_lastRequestPrefix$endpoint';
    final lastRequestTime = prefs.getInt(lastRequestKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    
    return (now - lastRequestTime) < _defaultThrottleDuration.inMilliseconds;
  }

  /// Record a request timestamp
  static Future<void> recordRequest(String endpoint) async {
    final prefs = await SharedPreferences.getInstance();
    final lastRequestKey = '$_lastRequestPrefix$endpoint';
    await prefs.setInt(lastRequestKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// Get cached data if available and not expired
  static Future<Map<String, dynamic>?> getCachedData(String key, {Duration? maxAge}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cachePrefix$key';
      final cachedJson = prefs.getString(cacheKey);
      
      if (cachedJson == null) return null;
      
      final cachedData = json.decode(cachedJson) as Map<String, dynamic>;
      final cacheTime = DateTime.parse(cachedData['cache_timestamp'] as String);
      final age = DateTime.now().difference(cacheTime);
      
      if (age > (maxAge ?? _defaultCacheDuration)) {
        // Cache expired, remove it
        await prefs.remove(cacheKey);
        return null;
      }
      
      return cachedData['data'] as Map<String, dynamic>;
    } catch (e) {
      print('Error reading cache for $key: $e');
      return null;
    }
  }

  /// Cache data with timestamp
  static Future<void> cacheData(String key, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cachePrefix$key';
      final cacheData = {
        'data': data,
        'cache_timestamp': DateTime.now().toIso8601String(),
      };
      await prefs.setString(cacheKey, json.encode(cacheData));
    } catch (e) {
      print('Error caching data for $key: $e');
    }
  }

  /// Clear cache for a specific key
  static Future<void> clearCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cachePrefix$key';
      await prefs.remove(cacheKey);
    } catch (e) {
      print('Error clearing cache for $key: $e');
    }
  }

  /// Clear all cached data
  static Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_cachePrefix));
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      print('Error clearing all cache: $e');
    }
  }

  /// Debounce API requests to prevent rapid-fire calls
  static Future<Map<String, dynamic>> debounceRequest(
    String key,
    Future<Map<String, dynamic>> Function() requestFunction, {
    Duration delay = const Duration(milliseconds: 500),
  }) async {
    // Cancel existing timer for this key
    _pendingRequests[key]?.cancel();
    
    // If there's already a completer for this key, return its future
    if (_requestCompleters.containsKey(key)) {
      return _requestCompleters[key]!.future;
    }

    // Create new completer
    final completer = Completer<Map<String, dynamic>>();
    _requestCompleters[key] = completer;

    // Set up debounced execution
    _pendingRequests[key] = Timer(delay, () async {
      try {
        final result = await requestFunction();
        completer.complete(result);
      } catch (e) {
        completer.completeError(e);
      } finally {
        _pendingRequests.remove(key);
        _requestCompleters.remove(key);
      }
    });

    return completer.future;
  }

  /// Batch multiple requests together
  static Future<List<Map<String, dynamic>>> batchRequests(
    List<Future<Map<String, dynamic>>> requests, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    try {
      final results = await Future.wait(requests, eagerError: false)
          .timeout(timeout);
      return results;
    } catch (e) {
      print('Batch request error: $e');
      // Return partial results if some requests succeeded
      final results = <Map<String, dynamic>>[];
      for (final request in requests) {
        try {
          final result = await request.timeout(const Duration(seconds: 2));
          results.add(result);
        } catch (e) {
          results.add({'error': e.toString()});
        }
      }
      return results;
    }
  }

  /// Smart request with caching, throttling, and error handling
  static Future<Map<String, dynamic>> smartRequest(
    String endpoint,
    Future<Response> Function() requestFunction, {
    Duration? cacheDuration,
    bool useCache = true,
    bool useThrottling = true,
  }) async {
    final cacheKey = endpoint.replaceAll('/', '_');

    // Check throttling
    if (useThrottling && await shouldThrottle(endpoint)) {
      throw DioException(
        requestOptions: RequestOptions(path: endpoint),
        message: 'Request throttled. Please wait before making another request.',
        type: DioExceptionType.unknown,
      );
    }

    // Try cache first
    if (useCache) {
      final cachedData = await getCachedData(cacheKey, maxAge: cacheDuration);
      if (cachedData != null) {
        return cachedData;
      }
    }

    try {
      // Record request for throttling
      if (useThrottling) {
        await recordRequest(endpoint);
      }

      // Make actual request
      final response = await requestFunction();
      
      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        
        // Cache successful response
        if (useCache) {
          await cacheData(cacheKey, data);
        }
        
        return data;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'Request failed with status ${response.statusCode}',
          type: DioExceptionType.badResponse,
        );
      }
    } catch (e) {
      print('Smart request error for $endpoint: $e');
      rethrow;
    }
  }

  /// Optimize image loading by caching network images
  static String getCachedImageUrl(String originalUrl) {
    // Add cache busting parameter to force refresh when needed
    final uri = Uri.parse(originalUrl);
    final cacheParam = DateTime.now().millisecondsSinceEpoch ~/ (1000 * 60 * 60); // Cache for 1 hour
    final newParams = Map<String, dynamic>.from(uri.queryParameters);
    newParams['cache'] = cacheParam.toString();
    
    return uri.replace(queryParameters: newParams).toString();
  }

  /// Preload critical data to improve perceived performance
  static Future<void> preloadCriticalData(List<Future<void>> Function() dataLoaders) async {
    try {
      final futures = dataLoaders();
      await Future.wait(futures, eagerError: false);
    } catch (e) {
      print('Error preloading critical data: $e');
    }
  }

  /// Monitor and log performance metrics
  static void logPerformanceMetric(String operation, Duration duration) {
    if (duration.inMilliseconds > 2000) {
      print('⚠️ Slow operation: $operation took ${duration.inMilliseconds}ms');
    } else if (duration.inMilliseconds > 1000) {
      print('🐌 Moderate operation: $operation took ${duration.inMilliseconds}ms');
    }
  }

  /// Execute operation with performance monitoring
  static Future<T> monitorPerformance<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      logPerformanceMetric(operationName, stopwatch.elapsed);
      return result;
    } catch (e) {
      stopwatch.stop();
      logPerformanceMetric('$operationName (failed)', stopwatch.elapsed);
      rethrow;
    }
  }

  /// Cleanup resources and cancel pending operations
  static void cleanup() {
    for (final timer in _pendingRequests.values) {
      timer.cancel();
    }
    _pendingRequests.clear();
    
    for (final completer in _requestCompleters.values) {
      if (!completer.isCompleted) {
        completer.completeError('Service cleanup');
      }
    }
    _requestCompleters.clear();
  }
}

/// Extension to add performance monitoring to Dio
extension DioPerformance on Dio {
  Future<Response<T>> getWithPerformance<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    Duration? cacheDuration,
    bool useCache = true,
  }) async {
    return PerformanceService.monitorPerformance(
      'GET $path',
      () => get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  Future<Response<T>> postWithPerformance<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return PerformanceService.monitorPerformance(
      'POST $path',
      () => post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }
}
