# Generated by Django 5.0.4 on 2025-07-29 11:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0002_customuser_first_login_at"),
        ("regional", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="customuser",
            name="auto_detect_location",
            field=models.BooleanField(
                default=True,
                help_text="Whether to automatically detect user's location",
            ),
        ),
        migrations.AddField(
            model_name="customuser",
            name="detected_country",
            field=models.ForeignKey(
                blank=True,
                help_text="Country detected from user's IP address",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="detected_users",
                to="regional.country",
            ),
        ),
        migrations.AddField(
            model_name="customuser",
            name="preferred_country",
            field=models.ForeignKey(
                blank=True,
                help_text="User's preferred country for content filtering",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="preferred_users",
                to="regional.country",
            ),
        ),
        migrations.AddField(
            model_name="customuser",
            name="show_global_content",
            field=models.BooleanField(
                default=True,
                help_text="Whether to show global content alongside regional content",
            ),
        ),
    ]
