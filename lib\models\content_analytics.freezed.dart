// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'content_analytics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContentAnalytics _$ContentAnalyticsFromJson(Map<String, dynamic> json) {
  return _ContentAnalytics.fromJson(json);
}

/// @nodoc
mixin _$ContentAnalytics {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'post_title')
  String get postTitle => throw _privateConstructorUsedError;
  @JsonKey(name: 'post_slug')
  String get postSlug => throw _privateConstructorUsedError;
  @JsonKey(name: 'estimated_reading_time')
  int get estimatedReadingTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'complexity_score')
  double get complexityScore => throw _privateConstructorUsedError;
  @JsonKey(name: 'word_count')
  int get wordCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'sentence_count')
  int get sentenceCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'paragraph_count')
  int get paragraphCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'readability_score')
  double get readabilityScore => throw _privateConstructorUsedError;
  @JsonKey(name: 'reading_level')
  String get readingLevel => throw _privateConstructorUsedError;
  @JsonKey(name: 'average_words_per_sentence')
  double get averageWordsPerSentence => throw _privateConstructorUsedError;
  @JsonKey(name: 'average_syllables_per_word')
  double get averageSyllablesPerWord => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_reading_time')
  int get totalReadingTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'completion_rate')
  double get completionRate => throw _privateConstructorUsedError;
  @JsonKey(name: 'average_session_duration')
  int get averageSessionDuration => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ContentAnalyticsCopyWith<ContentAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContentAnalyticsCopyWith<$Res> {
  factory $ContentAnalyticsCopyWith(
          ContentAnalytics value, $Res Function(ContentAnalytics) then) =
      _$ContentAnalyticsCopyWithImpl<$Res, ContentAnalytics>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'post_title') String postTitle,
      @JsonKey(name: 'post_slug') String postSlug,
      @JsonKey(name: 'estimated_reading_time') int estimatedReadingTime,
      @JsonKey(name: 'complexity_score') double complexityScore,
      @JsonKey(name: 'word_count') int wordCount,
      @JsonKey(name: 'sentence_count') int sentenceCount,
      @JsonKey(name: 'paragraph_count') int paragraphCount,
      @JsonKey(name: 'readability_score') double readabilityScore,
      @JsonKey(name: 'reading_level') String readingLevel,
      @JsonKey(name: 'average_words_per_sentence')
      double averageWordsPerSentence,
      @JsonKey(name: 'average_syllables_per_word')
      double averageSyllablesPerWord,
      @JsonKey(name: 'total_reading_time') int totalReadingTime,
      @JsonKey(name: 'completion_rate') double completionRate,
      @JsonKey(name: 'average_session_duration') int averageSessionDuration,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$ContentAnalyticsCopyWithImpl<$Res, $Val extends ContentAnalytics>
    implements $ContentAnalyticsCopyWith<$Res> {
  _$ContentAnalyticsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postTitle = null,
    Object? postSlug = null,
    Object? estimatedReadingTime = null,
    Object? complexityScore = null,
    Object? wordCount = null,
    Object? sentenceCount = null,
    Object? paragraphCount = null,
    Object? readabilityScore = null,
    Object? readingLevel = null,
    Object? averageWordsPerSentence = null,
    Object? averageSyllablesPerWord = null,
    Object? totalReadingTime = null,
    Object? completionRate = null,
    Object? averageSessionDuration = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      postTitle: null == postTitle
          ? _value.postTitle
          : postTitle // ignore: cast_nullable_to_non_nullable
              as String,
      postSlug: null == postSlug
          ? _value.postSlug
          : postSlug // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedReadingTime: null == estimatedReadingTime
          ? _value.estimatedReadingTime
          : estimatedReadingTime // ignore: cast_nullable_to_non_nullable
              as int,
      complexityScore: null == complexityScore
          ? _value.complexityScore
          : complexityScore // ignore: cast_nullable_to_non_nullable
              as double,
      wordCount: null == wordCount
          ? _value.wordCount
          : wordCount // ignore: cast_nullable_to_non_nullable
              as int,
      sentenceCount: null == sentenceCount
          ? _value.sentenceCount
          : sentenceCount // ignore: cast_nullable_to_non_nullable
              as int,
      paragraphCount: null == paragraphCount
          ? _value.paragraphCount
          : paragraphCount // ignore: cast_nullable_to_non_nullable
              as int,
      readabilityScore: null == readabilityScore
          ? _value.readabilityScore
          : readabilityScore // ignore: cast_nullable_to_non_nullable
              as double,
      readingLevel: null == readingLevel
          ? _value.readingLevel
          : readingLevel // ignore: cast_nullable_to_non_nullable
              as String,
      averageWordsPerSentence: null == averageWordsPerSentence
          ? _value.averageWordsPerSentence
          : averageWordsPerSentence // ignore: cast_nullable_to_non_nullable
              as double,
      averageSyllablesPerWord: null == averageSyllablesPerWord
          ? _value.averageSyllablesPerWord
          : averageSyllablesPerWord // ignore: cast_nullable_to_non_nullable
              as double,
      totalReadingTime: null == totalReadingTime
          ? _value.totalReadingTime
          : totalReadingTime // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      averageSessionDuration: null == averageSessionDuration
          ? _value.averageSessionDuration
          : averageSessionDuration // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContentAnalyticsImplCopyWith<$Res>
    implements $ContentAnalyticsCopyWith<$Res> {
  factory _$$ContentAnalyticsImplCopyWith(_$ContentAnalyticsImpl value,
          $Res Function(_$ContentAnalyticsImpl) then) =
      __$$ContentAnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'post_title') String postTitle,
      @JsonKey(name: 'post_slug') String postSlug,
      @JsonKey(name: 'estimated_reading_time') int estimatedReadingTime,
      @JsonKey(name: 'complexity_score') double complexityScore,
      @JsonKey(name: 'word_count') int wordCount,
      @JsonKey(name: 'sentence_count') int sentenceCount,
      @JsonKey(name: 'paragraph_count') int paragraphCount,
      @JsonKey(name: 'readability_score') double readabilityScore,
      @JsonKey(name: 'reading_level') String readingLevel,
      @JsonKey(name: 'average_words_per_sentence')
      double averageWordsPerSentence,
      @JsonKey(name: 'average_syllables_per_word')
      double averageSyllablesPerWord,
      @JsonKey(name: 'total_reading_time') int totalReadingTime,
      @JsonKey(name: 'completion_rate') double completionRate,
      @JsonKey(name: 'average_session_duration') int averageSessionDuration,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$$ContentAnalyticsImplCopyWithImpl<$Res>
    extends _$ContentAnalyticsCopyWithImpl<$Res, _$ContentAnalyticsImpl>
    implements _$$ContentAnalyticsImplCopyWith<$Res> {
  __$$ContentAnalyticsImplCopyWithImpl(_$ContentAnalyticsImpl _value,
      $Res Function(_$ContentAnalyticsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postTitle = null,
    Object? postSlug = null,
    Object? estimatedReadingTime = null,
    Object? complexityScore = null,
    Object? wordCount = null,
    Object? sentenceCount = null,
    Object? paragraphCount = null,
    Object? readabilityScore = null,
    Object? readingLevel = null,
    Object? averageWordsPerSentence = null,
    Object? averageSyllablesPerWord = null,
    Object? totalReadingTime = null,
    Object? completionRate = null,
    Object? averageSessionDuration = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$ContentAnalyticsImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      postTitle: null == postTitle
          ? _value.postTitle
          : postTitle // ignore: cast_nullable_to_non_nullable
              as String,
      postSlug: null == postSlug
          ? _value.postSlug
          : postSlug // ignore: cast_nullable_to_non_nullable
              as String,
      estimatedReadingTime: null == estimatedReadingTime
          ? _value.estimatedReadingTime
          : estimatedReadingTime // ignore: cast_nullable_to_non_nullable
              as int,
      complexityScore: null == complexityScore
          ? _value.complexityScore
          : complexityScore // ignore: cast_nullable_to_non_nullable
              as double,
      wordCount: null == wordCount
          ? _value.wordCount
          : wordCount // ignore: cast_nullable_to_non_nullable
              as int,
      sentenceCount: null == sentenceCount
          ? _value.sentenceCount
          : sentenceCount // ignore: cast_nullable_to_non_nullable
              as int,
      paragraphCount: null == paragraphCount
          ? _value.paragraphCount
          : paragraphCount // ignore: cast_nullable_to_non_nullable
              as int,
      readabilityScore: null == readabilityScore
          ? _value.readabilityScore
          : readabilityScore // ignore: cast_nullable_to_non_nullable
              as double,
      readingLevel: null == readingLevel
          ? _value.readingLevel
          : readingLevel // ignore: cast_nullable_to_non_nullable
              as String,
      averageWordsPerSentence: null == averageWordsPerSentence
          ? _value.averageWordsPerSentence
          : averageWordsPerSentence // ignore: cast_nullable_to_non_nullable
              as double,
      averageSyllablesPerWord: null == averageSyllablesPerWord
          ? _value.averageSyllablesPerWord
          : averageSyllablesPerWord // ignore: cast_nullable_to_non_nullable
              as double,
      totalReadingTime: null == totalReadingTime
          ? _value.totalReadingTime
          : totalReadingTime // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      averageSessionDuration: null == averageSessionDuration
          ? _value.averageSessionDuration
          : averageSessionDuration // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContentAnalyticsImpl implements _ContentAnalytics {
  const _$ContentAnalyticsImpl(
      {required this.id,
      @JsonKey(name: 'post_title') required this.postTitle,
      @JsonKey(name: 'post_slug') required this.postSlug,
      @JsonKey(name: 'estimated_reading_time')
      required this.estimatedReadingTime,
      @JsonKey(name: 'complexity_score') this.complexityScore = 0.0,
      @JsonKey(name: 'word_count') this.wordCount = 0,
      @JsonKey(name: 'sentence_count') this.sentenceCount = 0,
      @JsonKey(name: 'paragraph_count') this.paragraphCount = 0,
      @JsonKey(name: 'readability_score') this.readabilityScore = 0.0,
      @JsonKey(name: 'reading_level') this.readingLevel = 'Unknown',
      @JsonKey(name: 'average_words_per_sentence')
      this.averageWordsPerSentence = 0.0,
      @JsonKey(name: 'average_syllables_per_word')
      this.averageSyllablesPerWord = 0.0,
      @JsonKey(name: 'total_reading_time') this.totalReadingTime = 0,
      @JsonKey(name: 'completion_rate') this.completionRate = 0.0,
      @JsonKey(name: 'average_session_duration')
      this.averageSessionDuration = 0,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});

  factory _$ContentAnalyticsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContentAnalyticsImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'post_title')
  final String postTitle;
  @override
  @JsonKey(name: 'post_slug')
  final String postSlug;
  @override
  @JsonKey(name: 'estimated_reading_time')
  final int estimatedReadingTime;
  @override
  @JsonKey(name: 'complexity_score')
  final double complexityScore;
  @override
  @JsonKey(name: 'word_count')
  final int wordCount;
  @override
  @JsonKey(name: 'sentence_count')
  final int sentenceCount;
  @override
  @JsonKey(name: 'paragraph_count')
  final int paragraphCount;
  @override
  @JsonKey(name: 'readability_score')
  final double readabilityScore;
  @override
  @JsonKey(name: 'reading_level')
  final String readingLevel;
  @override
  @JsonKey(name: 'average_words_per_sentence')
  final double averageWordsPerSentence;
  @override
  @JsonKey(name: 'average_syllables_per_word')
  final double averageSyllablesPerWord;
  @override
  @JsonKey(name: 'total_reading_time')
  final int totalReadingTime;
  @override
  @JsonKey(name: 'completion_rate')
  final double completionRate;
  @override
  @JsonKey(name: 'average_session_duration')
  final int averageSessionDuration;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  String toString() {
    return 'ContentAnalytics(id: $id, postTitle: $postTitle, postSlug: $postSlug, estimatedReadingTime: $estimatedReadingTime, complexityScore: $complexityScore, wordCount: $wordCount, sentenceCount: $sentenceCount, paragraphCount: $paragraphCount, readabilityScore: $readabilityScore, readingLevel: $readingLevel, averageWordsPerSentence: $averageWordsPerSentence, averageSyllablesPerWord: $averageSyllablesPerWord, totalReadingTime: $totalReadingTime, completionRate: $completionRate, averageSessionDuration: $averageSessionDuration, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContentAnalyticsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.postTitle, postTitle) ||
                other.postTitle == postTitle) &&
            (identical(other.postSlug, postSlug) ||
                other.postSlug == postSlug) &&
            (identical(other.estimatedReadingTime, estimatedReadingTime) ||
                other.estimatedReadingTime == estimatedReadingTime) &&
            (identical(other.complexityScore, complexityScore) ||
                other.complexityScore == complexityScore) &&
            (identical(other.wordCount, wordCount) ||
                other.wordCount == wordCount) &&
            (identical(other.sentenceCount, sentenceCount) ||
                other.sentenceCount == sentenceCount) &&
            (identical(other.paragraphCount, paragraphCount) ||
                other.paragraphCount == paragraphCount) &&
            (identical(other.readabilityScore, readabilityScore) ||
                other.readabilityScore == readabilityScore) &&
            (identical(other.readingLevel, readingLevel) ||
                other.readingLevel == readingLevel) &&
            (identical(
                    other.averageWordsPerSentence, averageWordsPerSentence) ||
                other.averageWordsPerSentence == averageWordsPerSentence) &&
            (identical(
                    other.averageSyllablesPerWord, averageSyllablesPerWord) ||
                other.averageSyllablesPerWord == averageSyllablesPerWord) &&
            (identical(other.totalReadingTime, totalReadingTime) ||
                other.totalReadingTime == totalReadingTime) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.averageSessionDuration, averageSessionDuration) ||
                other.averageSessionDuration == averageSessionDuration) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      postTitle,
      postSlug,
      estimatedReadingTime,
      complexityScore,
      wordCount,
      sentenceCount,
      paragraphCount,
      readabilityScore,
      readingLevel,
      averageWordsPerSentence,
      averageSyllablesPerWord,
      totalReadingTime,
      completionRate,
      averageSessionDuration,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ContentAnalyticsImplCopyWith<_$ContentAnalyticsImpl> get copyWith =>
      __$$ContentAnalyticsImplCopyWithImpl<_$ContentAnalyticsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContentAnalyticsImplToJson(
      this,
    );
  }
}

abstract class _ContentAnalytics implements ContentAnalytics {
  const factory _ContentAnalytics(
          {required final int id,
          @JsonKey(name: 'post_title') required final String postTitle,
          @JsonKey(name: 'post_slug') required final String postSlug,
          @JsonKey(name: 'estimated_reading_time')
          required final int estimatedReadingTime,
          @JsonKey(name: 'complexity_score') final double complexityScore,
          @JsonKey(name: 'word_count') final int wordCount,
          @JsonKey(name: 'sentence_count') final int sentenceCount,
          @JsonKey(name: 'paragraph_count') final int paragraphCount,
          @JsonKey(name: 'readability_score') final double readabilityScore,
          @JsonKey(name: 'reading_level') final String readingLevel,
          @JsonKey(name: 'average_words_per_sentence')
          final double averageWordsPerSentence,
          @JsonKey(name: 'average_syllables_per_word')
          final double averageSyllablesPerWord,
          @JsonKey(name: 'total_reading_time') final int totalReadingTime,
          @JsonKey(name: 'completion_rate') final double completionRate,
          @JsonKey(name: 'average_session_duration')
          final int averageSessionDuration,
          @JsonKey(name: 'created_at') required final DateTime createdAt,
          @JsonKey(name: 'updated_at') required final DateTime updatedAt}) =
      _$ContentAnalyticsImpl;

  factory _ContentAnalytics.fromJson(Map<String, dynamic> json) =
      _$ContentAnalyticsImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'post_title')
  String get postTitle;
  @override
  @JsonKey(name: 'post_slug')
  String get postSlug;
  @override
  @JsonKey(name: 'estimated_reading_time')
  int get estimatedReadingTime;
  @override
  @JsonKey(name: 'complexity_score')
  double get complexityScore;
  @override
  @JsonKey(name: 'word_count')
  int get wordCount;
  @override
  @JsonKey(name: 'sentence_count')
  int get sentenceCount;
  @override
  @JsonKey(name: 'paragraph_count')
  int get paragraphCount;
  @override
  @JsonKey(name: 'readability_score')
  double get readabilityScore;
  @override
  @JsonKey(name: 'reading_level')
  String get readingLevel;
  @override
  @JsonKey(name: 'average_words_per_sentence')
  double get averageWordsPerSentence;
  @override
  @JsonKey(name: 'average_syllables_per_word')
  double get averageSyllablesPerWord;
  @override
  @JsonKey(name: 'total_reading_time')
  int get totalReadingTime;
  @override
  @JsonKey(name: 'completion_rate')
  double get completionRate;
  @override
  @JsonKey(name: 'average_session_duration')
  int get averageSessionDuration;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ContentAnalyticsImplCopyWith<_$ContentAnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
