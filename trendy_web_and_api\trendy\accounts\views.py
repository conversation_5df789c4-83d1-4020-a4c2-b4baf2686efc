from rest_framework import status, permissions, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.authtoken.models import Token
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.db.models import Q
from django.shortcuts import get_object_or_404
from .models import UserSettings, Notification, PasswordResetToken
from .serializers import (
    UserRegistrationSerializer, LoginSerializer, UserSerializer,
    UserProfileUpdateSerializer, ChangePasswordSerializer,
    PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    UserSettingsSerializer, NotificationSerializer, EmailVerificationSerializer
)

User = get_user_model()


class RegisterView(APIView):
    """User registration endpoint"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            token, created = Token.objects.get_or_create(user=user)

            # Send verification email for non-admin users
            email_sent = False
            if not (user.is_staff or user.is_superuser):
                from .email_service import EmailService
                email_sent = EmailService.send_account_verification_email(user)

            # Different message for admin users
            if user.is_staff or user.is_superuser:
                message = 'Registration successful. Admin account created and ready to use.'
            else:
                if email_sent:
                    message = 'Registration successful. Please check your email to verify your account.'
                else:
                    message = 'Registration successful. Please use the resend verification option to verify your email.'

            return Response({
                'user': UserSerializer(user).data,
                'token': token.key,
                'message': message
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginView(APIView):
    """User login endpoint"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # Track first login
            is_first_login = user.first_login_at is None
            if is_first_login:
                from django.utils import timezone
                user.first_login_at = timezone.now()
                user.save(update_fields=['first_login_at'])

            token, created = Token.objects.get_or_create(user=user)
            return Response({
                'user': UserSerializer(user).data,
                'token': token.key,
                'is_first_login': is_first_login,
                'message': 'Login successful'
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_welcome_bonus_info(request):
    """Get welcome bonus information for the current user"""
    try:
        user = request.user

        # Check if user is truly new (registered recently)
        from datetime import timedelta
        from django.utils import timezone
        is_new_user = user.date_joined >= timezone.now() - timedelta(hours=24)

        # Check if user has received welcome bonus
        from gamification.models import PointTransaction
        welcome_transactions = PointTransaction.objects.filter(
            user=user,
            transaction_type='welcome'
        ).order_by('-created_at')

        if welcome_transactions.exists():
            latest_welcome = welcome_transactions.first()
            return Response({
                'success': True,
                'has_welcome_bonus': True,
                'is_new_user': is_new_user,
                'bonus_amount': latest_welcome.points,
                'bonus_date': latest_welcome.created_at,
                'description': latest_welcome.description,
                'message': f'You received {latest_welcome.points} welcome points!'
            })
        else:
            return Response({
                'success': True,
                'has_welcome_bonus': False,
                'is_new_user': is_new_user,
                'message': 'No welcome bonus found'
            })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogoutView(APIView):
    """User logout endpoint"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            request.user.auth_token.delete()
            return Response({'message': 'Logout successful'}, status=status.HTTP_200_OK)
        except:
            return Response({'message': 'Logout successful'}, status=status.HTTP_200_OK)


class ProfileView(APIView):
    """User profile endpoint"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)
    
    def put(self, request):
        serializer = UserProfileUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(UserSerializer(request.user).data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """Change password endpoint"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = request.user
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            
            # Delete all tokens to force re-login
            Token.objects.filter(user=user).delete()
            
            return Response({'message': 'Password changed successfully'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetRequestView(APIView):
    """Password reset request endpoint"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            user = User.objects.get(email=email)
            
            # Create password reset token
            reset_token = PasswordResetToken.objects.create(user=user)
            
            # Send email
            subject = 'Reset your Trendy password'
            message = f'''
            Hi {user.username},
            
            You requested a password reset for your Trendy account.
            
            Click the link below to reset your password:
            {settings.FRONTEND_URL}/reset-password/{reset_token.token}
            
            This link will expire in 24 hours.
            
            If you didn't request this, please ignore this email.
            
            Best regards,
            The Trendy Team
            '''
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=False,
            )
            
            return Response({'message': 'Password reset email sent'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(APIView):
    """Password reset confirmation endpoint"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            reset_token = serializer.validated_data['reset_token']
            new_password = serializer.validated_data['new_password']
            
            # Reset password
            user = reset_token.user
            user.set_password(new_password)
            user.save()
            
            # Mark token as used
            reset_token.mark_as_used()
            
            # Delete all tokens to force re-login
            Token.objects.filter(user=user).delete()
            
            return Response({'message': 'Password reset successful'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class EmailVerificationView(APIView):
    """Email verification endpoint"""
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        if serializer.is_valid():
            token = serializer.validated_data['token']
            user = User.objects.get(email_verification_token=token)
            user.is_email_verified = True
            user.save(update_fields=['is_email_verified'])
            
            return Response({'message': 'Email verified successfully'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResendVerificationView(APIView):
    """Resend email verification endpoint"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        user = request.user
        if user.is_email_verified:
            return Response({'message': 'Email is already verified'}, status=status.HTTP_400_BAD_REQUEST)

        # Check rate limiting (max 3 emails per hour)
        if user.email_verification_sent_at:
            time_since_last = timezone.now() - user.email_verification_sent_at
            if time_since_last.total_seconds() < 1200:  # 20 minutes
                remaining_time = 20 - int(time_since_last.total_seconds() / 60)
                return Response({
                    'message': f'Please wait {remaining_time} minutes before requesting another verification email'
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        from .email_service import EmailService
        email_sent = EmailService.send_account_verification_email(user)

        if email_sent:
            return Response({'message': 'Verification email sent successfully'}, status=status.HTTP_200_OK)
        else:
            return Response({'message': 'Failed to send verification email. Please try again later.'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserSettingsView(APIView):
    """User settings endpoint"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        settings_obj, created = UserSettings.objects.get_or_create(user=request.user)
        serializer = UserSettingsSerializer(settings_obj)
        return Response(serializer.data)
    
    def put(self, request):
        settings_obj, created = UserSettings.objects.get_or_create(user=request.user)
        serializer = UserSettingsSerializer(settings_obj, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class NotificationPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class NotificationListView(generics.ListAPIView):
    """List user notifications"""
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = NotificationPagination
    
    def get_queryset(self):
        return Notification.objects.filter(recipient=self.request.user)


class NotificationDetailView(APIView):
    """Notification detail endpoint"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, pk):
        try:
            notification = Notification.objects.get(pk=pk, recipient=request.user)
            serializer = NotificationSerializer(notification)
            return Response(serializer.data)
        except Notification.DoesNotExist:
            return Response({'error': 'Notification not found'}, status=status.HTTP_404_NOT_FOUND)
    
    def patch(self, request, pk):
        try:
            notification = Notification.objects.get(pk=pk, recipient=request.user)
            if 'is_read' in request.data and request.data['is_read']:
                notification.mark_as_read()
            return Response(NotificationSerializer(notification).data)
        except Notification.DoesNotExist:
            return Response({'error': 'Notification not found'}, status=status.HTTP_404_NOT_FOUND)


class MarkAllNotificationsReadView(APIView):
    """Mark all notifications as read"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        Notification.objects.filter(recipient=request.user, is_read=False).update(
            is_read=True,
            read_at=timezone.now()
        )
        return Response({'message': 'All notifications marked as read'}, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def notification_count(request):
    """Get unread notification count"""
    count = Notification.objects.filter(recipient=request.user, is_read=False).count()
    return Response({'unread_count': count})


@api_view(['GET'])
@permission_classes([AllowAny])
def get_user_profile(request, username):
    """Get any user's public profile"""
    try:
        User = get_user_model()
        user = get_object_or_404(User, username=username)

        # Get user profile data
        profile_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email if getattr(user, 'is_profile_public', True) else None,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'bio': getattr(user, 'bio', ''),
            'location': getattr(user, 'location', ''),
            'website': getattr(user, 'website', ''),
            'avatar': None,  # Add avatar URL if available
            'is_email_verified': user.is_email_verified,
            'is_profile_public': getattr(user, 'is_profile_public', True),
            'date_joined': user.date_joined.isoformat(),
        }

        # Add follower/following counts
        from social.models import Follow
        profile_data.update({
            'followers_count': Follow.objects.filter(following=user).count(),
            'following_count': Follow.objects.filter(follower=user).count(),
            'posts_count': user.posts.filter(status='published').count() if hasattr(user, 'posts') else 0,
        })

        # Check if current user is following this user
        if request.user.is_authenticated:
            profile_data['is_following'] = Follow.objects.filter(
                follower=request.user,
                following=user
            ).exists()
        else:
            profile_data['is_following'] = False

        return Response(profile_data)

    except User.DoesNotExist:
        return Response({
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_user_posts(request, username):
    """Get posts by a specific user"""
    try:
        User = get_user_model()
        user = get_object_or_404(User, username=username)

        # Get user's posts
        from blog.models import Post
        from blog.serializers import PostSerializer
        from django.core.paginator import Paginator

        posts = Post.objects.filter(
            author=user,
            status='published'
        ).order_by('-created_at')

        # Add pagination
        page = request.GET.get('page', 1)
        paginator = Paginator(posts, 10)  # 10 posts per page
        page_obj = paginator.get_page(page)

        serializer = PostSerializer(page_obj, many=True, context={'request': request})

        return Response({
            'results': serializer.data,
            'count': paginator.count,
            'next': page_obj.has_next(),
            'previous': page_obj.has_previous(),
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages
        })

    except User.DoesNotExist:
        return Response({
            'error': 'User not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
