// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title TrendyToken (TRD)
 * @dev ERC20 token for the Trendy social media platform
 * Features:
 * - Reward distribution for user achievements
 * - Staking mechanism for earning additional rewards
 * - Governance capabilities for community decisions
 * - Anti-whale mechanisms to prevent market manipulation
 */
contract TrendyToken is ERC20, ERC20Burnable, Pausable, Ownable {
    
    // Token configuration
    uint256 public constant MAX_SUPPLY = 1_000_000_000 * 10**18; // 1 billion tokens
    uint256 public constant INITIAL_SUPPLY = 100_000_000 * 10**18; // 100 million tokens
    
    // Reward distribution
    mapping(address => bool) public rewardDistributors;
    mapping(address => uint256) public lastRewardTime;
    uint256 public dailyRewardLimit = 1000 * 10**18; // 1000 TRD per day per user
    
    // Staking
    mapping(address => uint256) public stakedBalances;
    mapping(address => uint256) public stakingStartTime;
    uint256 public stakingRewardRate = 15; // 15% APY
    uint256 public minimumStakingPeriod = 7 days;
    
    // Anti-whale protection
    uint256 public maxTransferAmount = 10_000 * 10**18; // 10,000 TRD max transfer
    mapping(address => bool) public whitelistedAddresses;
    
    // Events
    event RewardDistributed(address indexed user, uint256 amount, string reason);
    event TokensStaked(address indexed user, uint256 amount);
    event TokensUnstaked(address indexed user, uint256 amount, uint256 rewards);
    event RewardDistributorAdded(address indexed distributor);
    event RewardDistributorRemoved(address indexed distributor);
    
    constructor() ERC20("Trendy Token", "TRD") {
        _mint(msg.sender, INITIAL_SUPPLY);
        rewardDistributors[msg.sender] = true;
        whitelistedAddresses[msg.sender] = true;
    }
    
    /**
     * @dev Distribute rewards to users for achievements
     * @param users Array of user addresses
     * @param amounts Array of reward amounts
     * @param reasons Array of reward reasons
     */
    function distributeRewards(
        address[] calldata users,
        uint256[] calldata amounts,
        string[] calldata reasons
    ) external {
        require(rewardDistributors[msg.sender], "Not authorized to distribute rewards");
        require(users.length == amounts.length && amounts.length == reasons.length, "Array length mismatch");
        
        for (uint256 i = 0; i < users.length; i++) {
            address user = users[i];
            uint256 amount = amounts[i];
            
            // Check daily reward limit
            if (block.timestamp - lastRewardTime[user] >= 1 days) {
                lastRewardTime[user] = block.timestamp;
            }
            
            require(amount <= dailyRewardLimit, "Exceeds daily reward limit");
            require(totalSupply() + amount <= MAX_SUPPLY, "Exceeds max supply");
            
            _mint(user, amount);
            emit RewardDistributed(user, amount, reasons[i]);
        }
    }
    
    /**
     * @dev Stake tokens to earn rewards
     * @param amount Amount of tokens to stake
     */
    function stake(uint256 amount) external whenNotPaused {
        require(amount > 0, "Amount must be greater than 0");
        require(balanceOf(msg.sender) >= amount, "Insufficient balance");
        
        // Calculate and distribute pending rewards
        if (stakedBalances[msg.sender] > 0) {
            uint256 pendingRewards = calculateStakingRewards(msg.sender);
            if (pendingRewards > 0) {
                _mint(msg.sender, pendingRewards);
            }
        }
        
        // Transfer tokens to contract
        _transfer(msg.sender, address(this), amount);
        
        // Update staking info
        stakedBalances[msg.sender] += amount;
        stakingStartTime[msg.sender] = block.timestamp;
        
        emit TokensStaked(msg.sender, amount);
    }
    
    /**
     * @dev Unstake tokens and claim rewards
     * @param amount Amount of tokens to unstake
     */
    function unstake(uint256 amount) external {
        require(amount > 0, "Amount must be greater than 0");
        require(stakedBalances[msg.sender] >= amount, "Insufficient staked balance");
        require(
            block.timestamp >= stakingStartTime[msg.sender] + minimumStakingPeriod,
            "Minimum staking period not met"
        );
        
        // Calculate rewards
        uint256 rewards = calculateStakingRewards(msg.sender);
        
        // Update staking info
        stakedBalances[msg.sender] -= amount;
        if (stakedBalances[msg.sender] == 0) {
            stakingStartTime[msg.sender] = 0;
        } else {
            stakingStartTime[msg.sender] = block.timestamp;
        }
        
        // Transfer tokens back to user
        _transfer(address(this), msg.sender, amount);
        
        // Mint rewards
        if (rewards > 0) {
            _mint(msg.sender, rewards);
        }
        
        emit TokensUnstaked(msg.sender, amount, rewards);
    }
    
    /**
     * @dev Calculate pending staking rewards for a user
     * @param user User address
     * @return Pending rewards amount
     */
    function calculateStakingRewards(address user) public view returns (uint256) {
        if (stakedBalances[user] == 0 || stakingStartTime[user] == 0) {
            return 0;
        }
        
        uint256 stakingDuration = block.timestamp - stakingStartTime[user];
        uint256 annualReward = (stakedBalances[user] * stakingRewardRate) / 100;
        uint256 rewards = (annualReward * stakingDuration) / 365 days;
        
        return rewards;
    }
    
    /**
     * @dev Get total staked amount for a user
     * @param user User address
     * @return Staked amount
     */
    function getStakedBalance(address user) external view returns (uint256) {
        return stakedBalances[user];
    }
    
    /**
     * @dev Override transfer to implement anti-whale protection
     */
    function _transfer(address from, address to, uint256 amount) internal override {
        if (!whitelistedAddresses[from] && !whitelistedAddresses[to]) {
            require(amount <= maxTransferAmount, "Transfer amount exceeds maximum");
        }
        
        super._transfer(from, to, amount);
    }
    
    /**
     * @dev Add reward distributor (only owner)
     * @param distributor Address to add as reward distributor
     */
    function addRewardDistributor(address distributor) external onlyOwner {
        rewardDistributors[distributor] = true;
        emit RewardDistributorAdded(distributor);
    }
    
    /**
     * @dev Remove reward distributor (only owner)
     * @param distributor Address to remove as reward distributor
     */
    function removeRewardDistributor(address distributor) external onlyOwner {
        rewardDistributors[distributor] = false;
        emit RewardDistributorRemoved(distributor);
    }
    
    /**
     * @dev Add address to whitelist (only owner)
     * @param account Address to whitelist
     */
    function addToWhitelist(address account) external onlyOwner {
        whitelistedAddresses[account] = true;
    }
    
    /**
     * @dev Remove address from whitelist (only owner)
     * @param account Address to remove from whitelist
     */
    function removeFromWhitelist(address account) external onlyOwner {
        whitelistedAddresses[account] = false;
    }
    
    /**
     * @dev Update maximum transfer amount (only owner)
     * @param newMaxAmount New maximum transfer amount
     */
    function updateMaxTransferAmount(uint256 newMaxAmount) external onlyOwner {
        maxTransferAmount = newMaxAmount;
    }
    
    /**
     * @dev Update staking reward rate (only owner)
     * @param newRate New staking reward rate (percentage)
     */
    function updateStakingRewardRate(uint256 newRate) external onlyOwner {
        require(newRate <= 50, "Reward rate too high"); // Max 50% APY
        stakingRewardRate = newRate;
    }
    
    /**
     * @dev Pause contract (only owner)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract (only owner)
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Emergency withdrawal of staked tokens (only when paused)
     */
    function emergencyWithdraw() external whenPaused {
        uint256 stakedAmount = stakedBalances[msg.sender];
        require(stakedAmount > 0, "No staked tokens");
        
        stakedBalances[msg.sender] = 0;
        stakingStartTime[msg.sender] = 0;
        
        _transfer(address(this), msg.sender, stakedAmount);
    }
}
