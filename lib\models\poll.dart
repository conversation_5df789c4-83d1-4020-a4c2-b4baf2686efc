import 'package:freezed_annotation/freezed_annotation.dart';

part 'poll.freezed.dart';
part 'poll.g.dart';

@freezed
class Poll with _$Poll {
  const factory Poll({
    required int id,
    required String question,
    @Json<PERSON>ey(name: 'allow_multiple_choices') @Default(false) bool allowMultipleChoices,
    @J<PERSON><PERSON><PERSON>(name: 'show_results_immediately') @Default(true) bool showResultsImmediately,
    @Json<PERSON>ey(name: 'expires_at') DateTime? expiresAt,
    @Json<PERSON>ey(name: 'is_anonymous') @Default(false) bool isAnonymous,
    @<PERSON>son<PERSON>ey(name: 'total_votes') @Default(0) int totalVotes,
    @Json<PERSON><PERSON>(name: 'unique_voters') @Default(0) int uniqueVoters,
    @Json<PERSON>ey(name: 'is_expired') @Default(false) bool isExpired,
    @Json<PERSON>ey(name: 'is_active') @Default(true) bool isActive,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    
    @Default([]) List<PollOption> options,
    @JsonKey(name: 'user_vote') @Default([]) List<PollVote> userVote,
  }) = _Poll;

  factory Poll.fromJson(Map<String, dynamic> json) => _$PollFromJson(json);
}

@freezed
class PollOption with _$PollOption {
  const factory PollOption({
    required int id,
    required String text,
    @JsonKey(name: 'image_url') String? imageUrl,
    @JsonKey(name: 'vote_count') @Default(0) int voteCount,
    @JsonKey(name: 'vote_percentage') @Default(0.0) double votePercentage,
    @Default(0) int position,
  }) = _PollOption;

  factory PollOption.fromJson(Map<String, dynamic> json) => _$PollOptionFromJson(json);
}

@freezed
class PollVote with _$PollVote {
  const factory PollVote({
    required int id,
    required int option,
    @JsonKey(name: 'voted_at') required DateTime votedAt,
  }) = _PollVote;

  factory PollVote.fromJson(Map<String, dynamic> json) => _$PollVoteFromJson(json);
}

// Helper classes for creating polls
@freezed
class CreatePollOption with _$CreatePollOption {
  const factory CreatePollOption({
    required String text,
    @JsonKey(name: 'image_url') String? imageUrl,
    @Default(0) int position,
  }) = _CreatePollOption;

  factory CreatePollOption.fromJson(Map<String, dynamic> json) => _$CreatePollOptionFromJson(json);
}

@freezed
class CreatePoll with _$CreatePoll {
  const factory CreatePoll({
    required String question,
    @JsonKey(name: 'allow_multiple_choices') @Default(false) bool allowMultipleChoices,
    @JsonKey(name: 'show_results_immediately') @Default(true) bool showResultsImmediately,
    @JsonKey(name: 'expires_at') DateTime? expiresAt,
    @JsonKey(name: 'is_anonymous') @Default(false) bool isAnonymous,
    required List<CreatePollOption> options,
  }) = _CreatePoll;

  factory CreatePoll.fromJson(Map<String, dynamic> json) => _$CreatePollFromJson(json);
}
