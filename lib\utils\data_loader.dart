import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/rewards_provider.dart';
import '../providers/referral_provider.dart';
import '../providers/store_provider.dart';
import '../providers/community_provider.dart';
import '../providers/wallet_provider.dart';
import '../providers/advertising_provider.dart';

/// Utility class to ensure all app data loads properly
class DataLoader {
  /// Load all essential app data
  static Future<void> loadAllData(WidgetRef ref) async {
    try {
      print('🔄 Starting comprehensive data loading...');

      // Load core content data (always load these)
      await _loadCoreData(ref);

      // Load user-specific data if authenticated
      final authState = ref.read(enhancedAuthProvider);
      if (authState.isAuthenticated) {
        await _loadUserData(ref);
      }

      print('✅ All data loaded successfully!');
    } catch (e) {
      print('❌ Error loading app data: $e');
    }
  }

  /// Load core app data (posts, categories)
  static Future<void> _loadCoreData(WidgetRef ref) async {
    try {
      print('📱 Loading core app data...');

      // Load posts
      final postsNotifier = ref.read(postsProvider.notifier);
      await postsNotifier.getPosts();
      print('✅ Posts loaded');

      // Load categories
      final categoriesNotifier = ref.read(categoriesProvider.notifier);
      await categoriesNotifier.getCategories();
      print('✅ Categories loaded');
    } catch (e) {
      print('❌ Error loading core data: $e');
    }
  }

  /// Load user-specific data
  static Future<void> _loadUserData(WidgetRef ref) async {
    try {
      print('👤 Loading user-specific data...');

      // Load gamification data
      final gamificationNotifier = ref.read(gamificationProvider.notifier);
      await gamificationNotifier.loadUserLevel();
      await gamificationNotifier.loadUserBadges();
      await gamificationNotifier.loadPointTransactions();
      print('✅ Gamification data loaded');

      // Load rewards data
      final rewardsNotifier = ref.read(rewardsProvider.notifier);
      await rewardsNotifier.loadAvailableRewards();
      await rewardsNotifier.loadUserRewards();
      await rewardsNotifier.loadPayPalProfile();
      print('✅ Rewards data loaded');

      // Load referral data
      final referralNotifier = ref.read(referralProvider.notifier);
      await referralNotifier.loadReferralData();
      print('✅ Referral data loaded');

      // Load store data
      final storeNotifier = ref.read(storeProvider.notifier);
      await storeNotifier.loadVirtualItems();
      await storeNotifier.loadPointBoosts();
      await storeNotifier.loadPremiumStatus();
      print('✅ Store data loaded');

      // Load wallet data
      final walletNotifier = ref.read(walletProvider.notifier);
      await walletNotifier.loadWalletOverview();
      await walletNotifier.loadWalletSettings();
      print('✅ Wallet data loaded');

      // Load advertising data
      final advertisingNotifier = ref.read(advertisingProvider.notifier);
      await advertisingNotifier.loadAdvertisingData();
      print('✅ Advertising data loaded');
    } catch (e) {
      print('❌ Error loading user data: $e');
    }
  }

  /// Force refresh all data
  static Future<void> refreshAllData(WidgetRef ref) async {
    print('🔄 Force refreshing all data...');
    await loadAllData(ref);
  }

  /// Load data for specific screen
  static Future<void> loadScreenData(WidgetRef ref, String screenName) async {
    try {
      print('🔄 Loading data for $screenName screen...');

      switch (screenName) {
        case 'home':
          await _loadCoreData(ref);
          break;

        case 'community':
          final communityNotifier = ref.read(communityProvider.notifier);
          await communityNotifier.loadDiscoverUsers();
          await communityNotifier.loadTrendingUsers();
          break;

        case 'rewards':
          final rewardsNotifier = ref.read(rewardsProvider.notifier);
          await rewardsNotifier.loadAvailableRewards();
          await rewardsNotifier.loadUserRewards();
          break;

        case 'referral':
          final referralNotifier = ref.read(referralProvider.notifier);
          await referralNotifier.loadReferralData();
          break;

        case 'wallet':
          final walletNotifier = ref.read(walletProvider.notifier);
          await walletNotifier.loadWalletOverview();
          await walletNotifier.loadWalletSettings();
          break;

        case 'store':
          final storeNotifier = ref.read(storeProvider.notifier);
          await storeNotifier.loadVirtualItems();
          await storeNotifier.loadPointBoosts();
          await storeNotifier.loadPremiumStatus();
          break;

        case 'profile':
          final gamificationNotifier = ref.read(gamificationProvider.notifier);
          await gamificationNotifier.loadUserLevel();
          await gamificationNotifier.loadUserBadges();

          final rewardsNotifier = ref.read(rewardsProvider.notifier);
          await rewardsNotifier.loadUserRewards();

          final storeNotifier = ref.read(storeProvider.notifier);
          await storeNotifier.loadPremiumStatus();
          break;

        default:
          print('⚠️ Unknown screen: $screenName');
      }

      print('✅ $screenName data loaded successfully!');
    } catch (e) {
      print('❌ Error loading $screenName data: $e');
    }
  }

  /// Check if data is loaded
  static bool isDataLoaded(WidgetRef ref) {
    try {
      final postsState = ref.read(postsProvider);
      final authState = ref.read(enhancedAuthProvider);

      // Check if core data is loaded
      bool coreDataLoaded = postsState.when(
        data: (data) => data.results.isNotEmpty,
        loading: () => false,
        error: (_, __) => false,
      );

      // If user is authenticated, check user data too
      if (authState.isAuthenticated) {
        final gamificationState = ref.read(gamificationProvider);
        final rewardsState = ref.read(rewardsProvider);
        final referralState = ref.read(referralProvider);

        bool userDataLoaded = gamificationState.userLevel != null &&
            rewardsState.availableRewards.isNotEmpty &&
            referralState.referrals.isNotEmpty;

        return coreDataLoaded && userDataLoaded;
      }

      return coreDataLoaded;
    } catch (e) {
      print('❌ Error checking data status: $e');
      return false;
    }
  }

  /// Get data loading status
  static Map<String, bool> getDataStatus(WidgetRef ref) {
    try {
      final postsState = ref.read(postsProvider);
      final categoriesState = ref.read(categoriesProvider);
      final authState = ref.read(enhancedAuthProvider);

      Map<String, bool> status = {
        'posts': postsState.when(
          data: (data) => data.results.isNotEmpty,
          loading: () => false,
          error: (_, __) => false,
        ),
        'categories': categoriesState.when(
          data: (data) => data.results.isNotEmpty,
          loading: () => false,
          error: (_, __) => false,
        ),
        'authenticated': authState.isAuthenticated,
      };

      if (authState.isAuthenticated) {
        final gamificationState = ref.read(gamificationProvider);
        final rewardsState = ref.read(rewardsProvider);
        final referralState = ref.read(referralProvider);
        final storeState = ref.read(storeProvider);

        status.addAll({
          'userLevel': gamificationState.userLevel != null,
          'badges': gamificationState.badges.isNotEmpty,
          'rewards': rewardsState.availableRewards.isNotEmpty,
          'referrals': referralState.referrals.isNotEmpty,
          'store': storeState.pointBoosts.isNotEmpty,
        });
      }

      return status;
    } catch (e) {
      print('❌ Error getting data status: $e');
      return {};
    }
  }
}

/// Provider for data loading status
final dataLoaderProvider = Provider<DataLoader>((ref) {
  return DataLoader();
});

/// Provider to check if all data is loaded
final isDataLoadedProvider = Provider<bool>((ref) {
  try {
    final postsState = ref.watch(postsProvider);
    final authState = ref.watch(enhancedAuthProvider);

    // Check if core data is loaded
    bool coreDataLoaded = postsState.when(
      data: (data) => data.results.isNotEmpty,
      loading: () => false,
      error: (_, __) => false,
    );

    // If user is authenticated, check user data too
    if (authState.isAuthenticated) {
      final gamificationState = ref.watch(gamificationProvider);
      final rewardsState = ref.watch(rewardsProvider);
      final referralState = ref.watch(referralProvider);

      bool userDataLoaded = gamificationState.userLevel != null &&
          rewardsState.availableRewards.isNotEmpty &&
          referralState.referrals.isNotEmpty;

      return coreDataLoaded && userDataLoaded;
    }

    return coreDataLoaded;
  } catch (e) {
    return false;
  }
});

/// Provider for data loading status
final dataStatusProvider = Provider<Map<String, bool>>((ref) {
  try {
    final postsState = ref.watch(postsProvider);
    final categoriesState = ref.watch(categoriesProvider);
    final authState = ref.watch(enhancedAuthProvider);

    Map<String, bool> status = {
      'posts': postsState.when(
        data: (data) => data.results.isNotEmpty,
        loading: () => false,
        error: (_, __) => false,
      ),
      'categories': categoriesState.when(
        data: (data) => data.results.isNotEmpty,
        loading: () => false,
        error: (_, __) => false,
      ),
      'authenticated': authState.isAuthenticated,
    };

    if (authState.isAuthenticated) {
      final gamificationState = ref.watch(gamificationProvider);
      final rewardsState = ref.watch(rewardsProvider);
      final referralState = ref.watch(referralProvider);
      final storeState = ref.watch(storeProvider);

      status.addAll({
        'userLevel': gamificationState.userLevel != null,
        'badges': gamificationState.badges.isNotEmpty,
        'rewards': rewardsState.availableRewards.isNotEmpty,
        'referrals': referralState.referrals.isNotEmpty,
        'store': storeState.pointBoosts.isNotEmpty,
      });
    }

    return status;
  } catch (e) {
    return <String, bool>{};
  }
});
