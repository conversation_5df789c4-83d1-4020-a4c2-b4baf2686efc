import '../models/messaging.dart';
import 'api_service.dart';

class MessagingService {
  final ApiService _apiService;

  MessagingService(this._apiService);

  /// Get all conversations for the current user
  Future<ConversationResponse> getConversations({int page = 1}) async {
    try {
      final response = await _apiService.dio
          .get('/api/v1/messaging/conversations/', queryParameters: {
        'page': page.toString(),
      });

      if (response.statusCode == 200) {
        return ConversationResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to load conversations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading conversations: $e');
    }
  }

  /// Get a specific conversation by ID
  Future<Conversation> getConversation(int conversationId) async {
    try {
      final response = await _apiService.dio
          .get('/api/v1/messaging/conversations/$conversationId/');

      if (response.statusCode == 200) {
        return Conversation.from<PERSON>son(response.data);
      } else {
        throw Exception('Failed to load conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading conversation: $e');
    }
  }

  /// Get messages for a specific conversation
  Future<MessageListResponse> getMessages(int conversationId,
      {int page = 1}) async {
    try {
      final response = await _apiService.dio.get(
          '/api/v1/messaging/conversations/$conversationId/messages/',
          queryParameters: {
            'page': page.toString(),
          });

      if (response.statusCode == 200) {
        return MessageListResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to load messages: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading messages: $e');
    }
  }

  /// Send a message in a conversation
  Future<Message> sendMessage(int conversationId, String content) async {
    try {
      final response = await _apiService.dio.post(
        '/api/v1/messaging/conversations/$conversationId/messages/',
        data: {'content': content},
      );

      if (response.statusCode == 201) {
        return Message.fromJson(response.data);
      } else {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error sending message: $e');
    }
  }

  /// Start a new conversation with a user
  Future<StartConversationResponse> startConversation(String username,
      {String? initialMessage}) async {
    try {
      final requestBody = <String, dynamic>{
        'username': username,
      };

      if (initialMessage != null && initialMessage.isNotEmpty) {
        requestBody['message'] = initialMessage;
      }

      final response = await _apiService.dio.post(
        '/api/v1/messaging/start-conversation/',
        data: requestBody,
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        return StartConversationResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to start conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error starting conversation: $e');
    }
  }

  /// Mark a conversation as read
  Future<void> markConversationAsRead(int conversationId) async {
    try {
      final response = await _apiService.dio.post(
        '/api/v1/messaging/conversations/$conversationId/mark-read/',
        data: {},
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to mark conversation as read: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error marking conversation as read: $e');
    }
  }

  /// Get total unread message count
  Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.dio.get('/api/v1/messaging/unread-count/');

      if (response.statusCode == 200) {
        final unreadResponse = UnreadCountResponse.fromJson(response.data);
        return unreadResponse.unreadCount;
      } else {
        throw Exception('Failed to get unread count: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting unread count: $e');
    }
  }

  /// Delete a conversation (archive it)
  Future<void> deleteConversation(int conversationId) async {
    try {
      final response = await _apiService.dio
          .delete('/api/v1/messaging/conversations/$conversationId/');

      if (response.statusCode != 204) {
        throw Exception(
            'Failed to delete conversation: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error deleting conversation: $e');
    }
  }

  /// Update a message
  Future<Message> updateMessage(int messageId, String content) async {
    try {
      final response = await _apiService.dio.put(
        '/api/v1/messaging/messages/$messageId/',
        data: {'content': content},
      );

      if (response.statusCode == 200) {
        return Message.fromJson(response.data);
      } else {
        throw Exception('Failed to update message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating message: $e');
    }
  }

  /// Delete a message
  Future<void> deleteMessage(int messageId) async {
    try {
      final response = await _apiService.dio
          .delete('/api/v1/messaging/messages/$messageId/');

      if (response.statusCode != 204) {
        throw Exception('Failed to delete message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error deleting message: $e');
    }
  }
}
