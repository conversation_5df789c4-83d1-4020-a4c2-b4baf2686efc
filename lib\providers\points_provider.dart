import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';

// Unified Points State
class UnifiedPointsState {
  final int gamificationPoints;
  final int storePoints;
  final int totalConverted;
  final int dailyConversionsRemaining;
  final Map<String, dynamic>? conversionSettings;
  final List<dynamic> conversionHistory;
  final bool isLoading;
  final String? error;

  const UnifiedPointsState({
    this.gamificationPoints = 0,
    this.storePoints = 0,
    this.totalConverted = 0,
    this.dailyConversionsRemaining = 0,
    this.conversionSettings,
    this.conversionHistory = const [],
    this.isLoading = false,
    this.error,
  });

  UnifiedPointsState copyWith({
    int? gamificationPoints,
    int? storePoints,
    int? totalConverted,
    int? dailyConversionsRemaining,
    Map<String, dynamic>? conversionSettings,
    List<dynamic>? conversionHistory,
    bool? isLoading,
    String? error,
  }) {
    return UnifiedPointsState(
      gamificationPoints: gamificationPoints ?? this.gamificationPoints,
      storePoints: storePoints ?? this.storePoints,
      totalConverted: totalConverted ?? this.totalConverted,
      dailyConversionsRemaining: dailyConversionsRemaining ?? this.dailyConversionsRemaining,
      conversionSettings: conversionSettings ?? this.conversionSettings,
      conversionHistory: conversionHistory ?? this.conversionHistory,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Unified Points Notifier
class UnifiedPointsNotifier extends StateNotifier<UnifiedPointsState> {
  final ApiService _apiService;

  UnifiedPointsNotifier(this._apiService) : super(const UnifiedPointsState());

  // Load all points data using unified endpoint
  Future<void> loadUnifiedPoints() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final response = await _apiService.getUnifiedUserPoints();
      
      if (response['success'] == true) {
        final data = response['data'];
        final gamificationData = data['gamification'];
        final storeData = data['store'];
        
        state = state.copyWith(
          gamificationPoints: (gamificationData['total_points'] as num?)?.toInt() ?? 0,
          storePoints: (storeData['balance'] as num?)?.toInt() ?? 0,
          totalConverted: (storeData['total_converted'] as num?)?.toInt() ?? 0,
          dailyConversionsRemaining: (storeData['daily_conversions_remaining'] as num?)?.toInt() ?? 0,
          isLoading: false,
        );
      } else {
        throw Exception('Failed to load unified points data');
      }
    } catch (e) {
      print('Error loading unified points: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load points: ${e.toString()}',
      );
    }
  }

  // Load conversion settings
  Future<void> loadConversionSettings() async {
    try {
      final response = await _apiService.getConversionSettings();
      
      if (response['success'] == true) {
        state = state.copyWith(
          conversionSettings: response['settings'],
        );
      }
    } catch (e) {
      print('Error loading conversion settings: $e');
    }
  }

  // Load conversion history
  Future<void> loadConversionHistory() async {
    try {
      final history = await _apiService.getConversionHistory();
      state = state.copyWith(conversionHistory: history);
    } catch (e) {
      print('Error loading conversion history: $e');
    }
  }

  // Get conversion preview
  Future<Map<String, dynamic>?> getConversionPreview(int gamificationPoints) async {
    try {
      final response = await _apiService.getConversionPreview(gamificationPoints);
      
      if (response['success'] == true) {
        return response['preview'];
      } else {
        throw Exception(response['error'] ?? 'Failed to get conversion preview');
      }
    } catch (e) {
      print('Error getting conversion preview: $e');
      return null;
    }
  }

  // Convert gamification points to store points
  Future<bool> convertPoints(int gamificationPoints) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final response = await _apiService.convertGamificationToStorePoints(gamificationPoints);
      
      if (response['success'] == true) {
        final result = response['conversion_result'];
        
        // Update state with new balances
        state = state.copyWith(
          gamificationPoints: state.gamificationPoints - (result['gamification_points_spent'] as num).toInt(),
          storePoints: (result['new_store_balance'] as num).toInt(),
          totalConverted: state.totalConverted + (result['store_points_received'] as num).toInt(),
          isLoading: false,
        );
        
        // Reload full data to ensure consistency
        await loadUnifiedPoints();
        await loadConversionHistory();
        
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['error'] ?? 'Conversion failed',
        );
        return false;
      }
    } catch (e) {
      print('Error converting points: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Conversion failed: ${e.toString()}',
      );
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Refresh all data
  Future<void> refreshAll() async {
    await Future.wait([
      loadUnifiedPoints(),
      loadConversionSettings(),
      loadConversionHistory(),
    ]);
  }
}

// Providers
final apiServiceProvider = Provider<ApiService>((ref) => ApiService());

final unifiedPointsProvider = StateNotifierProvider<UnifiedPointsNotifier, UnifiedPointsState>((ref) {
  return UnifiedPointsNotifier(ref.watch(apiServiceProvider));
});

// Convenience providers for specific values
final gamificationPointsProvider = Provider<int>((ref) {
  return ref.watch(unifiedPointsProvider).gamificationPoints;
});

final storePointsProvider = Provider<int>((ref) {
  return ref.watch(unifiedPointsProvider).storePoints;
});

final currentPointsProvider = Provider<int>((ref) {
  // This provider returns gamification points for backward compatibility
  return ref.watch(unifiedPointsProvider).gamificationPoints;
});

final canConvertPointsProvider = Provider<bool>((ref) {
  final state = ref.watch(unifiedPointsProvider);
  final settings = state.conversionSettings;
  
  if (settings == null) return false;
  
  final minAmount = settings['minimum_conversion_amount'] ?? 0;
  final conversionEnabled = settings['conversion_enabled'] ?? false;
  final maintenanceMode = settings['maintenance_mode'] ?? false;
  
  return conversionEnabled && 
         !maintenanceMode && 
         state.gamificationPoints >= minAmount &&
         state.dailyConversionsRemaining > 0;
});
