<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
  </defs>

  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#gradient)" filter="url(#shadow)"/>

  <!-- White container for icon -->
  <circle cx="256" cy="256" r="180" fill="white" opacity="0.95"/>

  <!-- Trending up icon -->
  <g transform="translate(256, 256)">
    <!-- Arrow line -->
    <path d="M-80 40 L80 -40" stroke="url(#gradient)" stroke-width="16" stroke-linecap="round"/>

    <!-- Arrow head -->
    <path d="M50 -40 L80 -40 L80 -10" stroke="url(#gradient)" stroke-width="16" stroke-linecap="round" stroke-linejoin="round" fill="none"/>

    <!-- Data points -->
    <circle cx="-60" cy="20" r="8" fill="url(#gradient)"/>
    <circle cx="-20" cy="0" r="8" fill="url(#gradient)"/>
    <circle cx="20" cy="-20" r="8" fill="url(#gradient)"/>
    <circle cx="60" cy="-40" r="8" fill="url(#gradient)"/>

    <!-- Connecting lines -->
    <path d="M-60 20 L-20 0 L20 -20 L60 -40" stroke="url(#gradient)" stroke-width="4" stroke-linecap="round" fill="none" opacity="0.6"/>
  </g>

  <!-- Subtle "T" for Trendy -->
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white" opacity="0.9">T</text>
</svg>
