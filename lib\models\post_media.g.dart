// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_media.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PostMediaImpl _$$PostMediaImplFromJson(Map<String, dynamic> json) =>
    _$PostMediaImpl(
      id: (json['id'] as num).toInt(),
      mediaType: json['media_type'] as String? ?? 'image',
      image: json['image'] as String?,
      imageUrl: json['image_url'] as String?,
      imageUrlFull: json['image_url_full'] as String?,
      video: json['video'] as String?,
      videoUrl: json['video_url'] as String?,
      thumbnail: json['thumbnail'] as String?,
      caption: json['caption'] as String? ?? '',
      title: json['title'] as String?,
      description: json['description'] as String?,
      order: (json['order'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$$PostMediaImplToJson(_$PostMediaImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'media_type': instance.mediaType,
      'image': instance.image,
      'image_url': instance.imageUrl,
      'image_url_full': instance.imageUrlFull,
      'video': instance.video,
      'video_url': instance.videoUrl,
      'thumbnail': instance.thumbnail,
      'caption': instance.caption,
      'title': instance.title,
      'description': instance.description,
      'order': instance.order,
      'created_at': instance.createdAt.toIso8601String(),
    };
