from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import Q, Prefetch
from django.contrib.auth import get_user_model
from .models import Conversation, Message, ConversationMembership
from .serializers import (
    ConversationSerializer, MessageSerializer, ConversationCreateSerializer,
    ConversationMembershipSerializer
)

User = get_user_model()


class ConversationListCreateView(generics.ListCreateAPIView):
    """List user's conversations or create a new one"""
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ConversationCreateSerializer
        return ConversationSerializer

    def get_queryset(self):
        """Get conversations for the current user"""
        return Conversation.objects.filter(
            participants=self.request.user,
            is_active=True
        ).prefetch_related(
            'participants',
            'messages'
        ).distinct()


class ConversationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Get, update, or delete a specific conversation"""
    permission_classes = [IsAuthenticated]
    serializer_class = ConversationSerializer

    def get_queryset(self):
        """Get conversations for the current user"""
        return Conversation.objects.filter(
            participants=self.request.user,
            is_active=True
        ).prefetch_related('participants')

    def perform_destroy(self, instance):
        """Soft delete conversation for current user"""
        membership = ConversationMembership.objects.get(
            conversation=instance,
            user=self.request.user
        )
        membership.is_archived = True
        membership.save()


class MessageListCreateView(generics.ListCreateAPIView):
    """List messages in a conversation or send a new message"""
    permission_classes = [IsAuthenticated]
    serializer_class = MessageSerializer

    def get_queryset(self):
        """Get messages for a specific conversation"""
        conversation_id = self.kwargs['conversation_id']
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            participants=self.request.user
        )

        # Mark conversation as read
        try:
            membership = ConversationMembership.objects.get(
                conversation=conversation,
                user=self.request.user
            )
            membership.mark_as_read()
        except ConversationMembership.DoesNotExist:
            pass

        return Message.objects.filter(
            conversation=conversation,
            is_deleted=False
        ).select_related('sender').order_by('created_at')

    def perform_create(self, serializer):
        """Create a new message in the conversation"""
        conversation_id = self.kwargs['conversation_id']
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            participants=self.request.user
        )
        serializer.save(conversation=conversation)


class MessageDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Get, update, or delete a specific message"""
    permission_classes = [IsAuthenticated]
    serializer_class = MessageSerializer

    def get_queryset(self):
        """Get messages that belong to current user's conversations"""
        return Message.objects.filter(
            conversation__participants=self.request.user,
            is_deleted=False
        ).select_related('sender', 'conversation')

    def perform_update(self, serializer):
        """Only allow sender to update their own messages"""
        if serializer.instance.sender != self.request.user:
            raise permissions.PermissionDenied("You can only edit your own messages")
        serializer.save()

    def perform_destroy(self, instance):
        """Soft delete message (only sender can delete)"""
        if instance.sender != self.request.user:
            raise permissions.PermissionDenied("You can only delete your own messages")
        instance.is_deleted = True
        instance.save()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_conversation_read(request, conversation_id):
    """Mark all messages in a conversation as read"""
    try:
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            participants=request.user
        )

        membership = ConversationMembership.objects.get(
            conversation=conversation,
            user=request.user
        )
        membership.mark_as_read()

        return Response({'status': 'success', 'message': 'Conversation marked as read'})

    except ConversationMembership.DoesNotExist:
        return Response(
            {'error': 'You are not a member of this conversation'},
            status=status.HTTP_403_FORBIDDEN
        )
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_unread_count(request):
    """Get total unread message count for current user"""
    try:
        total_unread = 0
        memberships = ConversationMembership.objects.filter(
            user=request.user,
            conversation__is_active=True,
            is_archived=False
        )

        for membership in memberships:
            total_unread += membership.unread_count

        return Response({'unread_count': total_unread})

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_conversation(request):
    """Start a new conversation with a user"""
    try:
        username = request.data.get('username')
        initial_message = request.data.get('message', '')

        if not username:
            return Response(
                {'error': 'Username is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user exists
        try:
            other_user = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        if other_user == request.user:
            return Response(
                {'error': 'You cannot start a conversation with yourself'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if conversation already exists
        existing_conversation = Conversation.objects.filter(
            participants=request.user
        ).filter(
            participants=other_user
        ).first()

        if existing_conversation:
            serializer = ConversationSerializer(existing_conversation, context={'request': request})
            return Response({
                'conversation': serializer.data,
                'created': False,
                'message': 'Conversation already exists'
            })

        # Create new conversation
        conversation = Conversation.objects.create()
        conversation.participants.add(request.user, other_user)

        # Create memberships
        ConversationMembership.objects.create(
            conversation=conversation,
            user=request.user
        )
        ConversationMembership.objects.create(
            conversation=conversation,
            user=other_user
        )

        # Send initial message if provided
        if initial_message:
            Message.objects.create(
                conversation=conversation,
                sender=request.user,
                content=initial_message
            )

        serializer = ConversationSerializer(conversation, context={'request': request})
        return Response({
            'conversation': serializer.data,
            'created': True,
            'message': 'Conversation created successfully'
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
