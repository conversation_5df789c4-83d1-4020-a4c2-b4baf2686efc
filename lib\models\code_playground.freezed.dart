// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'code_playground.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CodePlayground _$CodePlaygroundFromJson(Map<String, dynamic> json) {
  return _CodePlayground.fromJson(json);
}

/// @nodoc
mixin _$CodePlayground {
  int get id => throw _privateConstructorUsedError;
  String get language => throw _privateConstructorUsedError;
  @JsonKey(name: 'initial_code')
  String get initialCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'expected_output')
  String get expectedOutput => throw _privateConstructorUsedError;
  String get instructions => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_editable')
  bool get isEditable => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_line_numbers')
  bool get showLineNumbers => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CodePlaygroundCopyWith<CodePlayground> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CodePlaygroundCopyWith<$Res> {
  factory $CodePlaygroundCopyWith(
          CodePlayground value, $Res Function(CodePlayground) then) =
      _$CodePlaygroundCopyWithImpl<$Res, CodePlayground>;
  @useResult
  $Res call(
      {int id,
      String language,
      @JsonKey(name: 'initial_code') String initialCode,
      @JsonKey(name: 'expected_output') String expectedOutput,
      String instructions,
      @JsonKey(name: 'is_editable') bool isEditable,
      @JsonKey(name: 'show_line_numbers') bool showLineNumbers,
      @JsonKey(name: 'created_at') DateTime createdAt});
}

/// @nodoc
class _$CodePlaygroundCopyWithImpl<$Res, $Val extends CodePlayground>
    implements $CodePlaygroundCopyWith<$Res> {
  _$CodePlaygroundCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? language = null,
    Object? initialCode = null,
    Object? expectedOutput = null,
    Object? instructions = null,
    Object? isEditable = null,
    Object? showLineNumbers = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      initialCode: null == initialCode
          ? _value.initialCode
          : initialCode // ignore: cast_nullable_to_non_nullable
              as String,
      expectedOutput: null == expectedOutput
          ? _value.expectedOutput
          : expectedOutput // ignore: cast_nullable_to_non_nullable
              as String,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      isEditable: null == isEditable
          ? _value.isEditable
          : isEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      showLineNumbers: null == showLineNumbers
          ? _value.showLineNumbers
          : showLineNumbers // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CodePlaygroundImplCopyWith<$Res>
    implements $CodePlaygroundCopyWith<$Res> {
  factory _$$CodePlaygroundImplCopyWith(_$CodePlaygroundImpl value,
          $Res Function(_$CodePlaygroundImpl) then) =
      __$$CodePlaygroundImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String language,
      @JsonKey(name: 'initial_code') String initialCode,
      @JsonKey(name: 'expected_output') String expectedOutput,
      String instructions,
      @JsonKey(name: 'is_editable') bool isEditable,
      @JsonKey(name: 'show_line_numbers') bool showLineNumbers,
      @JsonKey(name: 'created_at') DateTime createdAt});
}

/// @nodoc
class __$$CodePlaygroundImplCopyWithImpl<$Res>
    extends _$CodePlaygroundCopyWithImpl<$Res, _$CodePlaygroundImpl>
    implements _$$CodePlaygroundImplCopyWith<$Res> {
  __$$CodePlaygroundImplCopyWithImpl(
      _$CodePlaygroundImpl _value, $Res Function(_$CodePlaygroundImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? language = null,
    Object? initialCode = null,
    Object? expectedOutput = null,
    Object? instructions = null,
    Object? isEditable = null,
    Object? showLineNumbers = null,
    Object? createdAt = null,
  }) {
    return _then(_$CodePlaygroundImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      initialCode: null == initialCode
          ? _value.initialCode
          : initialCode // ignore: cast_nullable_to_non_nullable
              as String,
      expectedOutput: null == expectedOutput
          ? _value.expectedOutput
          : expectedOutput // ignore: cast_nullable_to_non_nullable
              as String,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      isEditable: null == isEditable
          ? _value.isEditable
          : isEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      showLineNumbers: null == showLineNumbers
          ? _value.showLineNumbers
          : showLineNumbers // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CodePlaygroundImpl implements _CodePlayground {
  const _$CodePlaygroundImpl(
      {required this.id,
      this.language = 'python',
      @JsonKey(name: 'initial_code') this.initialCode = '',
      @JsonKey(name: 'expected_output') this.expectedOutput = '',
      this.instructions = '',
      @JsonKey(name: 'is_editable') this.isEditable = true,
      @JsonKey(name: 'show_line_numbers') this.showLineNumbers = true,
      @JsonKey(name: 'created_at') required this.createdAt});

  factory _$CodePlaygroundImpl.fromJson(Map<String, dynamic> json) =>
      _$$CodePlaygroundImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey()
  final String language;
  @override
  @JsonKey(name: 'initial_code')
  final String initialCode;
  @override
  @JsonKey(name: 'expected_output')
  final String expectedOutput;
  @override
  @JsonKey()
  final String instructions;
  @override
  @JsonKey(name: 'is_editable')
  final bool isEditable;
  @override
  @JsonKey(name: 'show_line_numbers')
  final bool showLineNumbers;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  String toString() {
    return 'CodePlayground(id: $id, language: $language, initialCode: $initialCode, expectedOutput: $expectedOutput, instructions: $instructions, isEditable: $isEditable, showLineNumbers: $showLineNumbers, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CodePlaygroundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.initialCode, initialCode) ||
                other.initialCode == initialCode) &&
            (identical(other.expectedOutput, expectedOutput) ||
                other.expectedOutput == expectedOutput) &&
            (identical(other.instructions, instructions) ||
                other.instructions == instructions) &&
            (identical(other.isEditable, isEditable) ||
                other.isEditable == isEditable) &&
            (identical(other.showLineNumbers, showLineNumbers) ||
                other.showLineNumbers == showLineNumbers) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, language, initialCode,
      expectedOutput, instructions, isEditable, showLineNumbers, createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CodePlaygroundImplCopyWith<_$CodePlaygroundImpl> get copyWith =>
      __$$CodePlaygroundImplCopyWithImpl<_$CodePlaygroundImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CodePlaygroundImplToJson(
      this,
    );
  }
}

abstract class _CodePlayground implements CodePlayground {
  const factory _CodePlayground(
          {required final int id,
          final String language,
          @JsonKey(name: 'initial_code') final String initialCode,
          @JsonKey(name: 'expected_output') final String expectedOutput,
          final String instructions,
          @JsonKey(name: 'is_editable') final bool isEditable,
          @JsonKey(name: 'show_line_numbers') final bool showLineNumbers,
          @JsonKey(name: 'created_at') required final DateTime createdAt}) =
      _$CodePlaygroundImpl;

  factory _CodePlayground.fromJson(Map<String, dynamic> json) =
      _$CodePlaygroundImpl.fromJson;

  @override
  int get id;
  @override
  String get language;
  @override
  @JsonKey(name: 'initial_code')
  String get initialCode;
  @override
  @JsonKey(name: 'expected_output')
  String get expectedOutput;
  @override
  String get instructions;
  @override
  @JsonKey(name: 'is_editable')
  bool get isEditable;
  @override
  @JsonKey(name: 'show_line_numbers')
  bool get showLineNumbers;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$CodePlaygroundImplCopyWith<_$CodePlaygroundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
