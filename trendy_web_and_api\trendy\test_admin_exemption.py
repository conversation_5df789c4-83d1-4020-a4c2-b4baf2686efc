#!/usr/bin/env python
"""
Test script to verify admin exemption from email verification
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.serializers import LoginSerializer

User = get_user_model()

def test_admin_exemption():
    """Test that admin users are exempt from email verification"""
    
    print("🔍 Testing Admin Email Verification Exemption")
    print("=" * 50)
    
    # Test 1: Check if admin users have auto-verified emails
    print("\n1. Checking existing admin users...")
    admin_users = User.objects.filter(is_staff=True)
    
    for user in admin_users:
        print(f"   👤 {user.username} (Staff: {user.is_staff}, Superuser: {user.is_superuser})")
        print(f"      📧 Email verified: {user.is_email_verified}")
        print(f"      🔒 Requires verification: {user.requires_email_verification}")
    
    # Test 2: Create a new admin user and verify auto-verification
    print("\n2. Creating new admin user...")
    try:
        test_admin = User.objects.create_user(
            username='test_admin',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        print(f"   ✅ Created admin user: {test_admin.username}")
        print(f"   📧 Email auto-verified: {test_admin.is_email_verified}")
        print(f"   🔒 Requires verification: {test_admin.requires_email_verification}")
        
        # Clean up
        test_admin.delete()
        print("   🗑️ Test admin user deleted")
        
    except Exception as e:
        print(f"   ❌ Error creating test admin: {e}")
    
    # Test 3: Create a regular user and verify they need verification
    print("\n3. Creating regular user for comparison...")
    try:
        test_user = User.objects.create_user(
            username='test_user',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"   ✅ Created regular user: {test_user.username}")
        print(f"   📧 Email verified: {test_user.is_email_verified}")
        print(f"   🔒 Requires verification: {test_user.requires_email_verification}")
        
        # Clean up
        test_user.delete()
        print("   🗑️ Test regular user deleted")
        
    except Exception as e:
        print(f"   ❌ Error creating test user: {e}")
    
    # Test 4: Test login serializer validation
    print("\n4. Testing login serializer validation...")
    
    # Create test users for login test
    try:
        admin_user = User.objects.create_user(
            username='login_admin',
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        
        regular_user = User.objects.create_user(
            username='login_user',
            email='<EMAIL>',
            password='testpass123',
            is_email_verified=False
        )
        
        # Test admin login (should work)
        admin_login_data = {
            'email_or_username': '<EMAIL>',
            'password': 'testpass123'
        }
        
        admin_serializer = LoginSerializer(data=admin_login_data)
        if admin_serializer.is_valid():
            print("   ✅ Admin login validation passed (as expected)")
        else:
            print(f"   ❌ Admin login validation failed: {admin_serializer.errors}")
        
        # Test regular user login (should fail due to unverified email)
        user_login_data = {
            'email_or_username': '<EMAIL>',
            'password': 'testpass123'
        }
        
        user_serializer = LoginSerializer(data=user_login_data)
        if not user_serializer.is_valid():
            print("   ✅ Regular user login validation failed (as expected)")
            print(f"      Error: {user_serializer.errors}")
        else:
            print("   ❌ Regular user login validation passed (unexpected)")
        
        # Clean up
        admin_user.delete()
        regular_user.delete()
        print("   🗑️ Test login users deleted")
        
    except Exception as e:
        print(f"   ❌ Error in login test: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Admin exemption test completed!")

if __name__ == '__main__':
    test_admin_exemption()
