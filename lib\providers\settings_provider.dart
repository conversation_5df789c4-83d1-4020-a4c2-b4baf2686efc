import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_settings.dart';
import '../services/api_service.dart';
import 'auth_provider.dart';

// Settings State
class SettingsState {
  final UserSettings? settings;
  final bool isLoading;
  final String? error;

  const SettingsState({
    this.settings,
    this.isLoading = false,
    this.error,
  });

  SettingsState copyWith({
    UserSettings? settings,
    bool? isLoading,
    String? error,
  }) {
    return SettingsState(
      settings: settings ?? this.settings,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Settings Notifier
class SettingsNotifier extends StateNotifier<SettingsState> {
  final ApiService _apiService;

  SettingsNotifier(this._apiService) : super(const SettingsState());

  Future<void> loadSettings() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final settings = await _apiService.getUserSettings();
      state = state.copyWith(settings: settings, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> updateSettings(UserSettings newSettings) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedSettings = await _apiService.updateUserSettings(newSettings);
      state = state.copyWith(settings: updatedSettings, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> updateNotificationSettings({
    bool? emailNotifications,
    bool? pushNotifications,
    bool? commentNotifications,
    bool? likeNotifications,
    bool? followNotifications,
  }) async {
    if (state.settings == null) return;

    final updatedSettings = state.settings!.copyWith(
      emailNotifications: emailNotifications ?? state.settings!.emailNotifications,
      pushNotifications: pushNotifications ?? state.settings!.pushNotifications,
      commentNotifications: commentNotifications ?? state.settings!.commentNotifications,
      likeNotifications: likeNotifications ?? state.settings!.likeNotifications,
      followNotifications: followNotifications ?? state.settings!.followNotifications,
    );

    await updateSettings(updatedSettings);
  }

  Future<void> updatePrivacySettings({
    String? profileVisibility,
    bool? showEmail,
    bool? showPhone,
  }) async {
    if (state.settings == null) return;

    final updatedSettings = state.settings!.copyWith(
      profileVisibility: profileVisibility ?? state.settings!.profileVisibility,
      showEmail: showEmail ?? state.settings!.showEmail,
      showPhone: showPhone ?? state.settings!.showPhone,
    );

    await updateSettings(updatedSettings);
  }

  Future<void> updateContentSettings({
    String? contentLanguage,
    int? postsPerPage,
    bool? autoPlayVideos,
  }) async {
    if (state.settings == null) return;

    final updatedSettings = state.settings!.copyWith(
      contentLanguage: contentLanguage ?? state.settings!.contentLanguage,
      postsPerPage: postsPerPage ?? state.settings!.postsPerPage,
      autoPlayVideos: autoPlayVideos ?? state.settings!.autoPlayVideos,
    );

    await updateSettings(updatedSettings);
  }

  Future<void> updateTheme(String theme) async {
    if (state.settings == null) return;

    final updatedSettings = state.settings!.copyWith(theme: theme);
    await updateSettings(updatedSettings);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Settings Provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, SettingsState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return SettingsNotifier(apiService);
});

// Auto-load settings when user is authenticated
final autoLoadSettingsProvider = Provider<void>((ref) {
  final authState = ref.watch(enhancedAuthProvider);
  final settingsNotifier = ref.read(settingsProvider.notifier);

  if (authState.isAuthenticated && !authState.isLoading) {
    // Load settings when user is authenticated
    Future.microtask(() => settingsNotifier.loadSettings());
  }
});
