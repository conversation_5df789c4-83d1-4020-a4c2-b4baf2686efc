"""
Core app configuration for system maintenance and feature management
"""
from django.apps import AppConfig


class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    verbose_name = 'System Core'
    
    def ready(self):
        # Import signals
        try:
            from . import signals
        except ImportError:
            pass
        
        # Setup predefined feature toggles
        self.setup_predefined_features()
    
    def setup_predefined_features(self):
        """Setup predefined feature toggles on app ready"""
        try:
            from .models import FeatureToggle, PREDEFINED_FEATURES
            from django.db import transaction
            
            with transaction.atomic():
                for feature_data in PREDEFINED_FEATURES:
                    FeatureToggle.objects.get_or_create(
                        name=feature_data['name'],
                        defaults=feature_data
                    )
        except Exception:
            # Ignore errors during migrations or initial setup
            pass
