import 'package:dio/dio.dart';
import '../models/maintenance_status.dart';
import 'api_service.dart';

class MaintenanceService {
  final ApiService _apiService;

  MaintenanceService(this._apiService);

  /// Check system maintenance status
  Future<SystemStatus> getSystemStatus() async {
    try {
      final response = await _apiService.dio.get('/api/v1/system/status/');
      
      if (response.statusCode == 200) {
        return SystemStatus.fromJson(response.data);
      } else {
        throw Exception('Failed to get system status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 503) {
        // System is in maintenance mode
        final data = e.response?.data;
        if (data != null && data['error'] == 'maintenance_mode') {
          return SystemStatus(
            maintenanceActive: true,
            activeMaintenance: MaintenanceStatus(
              isActive: true,
              title: 'System Maintenance',
              message: data['message'] ?? 'System is under maintenance',
              maintenanceType: data['maintenance_type'] ?? 'maintenance',
              status: 'active',
              scheduledEnd: data['scheduled_end'] != null 
                  ? DateTime.parse(data['scheduled_end'])
                  : null,
            ),
            lastChecked: DateTime.now(),
          );
        }
      }
      
      print('Error checking system status: $e');
      // Return default status on error
      return SystemStatus(
        maintenanceActive: false,
        lastChecked: DateTime.now(),
      );
    } catch (e) {
      print('Unexpected error checking system status: $e');
      return SystemStatus(
        maintenanceActive: false,
        lastChecked: DateTime.now(),
      );
    }
  }

  /// Check if a specific feature is enabled
  Future<bool> isFeatureEnabled(String featureName) async {
    try {
      final response = await _apiService.dio.get('/api/v1/system/feature/$featureName/');
      
      if (response.statusCode == 200) {
        return response.data['enabled'] ?? true;
      } else {
        return true; // Default to enabled if can't check
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 503) {
        // Feature is disabled
        return false;
      }
      return true; // Default to enabled on error
    } catch (e) {
      print('Error checking feature status: $e');
      return true; // Default to enabled on error
    }
  }

  /// Get all feature toggles
  Future<Map<String, FeatureToggle>> getFeatureToggles() async {
    try {
      final response = await _apiService.dio.get('/api/v1/system/features/');
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = response.data;
        final Map<String, FeatureToggle> features = {};
        
        data.forEach((key, value) {
          features[key] = FeatureToggle.fromJson(value);
        });
        
        return features;
      } else {
        return {};
      }
    } catch (e) {
      print('Error getting feature toggles: $e');
      return {};
    }
  }

  /// Check maintenance status with lightweight endpoint
  Future<bool> isMaintenanceActive() async {
    try {
      final response = await _apiService.dio.get('/api/v1/system/health/');
      return response.statusCode == 503;
    } catch (e) {
      if (e is DioException && e.response?.statusCode == 503) {
        return true;
      }
      return false;
    }
  }
}
