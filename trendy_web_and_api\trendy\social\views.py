from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q, Count
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
from blog.models import Post
from search.services import AdvancedSearchService, SmartRecommendationService
from .models import (
    UserProfile, Follow, Bookmark, BookmarkCollection,
    ReadingList, ReadingListItem, Notification, UserActivity, Report
)
from .serializers import (
    UserProfileSerializer, FollowSerializer, BookmarkSerializer,
    BookmarkCollectionSerializer, ReadingListSerializer, ReadingListItemSerializer,
    NotificationSerializer, UserActivitySerializer, UserSummarySerializer,
    SearchResultSerializer, SearchSuggestionSerializer, ReportSerializer
)

User = get_user_model()

@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request, username=None):
    """Get or update user profile"""
    try:
        if username:
            # Get another user's profile
            user = get_object_or_404(User, username=username)
        else:
            # Get current user's profile
            user = request.user

        profile, created = UserProfile.objects.get_or_create(user=user)

        if request.method == 'GET':
            serializer = UserProfileSerializer(profile, context={'request': request})
            return Response(serializer.data)

        elif request.method == 'PUT':
            # Only allow users to update their own profile
            if user != request.user:
                return Response({
                    'error': 'Permission denied'
                }, status=status.HTTP_403_FORBIDDEN)

            serializer = UserProfileSerializer(
                profile,
                data=request.data,
                partial=True,
                context={'request': request}
            )

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def follow_user(request, username):
    """Follow or unfollow a user"""
    try:
        user_to_follow = get_object_or_404(User, username=username)

        if user_to_follow == request.user:
            return Response({
                'error': 'You cannot follow yourself'
            }, status=status.HTTP_400_BAD_REQUEST)

        follow, created = Follow.objects.get_or_create(
            follower=request.user,
            following=user_to_follow
        )

        if not created:
            # Unfollow
            follow.delete()
            return Response({
                'message': f'Unfollowed {username}',
                'following': False
            })
        else:
            # Follow
            # Create notification
            Notification.objects.create(
                recipient=user_to_follow,
                sender=request.user,
                notification_type='follow',
                title='New Follower',
                message=f'{request.user.username} started following you'
            )

            return Response({
                'message': f'Now following {username}',
                'following': True
            })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def user_followers(request, username):
    """Get user's followers"""
    try:
        user = get_object_or_404(User, username=username)
        followers = Follow.objects.filter(following=user).select_related('follower').order_by('-created_at')

        # Add pagination
        from django.core.paginator import Paginator
        page = request.GET.get('page', 1)
        paginator = Paginator(followers, 20)  # 20 followers per page
        page_obj = paginator.get_page(page)

        serializer = FollowSerializer(page_obj, many=True)

        return Response({
            'results': serializer.data,
            'count': paginator.count,
            'next': page_obj.has_next(),
            'previous': page_obj.has_previous(),
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def user_following(request, username):
    """Get users that this user follows"""
    try:
        user = get_object_or_404(User, username=username)
        following = Follow.objects.filter(follower=user).select_related('following').order_by('-created_at')

        # Add pagination
        from django.core.paginator import Paginator
        page = request.GET.get('page', 1)
        paginator = Paginator(following, 20)  # 20 following per page
        page_obj = paginator.get_page(page)

        serializer = FollowSerializer(page_obj, many=True)

        return Response({
            'results': serializer.data,
            'count': paginator.count,
            'next': page_obj.has_next(),
            'previous': page_obj.has_previous(),
            'current_page': page_obj.number,
            'total_pages': paginator.num_pages
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def bookmarks(request):
    """Get user bookmarks or create new bookmark"""
    try:
        if request.method == 'GET':
            bookmarks = Bookmark.objects.filter(user=request.user).select_related('post', 'collection').order_by('-created_at')
            serializer = BookmarkSerializer(bookmarks, many=True)
            return Response(serializer.data)

        elif request.method == 'POST':
            post_id = request.data.get('post_id')
            collection_id = request.data.get('collection_id')
            notes = request.data.get('notes', '')

            if not post_id:
                return Response({
                    'error': 'post_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            post = get_object_or_404(Post, id=post_id)
            collection = None

            if collection_id:
                collection = get_object_or_404(
                    BookmarkCollection,
                    id=collection_id,
                    user=request.user
                )

            bookmark, created = Bookmark.objects.get_or_create(
                user=request.user,
                post=post,
                defaults={
                    'collection': collection,
                    'notes': notes
                }
            )

            if not created:
                return Response({
                    'error': 'Post already bookmarked'
                }, status=status.HTTP_400_BAD_REQUEST)

            serializer = BookmarkSerializer(bookmark)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def remove_bookmark(request, post_id):
    """Remove bookmark"""
    try:
        bookmark = get_object_or_404(
            Bookmark,
            user=request.user,
            post_id=post_id
        )
        bookmark.delete()

        return Response({
            'message': 'Bookmark removed successfully'
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def notifications(request):
    """Get user notifications"""
    try:
        notifications = Notification.objects.filter(
            recipient=request.user
        ).select_related('sender').order_by('-created_at')

        # Mark as seen
        notifications.filter(is_seen=False).update(is_seen=True)

        # Pagination
        limit = min(int(request.GET.get('limit', 20)), 50)
        offset = int(request.GET.get('offset', 0))
        notifications = notifications[offset:offset + limit]

        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request, notification_id):
    """Mark notification as read"""
    try:
        notification = get_object_or_404(
            Notification,
            id=notification_id,
            recipient=request.user
        )

        notification.is_read = True
        notification.read_at = timezone.now()
        notification.save()

        return Response({
            'message': 'Notification marked as read'
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def advanced_search(request):
    """Advanced search with filters"""
    try:
        query = request.GET.get('q', '').strip()

        # Extract filters from query
        clean_query, extracted_filters = AdvancedSearchService.extract_search_filters(query)

        # Additional filters from query params
        filters = {
            'category': request.GET.get('category'),
            'author': request.GET.get('author'),
            'date_from': request.GET.get('date_from'),
            'date_to': request.GET.get('date_to'),
            'reading_time_min': request.GET.get('reading_time_min'),
            'reading_time_max': request.GET.get('reading_time_max'),
            'min_likes': request.GET.get('min_likes'),
            'min_comments': request.GET.get('min_comments'),
            'has_media': request.GET.get('has_media'),
            'sort_by': request.GET.get('sort_by', 'relevance'),
        }

        # Merge extracted filters
        filters.update(extracted_filters)

        # Remove None values
        filters = {k: v for k, v in filters.items() if v is not None}

        # Search posts
        posts = AdvancedSearchService.search_posts(clean_query, filters, request.user)

        # Search users
        users = AdvancedSearchService.search_users(clean_query, filters)

        # Pagination
        limit = min(int(request.GET.get('limit', 20)), 50)
        offset = int(request.GET.get('offset', 0))

        posts_page = posts[offset:offset + limit]
        users_page = users[offset:offset + limit]

        # Serialize results
        post_data = []
        for post in posts_page:
            post_data.append({
                'id': post.id,
                'title': post.title,
                'content': post.content[:200] + '...' if len(post.content) > 200 else post.content,
                'author': post.author.username,
                'created_at': post.created_at,
                'image': post.images.first().media_source if post.images.exists() else None,
            })

        user_serializer = UserSummarySerializer(users_page, many=True)

        result = {
            'posts': post_data,
            'users': user_serializer.data,
            'total_posts': posts.count(),
            'total_users': users.count(),
            'query': query,
            'filters': filters,
        }

        serializer = SearchResultSerializer(result)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def recommendations(request):
    """Get personalized recommendations"""
    try:
        limit = min(int(request.GET.get('limit', 10)), 20)

        user = request.user if request.user.is_authenticated else None
        posts = SmartRecommendationService.get_personalized_recommendations(user, limit)

        # Simple post data without full serializer
        post_data = []
        for post in posts:
            post_data.append({
                'id': post.id,
                'title': post.title,
                'content': post.content[:200] + '...' if len(post.content) > 200 else post.content,
                'author': post.author.username,
                'created_at': post.created_at,
                'image': post.images.first().media_source if post.images.exists() else None,
            })
        return Response(post_data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([AllowAny])
def search_suggestions(request):
    """Get search suggestions"""
    try:
        query = request.GET.get('q', '').strip()
        limit = min(int(request.GET.get('limit', 5)), 10)

        if len(query) < 2:
            # Return trending searches
            suggestions = [
                {'type': 'trending', 'text': term, 'query': term}
                for term in AdvancedSearchService.get_trending_searches(limit)
            ]
        else:
            suggestions = AdvancedSearchService.get_search_suggestions(query, limit)

        serializer = SearchSuggestionSerializer(suggestions, many=True)
        return Response(serializer.data)

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def discover_users(request):
    """Get users for discovery feed"""
    try:
        user = request.user

        # Get users that the current user is not following
        following_ids = Follow.objects.filter(follower=user).values_list('following_id', flat=True)

        # Exclude self and already following users
        discover_users = User.objects.exclude(
            id__in=list(following_ids) + [user.id]
        ).filter(
            is_active=True,
            is_email_verified=True
        ).order_by('-date_joined')[:20]

        users_data = []
        for discover_user in discover_users:
            # Get user profile safely
            try:
                profile = discover_user.profile
                avatar_url = profile.avatar.url if profile.avatar else None
                bio = profile.bio or ''
            except UserProfile.DoesNotExist:
                avatar_url = None
                bio = ''

            users_data.append({
                'id': discover_user.id,
                'username': discover_user.username,
                'email': discover_user.email,
                'first_name': discover_user.first_name or '',
                'last_name': discover_user.last_name or '',
                'full_name': discover_user.get_full_name(),
                'avatar_url': avatar_url or getattr(discover_user, 'avatar_url', None) or '',
                'bio': bio or getattr(discover_user, 'bio', '') or '',
                'is_email_verified': getattr(discover_user, 'is_email_verified', False),
                'receive_email_notifications': getattr(discover_user, 'receive_email_notifications', True),
                'receive_push_notifications': getattr(discover_user, 'receive_push_notifications', True),
                'is_profile_public': getattr(discover_user, 'is_profile_public', True),
                'is_staff': getattr(discover_user, 'is_staff', False),
                'is_superuser': getattr(discover_user, 'is_superuser', False),
                'followers_count': Follow.objects.filter(following=discover_user).count(),
                'following_count': Follow.objects.filter(follower=discover_user).count(),
                'posts_count': discover_user.posts.count() if hasattr(discover_user, 'posts') else 0,
                'is_following': False,
                'date_joined': discover_user.date_joined.isoformat(),
                'updated_at': discover_user.updated_at.isoformat() if hasattr(discover_user, 'updated_at') and discover_user.updated_at else None,
            })

        return Response({
            'success': True,
            'data': users_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting discover users: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def trending_users(request):
    """Get trending users based on recent activity"""
    try:
        # Get users with most followers
        trending_users = User.objects.filter(
            is_active=True,
            is_email_verified=True
        ).annotate(
            followers_count=Count('followers')
        ).order_by('-followers_count')[:20]

        users_data = []
        for trending_user in trending_users:
            is_following = Follow.objects.filter(
                follower=request.user,
                following=trending_user
            ).exists()

            # Get user profile safely
            try:
                profile = trending_user.profile
                avatar_url = profile.avatar.url if profile.avatar else None
                bio = profile.bio or ''
            except UserProfile.DoesNotExist:
                avatar_url = None
                bio = ''

            users_data.append({
                'id': trending_user.id,
                'username': trending_user.username,
                'email': trending_user.email,
                'first_name': trending_user.first_name or '',
                'last_name': trending_user.last_name or '',
                'full_name': trending_user.get_full_name(),
                'avatar_url': avatar_url or getattr(trending_user, 'avatar_url', None) or '',
                'bio': bio or getattr(trending_user, 'bio', '') or '',
                'is_email_verified': getattr(trending_user, 'is_email_verified', False),
                'receive_email_notifications': getattr(trending_user, 'receive_email_notifications', True),
                'receive_push_notifications': getattr(trending_user, 'receive_push_notifications', True),
                'is_profile_public': getattr(trending_user, 'is_profile_public', True),
                'is_staff': getattr(trending_user, 'is_staff', False),
                'is_superuser': getattr(trending_user, 'is_superuser', False),
                'followers_count': Follow.objects.filter(following=trending_user).count(),
                'following_count': Follow.objects.filter(follower=trending_user).count(),
                'posts_count': trending_user.posts.filter(status='published').count() if hasattr(trending_user, 'posts') else 0,
                'is_following': is_following,
                'date_joined': trending_user.date_joined.isoformat(),
                'updated_at': trending_user.updated_at.isoformat() if hasattr(trending_user, 'updated_at') and trending_user.updated_at else None,
            })

        return Response({
            'success': True,
            'data': users_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting trending users: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_following_list(request):
    """Get current user's following list"""
    try:
        user = request.user
        following = Follow.objects.filter(follower=user).select_related('following').order_by('-created_at')

        users_data = []
        for follow in following:
            followed_user = follow.following

            # Get user profile safely
            try:
                profile = followed_user.profile
                avatar_url = profile.avatar.url if profile.avatar else None
                bio = profile.bio or ''
            except UserProfile.DoesNotExist:
                avatar_url = None
                bio = ''

            users_data.append({
                'id': followed_user.id,
                'username': followed_user.username,
                'email': followed_user.email,
                'first_name': followed_user.first_name or '',
                'last_name': followed_user.last_name or '',
                'full_name': followed_user.get_full_name(),
                'avatar_url': avatar_url or getattr(followed_user, 'avatar_url', None) or '',
                'bio': bio or getattr(followed_user, 'bio', '') or '',
                'is_email_verified': getattr(followed_user, 'is_email_verified', False),
                'receive_email_notifications': getattr(followed_user, 'receive_email_notifications', True),
                'receive_push_notifications': getattr(followed_user, 'receive_push_notifications', True),
                'is_profile_public': getattr(followed_user, 'is_profile_public', True),
                'is_staff': getattr(followed_user, 'is_staff', False),
                'is_superuser': getattr(followed_user, 'is_superuser', False),
                'followers_count': Follow.objects.filter(following=followed_user).count(),
                'following_count': Follow.objects.filter(follower=followed_user).count(),
                'posts_count': followed_user.posts.count() if hasattr(followed_user, 'posts') else 0,
                'is_following': True,
                'date_joined': followed_user.date_joined.isoformat(),
                'updated_at': followed_user.updated_at.isoformat() if hasattr(followed_user, 'updated_at') and followed_user.updated_at else None,
            })

        return Response({
            'success': True,
            'data': users_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting following list: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_followers_list(request):
    """Get current user's followers list"""
    try:
        user = request.user
        followers = Follow.objects.filter(following=user).select_related('follower').order_by('-created_at')

        users_data = []
        for follow in followers:
            follower = follow.follower
            is_following = Follow.objects.filter(
                follower=user,
                following=follower
            ).exists()

            # Get user profile safely
            try:
                profile = follower.profile
                avatar_url = profile.avatar.url if profile.avatar else None
                bio = profile.bio or ''
            except UserProfile.DoesNotExist:
                avatar_url = None
                bio = ''

            users_data.append({
                'id': follower.id,
                'username': follower.username,
                'email': follower.email,
                'first_name': follower.first_name or '',
                'last_name': follower.last_name or '',
                'full_name': follower.get_full_name(),
                'avatar_url': avatar_url or getattr(follower, 'avatar_url', None) or '',
                'bio': bio or getattr(follower, 'bio', '') or '',
                'is_email_verified': getattr(follower, 'is_email_verified', False),
                'receive_email_notifications': getattr(follower, 'receive_email_notifications', True),
                'receive_push_notifications': getattr(follower, 'receive_push_notifications', True),
                'is_profile_public': getattr(follower, 'is_profile_public', True),
                'is_staff': getattr(follower, 'is_staff', False),
                'is_superuser': getattr(follower, 'is_superuser', False),
                'followers_count': Follow.objects.filter(following=follower).count(),
                'following_count': Follow.objects.filter(follower=follower).count(),
                'posts_count': follower.posts.filter(status='published').count() if hasattr(follower, 'posts') else 0,
                'is_following': is_following,
                'date_joined': follower.date_joined.isoformat(),
                'updated_at': follower.updated_at.isoformat() if hasattr(follower, 'updated_at') and follower.updated_at else None,
            })

        return Response({
            'success': True,
            'data': users_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error getting followers list: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_users(request):
    """Search users by username or name"""
    try:
        query = request.GET.get('q', '').strip()

        if not query:
            return Response({
                'success': True,
                'data': []
            })

        # Search users by username or full name
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query),
            is_active=True,
            is_email_verified=True
        ).exclude(id=request.user.id)[:20]

        users_data = []
        for user in users:
            is_following = Follow.objects.filter(
                follower=request.user,
                following=user
            ).exists()

            # Get user profile safely
            try:
                profile = user.profile
                avatar_url = profile.avatar.url if profile.avatar else None
                bio = profile.bio or ''
            except UserProfile.DoesNotExist:
                avatar_url = None
                bio = ''

            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name or '',
                'last_name': user.last_name or '',
                'full_name': user.get_full_name(),
                'avatar_url': avatar_url or getattr(user, 'avatar_url', None) or '',
                'bio': bio or getattr(user, 'bio', '') or '',
                'is_email_verified': getattr(user, 'is_email_verified', False),
                'receive_email_notifications': getattr(user, 'receive_email_notifications', True),
                'receive_push_notifications': getattr(user, 'receive_push_notifications', True),
                'is_profile_public': getattr(user, 'is_profile_public', True),
                'is_staff': getattr(user, 'is_staff', False),
                'is_superuser': getattr(user, 'is_superuser', False),
                'followers_count': Follow.objects.filter(following=user).count(),
                'following_count': Follow.objects.filter(follower=user).count(),
                'posts_count': user.posts.filter(status='published').count() if hasattr(user, 'posts') else 0,
                'is_following': is_following,
                'date_joined': user.date_joined.isoformat(),
                'updated_at': user.updated_at.isoformat() if hasattr(user, 'updated_at') and user.updated_at else None,
            })

        return Response({
            'success': True,
            'data': users_data
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Error searching users: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Report functionality
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_report(request):
    """Create a new report"""
    try:
        data = request.data.copy()
        data['reporter'] = request.user.id

        # Validate that either reported_user or reported_post is provided
        if not data.get('reported_user') and not data.get('reported_post'):
            return Response(
                {'error': 'Either reported_user or reported_post must be provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prevent self-reporting
        if data.get('reported_user') and int(data.get('reported_user')) == request.user.id:
            return Response(
                {'error': 'You cannot report yourself'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = ReportSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_reports(request):
    """Get reports made by the current user"""
    try:
        reports = Report.objects.filter(reporter=request.user).order_by('-created_at')
        serializer = ReportSerializer(reports, many=True)
        return Response(serializer.data)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_reports(request):
    """Get all reports for admin review (staff only)"""
    if not request.user.is_staff:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        status_filter = request.GET.get('status', 'pending')
        reports = Report.objects.filter(status=status_filter).order_by('-created_at')
        serializer = ReportSerializer(reports, many=True)
        return Response(serializer.data)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_report_status(request, report_id):
    """Update report status (staff only)"""
    if not request.user.is_staff:
        return Response(
            {'error': 'Permission denied'},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        report = get_object_or_404(Report, id=report_id)

        new_status = request.data.get('status')
        if new_status not in ['pending', 'reviewing', 'resolved', 'dismissed']:
            return Response(
                {'error': 'Invalid status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        report.status = new_status
        report.reviewed_by = request.user
        report.reviewed_at = timezone.now()

        if request.data.get('resolution_notes'):
            report.resolution_notes = request.data.get('resolution_notes')

        report.save()

        serializer = ReportSerializer(report)
        return Response(serializer.data)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
