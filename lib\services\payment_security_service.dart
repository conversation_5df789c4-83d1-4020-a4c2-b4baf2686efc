import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for payment security, validation, and fraud prevention
class PaymentSecurityService {
  static final PaymentSecurityService _instance = PaymentSecurityService._internal();
  factory PaymentSecurityService() => _instance;
  PaymentSecurityService._internal();

  static const String _failedAttemptsKey = 'payment_failed_attempts';
  static const String _lastFailureTimeKey = 'payment_last_failure_time';
  static const String _deviceIdKey = 'payment_device_id';
  static const int _maxFailedAttempts = 5;
  static const int _lockoutDurationMinutes = 30;

  /// Validate payment request before processing
  static Future<PaymentValidationResult> validatePaymentRequest({
    required double amount,
    required String paymentMethod,
    required String userId,
    String? currency,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Check if user is locked out due to failed attempts
      final lockoutResult = await _checkLockoutStatus();
      if (!lockoutResult.isValid) {
        return lockoutResult;
      }

      // Basic amount validation
      if (amount <= 0) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Payment amount must be greater than zero',
          errorCode: 'INVALID_AMOUNT',
        );
      }

      if (amount > 10000) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Payment amount exceeds maximum limit of \$10,000',
          errorCode: 'AMOUNT_TOO_HIGH',
        );
      }

      // Validate payment method
      if (!_isValidPaymentMethod(paymentMethod)) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Invalid payment method',
          errorCode: 'INVALID_PAYMENT_METHOD',
        );
      }

      // Currency validation
      final paymentCurrency = currency ?? 'USD';
      if (!_isValidCurrency(paymentCurrency)) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Unsupported currency: $paymentCurrency',
          errorCode: 'INVALID_CURRENCY',
        );
      }

      // Check for suspicious patterns
      final suspiciousResult = await _checkSuspiciousActivity(amount, userId, metadata);
      if (!suspiciousResult.isValid) {
        return suspiciousResult;
      }

      // Rate limiting check
      final rateLimitResult = await _checkRateLimit(userId);
      if (!rateLimitResult.isValid) {
        return rateLimitResult;
      }

      return PaymentValidationResult(isValid: true);
    } catch (e) {
      if (kDebugMode) {
        print('Payment validation error: $e');
      }
      return PaymentValidationResult(
        isValid: false,
        error: 'Payment validation failed',
        errorCode: 'VALIDATION_ERROR',
      );
    }
  }

  /// Generate secure payment token
  static String generatePaymentToken(String userId, double amount, String timestamp) {
    final data = '$userId:$amount:$timestamp';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Verify payment token
  static bool verifyPaymentToken(String token, String userId, double amount, String timestamp) {
    final expectedToken = generatePaymentToken(userId, amount, timestamp);
    return token == expectedToken;
  }

  /// Record failed payment attempt
  static Future<void> recordFailedAttempt() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentAttempts = prefs.getInt(_failedAttemptsKey) ?? 0;
      await prefs.setInt(_failedAttemptsKey, currentAttempts + 1);
      await prefs.setInt(_lastFailureTimeKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      if (kDebugMode) {
        print('Error recording failed attempt: $e');
      }
    }
  }

  /// Clear failed attempts (call on successful payment)
  static Future<void> clearFailedAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_failedAttemptsKey);
      await prefs.remove(_lastFailureTimeKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing failed attempts: $e');
      }
    }
  }

  /// Get device ID for fraud detection
  static Future<String> getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString(_deviceIdKey);
      
      if (deviceId == null) {
        deviceId = _generateDeviceId();
        await prefs.setString(_deviceIdKey, deviceId);
      }
      
      return deviceId;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting device ID: $e');
      }
      return _generateDeviceId();
    }
  }

  /// Check lockout status
  static Future<PaymentValidationResult> _checkLockoutStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedAttempts = prefs.getInt(_failedAttemptsKey) ?? 0;
      final lastFailureTime = prefs.getInt(_lastFailureTimeKey) ?? 0;

      if (failedAttempts >= _maxFailedAttempts) {
        final lockoutEndTime = DateTime.fromMillisecondsSinceEpoch(lastFailureTime)
            .add(const Duration(minutes: _lockoutDurationMinutes));
        
        if (DateTime.now().isBefore(lockoutEndTime)) {
          final remainingMinutes = lockoutEndTime.difference(DateTime.now()).inMinutes;
          return PaymentValidationResult(
            isValid: false,
            error: 'Too many failed payment attempts. Please try again in $remainingMinutes minutes.',
            errorCode: 'ACCOUNT_LOCKED',
          );
        } else {
          // Lockout period has expired, clear failed attempts
          await clearFailedAttempts();
        }
      }

      return PaymentValidationResult(isValid: true);
    } catch (e) {
      return PaymentValidationResult(isValid: true); // Fail open for availability
    }
  }

  /// Check for suspicious activity patterns
  static Future<PaymentValidationResult> _checkSuspiciousActivity(
    double amount,
    String userId,
    Map<String, dynamic>? metadata,
  ) async {
    // Check for unusually high amounts
    if (amount > 1000) {
      return PaymentValidationResult(
        isValid: false,
        error: 'High-value transactions require additional verification',
        errorCode: 'HIGH_VALUE_TRANSACTION',
      );
    }

    // Check for rapid successive payments (basic rate limiting)
    // This would typically integrate with backend fraud detection
    
    return PaymentValidationResult(isValid: true);
  }

  /// Check rate limiting
  static Future<PaymentValidationResult> _checkRateLimit(String userId) async {
    // Basic rate limiting - max 10 payment attempts per hour
    // In production, this would be handled by the backend
    return PaymentValidationResult(isValid: true);
  }

  /// Validate payment method
  static bool _isValidPaymentMethod(String paymentMethod) {
    const validMethods = ['paypal', 'store_points', 'wallet', 'in_app_purchase'];
    return validMethods.contains(paymentMethod.toLowerCase());
  }

  /// Validate currency
  static bool _isValidCurrency(String currency) {
    const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
    return validCurrencies.contains(currency.toUpperCase());
  }

  /// Generate unique device ID
  static String _generateDeviceId() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }
}

/// Payment validation result
class PaymentValidationResult {
  final bool isValid;
  final String? error;
  final String? errorCode;
  final Map<String, dynamic>? metadata;

  PaymentValidationResult({
    required this.isValid,
    this.error,
    this.errorCode,
    this.metadata,
  });

  @override
  String toString() {
    return 'PaymentValidationResult(isValid: $isValid, error: $error, errorCode: $errorCode)';
  }
}

/// Payment security context for additional validation
class PaymentSecurityContext {
  final String userId;
  final String deviceId;
  final String ipAddress;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  PaymentSecurityContext({
    required this.userId,
    required this.deviceId,
    required this.ipAddress,
    required this.timestamp,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'device_id': deviceId,
      'ip_address': ipAddress,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}
