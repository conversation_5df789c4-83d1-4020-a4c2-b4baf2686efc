from django.db import transaction
from django.db import models
from django.utils import timezone
from decimal import Decimal
from ..models import UserWallet, WalletTransaction, WalletDepositRequest, WalletWithdrawalRequest, WalletSettings


class WalletService:
    """Service for handling wallet operations"""
    
    @staticmethod
    def get_or_create_wallet(user):
        """Get or create user's wallet"""
        wallet, created = UserWallet.objects.get_or_create(
            user=user,
            defaults={
                'balance': Decimal('0.00'),
                'is_active': True,
                'is_verified': user.is_email_verified if hasattr(user, 'is_email_verified') else False
            }
        )
        return wallet
    
    @staticmethod
    def create_deposit_request(user, amount, payment_method='paypal'):
        """Create a deposit request"""
        try:
            wallet = WalletService.get_or_create_wallet(user)
            settings = WalletSettings.get_settings()
            
            # Validate amount
            if amount < settings.minimum_deposit:
                return False, f"Minimum deposit amount is ${settings.minimum_deposit}"
            
            if amount > settings.maximum_deposit:
                return False, f"Maximum deposit amount is ${settings.maximum_deposit}"
            
            # Check if deposits are enabled
            if not settings.deposits_enabled:
                return False, "Deposits are currently disabled"
            
            # Create deposit request
            deposit_request = WalletDepositRequest.objects.create(
                wallet=wallet,
                amount=amount,
                payment_method=payment_method,
                status='pending'
            )
            
            return True, deposit_request
            
        except Exception as e:
            return False, f"Error creating deposit request: {str(e)}"
    
    @staticmethod
    def process_deposit(deposit_request_id, external_transaction_id=None):
        """Process a successful deposit"""
        try:
            with transaction.atomic():
                deposit_request = WalletDepositRequest.objects.select_for_update().get(
                    id=deposit_request_id
                )
                
                if deposit_request.status != 'pending':
                    return False, "Deposit request is not in pending status"
                
                wallet = deposit_request.wallet
                amount = deposit_request.amount
                
                # Calculate fees
                settings = WalletSettings.get_settings()
                fee = amount * (settings.deposit_fee_percentage / 100)
                net_amount = amount - fee
                
                # Update wallet balance
                old_balance = wallet.balance
                wallet.balance += net_amount
                wallet.save()
                
                # Create transaction record
                WalletTransaction.objects.create(
                    wallet=wallet,
                    transaction_type='credit',
                    purpose='deposit',
                    amount=net_amount,
                    balance_before=old_balance,
                    balance_after=wallet.balance,
                    status='completed',
                    description=f"Wallet deposit via {deposit_request.payment_method}",
                    payment_method=deposit_request.payment_method,
                    external_transaction_id=external_transaction_id or '',
                    reference_id=str(deposit_request.id),
                    processed_at=timezone.now(),
                    completed_at=timezone.now()
                )
                
                # Update deposit request
                deposit_request.status = 'completed'
                deposit_request.external_transaction_id = external_transaction_id or ''
                deposit_request.processed_at = timezone.now()
                deposit_request.completed_at = timezone.now()
                deposit_request.save()
                
                return True, f"Successfully deposited ${net_amount} to wallet"
                
        except WalletDepositRequest.DoesNotExist:
            return False, "Deposit request not found"
        except Exception as e:
            return False, f"Error processing deposit: {str(e)}"
    
    @staticmethod
    def create_withdrawal_request(user, amount, paypal_email):
        """Create a withdrawal request"""
        try:
            wallet = WalletService.get_or_create_wallet(user)
            settings = WalletSettings.get_settings()
            
            # Check if withdrawals are enabled
            if not settings.withdrawals_enabled:
                return False, "Withdrawals are currently disabled"
            
            # Validate amount
            if amount < settings.minimum_withdrawal:
                return False, f"Minimum withdrawal amount is ${settings.minimum_withdrawal}"
            
            if amount > settings.maximum_withdrawal:
                return False, f"Maximum withdrawal amount is ${settings.maximum_withdrawal}"
            
            # Calculate total cost including fees
            fee = max(
                amount * (settings.withdrawal_fee_percentage / 100),
                settings.withdrawal_fee_fixed
            )
            total_cost = amount + fee
            
            # Check wallet balance
            if wallet.balance < total_cost:
                return False, f"Insufficient balance. Required: ${total_cost} (${amount} + ${fee} fee), Available: ${wallet.balance}"
            
            # Check daily/monthly limits
            daily_withdrawn = wallet.get_daily_spent()
            if daily_withdrawn + amount > settings.daily_withdrawal_limit:
                return False, f"Daily withdrawal limit exceeded. Limit: ${settings.daily_withdrawal_limit}, Already withdrawn today: ${daily_withdrawn}"
            
            monthly_withdrawn = wallet.get_monthly_spent()
            if monthly_withdrawn + amount > settings.monthly_withdrawal_limit:
                return False, f"Monthly withdrawal limit exceeded. Limit: ${settings.monthly_withdrawal_limit}, Already withdrawn this month: ${monthly_withdrawn}"
            
            # Create withdrawal request
            withdrawal_request = WalletWithdrawalRequest.objects.create(
                wallet=wallet,
                amount=amount,
                paypal_email=paypal_email,
                withdrawal_method='paypal',
                status='pending'
            )
            
            return True, withdrawal_request
            
        except Exception as e:
            return False, f"Error creating withdrawal request: {str(e)}"
    
    @staticmethod
    def spend_from_wallet(user, amount, purpose, description=""):
        """Spend money from user's wallet"""
        try:
            with transaction.atomic():
                wallet = WalletService.get_or_create_wallet(user)
                
                # Check if wallet can spend
                if not wallet.can_spend(amount):
                    return False, f"Insufficient wallet balance. Required: ${amount}, Available: ${wallet.balance}"
                
                # Check daily spending limit
                daily_spent = wallet.get_daily_spent()
                if daily_spent + amount > wallet.daily_spend_limit:
                    return False, f"Daily spending limit exceeded. Limit: ${wallet.daily_spend_limit}, Already spent today: ${daily_spent}"
                
                # Deduct from wallet
                old_balance = wallet.balance
                wallet.balance -= amount
                wallet.save()
                
                # Create transaction record
                transaction_record = WalletTransaction.objects.create(
                    wallet=wallet,
                    transaction_type='debit',
                    purpose=purpose,
                    amount=amount,
                    balance_before=old_balance,
                    balance_after=wallet.balance,
                    status='completed',
                    description=description or f"Purchase: {purpose}",
                    processed_at=timezone.now(),
                    completed_at=timezone.now()
                )
                
                return True, transaction_record
                
        except Exception as e:
            return False, f"Error spending from wallet: {str(e)}"
    
    @staticmethod
    def refund_to_wallet(user, amount, original_transaction_id, description=""):
        """Refund money to user's wallet"""
        try:
            with transaction.atomic():
                wallet = WalletService.get_or_create_wallet(user)
                
                # Add to wallet
                old_balance = wallet.balance
                wallet.balance += amount
                wallet.save()
                
                # Create transaction record
                transaction_record = WalletTransaction.objects.create(
                    wallet=wallet,
                    transaction_type='credit',
                    purpose='refund',
                    amount=amount,
                    balance_before=old_balance,
                    balance_after=wallet.balance,
                    status='completed',
                    description=description or f"Refund for transaction {original_transaction_id}",
                    reference_id=str(original_transaction_id),
                    processed_at=timezone.now(),
                    completed_at=timezone.now()
                )
                
                return True, transaction_record
                
        except Exception as e:
            return False, f"Error processing refund: {str(e)}"
    
    @staticmethod
    def get_wallet_stats(user):
        """Get wallet statistics for user"""
        try:
            wallet = WalletService.get_or_create_wallet(user)
            
            # Get transaction counts
            total_transactions = wallet.transactions.count()
            credits = wallet.transactions.filter(transaction_type='credit').count()
            debits = wallet.transactions.filter(transaction_type='debit').count()
            
            # Get spending stats
            daily_spent = wallet.get_daily_spent()
            monthly_spent = wallet.get_monthly_spent()
            
            # Get total amounts
            total_deposited = wallet.transactions.filter(
                transaction_type='credit',
                purpose='deposit'
            ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')
            
            total_spent = wallet.transactions.filter(
                transaction_type='debit'
            ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')
            
            return {
                'balance': float(wallet.balance),
                'formatted_balance': wallet.formatted_balance,
                'is_active': wallet.is_active,
                'is_verified': wallet.is_verified,
                'daily_spent': float(daily_spent),
                'monthly_spent': float(monthly_spent),
                'daily_limit': float(wallet.daily_spend_limit),
                'monthly_limit': float(wallet.monthly_spend_limit),
                'total_transactions': total_transactions,
                'total_credits': credits,
                'total_debits': debits,
                'total_deposited': float(total_deposited),
                'total_spent': float(total_spent),
            }
            
        except Exception as e:
            return None
