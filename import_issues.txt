   info • The import of 'package:json_annotation/json_annotation.dart' is unnecessary because all of the used elements are also provided by the import of 'package:freezed_annotation/freezed_annotation.dart' • lib/models/api_response.dart:2:8 • unnecessary_import
   info • The import of 'package:json_annotation/json_annotation.dart' is unnecessary because all of the used elements are also provided by the import of 'package:freezed_annotation/freezed_annotation.dart' • lib/models/paginated_response.dart:2:8 • unnecessary_import
warning • Unused import: 'package:trendy/models/comment.dart' • lib/models/post.dart:3:8 • unused_import
   info • The import of 'package:json_annotation/json_annotation.dart' is unnecessary because all of the used elements are also provided by the import of 'package:freezed_annotation/freezed_annotation.dart' • lib/models/search_results.dart:2:8 • unnecessary_import
warning • Unused import: '../services/performance_service.dart' • lib/providers/gamification_provider.dart:4:8 • unused_import
warning • Unused import: '../models/paginated_response.dart' • lib/providers/notifications_provider.dart:3:8 • unused_import
warning • Unused import: '../models/post_filter.dart' • lib/providers/provider.dart:7:8 • unused_import
warning • Unused import: '../models/post.dart' • lib/screens/home_screen.dart:5:8 • unused_import
warning • Unused import: '../models/category.dart' • lib/screens/home_screen.dart:6:8 • unused_import
warning • Unused import: '../widgets/gamification/challenges_widget.dart' • lib/screens/home_screen.dart:12:8 • unused_import
warning • Unused import: '../theme/app_theme.dart' • lib/screens/onboarding_screen.dart:5:8 • unused_import
warning • Unused import: '../providers/provider.dart' • lib/screens/profile_screen.dart:5:8 • unused_import
warning • Unused import: '../models/onboarding_models.dart' • lib/screens/splash_screen.dart:4:8 • unused_import
warning • Unused import: '../theme/app_theme.dart' • lib/screens/splash_screen.dart:8:8 • unused_import
warning • Unused import: '../theme/app_theme.dart' • lib/screens/welcome_screen.dart:5:8 • unused_import
warning • Unused import: '../models/reading_session.dart' • lib/services/analytics_service.dart:5:8 • unused_import
warning • Unused import: 'dart:convert' • lib/services/api_service.dart:1:8 • unused_import
warning • Unused import: 'package:shared_preferences/shared_preferences.dart' • lib/services/api_service.dart:3:8 • unused_import
warning • Unused import: '../models/post_filter.dart' • lib/services/api_service.dart:10:8 • unused_import
warning • Unused import: '../models/post_media.dart' • lib/services/api_service.dart:11:8 • unused_import
warning • Unused import: 'package:shared_preferences/shared_preferences.dart' • lib/services/gamification_service.dart:2:8 • unused_import
warning • Unused import: 'performance_service.dart' • lib/services/gamification_service.dart:6:8 • unused_import
warning • Unused import: 'package:flutter/foundation.dart' • lib/services/platform_audio_service.dart:2:8 • unused_import
warning • Unused import: '../models/user.dart' • lib/widgets/auth_guard.dart:5:8 • unused_import
warning • Unused import: '../theme/app_theme.dart' • lib/widgets/email_verification_banner.dart:4:8 • unused_import
warning • Unused import: 'package:flutter_staggered_animations/flutter_staggered_animations.dart' • lib/widgets/enhanced_login_dialog.dart:2:8 • unused_import
   info • The import of 'package:flutter/cupertino.dart' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/material.dart' • lib/widgets/error_placeholder.dart:1:8 • unnecessary_import
warning • Unused import: '../../theme/app_theme.dart' • lib/widgets/gamification/user_level_widget.dart:5:8 • unused_import
warning • Unused import: '../../theme/app_theme.dart' • lib/widgets/interactive/interactive_content_widget.dart:5:8 • unused_import
warning • Unused import: '../screens/advertising_screen.dart' • lib/widgets/main_navigation.dart:8:8 • unused_import
warning • Unused import: '../widgets/auth_guard.dart' • lib/widgets/main_navigation.dart:11:8 • unused_import
warning • Unused import: '../widgets/progressive_image.dart' • lib/widgets/media_gallery.dart:8:8 • unused_import
warning • Unused import: 'package:trendy/models/post_media.dart' • lib/widgets/post_card.dart:6:8 • unused_import
warning • Unused import: 'package:trendy/providers/provider.dart' • lib/widgets/post_card.dart:8:8 • unused_import
warning • Unused import: 'package:trendy/models/post_media.dart' • lib/widgets/post_preview_card.dart:6:8 • unused_import
warning • Unused import: 'package:trendy/providers/provider.dart' • lib/widgets/post_preview_card.dart:7:8 • unused_import
warning • Unused import: 'package:flutter_staggered_animations/flutter_staggered_animations.dart' • lib/widgets/splash_screen.dart:2:8 • unused_import
warning • Unused import: '../../services/platform_audio_service.dart' • lib/widgets/voice/voice_comment_widget.dart:6:8 • unused_import
warning • Unused import: 'dart:io' • lib/widgets/voice/voice_recorder_widget.dart:4:8 • unused_import
warning • Unused import: 'package:trendy/models/post.dart' • test/integration_test.dart:5:8 • unused_import
warning • Unused import: 'package:trendy/models/gamification.dart' • test/integration_test.dart:6:8 • unused_import
