#!/usr/bin/env python3
"""
Test script to verify API fixes for Flutter app
"""

import requests
import json

# API Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_wallet_overview():
    """Test wallet overview endpoint"""
    print("🧪 Testing Wallet Overview API...")
    
    # First, let's try to get a token (you'll need to replace with actual credentials)
    login_data = {
        "username": "testuser",  # Replace with actual username
        "password": "testpass123"  # Replace with actual password
    }
    
    try:
        # Try to login
        login_response = requests.post(f"{API_BASE}/auth/login/", json=login_data)
        print(f"Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {'Authorization': f'Token {token}'}
            
            # Test wallet overview
            wallet_response = requests.get(f"{API_BASE}/wallet/overview/", headers=headers)
            print(f"Wallet Overview Status: {wallet_response.status_code}")
            
            if wallet_response.status_code == 200:
                data = wallet_response.json()
                print("✅ Wallet Overview API working!")
                print(f"Response structure: {list(data.keys())}")
                
                if 'data' in data:
                    wallet_data = data['data']
                    print(f"Wallet data keys: {list(wallet_data.keys())}")
                    
                    if 'wallet' in wallet_data:
                        wallet_info = wallet_data['wallet']
                        print(f"Wallet info keys: {list(wallet_info.keys())}")
                        
                        # Check if the field names are correct (snake_case)
                        expected_fields = [
                            'balance', 'formatted_balance', 'is_active', 'is_verified',
                            'daily_spent', 'monthly_spent', 'daily_limit', 'monthly_limit',
                            'total_transactions', 'total_credits', 'total_debits',
                            'total_deposited', 'total_spent'
                        ]
                        
                        missing_fields = [field for field in expected_fields if field not in wallet_info]
                        if missing_fields:
                            print(f"⚠️ Missing fields: {missing_fields}")
                        else:
                            print("✅ All expected fields present with correct naming!")
                            
                return True
            else:
                print(f"❌ Wallet Overview failed: {wallet_response.text}")
                return False
        else:
            print(f"❌ Login failed: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing wallet overview: {e}")
        return False

def test_trending_users():
    """Test trending users endpoint"""
    print("\n🧪 Testing Trending Users API...")
    
    login_data = {
        "username": "testuser",  # Replace with actual username
        "password": "testpass123"  # Replace with actual password
    }
    
    try:
        # Try to login
        login_response = requests.post(f"{API_BASE}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {'Authorization': f'Token {token}'}
            
            # Test trending users
            trending_response = requests.get(f"{API_BASE}/social/trending-users/", headers=headers)
            print(f"Trending Users Status: {trending_response.status_code}")
            
            if trending_response.status_code == 200:
                data = trending_response.json()
                print("✅ Trending Users API working!")
                print(f"Response structure: {list(data.keys())}")
                
                if 'data' in data:
                    users_data = data['data']
                    print(f"Number of trending users: {len(users_data)}")
                    
                    if users_data:
                        first_user = users_data[0]
                        print(f"User data keys: {list(first_user.keys())}")
                        
                return True
            else:
                print(f"❌ Trending Users failed: {trending_response.text}")
                return False
        else:
            print(f"❌ Login failed for trending users test")
            return False
            
    except Exception as e:
        print(f"❌ Error testing trending users: {e}")
        return False

def test_blockchain_wallet():
    """Test blockchain wallet endpoint"""
    print("\n🧪 Testing Blockchain Wallet API...")
    
    login_data = {
        "username": "testuser",  # Replace with actual username
        "password": "testpass123"  # Replace with actual password
    }
    
    try:
        # Try to login
        login_response = requests.post(f"{API_BASE}/auth/login/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {'Authorization': f'Token {token}'}
            
            # Test blockchain wallet
            blockchain_response = requests.get(f"{API_BASE}/blockchain/wallet/", headers=headers)
            print(f"Blockchain Wallet Status: {blockchain_response.status_code}")
            
            if blockchain_response.status_code == 200:
                data = blockchain_response.json()
                print("✅ Blockchain Wallet API working!")
                print(f"Response structure: {list(data.keys())}")
                return True
            else:
                print(f"❌ Blockchain Wallet failed: {blockchain_response.text}")
                return False
        else:
            print(f"❌ Login failed for blockchain test")
            return False
            
    except Exception as e:
        print(f"❌ Error testing blockchain wallet: {e}")
        return False

def create_test_user():
    """Create a test user for API testing"""
    print("🔧 Creating test user...")
    
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/register/", json=user_data)
        print(f"User creation status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✅ Test user created successfully!")
            return True
        elif response.status_code == 400:
            # User might already exist
            print("ℹ️ Test user might already exist")
            return True
        else:
            print(f"❌ Failed to create test user: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        return False

def main():
    """Run all API tests"""
    print("🚀 Starting API Fix Verification Tests...\n")
    
    # Create test user first
    create_test_user()
    
    # Test APIs
    results = []
    results.append(test_wallet_overview())
    results.append(test_trending_users())
    results.append(test_blockchain_wallet())
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All API fixes are working correctly!")
        print("The Flutter app should now work without the parsing errors.")
    else:
        print("\n⚠️ Some APIs still have issues. Check the error messages above.")
    
    print("\n💡 Next steps:")
    print("1. Run the Flutter app to test the fixes")
    print("2. Check the Flutter console for any remaining errors")
    print("3. Test the blockchain features in the app")

if __name__ == "__main__":
    main()
