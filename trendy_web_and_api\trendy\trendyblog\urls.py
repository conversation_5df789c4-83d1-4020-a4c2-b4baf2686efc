"""
URL configuration for trendyblog project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from django.contrib.auth.views import LogoutView
from accounts import views as accounts_views



class CustomLogoutView(LogoutView):
    next_page = 'home'  # Redirect to home page after logout

    def get(self, request, *args, **kwargs):
        return self.post(request, *args, **kwargs)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('blog.urls')),
    # API URLs
    path('api/v1/accounts/', include('accounts.urls')),
    # User profile endpoints (alias for user endpoints without namespace conflict)
    path('api/v1/users/<str:username>/', accounts_views.get_user_profile, name='api_user_profile'),
    path('api/v1/users/<str:username>/posts/', accounts_views.get_user_posts, name='api_user_posts'),
    path('api/v1/gamification/', include('gamification.urls')),
    path('api/v1/monetization/', include('monetization.urls')),
    path('api/v1/payments/', include('payments.urls')),
    path('api/v1/wallet/', include('wallet.urls')),
    path('api/v1/blockchain/', include('blockchain.urls')),
    path('api/v1/social/', include('social.urls')),
    path('api/v1/advertising/', include('advertising.urls')),
    path('api/v1/ai-writing/', include('ai_writing.urls')),
    path('api/v1/interactive/', include('interactive.urls')),
    path('api/v1/analytics/', include('analytics.urls')),
    path('api/v1/regional/', include('regional.urls')),
    path('api/v1/messaging/', include('messaging.urls')),
    path('', include('core.urls')),  # Core maintenance system
    # Web Authentication URLs
    path('login/', auth_views.LoginView.as_view(template_name='blog/login.html'), name='login'),
    # path('logout/', auth_views.LogoutView.as_view(next_page='home'), name='logout'),
    path('logout/', CustomLogoutView.as_view(), name='logout'),

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
