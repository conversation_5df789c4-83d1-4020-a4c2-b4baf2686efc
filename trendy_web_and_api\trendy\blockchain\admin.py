"""
Django admin configuration for blockchain models
"""

from django.contrib import admin
from django.utils.html import format_html
from .models import (
    BlockchainNetwork, SmartContract, UserWalletAddress,
    BlockchainTransaction, TokenBalance, NFTAsset,
    StakingPool, UserStake, MarketplaceListing
)

@admin.register(BlockchainNetwork)
class BlockchainNetworkAdmin(admin.ModelAdmin):
    list_display = ['name', 'chain_id', 'native_token', 'is_active', 'gas_price_gwei']
    list_filter = ['is_active', 'native_token']
    search_fields = ['name', 'chain_id']
    readonly_fields = ['chain_id']

@admin.register(SmartContract)
class SmartContractAdmin(admin.ModelAdmin):
    list_display = ['name', 'contract_type', 'network', 'address', 'is_active', 'deployed_at']
    list_filter = ['contract_type', 'network', 'is_active']
    search_fields = ['name', 'address']
    readonly_fields = ['deployed_at']
    
    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing existing object
            return self.readonly_fields + ['address', 'abi']
        return self.readonly_fields

@admin.register(UserWalletAddress)
class UserWalletAddressAdmin(admin.ModelAdmin):
    list_display = ['user', 'network', 'address_short', 'is_primary', 'created_at']
    list_filter = ['network', 'is_primary', 'created_at']
    search_fields = ['user__username', 'user__email', 'address']
    readonly_fields = ['address', 'private_key_encrypted', 'created_at']
    
    def address_short(self, obj):
        return f"{obj.address[:10]}...{obj.address[-8:]}"
    address_short.short_description = 'Address'

@admin.register(BlockchainTransaction)
class BlockchainTransactionAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'transaction_type', 'amount', 'status', 
        'network', 'hash_short', 'created_at'
    ]
    list_filter = [
        'transaction_type', 'status', 'network', 'created_at'
    ]
    search_fields = [
        'user__username', 'user__email', 'blockchain_hash', 
        'from_address', 'to_address'
    ]
    readonly_fields = [
        'blockchain_hash', 'block_number', 'block_hash', 
        'gas_used', 'confirmations', 'confirmed_at'
    ]
    date_hierarchy = 'created_at'
    
    def hash_short(self, obj):
        if obj.blockchain_hash:
            return format_html(
                '<a href="{}/tx/{}" target="_blank">{}</a>',
                obj.network.explorer_url,
                obj.blockchain_hash,
                f"{obj.blockchain_hash[:10]}...{obj.blockchain_hash[-8:]}"
            )
        return '-'
    hash_short.short_description = 'Transaction Hash'
    hash_short.allow_tags = True

@admin.register(TokenBalance)
class TokenBalanceAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'contract', 'balance', 'staked_balance', 
        'total_balance_display', 'last_updated'
    ]
    list_filter = ['network', 'contract', 'last_updated']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['last_updated']
    
    def total_balance_display(self, obj):
        return f"{obj.total_balance:.6f}"
    total_balance_display.short_description = 'Total Balance'

@admin.register(NFTAsset)
class NFTAssetAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'user', 'token_id', 'rarity_display', 
        'contract', 'minted_at'
    ]
    list_filter = ['rarity', 'network', 'contract', 'minted_at']
    search_fields = ['name', 'user__username', 'token_id', 'description']
    readonly_fields = ['token_id', 'minted_at', 'last_transferred']
    date_hierarchy = 'minted_at'
    
    def rarity_display(self, obj):
        colors = {
            1: '#6c757d',  # Common - Gray
            2: '#28a745',  # Uncommon - Green
            3: '#007bff',  # Rare - Blue
            4: '#6f42c1',  # Epic - Purple
            5: '#ffc107',  # Legendary - Gold
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.rarity, '#000'),
            obj.get_rarity_display()
        )
    rarity_display.short_description = 'Rarity'

@admin.register(StakingPool)
class StakingPoolAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'apy_percentage', 'total_staked', 'active_stakers',
        'is_active', 'start_date', 'end_date'
    ]
    list_filter = ['is_active', 'network', 'start_date']
    search_fields = ['name', 'description']
    readonly_fields = ['total_staked', 'total_rewards_distributed', 'active_stakers', 'created_at']
    date_hierarchy = 'start_date'

@admin.register(UserStake)
class UserStakeAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'pool', 'amount_staked', 'rewards_earned',
        'pending_rewards_display', 'is_active', 'staked_at'
    ]
    list_filter = ['pool', 'is_active', 'staked_at']
    search_fields = ['user__username', 'user__email', 'pool__name']
    readonly_fields = ['rewards_earned', 'staked_at', 'unstaked_at']
    date_hierarchy = 'staked_at'
    
    def pending_rewards_display(self, obj):
        pending = obj.calculate_pending_rewards()
        return f"{pending:.6f}"
    pending_rewards_display.short_description = 'Pending Rewards'

@admin.register(MarketplaceListing)
class MarketplaceListingAdmin(admin.ModelAdmin):
    list_display = [
        'nft_asset', 'seller', 'listing_type', 'price',
        'currency_contract', 'status', 'created_at'
    ]
    list_filter = ['listing_type', 'status', 'currency_contract', 'created_at']
    search_fields = [
        'seller__username', 'nft_asset__name', 'nft_asset__token_id'
    ]
    readonly_fields = ['created_at', 'sold_at']
    date_hierarchy = 'created_at'

# Custom admin site configuration
admin.site.site_header = "Trendy Blockchain Administration"
admin.site.site_title = "Trendy Blockchain Admin"
admin.site.index_title = "Welcome to Trendy Blockchain Administration"
