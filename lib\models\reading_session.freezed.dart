// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reading_session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReadingSession _$ReadingSessionFromJson(Map<String, dynamic> json) {
  return _ReadingSession.fromJson(json);
}

/// @nodoc
mixin _$ReadingSession {
  int get id => throw _privateConstructorUsedError;
  User get user => throw _privateConstructorUsedError;
  Post get post => throw _privateConstructorUsedError;
  @JsonKey(name: 'start_time')
  DateTime get startTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'end_time')
  DateTime? get endTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'progress_percentage')
  double get progressPercentage => throw _privateConstructorUsedError;
  @JsonKey(name: 'reading_speed_wpm')
  int? get readingSpeedWpm => throw _privateConstructorUsedError;
  @JsonKey(name: 'session_duration')
  int get sessionDuration => throw _privateConstructorUsedError;
  @JsonKey(name: 'scroll_depth')
  double get scrollDepth => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_completed')
  bool get isCompleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'device_type')
  String get deviceType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReadingSessionCopyWith<ReadingSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReadingSessionCopyWith<$Res> {
  factory $ReadingSessionCopyWith(
          ReadingSession value, $Res Function(ReadingSession) then) =
      _$ReadingSessionCopyWithImpl<$Res, ReadingSession>;
  @useResult
  $Res call(
      {int id,
      User user,
      Post post,
      @JsonKey(name: 'start_time') DateTime startTime,
      @JsonKey(name: 'end_time') DateTime? endTime,
      @JsonKey(name: 'progress_percentage') double progressPercentage,
      @JsonKey(name: 'reading_speed_wpm') int? readingSpeedWpm,
      @JsonKey(name: 'session_duration') int sessionDuration,
      @JsonKey(name: 'scroll_depth') double scrollDepth,
      @JsonKey(name: 'is_completed') bool isCompleted,
      @JsonKey(name: 'device_type') String deviceType});

  $UserCopyWith<$Res> get user;
  $PostCopyWith<$Res> get post;
}

/// @nodoc
class _$ReadingSessionCopyWithImpl<$Res, $Val extends ReadingSession>
    implements $ReadingSessionCopyWith<$Res> {
  _$ReadingSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? post = null,
    Object? startTime = null,
    Object? endTime = freezed,
    Object? progressPercentage = null,
    Object? readingSpeedWpm = freezed,
    Object? sessionDuration = null,
    Object? scrollDepth = null,
    Object? isCompleted = null,
    Object? deviceType = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      post: null == post
          ? _value.post
          : post // ignore: cast_nullable_to_non_nullable
              as Post,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      readingSpeedWpm: freezed == readingSpeedWpm
          ? _value.readingSpeedWpm
          : readingSpeedWpm // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionDuration: null == sessionDuration
          ? _value.sessionDuration
          : sessionDuration // ignore: cast_nullable_to_non_nullable
              as int,
      scrollDepth: null == scrollDepth
          ? _value.scrollDepth
          : scrollDepth // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $PostCopyWith<$Res> get post {
    return $PostCopyWith<$Res>(_value.post, (value) {
      return _then(_value.copyWith(post: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReadingSessionImplCopyWith<$Res>
    implements $ReadingSessionCopyWith<$Res> {
  factory _$$ReadingSessionImplCopyWith(_$ReadingSessionImpl value,
          $Res Function(_$ReadingSessionImpl) then) =
      __$$ReadingSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      User user,
      Post post,
      @JsonKey(name: 'start_time') DateTime startTime,
      @JsonKey(name: 'end_time') DateTime? endTime,
      @JsonKey(name: 'progress_percentage') double progressPercentage,
      @JsonKey(name: 'reading_speed_wpm') int? readingSpeedWpm,
      @JsonKey(name: 'session_duration') int sessionDuration,
      @JsonKey(name: 'scroll_depth') double scrollDepth,
      @JsonKey(name: 'is_completed') bool isCompleted,
      @JsonKey(name: 'device_type') String deviceType});

  @override
  $UserCopyWith<$Res> get user;
  @override
  $PostCopyWith<$Res> get post;
}

/// @nodoc
class __$$ReadingSessionImplCopyWithImpl<$Res>
    extends _$ReadingSessionCopyWithImpl<$Res, _$ReadingSessionImpl>
    implements _$$ReadingSessionImplCopyWith<$Res> {
  __$$ReadingSessionImplCopyWithImpl(
      _$ReadingSessionImpl _value, $Res Function(_$ReadingSessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user = null,
    Object? post = null,
    Object? startTime = null,
    Object? endTime = freezed,
    Object? progressPercentage = null,
    Object? readingSpeedWpm = freezed,
    Object? sessionDuration = null,
    Object? scrollDepth = null,
    Object? isCompleted = null,
    Object? deviceType = null,
  }) {
    return _then(_$ReadingSessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      post: null == post
          ? _value.post
          : post // ignore: cast_nullable_to_non_nullable
              as Post,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      progressPercentage: null == progressPercentage
          ? _value.progressPercentage
          : progressPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      readingSpeedWpm: freezed == readingSpeedWpm
          ? _value.readingSpeedWpm
          : readingSpeedWpm // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionDuration: null == sessionDuration
          ? _value.sessionDuration
          : sessionDuration // ignore: cast_nullable_to_non_nullable
              as int,
      scrollDepth: null == scrollDepth
          ? _value.scrollDepth
          : scrollDepth // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      deviceType: null == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReadingSessionImpl implements _ReadingSession {
  const _$ReadingSessionImpl(
      {required this.id,
      required this.user,
      required this.post,
      @JsonKey(name: 'start_time') required this.startTime,
      @JsonKey(name: 'end_time') this.endTime,
      @JsonKey(name: 'progress_percentage') this.progressPercentage = 0.0,
      @JsonKey(name: 'reading_speed_wpm') this.readingSpeedWpm,
      @JsonKey(name: 'session_duration') this.sessionDuration = 0,
      @JsonKey(name: 'scroll_depth') this.scrollDepth = 0.0,
      @JsonKey(name: 'is_completed') this.isCompleted = false,
      @JsonKey(name: 'device_type') this.deviceType = 'unknown'});

  factory _$ReadingSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReadingSessionImplFromJson(json);

  @override
  final int id;
  @override
  final User user;
  @override
  final Post post;
  @override
  @JsonKey(name: 'start_time')
  final DateTime startTime;
  @override
  @JsonKey(name: 'end_time')
  final DateTime? endTime;
  @override
  @JsonKey(name: 'progress_percentage')
  final double progressPercentage;
  @override
  @JsonKey(name: 'reading_speed_wpm')
  final int? readingSpeedWpm;
  @override
  @JsonKey(name: 'session_duration')
  final int sessionDuration;
  @override
  @JsonKey(name: 'scroll_depth')
  final double scrollDepth;
  @override
  @JsonKey(name: 'is_completed')
  final bool isCompleted;
  @override
  @JsonKey(name: 'device_type')
  final String deviceType;

  @override
  String toString() {
    return 'ReadingSession(id: $id, user: $user, post: $post, startTime: $startTime, endTime: $endTime, progressPercentage: $progressPercentage, readingSpeedWpm: $readingSpeedWpm, sessionDuration: $sessionDuration, scrollDepth: $scrollDepth, isCompleted: $isCompleted, deviceType: $deviceType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReadingSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.post, post) || other.post == post) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.progressPercentage, progressPercentage) ||
                other.progressPercentage == progressPercentage) &&
            (identical(other.readingSpeedWpm, readingSpeedWpm) ||
                other.readingSpeedWpm == readingSpeedWpm) &&
            (identical(other.sessionDuration, sessionDuration) ||
                other.sessionDuration == sessionDuration) &&
            (identical(other.scrollDepth, scrollDepth) ||
                other.scrollDepth == scrollDepth) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user,
      post,
      startTime,
      endTime,
      progressPercentage,
      readingSpeedWpm,
      sessionDuration,
      scrollDepth,
      isCompleted,
      deviceType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReadingSessionImplCopyWith<_$ReadingSessionImpl> get copyWith =>
      __$$ReadingSessionImplCopyWithImpl<_$ReadingSessionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReadingSessionImplToJson(
      this,
    );
  }
}

abstract class _ReadingSession implements ReadingSession {
  const factory _ReadingSession(
          {required final int id,
          required final User user,
          required final Post post,
          @JsonKey(name: 'start_time') required final DateTime startTime,
          @JsonKey(name: 'end_time') final DateTime? endTime,
          @JsonKey(name: 'progress_percentage') final double progressPercentage,
          @JsonKey(name: 'reading_speed_wpm') final int? readingSpeedWpm,
          @JsonKey(name: 'session_duration') final int sessionDuration,
          @JsonKey(name: 'scroll_depth') final double scrollDepth,
          @JsonKey(name: 'is_completed') final bool isCompleted,
          @JsonKey(name: 'device_type') final String deviceType}) =
      _$ReadingSessionImpl;

  factory _ReadingSession.fromJson(Map<String, dynamic> json) =
      _$ReadingSessionImpl.fromJson;

  @override
  int get id;
  @override
  User get user;
  @override
  Post get post;
  @override
  @JsonKey(name: 'start_time')
  DateTime get startTime;
  @override
  @JsonKey(name: 'end_time')
  DateTime? get endTime;
  @override
  @JsonKey(name: 'progress_percentage')
  double get progressPercentage;
  @override
  @JsonKey(name: 'reading_speed_wpm')
  int? get readingSpeedWpm;
  @override
  @JsonKey(name: 'session_duration')
  int get sessionDuration;
  @override
  @JsonKey(name: 'scroll_depth')
  double get scrollDepth;
  @override
  @JsonKey(name: 'is_completed')
  bool get isCompleted;
  @override
  @JsonKey(name: 'device_type')
  String get deviceType;
  @override
  @JsonKey(ignore: true)
  _$$ReadingSessionImplCopyWith<_$ReadingSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
