from django.core.management.base import BaseCommand
from django.conf import settings
from ai_writing.models import AIConfiguration


class Command(BaseCommand):
    help = 'Setup default AI configurations for the Trendy application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--provider',
            type=str,
            default='openai',
            help='AI provider (openai, anthropic, google, custom)'
        )
        parser.add_argument(
            '--model',
            type=str,
            default='gpt-3.5-turbo',
            help='AI model name'
        )
        parser.add_argument(
            '--api-key',
            type=str,
            help='API key for the AI provider'
        )
        parser.add_argument(
            '--name',
            type=str,
            default='Default AI Configuration',
            help='Configuration name'
        )

    def handle(self, *args, **options):
        provider = options['provider']
        model = options['model']
        api_key = options['api_key']
        name = options['name']

        # Try to get API key from environment if not provided
        if not api_key:
            if provider == 'openai':
                api_key = getattr(settings, 'OPENAI_API_KEY', '')
            elif provider == 'anthropic':
                api_key = getattr(settings, 'ANTHROPIC_API_KEY', '')

        if not api_key:
            self.stdout.write(
                self.style.WARNING(
                    f'No API key provided for {provider}. You can add it later in Django admin.'
                )
            )

        # Create or update the configuration
        config, created = AIConfiguration.objects.get_or_create(
            name=name,
            defaults={
                'provider': provider,
                'model_name': model,
                'api_key': api_key or '',
                'is_default': True,
                'is_active': True,
                'max_tokens': 1000,
                'temperature': 0.7,
                'top_p': 1.0,
                'frequency_penalty': 0.0,
                'presence_penalty': 0.0,
                'requests_per_minute': 60,
                'requests_per_day': 1000,
                'enable_content_generation': True,
                'enable_grammar_improvement': True,
                'enable_seo_suggestions': True,
                'enable_text_completion': True,
                'enable_readability_analysis': True,
            }
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Created AI configuration: {config.name}'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️  AI configuration already exists: {config.name}'
                )
            )

        # Create additional example configurations
        self._create_example_configs()

        self.stdout.write(
            self.style.SUCCESS(
                '\n🎯 AI Configuration Setup Complete!\n'
                'You can now:\n'
                '1. Go to Django Admin → AI Writing → AI Configurations\n'
                '2. Configure your API keys and model parameters\n'
                '3. Enable/disable specific AI features\n'
                '4. Set rate limits and usage controls\n'
            )
        )

    def _create_example_configs(self):
        """Create example configurations for different providers"""
        
        example_configs = [
            {
                'name': 'OpenAI GPT-4',
                'provider': 'openai',
                'model_name': 'gpt-4',
                'is_active': False,
                'max_tokens': 2000,
                'temperature': 0.6,
            },
            {
                'name': 'Anthropic Claude',
                'provider': 'anthropic',
                'model_name': 'claude-3-sonnet',
                'is_active': False,
                'max_tokens': 1500,
                'temperature': 0.7,
            },
            {
                'name': 'Google Gemini',
                'provider': 'google',
                'model_name': 'gemini-pro',
                'is_active': False,
                'max_tokens': 1000,
                'temperature': 0.8,
            },
            {
                'name': 'Custom Local Model',
                'provider': 'custom',
                'model_name': 'custom-model',
                'api_base_url': 'http://localhost:8080/api/generate',
                'is_active': False,
                'max_tokens': 800,
                'temperature': 0.7,
            }
        ]

        for config_data in example_configs:
            config, created = AIConfiguration.objects.get_or_create(
                name=config_data['name'],
                defaults=config_data
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'  ➕ Created example config: {config.name}')
                )
