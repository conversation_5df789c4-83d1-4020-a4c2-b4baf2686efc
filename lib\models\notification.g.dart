// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppNotificationImpl _$$AppNotificationImplFromJson(
        Map<String, dynamic> json) =>
    _$AppNotificationImpl(
      id: (json['id'] as num).toInt(),
      notificationType: json['notification_type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      postId: (json['post_id'] as num?)?.toInt(),
      commentId: (json['comment_id'] as num?)?.toInt(),
      isRead: json['is_read'] as bool? ?? false,
      createdAt: json['created_at'] as String,
      readAt: json['read_at'] as String?,
      senderName: json['sender_name'] as String?,
      senderAvatar: json['sender_avatar'] as String?,
    );

Map<String, dynamic> _$$AppNotificationImplToJson(
        _$AppNotificationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'notification_type': instance.notificationType,
      'title': instance.title,
      'message': instance.message,
      'post_id': instance.postId,
      'comment_id': instance.commentId,
      'is_read': instance.isRead,
      'created_at': instance.createdAt,
      'read_at': instance.readAt,
      'sender_name': instance.senderName,
      'sender_avatar': instance.senderAvatar,
    };
