from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.core.cache import cache
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status

from .models import PaymentTransaction, PaymentSettings, UserPayPalProfile
from .security import PaymentSecurityService
from .views import create_payment_order, capture_payment_order


class PaymentSecurityServiceTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.factory = RequestFactory()
        cache.clear()

    def test_validate_payment_request_success(self):
        """Test successful payment validation"""
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('10.00'),
            payment_method='paypal',
            currency='USD'
        )
        
        self.assertTrue(is_valid)
        self.assertIsNone(error_message)
        self.assertIsNone(error_code)

    def test_validate_payment_request_invalid_amount(self):
        """Test payment validation with invalid amount"""
        # Test negative amount
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('-5.00'),
            payment_method='paypal'
        )
        
        self.assertFalse(is_valid)
        self.assertIn('at least', error_message)
        self.assertEqual(error_code, 'AMOUNT_TOO_LOW')

        # Test excessive amount
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('15000.00'),
            payment_method='paypal'
        )
        
        self.assertFalse(is_valid)
        self.assertIn('exceeds maximum', error_message)
        self.assertEqual(error_code, 'AMOUNT_TOO_HIGH')

    def test_validate_payment_request_invalid_payment_method(self):
        """Test payment validation with invalid payment method"""
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('10.00'),
            payment_method='invalid_method'
        )
        
        self.assertFalse(is_valid)
        self.assertIn('Invalid payment method', error_message)
        self.assertEqual(error_code, 'INVALID_PAYMENT_METHOD')

    def test_validate_payment_request_invalid_currency(self):
        """Test payment validation with invalid currency"""
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('10.00'),
            payment_method='paypal',
            currency='INVALID'
        )
        
        self.assertFalse(is_valid)
        self.assertIn('Unsupported currency', error_message)
        self.assertEqual(error_code, 'INVALID_CURRENCY')

    def test_user_lockout_mechanism(self):
        """Test user lockout after failed attempts"""
        # Record multiple failed attempts
        for _ in range(PaymentSecurityService.MAX_FAILED_ATTEMPTS):
            PaymentSecurityService.record_failed_attempt(self.user)

        # Next validation should fail due to lockout
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('10.00'),
            payment_method='paypal'
        )
        
        self.assertFalse(is_valid)
        self.assertIn('Too many failed', error_message)
        self.assertEqual(error_code, 'ACCOUNT_LOCKED')

    def test_clear_failed_attempts(self):
        """Test clearing failed attempts"""
        # Record failed attempts
        for _ in range(3):
            PaymentSecurityService.record_failed_attempt(self.user)

        # Clear attempts
        PaymentSecurityService.clear_failed_attempts(self.user)

        # Validation should now succeed
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('10.00'),
            payment_method='paypal'
        )
        
        self.assertTrue(is_valid)

    def test_generate_and_verify_payment_token(self):
        """Test payment token generation and verification"""
        user_id = self.user.id
        amount = Decimal('10.00')
        timestamp = '2024-01-01T00:00:00Z'

        # Generate token
        token = PaymentSecurityService.generate_payment_token(user_id, amount, timestamp)
        self.assertIsNotNone(token)
        self.assertIsInstance(token, str)

        # Verify token
        is_valid = PaymentSecurityService.verify_payment_token(token, user_id, amount, timestamp)
        self.assertTrue(is_valid)

        # Verify with wrong data should fail
        is_valid = PaymentSecurityService.verify_payment_token(token, user_id, Decimal('20.00'), timestamp)
        self.assertFalse(is_valid)

    def test_fraud_detection_rapid_payments(self):
        """Test fraud detection for rapid successive payments"""
        # Create multiple recent high-value transactions
        for i in range(3):
            PaymentTransaction.objects.create(
                user=self.user,
                transaction_type='incoming',
                payment_purpose='test',
                amount=Decimal('150.00'),
                description='Test transaction',
                reference_id=f'test_{i}',
                status='completed',
                created_at=timezone.now()
            )

        # Next validation should detect suspicious activity
        is_valid, error_message, error_code = PaymentSecurityService.validate_payment_request(
            user=self.user,
            amount=Decimal('200.00'),
            payment_method='paypal'
        )
        
        self.assertFalse(is_valid)
        self.assertIn('Suspicious payment pattern', error_message)
        self.assertEqual(error_code, 'SUSPICIOUS_ACTIVITY')

    def test_get_client_ip(self):
        """Test client IP extraction"""
        # Test with X-Forwarded-For header
        request = self.factory.post('/', HTTP_X_FORWARDED_FOR='***********, ********')
        ip = PaymentSecurityService.get_client_ip(request)
        self.assertEqual(ip, '***********')

        # Test with REMOTE_ADDR
        request = self.factory.post('/', REMOTE_ADDR='***********')
        ip = PaymentSecurityService.get_client_ip(request)
        self.assertEqual(ip, '***********')

        # Test with no IP information
        request = self.factory.post('/')
        ip = PaymentSecurityService.get_client_ip(request)
        self.assertEqual(ip, 'unknown')


class PaymentAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create payment settings
        PaymentSettings.objects.create(
            payments_enabled=True,
            payouts_enabled=True,
            minimum_payment=Decimal('1.00'),
            maximum_payment=Decimal('1000.00'),
            minimum_payout=Decimal('5.00')
        )

    def test_create_payment_order_success(self):
        """Test successful payment order creation"""
        self.client.force_authenticate(user=self.user)
        
        data = {
            'amount': 10.00,
            'purpose': 'premium_subscription',
            'description': 'Premium subscription payment'
        }

        with patch('payments.paypal_service.PayPalService') as mock_paypal:
            mock_paypal.return_value.create_payment_order.return_value = {
                'success': True,
                'order_id': 'test_order_123',
                'approval_url': 'https://paypal.com/approve',
                'transaction_id': 1
            }

            response = self.client.post('/api/v1/payments/create-order/', data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('order_id', response.data)

    def test_create_payment_order_invalid_amount(self):
        """Test payment order creation with invalid amount"""
        self.client.force_authenticate(user=self.user)
        
        data = {
            'amount': -5.00,
            'purpose': 'premium_subscription',
            'description': 'Invalid payment'
        }

        response = self.client.post('/api/v1/payments/create-order/', data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('error', response.data)

    def test_create_payment_order_security_validation(self):
        """Test payment order creation with security validation"""
        self.client.force_authenticate(user=self.user)
        
        # First, trigger lockout by recording failed attempts
        for _ in range(PaymentSecurityService.MAX_FAILED_ATTEMPTS):
            PaymentSecurityService.record_failed_attempt(self.user)

        data = {
            'amount': 10.00,
            'purpose': 'premium_subscription',
            'description': 'Test payment'
        }

        response = self.client.post('/api/v1/payments/create-order/', data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Too many failed', response.data['error'])
        self.assertEqual(response.data['error_code'], 'ACCOUNT_LOCKED')

    def test_capture_payment_order_success(self):
        """Test successful payment capture"""
        self.client.force_authenticate(user=self.user)
        
        # Create a test transaction
        transaction = PaymentTransaction.objects.create(
            user=self.user,
            transaction_type='incoming',
            payment_purpose='premium_subscription',
            amount=Decimal('10.00'),
            description='Test payment',
            reference_id='test_ref_123',
            paypal_payment_id='test_order_123',
            status='pending'
        )

        data = {
            'order_id': 'test_order_123'
        }

        with patch('payments.paypal_service.PayPalService') as mock_paypal:
            mock_paypal.return_value.capture_payment_order.return_value = {
                'success': True,
                'capture_id': 'capture_123',
                'transaction_id': transaction.id
            }

            response = self.client.post('/api/v1/payments/capture-order/', data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('capture_id', response.data)

    def test_capture_payment_order_missing_order_id(self):
        """Test payment capture with missing order ID"""
        self.client.force_authenticate(user=self.user)
        
        data = {}

        response = self.client.post('/api/v1/payments/capture-order/', data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('Order ID is required', response.data['error'])

    def test_payment_status_endpoint(self):
        """Test payment status retrieval"""
        self.client.force_authenticate(user=self.user)
        
        # Create a test transaction
        transaction = PaymentTransaction.objects.create(
            user=self.user,
            transaction_type='incoming',
            payment_purpose='premium_subscription',
            amount=Decimal('10.00'),
            description='Test payment',
            reference_id='test_ref_123',
            status='completed',
            completed_at=timezone.now()
        )

        response = self.client.get(f'/api/v1/payments/status/{transaction.id}/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['transaction']['status'], 'completed')
        self.assertEqual(response.data['transaction']['amount'], '10.00')

    def test_payment_status_not_found(self):
        """Test payment status for non-existent transaction"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.get('/api/v1/payments/status/999/')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertFalse(response.data['success'])
        self.assertIn('not found', response.data['error'])

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access payment endpoints"""
        data = {
            'amount': 10.00,
            'purpose': 'test',
            'description': 'Test'
        }

        response = self.client.post('/api/v1/payments/create-order/', data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
