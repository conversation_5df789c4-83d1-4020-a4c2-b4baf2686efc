// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MaintenanceStatusImpl _$$MaintenanceStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$MaintenanceStatusImpl(
      isActive: json['isActive'] as bool? ?? false,
      title: json['title'] as String? ?? '',
      message: json['message'] as String? ?? '',
      maintenanceType: json['maintenanceType'] as String? ?? '',
      status: json['status'] as String? ?? '',
      scheduledStart: json['scheduledStart'] == null
          ? null
          : DateTime.parse(json['scheduledStart'] as String),
      scheduledEnd: json['scheduledEnd'] == null
          ? null
          : DateTime.parse(json['scheduledEnd'] as String),
      actualStart: json['actualStart'] == null
          ? null
          : DateTime.parse(json['actualStart'] as String),
      actualEnd: json['actualEnd'] == null
          ? null
          : DateTime.parse(json['actualEnd'] as String),
      allowAdminAccess: json['allowAdminAccess'] as bool? ?? true,
      allowStaffAccess: json['allowStaffAccess'] as bool? ?? false,
    );

Map<String, dynamic> _$$MaintenanceStatusImplToJson(
        _$MaintenanceStatusImpl instance) =>
    <String, dynamic>{
      'isActive': instance.isActive,
      'title': instance.title,
      'message': instance.message,
      'maintenanceType': instance.maintenanceType,
      'status': instance.status,
      'scheduledStart': instance.scheduledStart?.toIso8601String(),
      'scheduledEnd': instance.scheduledEnd?.toIso8601String(),
      'actualStart': instance.actualStart?.toIso8601String(),
      'actualEnd': instance.actualEnd?.toIso8601String(),
      'allowAdminAccess': instance.allowAdminAccess,
      'allowStaffAccess': instance.allowStaffAccess,
    };

_$FeatureToggleImpl _$$FeatureToggleImplFromJson(Map<String, dynamic> json) =>
    _$FeatureToggleImpl(
      name: json['name'] as String? ?? '',
      displayName: json['displayName'] as String? ?? '',
      description: json['description'] as String? ?? '',
      featureType: json['featureType'] as String? ?? '',
      isEnabled: json['isEnabled'] as bool? ?? true,
      isGlobal: json['isGlobal'] as bool? ?? true,
      enabledForAdmins: json['enabledForAdmins'] as bool? ?? true,
      enabledForStaff: json['enabledForStaff'] as bool? ?? true,
      enabledForUsers: json['enabledForUsers'] as bool? ?? true,
      disableStart: json['disableStart'] == null
          ? null
          : DateTime.parse(json['disableStart'] as String),
      disableEnd: json['disableEnd'] == null
          ? null
          : DateTime.parse(json['disableEnd'] as String),
      disabledMessage: json['disabledMessage'] as String? ??
          'This feature is temporarily unavailable.',
    );

Map<String, dynamic> _$$FeatureToggleImplToJson(_$FeatureToggleImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'displayName': instance.displayName,
      'description': instance.description,
      'featureType': instance.featureType,
      'isEnabled': instance.isEnabled,
      'isGlobal': instance.isGlobal,
      'enabledForAdmins': instance.enabledForAdmins,
      'enabledForStaff': instance.enabledForStaff,
      'enabledForUsers': instance.enabledForUsers,
      'disableStart': instance.disableStart?.toIso8601String(),
      'disableEnd': instance.disableEnd?.toIso8601String(),
      'disabledMessage': instance.disabledMessage,
    };

_$SystemStatusImpl _$$SystemStatusImplFromJson(Map<String, dynamic> json) =>
    _$SystemStatusImpl(
      maintenanceActive: json['maintenanceActive'] as bool? ?? false,
      activeMaintenance: json['activeMaintenance'] == null
          ? null
          : MaintenanceStatus.fromJson(
              json['activeMaintenance'] as Map<String, dynamic>),
      featureToggles: (json['featureToggles'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry(k, FeatureToggle.fromJson(e as Map<String, dynamic>)),
          ) ??
          const {},
      featureStatus: (json['featureStatus'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as bool),
          ) ??
          const {},
      lastChecked: json['lastChecked'] == null
          ? null
          : DateTime.parse(json['lastChecked'] as String),
    );

Map<String, dynamic> _$$SystemStatusImplToJson(_$SystemStatusImpl instance) =>
    <String, dynamic>{
      'maintenanceActive': instance.maintenanceActive,
      'activeMaintenance': instance.activeMaintenance,
      'featureToggles': instance.featureToggles,
      'featureStatus': instance.featureStatus,
      'lastChecked': instance.lastChecked?.toIso8601String(),
    };
