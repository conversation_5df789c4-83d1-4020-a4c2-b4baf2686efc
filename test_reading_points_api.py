#!/usr/bin/env python3
"""
Test script to verify the reading points API endpoint is working
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "testuser"  # Change this to your username
PASSWORD = "testpass123"  # Change this to your password

def test_reading_points_api():
    print("🧪 Testing Reading Points API")
    print("=" * 40)
    
    # Step 1: Login to get authentication token
    print("1. Logging in...")
    login_response = requests.post(f"{BASE_URL}/api/v1/auth/login/", json={
        "username": USERNAME,
        "password": PASSWORD
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(f"Response: {login_response.text}")
        return
    
    login_data = login_response.json()
    token = login_data.get('access_token')
    if not token:
        print(f"❌ No access token in response: {login_data}")
        return
    
    print(f"✅ Login successful")
    
    # Step 2: Get headers with authentication
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Step 3: Get available posts
    print("\n2. Getting available posts...")
    posts_response = requests.get(f"{BASE_URL}/api/v1/posts/", headers=headers)
    
    if posts_response.status_code != 200:
        print(f"❌ Failed to get posts: {posts_response.status_code}")
        return
    
    posts_data = posts_response.json()
    if not posts_data.get('results'):
        print("❌ No posts found")
        return
    
    post_id = posts_data['results'][0]['id']
    print(f"✅ Found post ID: {post_id}")
    
    # Step 4: Test reading points validation
    print(f"\n3. Testing reading points validation for post {post_id}...")
    validation_response = requests.post(
        f"{BASE_URL}/api/v1/gamification/validate-reading-activity/",
        headers=headers,
        json={
            "post_id": post_id,
            "time_spent": 15,
            "scroll_percentage": 50.0
        }
    )
    
    if validation_response.status_code == 200:
        validation_data = validation_response.json()
        print(f"✅ Validation response: {validation_data}")
        
        if validation_data.get('can_earn_points'):
            print("✅ User can earn points for this post")
        else:
            print(f"⚠️ User cannot earn points: {validation_data.get('reason')}")
    else:
        print(f"❌ Validation failed: {validation_response.status_code}")
        print(f"Response: {validation_response.text}")
    
    # Step 5: Test awarding reading points
    print(f"\n4. Testing reading points award for post {post_id}...")
    award_response = requests.post(
        f"{BASE_URL}/api/v1/posts/{post_id}/award_reading_points/",
        headers=headers,
        json={
            "time_spent": 15,
            "scroll_percentage": 50.0
        }
    )
    
    print(f"Award response status: {award_response.status_code}")
    
    if award_response.status_code == 200:
        award_data = award_response.json()
        print(f"✅ Award response: {award_data}")
        
        if award_data.get('success'):
            print(f"🎉 Points awarded successfully!")
            print(f"Points: {award_data.get('points_awarded', 'Unknown')}")
        else:
            print(f"⚠️ Points not awarded: {award_data.get('message')}")
    else:
        print(f"❌ Award failed: {award_response.status_code}")
        print(f"Response: {award_response.text}")
    
    # Step 6: Check user's current points
    print(f"\n5. Checking user's current points...")
    profile_response = requests.get(f"{BASE_URL}/api/v1/gamification/user-level/", headers=headers)
    
    if profile_response.status_code == 200:
        profile_data = profile_response.json()
        print(f"✅ Current points: {profile_data.get('points', 'Unknown')}")
        print(f"Level: {profile_data.get('level', 'Unknown')}")
    else:
        print(f"❌ Failed to get user profile: {profile_response.status_code}")

if __name__ == "__main__":
    try:
        test_reading_points_api()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
