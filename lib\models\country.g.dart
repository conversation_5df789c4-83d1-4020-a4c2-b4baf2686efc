// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'country.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RegionImpl _$$RegionImplFromJson(Map<String, dynamic> json) => _$RegionImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String?,
      countryCount: (json['countryCount'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
    );

Map<String, dynamic> _$$RegionImplToJson(_$RegionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'description': instance.description,
      'countryCount': instance.countryCount,
      'isActive': instance.isActive,
    };

_$CountryImpl _$$CountryImplFromJson(Map<String, dynamic> json) =>
    _$CountryImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      code: json['code'] as String,
      code3: json['code_3'] as String?,
      region: json['region'] == null
          ? null
          : Region.fromJson(json['region'] as Map<String, dynamic>),
      currencyCode: json['currencyCode'] as String?,
      currencyName: json['currencyName'] as String?,
      phoneCode: json['phoneCode'] as String?,
      flagEmoji: json['flagEmoji'] as String?,
      displayName: json['displayName'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      allowGlobalContent: json['allowGlobalContent'] as bool? ?? true,
      priority: (json['priority'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$CountryImplToJson(_$CountryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'code_3': instance.code3,
      'region': instance.region,
      'currencyCode': instance.currencyCode,
      'currencyName': instance.currencyName,
      'phoneCode': instance.phoneCode,
      'flagEmoji': instance.flagEmoji,
      'displayName': instance.displayName,
      'isActive': instance.isActive,
      'allowGlobalContent': instance.allowGlobalContent,
      'priority': instance.priority,
    };

_$RegionalPreferencesImpl _$$RegionalPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$RegionalPreferencesImpl(
      preferredCountry: json['preferred_country'] == null
          ? null
          : Country.fromJson(json['preferred_country'] as Map<String, dynamic>),
      detectedCountry: json['detected_country'] == null
          ? null
          : Country.fromJson(json['detected_country'] as Map<String, dynamic>),
      effectiveCountry: json['effective_country'] == null
          ? null
          : Country.fromJson(json['effective_country'] as Map<String, dynamic>),
      showGlobalContent: json['show_global_content'] as bool? ?? true,
      autoDetectLocation: json['auto_detect_location'] as bool? ?? true,
    );

Map<String, dynamic> _$$RegionalPreferencesImplToJson(
        _$RegionalPreferencesImpl instance) =>
    <String, dynamic>{
      'preferred_country': instance.preferredCountry,
      'detected_country': instance.detectedCountry,
      'effective_country': instance.effectiveCountry,
      'show_global_content': instance.showGlobalContent,
      'auto_detect_location': instance.autoDetectLocation,
    };

_$RegionalStatsImpl _$$RegionalStatsImplFromJson(Map<String, dynamic> json) =>
    _$RegionalStatsImpl(
      userCountry: json['userCountry'] as String?,
      totalPosts: (json['totalPosts'] as num?)?.toInt() ?? 0,
      globalPosts: (json['globalPosts'] as num?)?.toInt() ?? 0,
      regionalPosts: (json['regionalPosts'] as num?)?.toInt() ?? 0,
      availableCountries: (json['availableCountries'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$RegionalStatsImplToJson(_$RegionalStatsImpl instance) =>
    <String, dynamic>{
      'userCountry': instance.userCountry,
      'totalPosts': instance.totalPosts,
      'globalPosts': instance.globalPosts,
      'regionalPosts': instance.regionalPosts,
      'availableCountries': instance.availableCountries,
    };

_$LocationHistoryImpl _$$LocationHistoryImplFromJson(
        Map<String, dynamic> json) =>
    _$LocationHistoryImpl(
      id: (json['id'] as num).toInt(),
      country: json['country'] == null
          ? null
          : Country.fromJson(json['country'] as Map<String, dynamic>),
      ipAddress: json['ipAddress'] as String,
      detectionMethod: json['detectionMethod'] as String,
      confidenceScore: (json['confidenceScore'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$LocationHistoryImplToJson(
        _$LocationHistoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'country': instance.country,
      'ipAddress': instance.ipAddress,
      'detectionMethod': instance.detectionMethod,
      'confidenceScore': instance.confidenceScore,
      'createdAt': instance.createdAt.toIso8601String(),
    };
