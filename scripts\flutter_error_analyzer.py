#!/usr/bin/env python3
"""
Flutter Error Analyzer Script
Analyzes Flutter compilation errors and provides detailed summaries and fixes
"""

import subprocess
import re
import json
import os
import sys
from datetime import datetime
from pathlib import Path

class FlutterErrorAnalyzer:
    def __init__(self, project_path="."):
        self.project_path = Path(project_path)
        self.errors = []
        self.warnings = []
        self.suggestions = []
        
    def run_flutter_analyze(self):
        """Run flutter analyze and capture output"""
        print("🔍 Running Flutter analysis...")
        
        try:
            # Change to project directory
            os.chdir(self.project_path)
            
            # Run flutter analyze
            result = subprocess.run(
                ['flutter', 'analyze', '--no-pub'],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return result.stdout, result.stderr, result.returncode
        except subprocess.TimeoutExpired:
            return "", "Analysis timed out", 1
        except FileNotFoundError:
            return "", "Flutter command not found. Please install Flutter SDK.", 1
        except Exception as e:
            return "", f"Error running analysis: {str(e)}", 1
    
    def run_flutter_doctor(self):
        """Run flutter doctor to check setup"""
        print("🩺 Running Flutter doctor...")
        
        try:
            result = subprocess.run(
                ['flutter', 'doctor', '-v'],
                capture_output=True,
                text=True,
                timeout=30
            )
            return result.stdout, result.stderr, result.returncode
        except Exception as e:
            return "", f"Error running flutter doctor: {str(e)}", 1
    
    def parse_analysis_output(self, stdout, stderr):
        """Parse flutter analyze output and categorize issues"""
        
        # Combine stdout and stderr
        full_output = stdout + "\n" + stderr
        
        # Patterns for different types of issues
        error_patterns = [
            r"error\s*•\s*(.+?)\s*•\s*(.+?):(\d+):(\d+)",
            r"Error:\s*(.+)",
            r"FAILURE:\s*(.+)",
        ]
        
        warning_patterns = [
            r"warning\s*•\s*(.+?)\s*•\s*(.+?):(\d+):(\d+)",
            r"Warning:\s*(.+)",
        ]
        
        info_patterns = [
            r"info\s*•\s*(.+?)\s*•\s*(.+?):(\d+):(\d+)",
            r"Info:\s*(.+)",
        ]
        
        # Parse errors
        for pattern in error_patterns:
            matches = re.finditer(pattern, full_output, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if len(match.groups()) >= 4:
                    self.errors.append({
                        'type': 'error',
                        'message': match.group(1).strip(),
                        'file': match.group(2).strip(),
                        'line': match.group(3),
                        'column': match.group(4),
                        'raw': match.group(0)
                    })
                else:
                    self.errors.append({
                        'type': 'error',
                        'message': match.group(1).strip(),
                        'file': 'unknown',
                        'line': '0',
                        'column': '0',
                        'raw': match.group(0)
                    })
        
        # Parse warnings
        for pattern in warning_patterns:
            matches = re.finditer(pattern, full_output, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if len(match.groups()) >= 4:
                    self.warnings.append({
                        'type': 'warning',
                        'message': match.group(1).strip(),
                        'file': match.group(2).strip(),
                        'line': match.group(3),
                        'column': match.group(4),
                        'raw': match.group(0)
                    })
                else:
                    self.warnings.append({
                        'type': 'warning',
                        'message': match.group(1).strip(),
                        'file': 'unknown',
                        'line': '0',
                        'column': '0',
                        'raw': match.group(0)
                    })
    
    def categorize_errors(self):
        """Categorize errors by type and provide suggestions"""
        
        categories = {
            'import_errors': [],
            'syntax_errors': [],
            'type_errors': [],
            'dependency_errors': [],
            'widget_errors': [],
            'other_errors': []
        }
        
        for error in self.errors:
            message = error['message'].lower()
            
            if any(keyword in message for keyword in ['import', 'library', 'package']):
                categories['import_errors'].append(error)
                self.add_import_suggestion(error)
            elif any(keyword in message for keyword in ['syntax', 'expected', 'unexpected']):
                categories['syntax_errors'].append(error)
                self.add_syntax_suggestion(error)
            elif any(keyword in message for keyword in ['type', 'cast', 'argument']):
                categories['type_errors'].append(error)
                self.add_type_suggestion(error)
            elif any(keyword in message for keyword in ['dependency', 'pubspec', 'version']):
                categories['dependency_errors'].append(error)
                self.add_dependency_suggestion(error)
            elif any(keyword in message for keyword in ['widget', 'stateful', 'stateless']):
                categories['widget_errors'].append(error)
                self.add_widget_suggestion(error)
            else:
                categories['other_errors'].append(error)
                self.add_general_suggestion(error)
        
        return categories
    
    def add_import_suggestion(self, error):
        """Add suggestions for import errors"""
        message = error['message']
        
        if 'size' in message.lower():
            self.suggestions.append({
                'error': error,
                'suggestion': "Add 'import 'dart:ui' show Size;' to fix Size class conflicts",
                'fix': "Import the Size class from dart:ui to avoid conflicts with custom Size classes"
            })
        elif 'cached_network_image' in message.lower():
            self.suggestions.append({
                'error': error,
                'suggestion': "Add 'cached_network_image: ^3.3.1' to pubspec.yaml dependencies",
                'fix': "Run 'flutter pub add cached_network_image' to add the package"
            })
        elif 'video_player' in message.lower():
            self.suggestions.append({
                'error': error,
                'suggestion': "Add 'video_player: ^2.8.2' to pubspec.yaml dependencies",
                'fix': "Run 'flutter pub add video_player' to add the package"
            })
        else:
            self.suggestions.append({
                'error': error,
                'suggestion': "Check if the imported file exists and the import path is correct",
                'fix': "Verify the file path and ensure all required dependencies are in pubspec.yaml"
            })
    
    def add_syntax_suggestion(self, error):
        """Add suggestions for syntax errors"""
        self.suggestions.append({
            'error': error,
            'suggestion': "Check for missing semicolons, brackets, or incorrect syntax",
            'fix': "Review the code at the specified line and column for syntax issues"
        })
    
    def add_type_suggestion(self, error):
        """Add suggestions for type errors"""
        self.suggestions.append({
            'error': error,
            'suggestion': "Check variable types and ensure proper type casting",
            'fix': "Verify that variable types match expected types or add proper type casting"
        })
    
    def add_dependency_suggestion(self, error):
        """Add suggestions for dependency errors"""
        self.suggestions.append({
            'error': error,
            'suggestion': "Run 'flutter pub get' to install dependencies",
            'fix': "Check pubspec.yaml for correct package versions and run 'flutter pub get'"
        })
    
    def add_widget_suggestion(self, error):
        """Add suggestions for widget errors"""
        self.suggestions.append({
            'error': error,
            'suggestion': "Check widget structure and ensure proper StatefulWidget/StatelessWidget usage",
            'fix': "Verify widget hierarchy and state management implementation"
        })
    
    def add_general_suggestion(self, error):
        """Add general suggestions for other errors"""
        self.suggestions.append({
            'error': error,
            'suggestion': "Review the error message and check Flutter documentation",
            'fix': "Consult Flutter documentation or search for similar issues online"
        })
    
    def generate_report(self, categories, doctor_output=""):
        """Generate a comprehensive error report"""
        
        report = []
        report.append("=" * 80)
        report.append("🔍 FLUTTER ERROR ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📁 Project: {self.project_path.absolute()}")
        report.append("")
        
        # Summary
        total_errors = len(self.errors)
        total_warnings = len(self.warnings)
        
        report.append("📊 SUMMARY")
        report.append("-" * 40)
        report.append(f"❌ Errors: {total_errors}")
        report.append(f"⚠️  Warnings: {total_warnings}")
        report.append(f"💡 Suggestions: {len(self.suggestions)}")
        report.append("")
        
        if total_errors == 0 and total_warnings == 0:
            report.append("🎉 NO ERRORS FOUND! Your Flutter app is ready to run!")
            report.append("")
        
        # Error categories
        if total_errors > 0:
            report.append("🔥 ERROR BREAKDOWN")
            report.append("-" * 40)
            
            for category, errors in categories.items():
                if errors:
                    category_name = category.replace('_', ' ').title()
                    report.append(f"📂 {category_name}: {len(errors)} errors")
                    
                    for i, error in enumerate(errors[:3], 1):  # Show first 3 errors
                        report.append(f"   {i}. {error['message']}")
                        report.append(f"      📍 {error['file']}:{error['line']}:{error['column']}")
                    
                    if len(errors) > 3:
                        report.append(f"   ... and {len(errors) - 3} more")
                    report.append("")
        
        # Suggestions
        if self.suggestions:
            report.append("💡 SUGGESTED FIXES")
            report.append("-" * 40)
            
            for i, suggestion in enumerate(self.suggestions[:10], 1):  # Show first 10 suggestions
                report.append(f"{i}. {suggestion['suggestion']}")
                report.append(f"   🔧 Fix: {suggestion['fix']}")
                report.append(f"   📍 File: {suggestion['error']['file']}:{suggestion['error']['line']}")
                report.append("")
        
        # Quick fixes
        report.append("🚀 QUICK FIXES TO TRY")
        report.append("-" * 40)
        report.append("1. Run 'flutter pub get' to install dependencies")
        report.append("2. Run 'flutter clean' then 'flutter pub get' to clean cache")
        report.append("3. Check that all imports point to existing files")
        report.append("4. Verify pubspec.yaml has all required dependencies")
        report.append("5. Ensure Flutter SDK is properly installed")
        report.append("")
        
        # Flutter doctor info
        if doctor_output:
            report.append("🩺 FLUTTER DOCTOR OUTPUT")
            report.append("-" * 40)
            report.append(doctor_output[:1000] + "..." if len(doctor_output) > 1000 else doctor_output)
            report.append("")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_report(self, report, filename="flutter_error_report.txt"):
        """Save report to file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"📄 Report saved to: {filename}")
        except Exception as e:
            print(f"❌ Failed to save report: {e}")
    
    def analyze(self):
        """Main analysis function"""
        print("🚀 Starting Flutter Error Analysis...")
        print("-" * 50)
        
        # Run flutter doctor
        doctor_stdout, doctor_stderr, doctor_code = self.run_flutter_doctor()
        
        # Run flutter analyze
        stdout, stderr, return_code = self.run_flutter_analyze()
        
        if return_code != 0 and "Flutter command not found" in stderr:
            print("❌ Flutter SDK not found!")
            print("Please install Flutter SDK: https://flutter.dev/docs/get-started/install")
            return
        
        # Parse output
        self.parse_analysis_output(stdout, stderr)
        
        # Categorize errors
        categories = self.categorize_errors()
        
        # Generate report
        report = self.generate_report(categories, doctor_stdout)
        
        # Print report
        print(report)
        
        # Save report
        self.save_report(report)
        
        # Return summary
        return {
            'errors': len(self.errors),
            'warnings': len(self.warnings),
            'suggestions': len(self.suggestions),
            'categories': categories
        }

def main():
    """Main function"""
    project_path = sys.argv[1] if len(sys.argv) > 1 else "."
    
    analyzer = FlutterErrorAnalyzer(project_path)
    result = analyzer.analyze()
    
    # Exit with error code if there are errors
    if result['errors'] > 0:
        sys.exit(1)
    else:
        print("✅ Analysis complete - No critical errors found!")
        sys.exit(0)

if __name__ == "__main__":
    main()
