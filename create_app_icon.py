from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    # Create a 512x512 image
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Define colors
    primary_color = (99, 102, 241)  # #6366F1
    secondary_color = (139, 92, 246)  # #8B5CF6
    white = (255, 255, 255)
    
    # Draw background circle with gradient effect
    center = size // 2
    radius = 240
    
    # Create gradient effect by drawing multiple circles
    for i in range(radius):
        alpha = int(255 * (1 - i / radius))
        r = int(primary_color[0] + (secondary_color[0] - primary_color[0]) * i / radius)
        g = int(primary_color[1] + (secondary_color[1] - primary_color[1]) * i / radius)
        b = int(primary_color[2] + (secondary_color[2] - primary_color[2]) * i / radius)
        
        draw.ellipse([center - radius + i, center - radius + i, 
                     center + radius - i, center + radius - i], 
                    fill=(r, g, b, alpha))
    
    # Draw white inner circle
    inner_radius = 180
    draw.ellipse([center - inner_radius, center - inner_radius,
                 center + inner_radius, center + inner_radius],
                fill=(*white, 240))
    
    # Draw trending arrow
    arrow_color = primary_color
    
    # Arrow line
    start_x, start_y = center - 80, center + 40
    end_x, end_y = center + 80, center - 40
    draw.line([start_x, start_y, end_x, end_y], fill=arrow_color, width=16)
    
    # Arrow head
    arrow_head_points = [
        (end_x - 30, end_y),
        (end_x, end_y),
        (end_x, end_y + 30)
    ]
    draw.line([arrow_head_points[0], arrow_head_points[1]], fill=arrow_color, width=16)
    draw.line([arrow_head_points[1], arrow_head_points[2]], fill=arrow_color, width=16)
    
    # Draw data points
    points = [
        (center - 60, center + 20),
        (center - 20, center),
        (center + 20, center - 20),
        (center + 60, center - 40)
    ]
    
    for point in points:
        draw.ellipse([point[0] - 8, point[1] - 8, point[0] + 8, point[1] + 8],
                    fill=arrow_color)
    
    # Draw connecting lines between points
    for i in range(len(points) - 1):
        draw.line([points[i], points[i + 1]], fill=arrow_color, width=4)
    
    # Save the image
    os.makedirs('assets/icons', exist_ok=True)
    img.save('assets/icons/app_icon.png', 'PNG')
    print("App icon created successfully!")

if __name__ == "__main__":
    create_app_icon()
