from django.contrib import admin
from .models import ReadingSession, ContentAnalytics


@admin.register(ReadingSession)
class ReadingSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'post', 'start_time', 'end_time']
    list_filter = ['start_time', 'end_time']
    search_fields = ['user__username', 'post__title']
    readonly_fields = ['start_time']
    date_hierarchy = 'start_time'


@admin.register(ContentAnalytics)
class ContentAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['post', 'estimated_reading_time', 'complexity_score', 'word_count']
    list_filter = ['complexity_score']
    search_fields = ['post__title']
    readonly_fields = ['created_at', 'updated_at']
