{% extends 'blog/base.html' %}
{% load static %}

{% block title %}Profile - Trendy Blog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-body text-center">
                <img src="{% static 'blog/images/default-avatar.png' %}" alt="Profile" class="rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                <h4>{{ user.get_full_name|default:user.username }}</h4>
                <p class="text-muted">@{{ user.username }}</p>
                <hr>
                <div class="d-flex justify-content-center">
                    <div class="px-3">
                        <h5>{{ user_posts.count }}</h5>
                        <small class="text-muted">Posts</small>
                    </div>
                    <div class="px-3">
                        <h5>{{ user.comment_set.count }}</h5>
                        <small class="text-muted">Comments</small>
                    </div>
                    <div class="px-3">
                        <h5>{{ user.liked_posts.count }}</h5>
                        <small class="text-muted">Likes</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    {% for post in user_posts|slice:":5" %}
                    <li class="mb-2">
                        <a href="{% url 'post-detail' post.slug %}" class="text-decoration-none">
                            <small class="text-muted">{{ post.created_at|date:"M d, Y" }}</small>
                            <div>{{ post.title }}</div>
                        </a>
                    </li>
                    {% empty %}
                    <li class="text-muted">No posts yet</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">Edit Profile</h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    {% for field in form %}
                    <div class="mb-3">
                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                        {{ field }}
                        {% if field.errors %}
                        <div class="invalid-feedback d-block">
                            {{ field.errors }}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </form>
            </div>
        </div>

        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0">My Posts</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for post in user_posts %}
                            <tr>
                                <td>{{ post.title }}</td>
                                <td>{{ post.category.name }}</td>
                                <td>
                                    <span class="badge {% if post.status == 'published' %}bg-success{% else %}bg-warning{% endif %}">
                                        {{ post.status }}
                                    </span>
                                </td>
                                <td>{{ post.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <a href="{% url 'post-detail' post.slug %}" class="btn btn-sm btn-primary">View</a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">No posts yet</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 