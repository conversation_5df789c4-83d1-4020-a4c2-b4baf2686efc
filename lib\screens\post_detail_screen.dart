// 2. Post Detail Screen
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:share_plus/share_plus.dart';
import '../models/comment.dart';
import '../models/paginated_response.dart';
import '../models/post.dart';
import '../providers/provider.dart';
import '../providers/auth_provider.dart';
import '../theme/app_theme.dart';
import 'package:trendy/widgets/post_media_preview.dart';
import 'package:trendy/widgets/enhanced_login_dialog.dart';
import 'package:trendy/widgets/reading_progress_tracker.dart';
import 'package:trendy/widgets/reading_engagement_tracker.dart';
import 'package:trendy/widgets/interactive/interactive_content_widget.dart';
import 'package:trendy/widgets/voice/text_to_speech_widget.dart';

class PostDetailScreen extends ConsumerWidget {
  final String pk;

  const PostDetailScreen({Key? key, required this.pk}) : super(key: key);

  void _sharePost(Post post) {
    final shareText =
        '''
${post.title}

${post.content.length > 200 ? '${post.content.substring(0, 200)}...' : post.content}

Check out this post on Trendy!
''';

    Share.share(shareText, subject: post.title);
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onPressed,
    bool isActive = false,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final postAsync = ref.watch(postProvider(pk));

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Post Details',
          style: TextStyle(fontWeight: FontWeight.w700, fontSize: 20),
        ),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.arrow_back_ios_new,
              size: 18,
              color: AppTheme.textPrimary,
            ),
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: postAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Post not found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'The post you are looking for does not exist or has been removed.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
        data: (post) => ReadingEngagementTracker(
          postId: post.id.toString(),
          onPointsAwarded: () {
            print('✅ Reading points awarded for post: ${post.title}');
          },
          onEngagementComplete: () {
            print('📖 Reading engagement completed for post: ${post.title}');
          },
          child: ReadingProgressTracker(
            postId: post.id,
            onReadingStarted: () {
              print('Reading started for post: ${post.title}');
            },
            onReadingCompleted: () {
              print('Reading completed for post: ${post.title}');
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Enhanced Header Card
                Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Author Info
                      Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: AppTheme.primaryGradient,
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.primaryColor.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: CircleAvatar(
                              radius: 24,
                              backgroundColor: Colors.transparent,
                              backgroundImage:
                                  post.author.avatarUrl != null &&
                                      post.author.avatarUrl!.isNotEmpty
                                  ? NetworkImage(post.author.avatarUrl!)
                                  : null,
                              child:
                                  post.author.avatarUrl == null ||
                                      post.author.avatarUrl!.isEmpty
                                  ? Text(
                                      post.author.username[0].toUpperCase(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    )
                                  : null,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  post.author.username,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 16,
                                    color: AppTheme.textPrimary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  timeago.format(post.createdAt),
                                  style: const TextStyle(
                                    color: AppTheme.textTertiary,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              gradient: AppTheme.accentGradient,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: AppTheme.accentColor.withOpacity(0.2),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Text(
                              post.category.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      // Title
                      Text(
                        post.title,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.w800,
                          color: AppTheme.textPrimary,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 20),
                      // Content
                      Text(
                        post.content,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.7,
                          color: AppTheme.textSecondary,
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),

                // Enhanced Media Section with Header
                if (post.mediaItems.isNotEmpty) ...[
                  // Media Section Header
                  Container(
                    margin: const EdgeInsets.fromLTRB(20, 16, 20, 8),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            gradient: AppTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryColor.withOpacity(0.2),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.photo_library_rounded,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${post.mediaItems.length} ${post.mediaItems.length == 1 ? 'Media' : 'Media Items'}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Media Items
                  Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 8,
                    ),
                    child: Column(
                      children: post.mediaItems.asMap().entries.map((entry) {
                        final index = entry.key;
                        final media = entry.value;
                        return Container(
                          margin: const EdgeInsets.only(bottom: 24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.08),
                                blurRadius: 20,
                                offset: const Offset(0, 4),
                                spreadRadius: 0,
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Media Type Indicator (for multiple media)
                              if (post.mediaItems.length > 1)
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    16,
                                    12,
                                    16,
                                    0,
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor
                                              .withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              media.mediaType == 'video'
                                                  ? Icons.play_circle_outline
                                                  : Icons.image_outlined,
                                              size: 14,
                                              color: AppTheme.primaryColor,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              '${index + 1}/${post.mediaItems.length}',
                                              style: const TextStyle(
                                                color: AppTheme.primaryColor,
                                                fontSize: 11,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const Spacer(),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: media.mediaType == 'video'
                                              ? AppTheme.errorColor.withOpacity(
                                                  0.1,
                                                )
                                              : AppTheme.successColor
                                                    .withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Text(
                                          media.mediaType.toUpperCase(),
                                          style: TextStyle(
                                            color: media.mediaType == 'video'
                                                ? AppTheme.errorColor
                                                : AppTheme.successColor,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              // Media Content
                              Padding(
                                padding: EdgeInsets.all(
                                  post.mediaItems.length > 1 ? 12 : 0,
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(
                                      post.mediaItems.length > 1 ? 12 : 20,
                                    ),
                                    bottom: Radius.circular(
                                      media.caption.isNotEmpty
                                          ? 0
                                          : (post.mediaItems.length > 1
                                                ? 12
                                                : 20),
                                    ),
                                  ),
                                  child: PostMediaPreview(media: media),
                                ),
                              ),
                              // Caption
                              if (media.caption.isNotEmpty)
                                Padding(
                                  padding: const EdgeInsets.fromLTRB(
                                    16,
                                    8,
                                    16,
                                    16,
                                  ),
                                  child: Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: AppTheme.backgroundColor,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Icon(
                                          Icons.format_quote,
                                          color: AppTheme.textTertiary,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            media.caption,
                                            style: const TextStyle(
                                              color: AppTheme.textSecondary,
                                              fontSize: 14,
                                              fontStyle: FontStyle.italic,
                                              height: 1.4,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],

                // Text-to-Speech Widget
                TextToSpeechWidget(text: post.content, title: post.title),

                // Interactive Content
                InteractiveContentWidget(postId: post.id),

                // Enhanced Action Bar
                Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 15,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // Like Button
                      _buildActionButton(
                        icon: post.isLiked
                            ? Icons.favorite
                            : Icons.favorite_border,
                        label: '${post.likeCount}',
                        color: post.isLiked
                            ? Colors.red
                            : AppTheme.textTertiary,
                        isActive: post.isLiked,
                        onPressed: () {
                          final authState = ref.read(enhancedAuthProvider);
                          if (!authState.isAuthenticated) {
                            showEnhancedLoginDialog(
                              context,
                              action: 'like this post',
                              title: 'Like Posts',
                              subtitle:
                                  'Sign in to like posts and show your appreciation to authors',
                              icon: Icons.favorite_rounded,
                            );
                            return;
                          }
                          ref.read(postProvider(pk).notifier).toggleLike();
                        },
                      ),
                      const SizedBox(width: 20),
                      // Comment Button
                      _buildActionButton(
                        icon: Icons.chat_bubble_outline,
                        label: '${post.commentCount}',
                        color: AppTheme.textTertiary,
                        onPressed: () {
                          final authState = ref.read(enhancedAuthProvider);
                          if (!authState.isAuthenticated) {
                            showEnhancedLoginDialog(
                              context,
                              action: 'comment on this post',
                              title: 'Join the Conversation',
                              subtitle:
                                  'Sign in to comment and engage with the community',
                              icon: Icons.chat_bubble_rounded,
                            );
                            return;
                          }
                          // Scroll to comments section
                        },
                      ),
                      const SizedBox(width: 20),
                      // Views
                      _buildActionButton(
                        icon: Icons.visibility_outlined,
                        label: '${post.views}',
                        color: AppTheme.textTertiary,
                        onPressed: null,
                      ),
                      const Spacer(),
                      // Share Button
                      Container(
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.share_outlined,
                            color: Colors.white,
                            size: 20,
                          ),
                          onPressed: () => _sharePost(post),
                          padding: const EdgeInsets.all(12),
                        ),
                      ),
                    ],
                  ),
                ),
                _CommentsSection(pk: pk),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _CommentsSection extends ConsumerStatefulWidget {
  final String pk;

  const _CommentsSection({Key? key, required this.pk}) : super(key: key);

  @override
  ConsumerState<_CommentsSection> createState() => _CommentsSectionState();
}

class _CommentsSectionState extends ConsumerState<_CommentsSection> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    final commentsAsync = ref.watch(commentsProvider(widget.pk));
    final authState = ref.watch(enhancedAuthProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                  ),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
                const Text(
                  'Comments',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.refresh, size: 20),
                  onPressed: () {
                    ref
                        .read(commentsProvider(widget.pk).notifier)
                        .getComments();
                  },
                  tooltip: 'Refresh comments',
                ),
              ],
            ),
            if (authState.isAuthenticated)
              TextButton.icon(
                onPressed: () => _showAddCommentDialog(context, ref, widget.pk),
                icon: const Icon(Icons.add),
                label: const Text('Add Comment'),
              ),
          ],
        ),
        if (_isExpanded)
          Column(
            children: [
              const SizedBox(height: 16),
              commentsAsync.when(
                loading: () => const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, stack) =>
                    Center(child: Text('Error loading comments: $error')),
                data: (comments) => _buildCommentsList(context, ref, comments),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildCommentsList(
    BuildContext context,
    WidgetRef ref,
    PaginatedResponse<Comment> comments,
  ) {
    if (comments.results.isEmpty) {
      final authState = ref.watch(enhancedAuthProvider);
      final theme = Theme.of(context);

      return Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.1),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 48,
                color: theme.colorScheme.primary.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No comments yet',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                authState.isAuthenticated
                    ? 'Be the first to share your thoughts!'
                    : 'Sign in to join the conversation',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.textTheme.bodyLarge?.color?.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              if (authState.isAuthenticated) ...[
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () =>
                      _showAddCommentDialog(context, ref, widget.pk),
                  icon: const Icon(Icons.add_comment),
                  label: const Text('Add Comment'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: comments.results.length,
          itemBuilder: (context, index) {
            final comment = comments.results[index];
            // All comments from API are now top-level with nested replies
            return _buildCommentTree(context, ref, comment);
          },
        ),
        if (comments.next != null)
          TextButton(
            onPressed: () {
              ref.read(commentsProvider(widget.pk).notifier).getComments();
            },
            child: const Text('Load More'),
          ),
      ],
    );
  }

  Widget _buildCommentTree(
    BuildContext context,
    WidgetRef ref,
    Comment comment,
  ) {
    return _CollapsibleCommentCard(
      comment: comment,
      postId: widget.pk,
      depth: 0,
    );
  }

  void _showAddCommentDialog(BuildContext context, WidgetRef ref, String pk) {
    showDialog(
      context: context,
      builder: (context) => _AddCommentDialog(pk: pk),
    );
  }
}

class _CollapsibleCommentCard extends ConsumerStatefulWidget {
  final Comment comment;
  final String postId;
  final int depth;

  const _CollapsibleCommentCard({
    required this.comment,
    required this.postId,
    required this.depth,
  });

  @override
  ConsumerState<_CollapsibleCommentCard> createState() =>
      _CollapsibleCommentCardState();
}

class _CollapsibleCommentCardState
    extends ConsumerState<_CollapsibleCommentCard>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = true;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final hasReplies = widget.comment.replies.isNotEmpty;
    final leftPadding = widget.depth * 24.0;

    return Container(
      margin: EdgeInsets.only(
        left: leftPadding,
        bottom: 8,
        top: widget.depth == 0 ? 8 : 0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main comment
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.depth == 0
                    ? AppTheme.primaryColor.withOpacity(0.2)
                    : Colors.grey.withOpacity(0.2),
                width: widget.depth == 0 ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: AppTheme.primaryColor,
                        child: Text(
                          widget.comment.author.username
                              .substring(0, 1)
                              .toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.comment.author.username,
                              style: Theme.of(context).textTheme.titleSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textPrimary,
                                  ),
                            ),
                            Text(
                              timeago.format(widget.comment.createdAt),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: AppTheme.textTertiary),
                            ),
                          ],
                        ),
                      ),
                      if (hasReplies)
                        IconButton(
                          icon: AnimatedRotation(
                            turns: _isExpanded ? 0.5 : 0,
                            duration: const Duration(milliseconds: 300),
                            child: const Icon(Icons.expand_more),
                          ),
                          onPressed: _toggleExpanded,
                          iconSize: 20,
                          color: AppTheme.textTertiary,
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Content
                  Text(
                    widget.comment.content,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondary,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Actions
                  Row(
                    children: [
                      if (authState.isAuthenticated) ...[
                        _buildActionButton(
                          icon: widget.comment.isLiked
                              ? Icons.favorite
                              : Icons.favorite_border,
                          label: widget.comment.likeCount.toString(),
                          color: widget.comment.isLiked
                              ? AppTheme.errorColor
                              : AppTheme.textTertiary,
                          onTap: () {
                            ref
                                .read(commentsProvider(widget.postId).notifier)
                                .toggleCommentLike(widget.comment.id);
                          },
                        ),
                        const SizedBox(width: 16),
                        _buildActionButton(
                          icon: Icons.reply,
                          label: 'Reply',
                          color: AppTheme.textTertiary,
                          onTap: () => _showReplyDialog(context),
                        ),
                      ],
                      if (hasReplies) ...[
                        const Spacer(),
                        Text(
                          '${widget.comment.replies.length} ${widget.comment.replies.length == 1 ? 'reply' : 'replies'}',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.textTertiary),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Replies
          if (hasReplies)
            SizeTransition(
              sizeFactor: _expandAnimation,
              child: Container(
                margin: const EdgeInsets.only(top: 8),
                child: Column(
                  children: widget.comment.replies.map((reply) {
                    return _CollapsibleCommentCard(
                      comment: reply,
                      postId: widget.postId,
                      depth: widget.depth + 1,
                    );
                  }).toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showReplyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) =>
          _AddCommentDialog(pk: widget.postId, parentId: widget.comment.id),
    );
  }
}

class _AddCommentDialog extends ConsumerStatefulWidget {
  final String pk;
  final int? parentId;

  const _AddCommentDialog({required this.pk, this.parentId});

  @override
  ConsumerState<_AddCommentDialog> createState() => _AddCommentDialogState();
}

class _AddCommentDialogState extends ConsumerState<_AddCommentDialog> {
  final _contentController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_contentController.text.isEmpty) return;

    final authState = ref.read(enhancedAuthProvider);
    if (!authState.isAuthenticated) {
      if (mounted) {
        Navigator.of(context).pop();
        showEnhancedLoginDialog(
          context,
          action: 'comment on this post',
          title: 'Join the Conversation',
          subtitle: 'Sign in to comment and engage with the community',
          icon: Icons.chat_bubble_rounded,
        );
      }
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      await ref
          .read(commentsProvider(widget.pk).notifier)
          .createComment(_contentController.text, parentId: widget.parentId);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      widget.parentId != null ? Icons.reply : Icons.add_comment,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      widget.parentId != null
                          ? 'Reply to Comment'
                          : 'Add Comment',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    color: AppTheme.textTertiary,
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Text field
              Container(
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.withOpacity(0.2)),
                ),
                child: TextField(
                  controller: _contentController,
                  decoration: InputDecoration(
                    hintText: widget.parentId != null
                        ? 'Write your reply...'
                        : 'Share your thoughts...',
                    hintStyle: TextStyle(color: AppTheme.textTertiary),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  maxLines: 4,
                  minLines: 3,
                ),
              ),
              const SizedBox(height: 24),

              // Actions
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          color: AppTheme.textSecondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isSubmitting ? null : _submit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Ink(
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Container(
                          alignment: Alignment.center,
                          child: _isSubmitting
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                              : Text(
                                  widget.parentId != null ? 'Reply' : 'Post',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
