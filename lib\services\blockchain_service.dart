import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/blockchain_models.dart';
import '../config/api_config.dart';
import 'platform_storage_service.dart';

class BlockchainService {
  static final BlockchainService _instance = BlockchainService._internal();
  factory BlockchainService() => _instance;
  BlockchainService._internal();

  final Dio _dio = Dio();

  // Initialize service with auth token
  Future<void> initialize() async {
    final token = await PlatformStorageService.getSecureData('token'); // Fixed: use same storage service as ApiService
    print('🔍 BlockchainService: Retrieved token: ${token != null ? "${token.substring(0, 20)}..." : "null"}');
    print('🔍 BlockchainService: Storage type: ${PlatformStorageService.storageType}');

    // Debug: Check if user is actually logged in by checking other storage
    final userData = await PlatformStorageService.getSecureObject('user_data');
    print('🔍 BlockchainService: User data exists: ${userData != null}');

    if (token != null) {
      _dio.options.headers['Authorization'] = 'Token $token';
      print('🔍 BlockchainService: Set Authorization header with token');
    } else {
      print('❌ BlockchainService: No token found in storage');
      print('💡 BlockchainService: User may need to log in again');
    }
    _dio.options.baseUrl = ApiConfig.baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  // Wallet Management
  Future<BlockchainWallet?> getUserWallet() async {
    try {
      await initialize();
      print('🔍 BlockchainService: Making request to /api/v1/blockchain/wallet/');
      final response = await _dio.get('/api/v1/blockchain/wallet/');
      print('🔍 BlockchainService: Response status: ${response.statusCode}');

      if (response.statusCode == 200 && response.data['success']) {
        print('✅ BlockchainService: Wallet retrieved successfully');
        return BlockchainWallet.fromJson(response.data['wallet']);
      }
      print('⚠️ BlockchainService: Wallet not found or unsuccessful response');
      return null;
    } catch (e) {
      print('❌ BlockchainService: Error getting user wallet: $e');
      return null;
    }
  }

  Future<BlockchainWallet?> createWallet() async {
    try {
      await initialize();
      print('🔍 BlockchainService: Making request to /api/v1/blockchain/wallet/create/');
      final response = await _dio.post('/api/v1/blockchain/wallet/create/');
      print('🔍 BlockchainService: Create wallet response status: ${response.statusCode}');
      print('🔍 BlockchainService: Create wallet response data: ${response.data}');

      if (response.statusCode == 200 && response.data['success']) {
        print('✅ BlockchainService: Wallet created successfully');
        return BlockchainWallet.fromJson(response.data['wallet']);
      } else if (response.statusCode == 400 &&
                 response.data['message'] != null &&
                 response.data['message'].toString().contains('already exists')) {
        print('ℹ️ BlockchainService: Wallet already exists, trying to fetch existing wallet');
        // If wallet already exists, try to get the existing wallet
        return await getUserWallet();
      }
      print('⚠️ BlockchainService: Wallet creation failed or unsuccessful response');
      print('⚠️ BlockchainService: Response data: ${response.data}');
      return null;
    } catch (e) {
      print('❌ BlockchainService: Error creating wallet: $e');
      // If it's a DioException with 400 status (wallet already exists), try to get existing wallet
      if (e.toString().contains('400') && e.toString().contains('already exists')) {
        print('ℹ️ BlockchainService: Wallet already exists (from exception), trying to fetch existing wallet');
        return await getUserWallet();
      }
      return null;
    }
  }

  /// Get or create wallet - handles both scenarios gracefully
  Future<BlockchainWallet?> getOrCreateWallet() async {
    try {
      // First try to get existing wallet
      final existingWallet = await getUserWallet();
      if (existingWallet != null) {
        print('✅ BlockchainService: Found existing wallet: ${existingWallet.address}');
        return existingWallet;
      }

      // If no wallet exists, create one
      print('🔍 BlockchainService: No existing wallet, creating new one...');
      final newWallet = await createWallet();
      if (newWallet != null) {
        print('✅ BlockchainService: Successfully created/retrieved wallet: ${newWallet.address}');
      } else {
        print('❌ BlockchainService: Failed to create or retrieve wallet');
      }
      return newWallet;
    } catch (e) {
      print('❌ BlockchainService: Error in getOrCreateWallet: $e');
      return null;
    }
  }

  /// Send activation code to user's email
  Future<BlockchainApiResponse> sendActivationCode() async {
    try {
      await initialize();
      print('🔍 BlockchainService: Requesting activation code...');
      final response = await _dio.post('/api/v1/blockchain/wallet/send-activation-code/');
      print('🔍 BlockchainService: Send activation code response: ${response.statusCode}');

      if (response.statusCode == 200 && response.data['success']) {
        print('✅ BlockchainService: Activation code sent successfully');
        return BlockchainApiResponse(
          success: true,
          message: response.data['message'] ?? 'Activation code sent',
          data: response.data,
        );
      } else {
        print('⚠️ BlockchainService: Failed to send activation code');
        return BlockchainApiResponse(
          success: false,
          message: response.data['message'] ?? 'Failed to send activation code',
        );
      }
    } catch (e) {
      print('❌ BlockchainService: Error sending activation code: $e');
      return const BlockchainApiResponse(
        success: false,
        message: 'Error sending activation code',
      );
    }
  }

  /// Activate wallet with activation code
  Future<BlockchainApiResponse> activateWallet(String activationCode) async {
    try {
      await initialize();
      print('🔍 BlockchainService: Activating wallet with code...');
      final response = await _dio.post('/api/v1/blockchain/wallet/activate/', data: {
        'activation_code': activationCode,
      });
      print('🔍 BlockchainService: Activate wallet response: ${response.statusCode}');

      if (response.statusCode == 200 && response.data['success']) {
        print('✅ BlockchainService: Wallet activated successfully');
        return BlockchainApiResponse(
          success: true,
          message: response.data['message'] ?? 'Wallet activated successfully',
          data: response.data,
        );
      } else {
        print('⚠️ BlockchainService: Failed to activate wallet');
        return BlockchainApiResponse(
          success: false,
          message: response.data['message'] ?? 'Failed to activate wallet',
        );
      }
    } catch (e) {
      print('❌ BlockchainService: Error activating wallet: $e');
      return const BlockchainApiResponse(
        success: false,
        message: 'Invalid or expired activation code',
      );
    }
  }

  // NFT Management
  Future<List<NFTAsset>> getUserNFTs() async {
    try {
      await initialize();
      final response = await _dio.get('/api/v1/blockchain/nfts/');
      
      if (response.statusCode == 200 && response.data['success']) {
        final List<dynamic> nftsJson = response.data['nfts'];
        return nftsJson.map((json) => NFTAsset.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting user NFTs: $e');
      return [];
    }
  }

  Future<BlockchainApiResponse> mintAchievementNFT({
    required int userId,
    required String name,
    required String description,
    required int rarity,
    required String imageUrl,
    Map<String, dynamic>? attributes,
  }) async {
    try {
      await initialize();
      final response = await _dio.post('/api/v1/blockchain/nfts/mint/', data: {
        'user_id': userId,
        'name': name,
        'description': description,
        'rarity': rarity,
        'image_url': imageUrl,
        'attributes': attributes ?? {},
      });
      
      return BlockchainApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error minting NFT: $e');
      return const BlockchainApiResponse(
        success: false,
        message: 'Failed to mint NFT',
      );
    }
  }

  // Token Management
  Future<BlockchainApiResponse> rewardTokens({
    required int userId,
    required double amount,
    required String description,
  }) async {
    try {
      await initialize();
      final response = await _dio.post('/api/v1/blockchain/tokens/reward/', data: {
        'user_id': userId,
        'amount': amount,
        'description': description,
      });
      
      return BlockchainApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error rewarding tokens: $e');
      return const BlockchainApiResponse(
        success: false,
        message: 'Failed to reward tokens',
      );
    }
  }

  // Staking Management
  Future<List<StakingPool>> getStakingPools() async {
    try {
      await initialize();
      final response = await _dio.get('/api/v1/blockchain/staking/pools/');
      
      if (response.statusCode == 200 && response.data['success']) {
        final List<dynamic> poolsJson = response.data['pools'];
        return poolsJson.map((json) => StakingPool.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting staking pools: $e');
      return [];
    }
  }

  Future<BlockchainApiResponse> stakeTokens({
    required int poolId,
    required double amount,
  }) async {
    try {
      await initialize();
      final response = await _dio.post('/api/v1/blockchain/staking/stake/', data: {
        'pool_id': poolId,
        'amount': amount,
      });
      
      return BlockchainApiResponse.fromJson(response.data);
    } catch (e) {
      print('Error staking tokens: $e');
      return const BlockchainApiResponse(
        success: false,
        message: 'Failed to stake tokens',
      );
    }
  }

  Future<List<UserStake>> getUserStakes() async {
    try {
      await initialize();
      final response = await _dio.get('/api/v1/blockchain/staking/positions/');
      
      if (response.statusCode == 200 && response.data['success']) {
        final List<dynamic> stakesJson = response.data['stakes'];
        return stakesJson.map((json) => UserStake.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting user stakes: $e');
      return [];
    }
  }

  // Utility Methods
  Future<String> getTokenBalance() async {
    final wallet = await getUserWallet();
    if (wallet != null && wallet.balances.isNotEmpty) {
      // Find TRD token balance
      final trdBalance = wallet.balances.firstWhere(
        (balance) => balance.contractName.toLowerCase().contains('trendy'),
        orElse: () => wallet.balances.first,
      );
      return trdBalance.totalBalance;
    }
    return '0.00';
  }

  Future<double> getTokenBalanceAsDouble() async {
    final balance = await getTokenBalance();
    return double.tryParse(balance) ?? 0.0;
  }

  Future<int> getNFTCount() async {
    final nfts = await getUserNFTs();
    return nfts.length;
  }

  Future<Map<NFTRarity, int>> getNFTsByRarity() async {
    final nfts = await getUserNFTs();
    final Map<NFTRarity, int> rarityCount = {};
    
    for (final rarity in NFTRarity.values) {
      rarityCount[rarity] = 0;
    }
    
    for (final nft in nfts) {
      final rarity = NFTRarity.fromValue(nft.rarity);
      rarityCount[rarity] = (rarityCount[rarity] ?? 0) + 1;
    }
    
    return rarityCount;
  }

  Future<double> getTotalStakedAmount() async {
    final stakes = await getUserStakes();
    double total = 0.0;
    
    for (final stake in stakes) {
      total += double.tryParse(stake.amountStaked) ?? 0.0;
    }
    
    return total;
  }

  Future<double> getTotalPendingRewards() async {
    final stakes = await getUserStakes();
    double total = 0.0;
    
    for (final stake in stakes) {
      total += double.tryParse(stake.pendingRewards) ?? 0.0;
    }
    
    return total;
  }

  // Achievement Notifications (Local Storage)
  Future<List<AchievementNotification>> getAchievementNotifications() async {
    try {
      final notificationsJson = await PlatformStorageService.getSecureData('achievement_notifications');
      if (notificationsJson != null) {
        final List<dynamic> notifications = jsonDecode(notificationsJson);
        return notifications.map((json) => AchievementNotification.fromJson(json)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting achievement notifications: $e');
      return [];
    }
  }

  Future<void> addAchievementNotification(AchievementNotification notification) async {
    try {
      final notifications = await getAchievementNotifications();
      notifications.insert(0, notification); // Add to beginning
      
      // Keep only last 50 notifications
      if (notifications.length > 50) {
        notifications.removeRange(50, notifications.length);
      }
      
      final notificationsJson = jsonEncode(notifications.map((n) => n.toJson()).toList());
      await PlatformStorageService.setSecureData('achievement_notifications', notificationsJson);
    } catch (e) {
      print('Error adding achievement notification: $e');
    }
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      final notifications = await getAchievementNotifications();
      final updatedNotifications = notifications.map((notification) {
        if (notification.id == notificationId) {
          return notification.copyWith(isRead: true);
        }
        return notification;
      }).toList();
      
      final notificationsJson = jsonEncode(updatedNotifications.map((n) => n.toJson()).toList());
      await PlatformStorageService.setSecureData('achievement_notifications', notificationsJson);
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  Future<int> getUnreadNotificationCount() async {
    final notifications = await getAchievementNotifications();
    return notifications.where((n) => !n.isRead).length;
  }

  // Clear all local data (for logout)
  Future<void> clearLocalData() async {
    await PlatformStorageService.deleteSecureData('achievement_notifications');
  }
}
