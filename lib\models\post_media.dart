import 'package:freezed_annotation/freezed_annotation.dart';

part 'post_media.freezed.dart';
part 'post_media.g.dart';

@freezed
class PostMedia with _$PostMedia {
  const factory PostMedia({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'media_type') @Default('image') String mediaType,
    String? image,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'image_url') String? imageUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'image_url_full') String? imageUrlFull,
    String? video,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'video_url') String? videoUrl,
    String? thumbnail,
    @Default('') String caption,
    String? title,
    String? description,
    @Default(0) int order,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
  }) = _PostMedia;

  factory PostMedia.fromJson(Map<String, dynamic> json) => _$PostMediaFromJson(json);
}