import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/maintenance_provider.dart';

class MaintenanceBanner extends ConsumerWidget {
  final bool showOnlyWhenActive;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;

  const MaintenanceBanner({
    super.key,
    this.showOnlyWhenActive = true,
    this.margin,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final systemStatus = ref.watch(systemStatusProvider);
    
    return systemStatus.when(
      data: (status) {
        if (showOnlyWhenActive && !status.maintenanceActive) {
          return const SizedBox.shrink();
        }

        if (status.maintenanceActive && status.activeMaintenance != null) {
          return _buildMaintenanceBanner(context, status.activeMaintenance!);
        }

        return const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildMaintenanceBanner(BuildContext context, maintenance) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning_rounded,
                  color: colorScheme.error,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    maintenance.title.isNotEmpty 
                        ? maintenance.title 
                        : 'System Maintenance',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onErrorContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                _buildMaintenanceTypeChip(context, maintenance.maintenanceType),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              maintenance.message.isNotEmpty 
                  ? maintenance.message 
                  : 'The system is currently under maintenance. Some features may be unavailable.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onErrorContainer,
              ),
            ),
            if (maintenance.scheduledEnd != null) ...[
              const SizedBox(height: 12),
              _buildEstimatedCompletion(context, maintenance.scheduledEnd),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceTypeChip(BuildContext context, String type) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Color chipColor;
    String displayText;

    switch (type.toLowerCase()) {
      case 'emergency':
        chipColor = Colors.red;
        displayText = 'Emergency';
        break;
      case 'upgrade':
        chipColor = Colors.orange;
        displayText = 'Upgrade';
        break;
      case 'database':
        chipColor = Colors.blue;
        displayText = 'Database';
        break;
      case 'partial':
        chipColor = Colors.amber;
        displayText = 'Partial';
        break;
      default:
        chipColor = colorScheme.primary;
        displayText = 'Maintenance';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: chipColor.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Text(
        displayText,
        style: theme.textTheme.labelSmall?.copyWith(
          color: chipColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEstimatedCompletion(BuildContext context, DateTime endTime) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final now = DateTime.now();
    final duration = endTime.difference(now);

    String timeText;
    if (duration.isNegative) {
      timeText = 'Maintenance should have completed';
    } else if (duration.inDays > 0) {
      timeText = 'Estimated completion: ${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      timeText = 'Estimated completion: ${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      timeText = 'Estimated completion: ${duration.inMinutes}m';
    } else {
      timeText = 'Completing soon...';
    }

    return Row(
      children: [
        Icon(
          Icons.schedule_rounded,
          color: colorScheme.onErrorContainer.withValues(alpha: 0.7),
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          timeText,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onErrorContainer.withValues(alpha: 0.8),
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }
}

class MaintenanceStatusIndicator extends ConsumerWidget {
  final double size;
  final bool showText;

  const MaintenanceStatusIndicator({
    super.key,
    this.size = 24,
    this.showText = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMaintenanceActive = ref.watch(maintenanceStatusProvider);
    final theme = Theme.of(context);

    if (!isMaintenanceActive) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withValues(alpha: 0.3),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Icon(
            Icons.build_rounded,
            color: Colors.white,
            size: size * 0.6,
          ),
        ),
        if (showText) ...[
          const SizedBox(width: 8),
          Text(
            'Maintenance',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ],
    );
  }
}

class MaintenanceDialog extends ConsumerWidget {
  const MaintenanceDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final systemStatus = ref.watch(systemStatusProvider);
    
    return systemStatus.when(
      data: (status) {
        if (!status.maintenanceActive || status.activeMaintenance == null) {
          return const SizedBox.shrink();
        }

        return AlertDialog(
          icon: const Icon(
            Icons.warning_rounded,
            color: Colors.orange,
            size: 48,
          ),
          title: Text(
            status.activeMaintenance!.title.isNotEmpty 
                ? status.activeMaintenance!.title 
                : 'System Maintenance',
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                status.activeMaintenance!.message.isNotEmpty 
                    ? status.activeMaintenance!.message 
                    : 'The system is currently under maintenance. Some features may be unavailable.',
              ),
              if (status.activeMaintenance!.scheduledEnd != null) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.schedule, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Expected completion: ${_formatDateTime(status.activeMaintenance!.scheduledEnd!)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
