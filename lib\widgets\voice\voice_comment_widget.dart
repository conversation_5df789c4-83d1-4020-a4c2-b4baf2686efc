import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../models/voice_comment.dart';
import '../../services/voice_service.dart';
import '../../services/platform_audio_service.dart';
import '../../theme/app_theme.dart';
import '../platform_feature_handler.dart';

class VoiceCommentWidget extends ConsumerStatefulWidget {
  final VoiceComment voiceComment;
  final VoidCallback? onLike;
  final VoidCallback? onDelete;

  const VoiceCommentWidget({
    super.key,
    required this.voiceComment,
    this.onLike,
    this.onDelete,
  });

  @override
  ConsumerState<VoiceCommentWidget> createState() => _VoiceCommentWidgetState();
}

class _VoiceCommentWidgetState extends ConsumerState<VoiceCommentWidget>
    with TickerProviderStateMixin, AudioFeatureMixin {
  final VoiceService _voiceService = VoiceService();
  
  bool _isPlaying = false;
  bool _isLoading = false;
  double _playbackProgress = 0.0;
  Timer? _progressTimer;
  
  late AnimationController _waveAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _waveAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _progressTimer?.cancel();
    _waveAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  Future<void> _togglePlayback() async {
    if (_isPlaying) {
      await _stopPlayback();
    } else {
      await _startPlayback();
    }
  }

  Future<void> _startPlayback() async {
    setState(() {
      _isLoading = true;
    });

    await safeAudioOperation(
      () async {
        await _voiceService.playAudioFromUrl(widget.voiceComment.audioUrl);

        setState(() {
          _isPlaying = true;
          _isLoading = false;
        });

        _waveAnimationController.repeat();
        _pulseAnimationController.forward();

        // Start progress tracking
        _startProgressTracking();
      },
      customErrorMessage: 'Failed to play voice comment',
    );

    // Reset loading state if operation failed
    if (!_isPlaying) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _stopPlayback() async {
    await _voiceService.stopAudio();
    
    setState(() {
      _isPlaying = false;
      _playbackProgress = 0.0;
    });
    
    _waveAnimationController.stop();
    _pulseAnimationController.reverse();
    _progressTimer?.cancel();
  }

  void _startProgressTracking() {
    _progressTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isPlaying) {
        timer.cancel();
        return;
      }
      
      // Simulate progress (in a real app, you'd get this from the audio player)
      setState(() {
        _playbackProgress += 0.1 / widget.voiceComment.durationSeconds;
        if (_playbackProgress >= 1.0) {
          _playbackProgress = 1.0;
          _stopPlayback();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: widget.voiceComment.userAvatar != null
                      ? NetworkImage(widget.voiceComment.userAvatar!)
                      : null,
                  child: widget.voiceComment.userAvatar == null
                      ? Text(
                          widget.voiceComment.userName[0].toUpperCase(),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.voiceComment.userName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        _formatTimeAgo(widget.voiceComment.createdAt),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.onDelete != null)
                  IconButton(
                    onPressed: widget.onDelete,
                    icon: const Icon(Icons.more_vert),
                    iconSize: 20,
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Audio player
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  // Play/Pause button
                  ScaleTransition(
                    scale: _pulseAnimation,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        onPressed: _isLoading ? null : _togglePlayback,
                        icon: _isLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppTheme.primaryColor,
                                  ),
                                ),
                              )
                            : Icon(
                                _isPlaying ? Icons.pause : Icons.play_arrow,
                                color: AppTheme.primaryColor,
                                size: 24,
                              ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Waveform visualization
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Animated waveform
                        SizedBox(
                          height: 40,
                          child: AnimatedBuilder(
                            animation: _waveAnimation,
                            builder: (context, child) {
                              return CustomPaint(
                                painter: WaveformPainter(
                                  progress: _playbackProgress,
                                  isPlaying: _isPlaying,
                                  animationValue: _waveAnimation.value,
                                ),
                                size: const Size(double.infinity, 40),
                              );
                            },
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Duration and progress
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _formatDuration(
                                (_playbackProgress * widget.voiceComment.durationSeconds).round(),
                              ),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _formatDuration(widget.voiceComment.durationSeconds),
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Transcription (if available)
            if (widget.voiceComment.transcription != null &&
                widget.voiceComment.transcription!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.transcribe,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Transcription',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.voiceComment.transcription!,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // Actions
            Row(
              children: [
                InkWell(
                  onTap: widget.onLike,
                  borderRadius: BorderRadius.circular(20),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          widget.voiceComment.isLiked
                              ? Icons.favorite
                              : Icons.favorite_border,
                          size: 16,
                          color: widget.voiceComment.isLiked
                              ? Colors.red
                              : Colors.grey[600],
                        ),
                        if (widget.voiceComment.likeCount > 0) ...[
                          const SizedBox(width: 4),
                          Text(
                            '${widget.voiceComment.likeCount}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                InkWell(
                  onTap: () {
                    // Reply to voice comment
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.reply,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Reply',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

class WaveformPainter extends CustomPainter {
  final double progress;
  final bool isPlaying;
  final double animationValue;

  WaveformPainter({
    required this.progress,
    required this.isPlaying,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final activePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    const barCount = 30;
    final barWidth = size.width / barCount;
    
    for (int i = 0; i < barCount; i++) {
      final x = i * barWidth + barWidth / 2;
      final normalizedI = i / barCount;
      
      // Create varied heights for waveform effect
      final baseHeight = size.height * 0.3;
      final waveHeight = baseHeight + 
          (size.height * 0.4 * (0.5 + 0.5 * 
          (isPlaying ? animationValue : 0) * 
          (1 + 0.5 * (normalizedI * 2 - 1).abs())));
      
      final y1 = (size.height - waveHeight) / 2;
      final y2 = y1 + waveHeight;
      
      // Use active paint if this bar is within the progress
      final currentPaint = normalizedI <= progress ? activePaint : paint;
      
      canvas.drawLine(
        Offset(x, y1),
        Offset(x, y2),
        currentPaint,
      );
    }
  }

  @override
  bool shouldRepaint(WaveformPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.isPlaying != isPlaying ||
           oldDelegate.animationValue != animationValue;
  }
}
