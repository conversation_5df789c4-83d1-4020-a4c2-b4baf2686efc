import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:trendy/services/api_service.dart';
import 'package:trendy/services/payment_status_service.dart';
import 'package:trendy/services/payment_security_service.dart';
import 'package:trendy/services/error_reporting_service.dart';
import 'package:trendy/providers/store_provider.dart';
import 'package:trendy/models/reward_models.dart';

// Generate mocks
@GenerateMocks([ApiService])
import 'shop_functionality_test.mocks.dart';

void main() {
  group('Shop Functionality Tests', () {
    late MockApiService mockApiService;
    late ProviderContainer container;

    setUp(() {
      mockApiService = MockApiService();
      container = ProviderContainer(
        overrides: [
          // Override the API service provider with mock
          // apiServiceProvider.overrideWithValue(mockApiService),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Store Provider Tests', () {
      test('should load virtual items successfully', () async {
        // Arrange
        final mockResponse = {
          'success': true,
          'items': [
            {
              'id': '1',
              'name': 'Test Item',
              'description': 'Test Description',
              'points_cost': 100,
              'category': 'cosmetic',
              'rarity': 'common',
              'is_active': true,
            }
          ]
        };

        when(mockApiService.getVirtualItems()).thenAnswer((_) async => mockResponse);

        // Act
        final storeNotifier = StoreNotifier(mockApiService);
        await storeNotifier.loadVirtualItems();

        // Assert
        expect(storeNotifier.state.virtualItems.length, equals(1));
        expect(storeNotifier.state.virtualItems.first.name, equals('Test Item'));
        expect(storeNotifier.state.error, isNull);
      });

      test('should handle virtual items loading error', () async {
        // Arrange
        when(mockApiService.getVirtualItems()).thenThrow(Exception('Network error'));

        // Act
        final storeNotifier = StoreNotifier(mockApiService);
        await storeNotifier.loadVirtualItems();

        // Assert
        expect(storeNotifier.state.virtualItems.length, equals(0));
        expect(storeNotifier.state.error, isNotNull);
        expect(storeNotifier.state.error, contains('Network error'));
      });

      test('should load point boost packages successfully', () async {
        // Arrange
        final mockResponse = {
          'success': true,
          'packages': [
            {
              'id': '1',
              'name': 'Small Boost',
              'description': 'Get 100 points',
              'base_points': 100,
              'bonus_points': 0,
              'total_points': 100,
              'price': '1.99',
              'currency': 'USD',
              'is_active': true,
              'is_popular': false,
            }
          ]
        };

        when(mockApiService.getPointBoostPackages()).thenAnswer((_) async => mockResponse);

        // Act
        final storeNotifier = StoreNotifier(mockApiService);
        await storeNotifier.loadPointBoosts();

        // Assert
        expect(storeNotifier.state.pointBoosts.length, equals(1));
        expect(storeNotifier.state.pointBoosts.first.name, equals('Small Boost'));
        expect(storeNotifier.state.error, isNull);
      });

      test('should handle point boost loading error', () async {
        // Arrange
        final mockResponse = {
          'success': false,
          'message': 'Failed to load packages'
        };

        when(mockApiService.getPointBoostPackages()).thenAnswer((_) async => mockResponse);

        // Act
        final storeNotifier = StoreNotifier(mockApiService);
        await storeNotifier.loadPointBoosts();

        // Assert
        expect(storeNotifier.state.pointBoosts.length, equals(0));
        expect(storeNotifier.state.error, isNotNull);
        expect(storeNotifier.state.error, contains('Failed to load point boost packages'));
      });

      test('should purchase virtual item successfully', () async {
        // Arrange
        final mockResponse = {
          'success': true,
          'message': 'Item purchased successfully',
          'remaining_points': 50
        };

        when(mockApiService.purchaseVirtualItem('1')).thenAnswer((_) async => mockResponse);

        // Act
        final storeNotifier = StoreNotifier(mockApiService);
        final result = await storeNotifier.purchaseVirtualItem('1');

        // Assert
        expect(result['success'], isTrue);
        expect(result['message'], equals('Item purchased successfully'));
      });

      test('should handle virtual item purchase failure', () async {
        // Arrange
        final mockResponse = {
          'success': false,
          'error': 'Insufficient points'
        };

        when(mockApiService.purchaseVirtualItem('1')).thenAnswer((_) async => mockResponse);

        // Act
        final storeNotifier = StoreNotifier(mockApiService);
        final result = await storeNotifier.purchaseVirtualItem('1');

        // Assert
        expect(result['success'], isFalse);
        expect(result['message'], equals('Insufficient points'));
      });
    });

    group('Payment Security Tests', () {
      test('should validate payment request successfully', () async {
        // Act
        final result = await PaymentSecurityService.validatePayment(
          amount: 10.0,
          paymentMethod: 'paypal',
          currency: 'USD',
        );

        // Assert
        expect(result.isValid, isTrue);
        expect(result.error, isNull);
      });

      test('should reject invalid payment amount', () async {
        // Act
        final result = await PaymentSecurityService.validatePayment(
          amount: -5.0,
          paymentMethod: 'paypal',
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.error, contains('greater than zero'));
      });

      test('should reject excessive payment amount', () async {
        // Act
        final result = await PaymentSecurityService.validatePayment(
          amount: 15000.0,
          paymentMethod: 'paypal',
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.error, contains('exceeds maximum limit'));
      });

      test('should reject invalid payment method', () async {
        // Act
        final result = await PaymentSecurityService.validatePayment(
          amount: 10.0,
          paymentMethod: 'invalid_method',
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.error, contains('Payment method is required'));
      });

      test('should reject invalid currency', () async {
        // Act
        final result = await PaymentSecurityService.validatePayment(
          amount: 10.0,
          paymentMethod: 'paypal',
          currency: 'INVALID',
        );

        // Assert
        expect(result.isValid, isFalse);
        expect(result.error, contains('Unsupported currency'));
      });

      test('should generate and verify payment token', () {
        // Arrange
        const userId = 'user123';
        const amount = 10.0;
        const timestamp = '2024-01-01T00:00:00Z';

        // Act
        final token = PaymentSecurityService.generatePaymentToken(userId, amount, timestamp);
        final isValid = PaymentSecurityService.verifyPaymentToken(token, userId, amount, timestamp);

        // Assert
        expect(token, isNotEmpty);
        expect(isValid, isTrue);
      });

      test('should reject invalid payment token', () {
        // Arrange
        const userId = 'user123';
        const amount = 10.0;
        const timestamp = '2024-01-01T00:00:00Z';
        const invalidToken = 'invalid_token';

        // Act
        final isValid = PaymentSecurityService.verifyPaymentToken(invalidToken, userId, amount, timestamp);

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Error Handling Tests', () {
      test('should provide user-friendly error messages for PayPal errors', () {
        // Arrange
        final paypalError = Exception('PayPal account not verified');

        // Act
        final message = ErrorReportingService.getPaymentErrorMessage(paypalError);

        // Assert
        expect(message, contains('verify your PayPal account'));
      });

      test('should provide user-friendly error messages for insufficient points', () {
        // Arrange
        final pointsError = Exception('Insufficient points for purchase');

        // Act
        final message = ErrorReportingService.getPaymentErrorMessage(pointsError);

        // Assert
        expect(message, contains('don\'t have enough store points'));
      });

      test('should provide user-friendly error messages for network errors', () {
        // Arrange
        final networkError = Exception('SocketException: Network unreachable');

        // Act
        final message = ErrorReportingService.getPaymentErrorMessage(networkError);

        // Assert
        expect(message, contains('check your internet connection'));
      });

      test('should provide context-specific error messages', () {
        // Arrange
        final error = Exception('Generic error');

        // Act
        final message = ErrorReportingService.getPaymentErrorMessage(
          error,
          context: 'premium_subscription',
        );

        // Assert
        expect(message, contains('premium subscription'));
      });
    });

    group('Payment Status Service Tests', () {
      test('should create payment status from JSON', () {
        // Arrange
        final json = {
          'id': 'txn_123',
          'status': 'completed',
          'amount': '10.00',
          'currency': 'USD',
          'payment_method': 'paypal',
          'created_at': '2024-01-01T00:00:00Z',
          'completed_at': '2024-01-01T00:05:00Z',
        };

        // Act
        final status = PaymentStatus.fromJson(json);

        // Assert
        expect(status.transactionId, equals('txn_123'));
        expect(status.status, equals('completed'));
        expect(status.amount, equals(10.0));
        expect(status.currency, equals('USD'));
        expect(status.isCompleted, isTrue);
        expect(status.isPending, isFalse);
        expect(status.isFailed, isFalse);
      });

      test('should correctly identify payment status states', () {
        // Test pending status
        final pendingStatus = PaymentStatus(
          transactionId: 'txn_1',
          status: 'pending',
          amount: 10.0,
          currency: 'USD',
          paymentMethod: 'paypal',
          createdAt: DateTime.now(),
        );

        expect(pendingStatus.isPending, isTrue);
        expect(pendingStatus.isCompleted, isFalse);
        expect(pendingStatus.isFailed, isFalse);

        // Test completed status
        final completedStatus = PaymentStatus(
          transactionId: 'txn_2',
          status: 'completed',
          amount: 10.0,
          currency: 'USD',
          paymentMethod: 'paypal',
          createdAt: DateTime.now(),
        );

        expect(completedStatus.isPending, isFalse);
        expect(completedStatus.isCompleted, isTrue);
        expect(completedStatus.isFailed, isFalse);

        // Test failed status
        final failedStatus = PaymentStatus(
          transactionId: 'txn_3',
          status: 'failed',
          amount: 10.0,
          currency: 'USD',
          paymentMethod: 'paypal',
          createdAt: DateTime.now(),
        );

        expect(failedStatus.isPending, isFalse);
        expect(failedStatus.isCompleted, isFalse);
        expect(failedStatus.isFailed, isTrue);
      });
    });
  });
}
