import 'package:flutter/material.dart';
import '../services/platform_audio_service.dart';
import '../services/platform_storage_service.dart';

/// Widget that handles platform-specific feature availability
class PlatformFeatureHandler extends StatelessWidget {
  final Widget child;
  final VoidCallback? onFeatureUnavailable;
  final String? customMessage;

  const PlatformFeatureHandler({
    Key? key,
    required this.child,
    this.onFeatureUnavailable,
    this.customMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return child;
  }

  /// Show a dialog explaining why a feature is not available
  static void showFeatureUnavailableDialog(
    BuildContext context, {
    required String feature,
    required String reason,
    String? solution,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning_amber, color: Colors.orange),
            const SizedBox(width: 8),
            Text('$feature Unavailable'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(reason),
            if (solution != null) ...[
              const SizedBox(height: 16),
              const Text(
                'Solution:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(solution),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show audio unavailable dialog
  static void showAudioUnavailableDialog(BuildContext context) {
    showFeatureUnavailableDialog(
      context,
      feature: 'Audio Playback',
      reason: PlatformAudioService.platformMessage,
      solution: 'Install required system dependencies or use a mobile device for full audio support.',
    );
  }

  /// Show recording unavailable dialog
  static void showRecordingUnavailableDialog(BuildContext context) {
    showFeatureUnavailableDialog(
      context,
      feature: 'Audio Recording',
      reason: 'Audio recording is not supported on this platform.',
      solution: 'Use a mobile device for voice recording features.',
    );
  }

  /// Show secure storage warning dialog
  static void showSecureStorageWarningDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: Colors.orange),
            const SizedBox(width: 8),
            const Text('Security Notice'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Currently using: ${PlatformStorageService.storageType}'),
            const SizedBox(height: 16),
            if (!PlatformStorageService.isSecureStorageAvailable) ...[
              const Text(
                'Your authentication data is stored using a fallback method that may be less secure.',
                style: TextStyle(color: Colors.orange),
              ),
              const SizedBox(height: 16),
              const Text(
                'Recommendation:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(PlatformStorageService.securityRecommendations),
            ] else ...[
              const Text(
                'Your authentication data is stored securely.',
                style: TextStyle(color: Colors.green),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Mixin for widgets that use audio features
mixin AudioFeatureMixin<T extends StatefulWidget> on State<T> {
  /// Safely execute audio operations with error handling
  Future<void> safeAudioOperation(
    Future<void> Function() audioOperation, {
    String? customErrorMessage,
  }) async {
    try {
      await audioOperation();
    } on AudioNotSupportedException catch (e) {
      if (mounted) {
        PlatformFeatureHandler.showAudioUnavailableDialog(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(customErrorMessage ?? 'Audio operation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Safely execute recording operations with error handling
  Future<void> safeRecordingOperation(
    Future<void> Function() recordingOperation, {
    String? customErrorMessage,
  }) async {
    try {
      await recordingOperation();
    } on AudioNotSupportedException catch (e) {
      if (mounted) {
        PlatformFeatureHandler.showRecordingUnavailableDialog(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(customErrorMessage ?? 'Recording operation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check if audio features are available and show appropriate UI
  Widget buildAudioButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isRecording = false,
  }) {
    final isSupported = isRecording 
        ? PlatformAudioService.isRecordingSupported 
        : PlatformAudioService.isAudioSupported;

    return ElevatedButton.icon(
      onPressed: isSupported ? onPressed : () {
        if (isRecording) {
          PlatformFeatureHandler.showRecordingUnavailableDialog(context);
        } else {
          PlatformFeatureHandler.showAudioUnavailableDialog(context);
        }
      },
      icon: Icon(
        icon,
        color: isSupported ? null : Colors.grey,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isSupported ? null : Colors.grey,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isSupported ? null : Colors.grey[200],
      ),
    );
  }
}

/// Widget that shows platform compatibility status
class PlatformCompatibilityIndicator extends StatelessWidget {
  const PlatformCompatibilityIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Platform Compatibility',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureStatus(
              'Audio Playback',
              PlatformAudioService.isAudioSupported,
              PlatformAudioService.isAudioSupported 
                  ? 'Fully supported' 
                  : PlatformAudioService.platformMessage,
            ),
            _buildFeatureStatus(
              'Audio Recording',
              PlatformAudioService.isRecordingSupported,
              PlatformAudioService.isRecordingSupported 
                  ? 'Fully supported' 
                  : 'Not supported on this platform',
            ),
            _buildFeatureStatus(
              'Secure Storage',
              PlatformStorageService.isSecureStorageAvailable,
              PlatformStorageService.isSecureStorageAvailable 
                  ? 'Fully secure' 
                  : 'Using fallback storage',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureStatus(String feature, bool isSupported, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            isSupported ? Icons.check_circle : Icons.warning,
            color: isSupported ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
