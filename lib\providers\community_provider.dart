import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../services/connectivity_service.dart';
import '../services/offline_storage_service.dart';
import '../models/post.dart';
import '../models/user.dart';
import '../models/app_error.dart';
import 'provider.dart';
import 'offline_provider.dart';

// Community State
class CommunityState {
  final bool isLoading;
  final String? error;
  final List<dynamic> discoverUsers;
  final List<dynamic> trendingUsers;
  final List<dynamic> following;
  final List<dynamic> followers;
  final List<dynamic> searchResults;
  final UserProfile? selectedUserProfile;
  final List<Post> userPosts;
  final List<dynamic> userFollowers;
  final List<dynamic> userFollowing;

  CommunityState({
    this.isLoading = false,
    this.error,
    this.discoverUsers = const [],
    this.trendingUsers = const [],
    this.following = const [],
    this.followers = const [],
    this.searchResults = const [],
    this.selectedUserProfile,
    this.userPosts = const [],
    this.userFollowers = const [],
    this.userFollowing = const [],
  });

  CommunityState copyWith({
    bool? isLoading,
    String? error,
    List<dynamic>? discoverUsers,
    List<dynamic>? trendingUsers,
    List<dynamic>? following,
    List<dynamic>? followers,
    List<dynamic>? searchResults,
    UserProfile? selectedUserProfile,
    List<Post>? userPosts,
    List<dynamic>? userFollowers,
    List<dynamic>? userFollowing,
  }) {
    return CommunityState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      discoverUsers: discoverUsers ?? this.discoverUsers,
      trendingUsers: trendingUsers ?? this.trendingUsers,
      following: following ?? this.following,
      followers: followers ?? this.followers,
      searchResults: searchResults ?? this.searchResults,
      selectedUserProfile: selectedUserProfile ?? this.selectedUserProfile,
      userPosts: userPosts ?? this.userPosts,
      userFollowers: userFollowers ?? this.userFollowers,
      userFollowing: userFollowing ?? this.userFollowing,
    );
  }
}

// Community Notifier
class CommunityNotifier extends StateNotifier<CommunityState> {
  final ApiService _apiService;

  CommunityNotifier(this._apiService) : super(CommunityState());

  // Load discover users
  Future<void> loadDiscoverUsers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getSocialDiscover();
      if (response.isSuccess) {
        final users = response.data as List? ?? [];
        state = state.copyWith(
          discoverUsers: users,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load discover users',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Error loading discover users: $e',
        isLoading: false,
      );
    }
  }

  // Load trending users
  Future<void> loadTrendingUsers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getSocialTrending();
      if (response.isSuccess) {
        final users = response.data as List? ?? [];
        state = state.copyWith(
          trendingUsers: users,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load trending users',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Error loading trending users: $e',
        isLoading: false,
      );
    }
  }

  // Load user's following list
  Future<void> loadFollowing() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getSocialFollowing();
      if (response.isSuccess) {
        final users = response.data as List? ?? [];
        state = state.copyWith(
          following: users,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load following',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Error loading following: $e',
        isLoading: false,
      );
    }
  }

  // Load user's followers list
  Future<void> loadFollowers() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getSocialFollowers();
      if (response.isSuccess) {
        final users = response.data as List? ?? [];
        state = state.copyWith(
          followers: users,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load followers',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Error loading followers: $e',
        isLoading: false,
      );
    }
  }

  // Search users
  Future<void> searchUsers(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(searchResults: []);
      return;
    }

    try {
      final response = await _apiService.searchSocialUsers(query);
      if (response.isSuccess) {
        final users = response.data as List? ?? [];
        state = state.copyWith(searchResults: users);
      }
    } catch (e) {
      print('Error searching users: $e');
    }
  }

  // Toggle follow/unfollow user
  Future<void> toggleFollow(String username) async {
    try {
      final response = await _apiService.toggleFollow(username);

      // The existing toggleFollow returns Map<String, dynamic>
      final isFollowing = response['following'] == true;

      // Update the user in all relevant lists
      _updateUserFollowStatus(username, isFollowing);

      // Refresh followers and following lists to ensure synchronization
      await _refreshFollowLists();

      // Show success message
      print(response['message'] ?? 'Follow status updated');
    } catch (e) {
      print('Error toggling follow: $e');
    }
  }

  // Load specific user profile
  Future<void> loadUserProfile(String username) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getUserProfile(username);
      // Convert Map<String, dynamic> to UserProfile
      final userProfile = UserProfile.fromJson(response);
      state = state.copyWith(
        selectedUserProfile: userProfile,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Error loading user profile: $e',
        isLoading: false,
      );
    }
  }

  // Load user's posts
  Future<void> loadUserPosts(String username) async {
    try {
      final posts = await _apiService.getUserPosts(username);
      // The existing getUserPosts returns List<Post>
      state = state.copyWith(userPosts: posts);
    } catch (e) {
      print('Error loading user posts: $e');
    }
  }

  // Load user's followers
  Future<void> loadUserFollowers(String username) async {
    try {
      final response = await _apiService.getUserFollowers(username);
      // The existing getUserFollowers returns Map<String, dynamic>
      final users = response['results'] as List? ?? [];
      state = state.copyWith(userFollowers: users);
    } catch (e) {
      print('Error loading user followers: $e');
    }
  }

  // Load user's following
  Future<void> loadUserFollowing(String username) async {
    try {
      final response = await _apiService.getUserFollowing(username);
      // The existing getUserFollowing returns Map<String, dynamic>
      final users = response['results'] as List? ?? [];
      state = state.copyWith(userFollowing: users);
    } catch (e) {
      print('Error loading user following: $e');
    }
  }

  // Refresh followers and following lists after follow/unfollow actions
  Future<void> _refreshFollowLists() async {
    try {
      // Refresh both lists in parallel for better performance
      await Future.wait([
        loadFollowing(),
        loadFollowers(),
      ]);
    } catch (e) {
      print('Error refreshing follow lists: $e');
    }
  }

  // Helper method to update follow status across all lists
  void _updateUserFollowStatus(String username, bool isFollowing) {
    // Update discover users
    final updatedDiscoverUsers = state.discoverUsers.map((user) {
      if (_getUsernameFromUser(user) == username) {
        return _updateUserObject(user, isFollowing);
      }
      return user;
    }).toList();

    // Update trending users
    final updatedTrendingUsers = state.trendingUsers.map((user) {
      if (_getUsernameFromUser(user) == username) {
        return _updateUserObject(user, isFollowing);
      }
      return user;
    }).toList();

    // Update search results
    final updatedSearchResults = state.searchResults.map((user) {
      if (_getUsernameFromUser(user) == username) {
        return _updateUserObject(user, isFollowing);
      }
      return user;
    }).toList();

    // Update selected user profile
    UserProfile? updatedSelectedProfile = state.selectedUserProfile;
    if (_getUsernameFromUser(updatedSelectedProfile) == username) {
      updatedSelectedProfile =
          _updateUserObject(updatedSelectedProfile, isFollowing)
              as UserProfile?;
    }

    state = state.copyWith(
      discoverUsers: updatedDiscoverUsers,
      trendingUsers: updatedTrendingUsers,
      searchResults: updatedSearchResults,
      selectedUserProfile: updatedSelectedProfile,
    );
  }

  // Helper method to update user object with new follow status
  dynamic _updateUserObject(dynamic user, bool isFollowing) {
    if (user is UserProfile) {
      // Handle UserProfile objects properly
      final newFollowersCount = user.followersCount + (isFollowing ? 1 : -1);
      return user.copyWith(
        isFollowing: isFollowing,
        followersCount: newFollowersCount,
      );
    } else if (user is Map<String, dynamic>) {
      // Handle Map objects for other user lists
      final currentFollowersCount =
          user['followers_count'] ?? user['followersCount'] ?? 0;
      final newFollowersCount = currentFollowersCount + (isFollowing ? 1 : -1);

      return {
        ...user,
        'is_following': isFollowing,
        'isFollowing': isFollowing, // Keep both for compatibility
        'followers_count': newFollowersCount,
        'followersCount': newFollowersCount, // Keep both for compatibility
      };
    }
    return user;
  }

  // Refresh all community data
  Future<void> refreshAllData() async {
    try {
      await Future.wait([
        loadDiscoverUsers(),
        loadTrendingUsers(),
        loadFollowing(),
        loadFollowers(),
      ]);
    } catch (e) {
      print('Error refreshing all community data: $e');
    }
  }

  // Helper method to safely get username from user object
  String _getUsernameFromUser(dynamic user) {
    if (user is UserProfile) {
      return user.username;
    } else if (user is Map<String, dynamic>) {
      return user['username'] ??
          user['follower_username'] ??
          user['following_username'] ??
          '';
    }
    return '';
  }

  // Clear state
  void clearState() {
    state = CommunityState();
  }
}

// Provider
final communityProvider =
    StateNotifierProvider<CommunityNotifier, CommunityState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return CommunityNotifier(apiService);
});
