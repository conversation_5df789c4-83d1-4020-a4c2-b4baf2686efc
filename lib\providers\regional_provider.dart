import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/models/country.dart';
import 'package:trendy/services/regional_service.dart';
import 'package:trendy/services/auth_service.dart';

// Provider for available countries
final countriesProvider = FutureProvider<List<Country>>((ref) async {
  return await RegionalService.getCountries();
});

// Provider for available regions
final regionsProvider = FutureProvider<List<Region>>((ref) async {
  return await RegionalService.getRegions();
});

// Provider for user's regional preferences
final regionalPreferencesProvider =
    FutureProvider<RegionalPreferences>((ref) async {
  return await RegionalService.getUserRegionalPreferences();
});

// Provider for regional statistics
final regionalStatsProvider = FutureProvider<RegionalStats>((ref) async {
  return await RegionalService.getRegionalStats();
});

// Provider for location history
final locationHistoryProvider =
    FutureProvider<List<LocationHistory>>((ref) async {
  return await RegionalService.getLocationHistory();
});

// State provider for selected country (for UI state)
final selectedCountryProvider = StateProvider<Country?>((ref) => null);

// State provider for showing global content toggle
final showGlobalContentProvider = StateProvider<bool>((ref) => true);

// State provider for auto-detect location toggle
final autoDetectLocationProvider = StateProvider<bool>((ref) => true);

// Provider for checking if user has regional preferences
final hasRegionalPreferencesProvider = FutureProvider<bool>((ref) async {
  return await RegionalService.hasRegionalPreferences();
});

// Notifier for managing regional preferences
class RegionalPreferencesNotifier
    extends StateNotifier<AsyncValue<RegionalPreferences>> {
  final Ref? _ref;

  RegionalPreferencesNotifier([this._ref]) : super(const AsyncValue.loading()) {
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    try {
      state = const AsyncValue.loading();

      // Check if user is authenticated first
      final token = await AuthService.getToken();
      if (token == null) {
        print('🔒 User not authenticated, using default preferences');
        // User not authenticated, provide default preferences
        state = AsyncValue.data(const RegionalPreferences(
          showGlobalContent: true,
          autoDetectLocation: true,
        ));
        return;
      }

      print('🔄 Loading regional preferences from API...');
      final preferences = await RegionalService.getUserRegionalPreferences();
      print(
          '✅ Loaded preferences: preferred=${preferences.preferredCountry?.code}, effective=${preferences.effectiveCountry?.code}, showGlobal=${preferences.showGlobalContent}');
      state = AsyncValue.data(preferences);
    } catch (error) {
      print('❌ Error loading preferences: $error');
      // If there's an error, provide default preferences instead of error state
      state = AsyncValue.data(const RegionalPreferences(
        showGlobalContent: true,
        autoDetectLocation: true,
      ));
    }
  }

  Future<void> setPreferredCountry(String countryCode) async {
    try {
      final success = await RegionalService.setPreferredCountry(countryCode);
      if (success) {
        await _loadPreferences(); // Reload preferences
        _invalidateRelatedProviders(); // Force refresh related providers
        print('✅ Preferred country set to: $countryCode');
      } else {
        throw Exception('Failed to set preferred country');
      }
    } catch (error, stackTrace) {
      print('❌ Error setting preferred country: $error');
      state = AsyncValue.error(error, stackTrace);
      rethrow; // Re-throw so UI can handle it
    }
  }

  Future<void> clearPreferredCountry() async {
    try {
      final success = await RegionalService.clearPreferredCountry();
      if (success) {
        await _loadPreferences(); // Reload preferences
        _invalidateRelatedProviders(); // Force refresh related providers
      } else {
        throw Exception('Failed to clear preferred country');
      }
    } catch (error, stackTrace) {
      print('Error clearing preferred country: $error');
      state = AsyncValue.error(error, stackTrace);
      rethrow; // Re-throw so UI can handle it
    }
  }

  Future<void> updateSettings({
    bool? showGlobalContent,
    bool? autoDetectLocation,
  }) async {
    try {
      final success = await RegionalService.updateRegionalSettings(
        showGlobalContent: showGlobalContent,
        autoDetectLocation: autoDetectLocation,
      );
      if (success) {
        await _loadPreferences(); // Reload preferences
      } else {
        throw Exception('Failed to update regional settings');
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> detectLocation() async {
    try {
      final detectedCountry = await RegionalService.detectLocation();
      if (detectedCountry != null) {
        await _loadPreferences(); // Reload preferences to get updated detected country
      } else {
        throw Exception('Could not detect location');
      }
    } catch (error, stackTrace) {
      print('Error detecting location: $error');
      state = AsyncValue.error(error, stackTrace);
      rethrow; // Re-throw so UI can handle it
    }
  }

  void refresh() {
    _loadPreferences();
  }

  void _invalidateRelatedProviders() {
    // Force refresh of related providers that depend on regional preferences
    if (_ref != null) {
      print('🔄 Invalidating related providers...');
      _ref!.invalidate(regionalStatsProvider);
      // Note: effectiveCountryProvider and regionalPostsFilterProvider will automatically
      // refresh when this notifier's state changes since they watch it
    }
  }
}

// Provider for the regional preferences notifier
final regionalPreferencesNotifierProvider = StateNotifierProvider<
    RegionalPreferencesNotifier, AsyncValue<RegionalPreferences>>((ref) {
  return RegionalPreferencesNotifier(ref);
});

// Computed provider for effective country
final effectiveCountryProvider = Provider<Country?>((ref) {
  final preferencesAsync = ref.watch(regionalPreferencesNotifierProvider);
  return preferencesAsync.when(
    data: (preferences) {
      final effectiveCountry = preferences.effectiveCountry;
      print(
          '🎯 Effective Country: ${effectiveCountry?.name ?? 'null'} (${effectiveCountry?.code ?? 'null'})');
      return effectiveCountry;
    },
    loading: () {
      print('⏳ Effective Country: Loading...');
      return null;
    },
    error: (_, __) {
      print('❌ Effective Country: Error');
      return null;
    },
  );
});

// Provider for country search/filtering
final countrySearchProvider = StateProvider<String>((ref) => '');

// Filtered countries provider based on search
final filteredCountriesProvider = Provider<AsyncValue<List<Country>>>((ref) {
  final countriesAsync = ref.watch(countriesProvider);
  final searchQuery = ref.watch(countrySearchProvider);

  return countriesAsync.when(
    data: (countries) {
      if (searchQuery.isEmpty) {
        return AsyncValue.data(countries);
      }
      final filtered = countries
          .where((country) =>
              country.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
              country.code.toLowerCase().contains(searchQuery.toLowerCase()))
          .toList();
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Provider for popular/priority countries
final popularCountriesProvider = Provider<AsyncValue<List<Country>>>((ref) {
  final countriesAsync = ref.watch(countriesProvider);

  return countriesAsync.when(
    data: (countries) {
      final popular =
          countries.where((country) => country.priority > 50).toList();
      popular.sort((a, b) => b.priority.compareTo(a.priority));
      return AsyncValue.data(popular.take(10).toList());
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Provider for African countries (since the app seems focused on Africa)
final africanCountriesProvider = Provider<AsyncValue<List<Country>>>((ref) {
  final regionsAsync = ref.watch(regionsProvider);

  return regionsAsync.when(
    data: (regions) {
      final africaRegion = regions.firstWhere(
        (region) => region.code == 'AF',
        orElse: () => const Region(id: 0, name: '', code: ''),
      );

      if (africaRegion.id == 0) {
        return const AsyncValue.data([]);
      }

      // This would need to be implemented properly with countries per region
      // For now, return empty list
      return const AsyncValue.data([]);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Provider for regional posts filtering parameters
final regionalPostsFilterProvider = Provider<Map<String, dynamic>>((ref) {
  final effectiveCountry = ref.watch(effectiveCountryProvider);
  final preferencesAsync = ref.watch(regionalPreferencesNotifierProvider);

  return preferencesAsync.when(
    data: (preferences) {
      final Map<String, dynamic> filters = {};

      // If user wants to show global content only (no country selected)
      if (preferences.showGlobalContent && effectiveCountry == null) {
        filters['show_global'] = true;
        print('🌍 Regional Filter: Global content only');
      }
      // If user has a country selected
      else if (effectiveCountry != null) {
        filters['country'] = effectiveCountry.code;
        // If user also wants to see global content along with regional content
        if (preferences.showGlobalContent) {
          filters['show_global'] = true;
          print(
              '🏳️ Regional Filter: ${effectiveCountry.code} + Global content');
        } else {
          print('🏳️ Regional Filter: ${effectiveCountry.code} only');
        }
      } else {
        print('❓ Regional Filter: No filters applied');
      }

      print('📊 Final filters: $filters');
      return filters;
    },
    loading: () => <String, dynamic>{},
    error: (_, __) => <String, dynamic>{},
  );
});
