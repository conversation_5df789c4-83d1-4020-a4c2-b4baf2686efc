# 🔧 Blockchain Setup Fix

## Issue
When creating superusers, you're getting this error:
```
Error creating blockchain wallet for user cheru: BlockchainNetwork matching query does not exist.
```

## Root Cause
The blockchain system tries to automatically create wallets for new users, but the `BlockchainNetwork` table is empty.

## Quick Fix

### Option 1: Use the Management Command (Recommended)
```bash
# Navigate to your Django project
cd trendy_web_and_api/trendy

# Run the blockchain setup command
python manage.py setup_blockchain

# This will create:
# - Blockchain networks (Polygon, BSC, etc.)
# - Basic smart contracts
# - Missing wallets for existing users
```

### Option 2: Use the Fix Script
```bash
# Navigate to the trendy folder
cd trendy

# Run the fix script
python fix_blockchain_setup.py

# This does the same as Option 1 but with more detailed output
```

### Option 3: Manual Setup (Advanced)
```bash
# Open Django shell
cd trendy_web_and_api/trendy
python manage.py shell

# Run this Python code:
from django.conf import settings
from blockchain.models import BlockchainNetwork

# Create Polygon Testnet (default)
network, created = BlockchainNetwork.objects.get_or_create(
    name='polygon_testnet',
    defaults={
        'chain_id': 80001,
        'rpc_url': 'https://rpc-mumbai.maticvigil.com',
        'explorer_url': 'https://mumbai.polygonscan.com',
        'native_token': 'MATIC',
        'gas_price_gwei': 20,
        'is_active': True
    }
)

print(f"Network created: {created}")
exit()
```

## Verification

After running any of the above options, you should be able to:

1. **Create superusers without errors:**
   ```bash
   python manage.py createsuperuser --username newuser --email <EMAIL>
   ```

2. **Check that blockchain networks exist:**
   ```bash
   python manage.py shell
   >>> from blockchain.models import BlockchainNetwork
   >>> BlockchainNetwork.objects.all()
   >>> exit()
   ```

3. **Verify wallets are created:**
   ```bash
   python manage.py shell
   >>> from blockchain.models import UserWalletAddress
   >>> UserWalletAddress.objects.all()
   >>> exit()
   ```

## What the Fix Does

1. **Creates Blockchain Networks:**
   - Polygon Testnet (default for development)
   - Polygon Mainnet
   - Binance Smart Chain

2. **Creates Basic Smart Contracts:**
   - TRD Token contract (mock for development)
   - Achievement NFT contract (mock for development)

3. **Creates Missing Wallets:**
   - Finds users without blockchain wallets
   - Creates inactive wallets for them
   - Generates activation codes

## Future Prevention

The blockchain signal has been updated to gracefully handle missing networks. If you create users before setting up blockchain networks, they simply won't get wallets until you run the setup.

## Production Notes

- The created contracts use mock addresses for development
- Replace with real deployed contract addresses for production
- Set up proper email sending for activation codes
- Configure proper RPC endpoints for mainnet usage

## Need Help?

If you encounter any issues:

1. Check Django logs for detailed error messages
2. Ensure all migrations are applied: `python manage.py migrate`
3. Verify settings.py has correct BLOCKCHAIN_NETWORKS configuration
4. Make sure you're in the correct virtual environment
