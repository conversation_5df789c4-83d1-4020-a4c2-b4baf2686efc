#!/bin/bash

# 🗄️ SQLite Database Backup Script
# This script creates a backup of your SQLite database with timestamp

set -e  # Exit on any error

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🗄️ SQLite Database Backup Script${NC}"
echo "=================================="

# Configuration
DB_PATH="trendy_web_and_api/trendy/db.sqlite3"
BACKUP_DIR="database_backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="db_backup_${TIMESTAMP}.sqlite3"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Check if database exists
if [ ! -f "$DB_PATH" ]; then
    echo -e "${YELLOW}⚠️ Database file not found: $DB_PATH${NC}"
    echo "Make sure you're running this from the project root directory."
    exit 1
fi

echo -e "${BLUE}📍 Database: $DB_PATH${NC}"
echo -e "${BLUE}📦 Backup to: $BACKUP_PATH${NC}"

# Create backup
echo -e "${BLUE}🔄 Creating backup...${NC}"
cp "$DB_PATH" "$BACKUP_PATH"

# Verify backup
if [ -f "$BACKUP_PATH" ]; then
    ORIGINAL_SIZE=$(stat -f%z "$DB_PATH" 2>/dev/null || stat -c%s "$DB_PATH" 2>/dev/null)
    BACKUP_SIZE=$(stat -f%z "$BACKUP_PATH" 2>/dev/null || stat -c%s "$BACKUP_PATH" 2>/dev/null)
    
    echo -e "${GREEN}✅ Backup created successfully!${NC}"
    echo -e "${BLUE}📊 Original size: ${ORIGINAL_SIZE} bytes${NC}"
    echo -e "${BLUE}📊 Backup size: ${BACKUP_SIZE} bytes${NC}"
    
    # Create a latest backup link
    LATEST_BACKUP="${BACKUP_DIR}/latest_backup.sqlite3"
    cp "$BACKUP_PATH" "$LATEST_BACKUP"
    echo -e "${GREEN}🔗 Latest backup: $LATEST_BACKUP${NC}"
    
    # List recent backups
    echo -e "\n${BLUE}📋 Recent backups:${NC}"
    ls -la "$BACKUP_DIR"/*.sqlite3 | tail -5
    
    echo -e "\n${GREEN}🎉 Backup completed successfully!${NC}"
    echo -e "${BLUE}💡 To restore: ./restore_database.sh $BACKUP_NAME${NC}"
    
else
    echo -e "${RED}❌ Backup failed!${NC}"
    exit 1
fi

# Optional: Create SQL dump for version control
echo -e "\n${BLUE}🔄 Creating SQL dump for version control...${NC}"
cd trendy_web_and_api/trendy
source venv/bin/activate 2>/dev/null || echo "Virtual environment not found, continuing..."

# Create SQL dump
python manage.py dumpdata --format=json --indent=2 > "../../database_backups/data_dump_${TIMESTAMP}.json"

if [ -f "../../database_backups/data_dump_${TIMESTAMP}.json" ]; then
    echo -e "${GREEN}✅ JSON data dump created: data_dump_${TIMESTAMP}.json${NC}"
    echo -e "${BLUE}💡 This file can be committed to Git for sharing data${NC}"
else
    echo -e "${YELLOW}⚠️ Could not create JSON dump (Django environment issue)${NC}"
fi

echo -e "\n${GREEN}🎯 Summary:${NC}"
echo -e "  📦 SQLite backup: $BACKUP_NAME"
echo -e "  📄 JSON dump: data_dump_${TIMESTAMP}.json"
echo -e "  🔗 Latest backup: latest_backup.sqlite3"
