// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'poll.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Poll _$PollFromJson(Map<String, dynamic> json) {
  return _Poll.fromJson(json);
}

/// @nodoc
mixin _$Poll {
  int get id => throw _privateConstructorUsedError;
  String get question => throw _privateConstructorUsedError;
  @JsonKey(name: 'allow_multiple_choices')
  bool get allowMultipleChoices => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_results_immediately')
  bool get showResultsImmediately => throw _privateConstructorUsedError;
  @JsonKey(name: 'expires_at')
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_anonymous')
  bool get isAnonymous => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_votes')
  int get totalVotes => throw _privateConstructorUsedError;
  @JsonKey(name: 'unique_voters')
  int get uniqueVoters => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_expired')
  bool get isExpired => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  List<PollOption> get options => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_vote')
  List<PollVote> get userVote => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PollCopyWith<Poll> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PollCopyWith<$Res> {
  factory $PollCopyWith(Poll value, $Res Function(Poll) then) =
      _$PollCopyWithImpl<$Res, Poll>;
  @useResult
  $Res call(
      {int id,
      String question,
      @JsonKey(name: 'allow_multiple_choices') bool allowMultipleChoices,
      @JsonKey(name: 'show_results_immediately') bool showResultsImmediately,
      @JsonKey(name: 'expires_at') DateTime? expiresAt,
      @JsonKey(name: 'is_anonymous') bool isAnonymous,
      @JsonKey(name: 'total_votes') int totalVotes,
      @JsonKey(name: 'unique_voters') int uniqueVoters,
      @JsonKey(name: 'is_expired') bool isExpired,
      @JsonKey(name: 'is_active') bool isActive,
      @JsonKey(name: 'created_at') DateTime createdAt,
      List<PollOption> options,
      @JsonKey(name: 'user_vote') List<PollVote> userVote});
}

/// @nodoc
class _$PollCopyWithImpl<$Res, $Val extends Poll>
    implements $PollCopyWith<$Res> {
  _$PollCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? question = null,
    Object? allowMultipleChoices = null,
    Object? showResultsImmediately = null,
    Object? expiresAt = freezed,
    Object? isAnonymous = null,
    Object? totalVotes = null,
    Object? uniqueVoters = null,
    Object? isExpired = null,
    Object? isActive = null,
    Object? createdAt = null,
    Object? options = null,
    Object? userVote = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      allowMultipleChoices: null == allowMultipleChoices
          ? _value.allowMultipleChoices
          : allowMultipleChoices // ignore: cast_nullable_to_non_nullable
              as bool,
      showResultsImmediately: null == showResultsImmediately
          ? _value.showResultsImmediately
          : showResultsImmediately // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isAnonymous: null == isAnonymous
          ? _value.isAnonymous
          : isAnonymous // ignore: cast_nullable_to_non_nullable
              as bool,
      totalVotes: null == totalVotes
          ? _value.totalVotes
          : totalVotes // ignore: cast_nullable_to_non_nullable
              as int,
      uniqueVoters: null == uniqueVoters
          ? _value.uniqueVoters
          : uniqueVoters // ignore: cast_nullable_to_non_nullable
              as int,
      isExpired: null == isExpired
          ? _value.isExpired
          : isExpired // ignore: cast_nullable_to_non_nullable
              as bool,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<PollOption>,
      userVote: null == userVote
          ? _value.userVote
          : userVote // ignore: cast_nullable_to_non_nullable
              as List<PollVote>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PollImplCopyWith<$Res> implements $PollCopyWith<$Res> {
  factory _$$PollImplCopyWith(
          _$PollImpl value, $Res Function(_$PollImpl) then) =
      __$$PollImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String question,
      @JsonKey(name: 'allow_multiple_choices') bool allowMultipleChoices,
      @JsonKey(name: 'show_results_immediately') bool showResultsImmediately,
      @JsonKey(name: 'expires_at') DateTime? expiresAt,
      @JsonKey(name: 'is_anonymous') bool isAnonymous,
      @JsonKey(name: 'total_votes') int totalVotes,
      @JsonKey(name: 'unique_voters') int uniqueVoters,
      @JsonKey(name: 'is_expired') bool isExpired,
      @JsonKey(name: 'is_active') bool isActive,
      @JsonKey(name: 'created_at') DateTime createdAt,
      List<PollOption> options,
      @JsonKey(name: 'user_vote') List<PollVote> userVote});
}

/// @nodoc
class __$$PollImplCopyWithImpl<$Res>
    extends _$PollCopyWithImpl<$Res, _$PollImpl>
    implements _$$PollImplCopyWith<$Res> {
  __$$PollImplCopyWithImpl(_$PollImpl _value, $Res Function(_$PollImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? question = null,
    Object? allowMultipleChoices = null,
    Object? showResultsImmediately = null,
    Object? expiresAt = freezed,
    Object? isAnonymous = null,
    Object? totalVotes = null,
    Object? uniqueVoters = null,
    Object? isExpired = null,
    Object? isActive = null,
    Object? createdAt = null,
    Object? options = null,
    Object? userVote = null,
  }) {
    return _then(_$PollImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      allowMultipleChoices: null == allowMultipleChoices
          ? _value.allowMultipleChoices
          : allowMultipleChoices // ignore: cast_nullable_to_non_nullable
              as bool,
      showResultsImmediately: null == showResultsImmediately
          ? _value.showResultsImmediately
          : showResultsImmediately // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isAnonymous: null == isAnonymous
          ? _value.isAnonymous
          : isAnonymous // ignore: cast_nullable_to_non_nullable
              as bool,
      totalVotes: null == totalVotes
          ? _value.totalVotes
          : totalVotes // ignore: cast_nullable_to_non_nullable
              as int,
      uniqueVoters: null == uniqueVoters
          ? _value.uniqueVoters
          : uniqueVoters // ignore: cast_nullable_to_non_nullable
              as int,
      isExpired: null == isExpired
          ? _value.isExpired
          : isExpired // ignore: cast_nullable_to_non_nullable
              as bool,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      options: null == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<PollOption>,
      userVote: null == userVote
          ? _value._userVote
          : userVote // ignore: cast_nullable_to_non_nullable
              as List<PollVote>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PollImpl implements _Poll {
  const _$PollImpl(
      {required this.id,
      required this.question,
      @JsonKey(name: 'allow_multiple_choices')
      this.allowMultipleChoices = false,
      @JsonKey(name: 'show_results_immediately')
      this.showResultsImmediately = true,
      @JsonKey(name: 'expires_at') this.expiresAt,
      @JsonKey(name: 'is_anonymous') this.isAnonymous = false,
      @JsonKey(name: 'total_votes') this.totalVotes = 0,
      @JsonKey(name: 'unique_voters') this.uniqueVoters = 0,
      @JsonKey(name: 'is_expired') this.isExpired = false,
      @JsonKey(name: 'is_active') this.isActive = true,
      @JsonKey(name: 'created_at') required this.createdAt,
      final List<PollOption> options = const [],
      @JsonKey(name: 'user_vote') final List<PollVote> userVote = const []})
      : _options = options,
        _userVote = userVote;

  factory _$PollImpl.fromJson(Map<String, dynamic> json) =>
      _$$PollImplFromJson(json);

  @override
  final int id;
  @override
  final String question;
  @override
  @JsonKey(name: 'allow_multiple_choices')
  final bool allowMultipleChoices;
  @override
  @JsonKey(name: 'show_results_immediately')
  final bool showResultsImmediately;
  @override
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
  @override
  @JsonKey(name: 'is_anonymous')
  final bool isAnonymous;
  @override
  @JsonKey(name: 'total_votes')
  final int totalVotes;
  @override
  @JsonKey(name: 'unique_voters')
  final int uniqueVoters;
  @override
  @JsonKey(name: 'is_expired')
  final bool isExpired;
  @override
  @JsonKey(name: 'is_active')
  final bool isActive;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  final List<PollOption> _options;
  @override
  @JsonKey()
  List<PollOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  final List<PollVote> _userVote;
  @override
  @JsonKey(name: 'user_vote')
  List<PollVote> get userVote {
    if (_userVote is EqualUnmodifiableListView) return _userVote;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_userVote);
  }

  @override
  String toString() {
    return 'Poll(id: $id, question: $question, allowMultipleChoices: $allowMultipleChoices, showResultsImmediately: $showResultsImmediately, expiresAt: $expiresAt, isAnonymous: $isAnonymous, totalVotes: $totalVotes, uniqueVoters: $uniqueVoters, isExpired: $isExpired, isActive: $isActive, createdAt: $createdAt, options: $options, userVote: $userVote)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PollImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.allowMultipleChoices, allowMultipleChoices) ||
                other.allowMultipleChoices == allowMultipleChoices) &&
            (identical(other.showResultsImmediately, showResultsImmediately) ||
                other.showResultsImmediately == showResultsImmediately) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.isAnonymous, isAnonymous) ||
                other.isAnonymous == isAnonymous) &&
            (identical(other.totalVotes, totalVotes) ||
                other.totalVotes == totalVotes) &&
            (identical(other.uniqueVoters, uniqueVoters) ||
                other.uniqueVoters == uniqueVoters) &&
            (identical(other.isExpired, isExpired) ||
                other.isExpired == isExpired) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            const DeepCollectionEquality().equals(other._options, _options) &&
            const DeepCollectionEquality().equals(other._userVote, _userVote));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      question,
      allowMultipleChoices,
      showResultsImmediately,
      expiresAt,
      isAnonymous,
      totalVotes,
      uniqueVoters,
      isExpired,
      isActive,
      createdAt,
      const DeepCollectionEquality().hash(_options),
      const DeepCollectionEquality().hash(_userVote));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PollImplCopyWith<_$PollImpl> get copyWith =>
      __$$PollImplCopyWithImpl<_$PollImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PollImplToJson(
      this,
    );
  }
}

abstract class _Poll implements Poll {
  const factory _Poll(
      {required final int id,
      required final String question,
      @JsonKey(name: 'allow_multiple_choices') final bool allowMultipleChoices,
      @JsonKey(name: 'show_results_immediately')
      final bool showResultsImmediately,
      @JsonKey(name: 'expires_at') final DateTime? expiresAt,
      @JsonKey(name: 'is_anonymous') final bool isAnonymous,
      @JsonKey(name: 'total_votes') final int totalVotes,
      @JsonKey(name: 'unique_voters') final int uniqueVoters,
      @JsonKey(name: 'is_expired') final bool isExpired,
      @JsonKey(name: 'is_active') final bool isActive,
      @JsonKey(name: 'created_at') required final DateTime createdAt,
      final List<PollOption> options,
      @JsonKey(name: 'user_vote') final List<PollVote> userVote}) = _$PollImpl;

  factory _Poll.fromJson(Map<String, dynamic> json) = _$PollImpl.fromJson;

  @override
  int get id;
  @override
  String get question;
  @override
  @JsonKey(name: 'allow_multiple_choices')
  bool get allowMultipleChoices;
  @override
  @JsonKey(name: 'show_results_immediately')
  bool get showResultsImmediately;
  @override
  @JsonKey(name: 'expires_at')
  DateTime? get expiresAt;
  @override
  @JsonKey(name: 'is_anonymous')
  bool get isAnonymous;
  @override
  @JsonKey(name: 'total_votes')
  int get totalVotes;
  @override
  @JsonKey(name: 'unique_voters')
  int get uniqueVoters;
  @override
  @JsonKey(name: 'is_expired')
  bool get isExpired;
  @override
  @JsonKey(name: 'is_active')
  bool get isActive;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  List<PollOption> get options;
  @override
  @JsonKey(name: 'user_vote')
  List<PollVote> get userVote;
  @override
  @JsonKey(ignore: true)
  _$$PollImplCopyWith<_$PollImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PollOption _$PollOptionFromJson(Map<String, dynamic> json) {
  return _PollOption.fromJson(json);
}

/// @nodoc
mixin _$PollOption {
  int get id => throw _privateConstructorUsedError;
  String get text => throw _privateConstructorUsedError;
  @JsonKey(name: 'image_url')
  String? get imageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'vote_count')
  int get voteCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'vote_percentage')
  double get votePercentage => throw _privateConstructorUsedError;
  int get position => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PollOptionCopyWith<PollOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PollOptionCopyWith<$Res> {
  factory $PollOptionCopyWith(
          PollOption value, $Res Function(PollOption) then) =
      _$PollOptionCopyWithImpl<$Res, PollOption>;
  @useResult
  $Res call(
      {int id,
      String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'vote_count') int voteCount,
      @JsonKey(name: 'vote_percentage') double votePercentage,
      int position});
}

/// @nodoc
class _$PollOptionCopyWithImpl<$Res, $Val extends PollOption>
    implements $PollOptionCopyWith<$Res> {
  _$PollOptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? imageUrl = freezed,
    Object? voteCount = null,
    Object? votePercentage = null,
    Object? position = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      voteCount: null == voteCount
          ? _value.voteCount
          : voteCount // ignore: cast_nullable_to_non_nullable
              as int,
      votePercentage: null == votePercentage
          ? _value.votePercentage
          : votePercentage // ignore: cast_nullable_to_non_nullable
              as double,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PollOptionImplCopyWith<$Res>
    implements $PollOptionCopyWith<$Res> {
  factory _$$PollOptionImplCopyWith(
          _$PollOptionImpl value, $Res Function(_$PollOptionImpl) then) =
      __$$PollOptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'vote_count') int voteCount,
      @JsonKey(name: 'vote_percentage') double votePercentage,
      int position});
}

/// @nodoc
class __$$PollOptionImplCopyWithImpl<$Res>
    extends _$PollOptionCopyWithImpl<$Res, _$PollOptionImpl>
    implements _$$PollOptionImplCopyWith<$Res> {
  __$$PollOptionImplCopyWithImpl(
      _$PollOptionImpl _value, $Res Function(_$PollOptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? imageUrl = freezed,
    Object? voteCount = null,
    Object? votePercentage = null,
    Object? position = null,
  }) {
    return _then(_$PollOptionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      voteCount: null == voteCount
          ? _value.voteCount
          : voteCount // ignore: cast_nullable_to_non_nullable
              as int,
      votePercentage: null == votePercentage
          ? _value.votePercentage
          : votePercentage // ignore: cast_nullable_to_non_nullable
              as double,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PollOptionImpl implements _PollOption {
  const _$PollOptionImpl(
      {required this.id,
      required this.text,
      @JsonKey(name: 'image_url') this.imageUrl,
      @JsonKey(name: 'vote_count') this.voteCount = 0,
      @JsonKey(name: 'vote_percentage') this.votePercentage = 0.0,
      this.position = 0});

  factory _$PollOptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PollOptionImplFromJson(json);

  @override
  final int id;
  @override
  final String text;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey(name: 'vote_count')
  final int voteCount;
  @override
  @JsonKey(name: 'vote_percentage')
  final double votePercentage;
  @override
  @JsonKey()
  final int position;

  @override
  String toString() {
    return 'PollOption(id: $id, text: $text, imageUrl: $imageUrl, voteCount: $voteCount, votePercentage: $votePercentage, position: $position)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PollOptionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.voteCount, voteCount) ||
                other.voteCount == voteCount) &&
            (identical(other.votePercentage, votePercentage) ||
                other.votePercentage == votePercentage) &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, text, imageUrl, voteCount, votePercentage, position);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PollOptionImplCopyWith<_$PollOptionImpl> get copyWith =>
      __$$PollOptionImplCopyWithImpl<_$PollOptionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PollOptionImplToJson(
      this,
    );
  }
}

abstract class _PollOption implements PollOption {
  const factory _PollOption(
      {required final int id,
      required final String text,
      @JsonKey(name: 'image_url') final String? imageUrl,
      @JsonKey(name: 'vote_count') final int voteCount,
      @JsonKey(name: 'vote_percentage') final double votePercentage,
      final int position}) = _$PollOptionImpl;

  factory _PollOption.fromJson(Map<String, dynamic> json) =
      _$PollOptionImpl.fromJson;

  @override
  int get id;
  @override
  String get text;
  @override
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @override
  @JsonKey(name: 'vote_count')
  int get voteCount;
  @override
  @JsonKey(name: 'vote_percentage')
  double get votePercentage;
  @override
  int get position;
  @override
  @JsonKey(ignore: true)
  _$$PollOptionImplCopyWith<_$PollOptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PollVote _$PollVoteFromJson(Map<String, dynamic> json) {
  return _PollVote.fromJson(json);
}

/// @nodoc
mixin _$PollVote {
  int get id => throw _privateConstructorUsedError;
  int get option => throw _privateConstructorUsedError;
  @JsonKey(name: 'voted_at')
  DateTime get votedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PollVoteCopyWith<PollVote> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PollVoteCopyWith<$Res> {
  factory $PollVoteCopyWith(PollVote value, $Res Function(PollVote) then) =
      _$PollVoteCopyWithImpl<$Res, PollVote>;
  @useResult
  $Res call({int id, int option, @JsonKey(name: 'voted_at') DateTime votedAt});
}

/// @nodoc
class _$PollVoteCopyWithImpl<$Res, $Val extends PollVote>
    implements $PollVoteCopyWith<$Res> {
  _$PollVoteCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? option = null,
    Object? votedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      option: null == option
          ? _value.option
          : option // ignore: cast_nullable_to_non_nullable
              as int,
      votedAt: null == votedAt
          ? _value.votedAt
          : votedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PollVoteImplCopyWith<$Res>
    implements $PollVoteCopyWith<$Res> {
  factory _$$PollVoteImplCopyWith(
          _$PollVoteImpl value, $Res Function(_$PollVoteImpl) then) =
      __$$PollVoteImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, int option, @JsonKey(name: 'voted_at') DateTime votedAt});
}

/// @nodoc
class __$$PollVoteImplCopyWithImpl<$Res>
    extends _$PollVoteCopyWithImpl<$Res, _$PollVoteImpl>
    implements _$$PollVoteImplCopyWith<$Res> {
  __$$PollVoteImplCopyWithImpl(
      _$PollVoteImpl _value, $Res Function(_$PollVoteImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? option = null,
    Object? votedAt = null,
  }) {
    return _then(_$PollVoteImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      option: null == option
          ? _value.option
          : option // ignore: cast_nullable_to_non_nullable
              as int,
      votedAt: null == votedAt
          ? _value.votedAt
          : votedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PollVoteImpl implements _PollVote {
  const _$PollVoteImpl(
      {required this.id,
      required this.option,
      @JsonKey(name: 'voted_at') required this.votedAt});

  factory _$PollVoteImpl.fromJson(Map<String, dynamic> json) =>
      _$$PollVoteImplFromJson(json);

  @override
  final int id;
  @override
  final int option;
  @override
  @JsonKey(name: 'voted_at')
  final DateTime votedAt;

  @override
  String toString() {
    return 'PollVote(id: $id, option: $option, votedAt: $votedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PollVoteImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.option, option) || other.option == option) &&
            (identical(other.votedAt, votedAt) || other.votedAt == votedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, option, votedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PollVoteImplCopyWith<_$PollVoteImpl> get copyWith =>
      __$$PollVoteImplCopyWithImpl<_$PollVoteImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PollVoteImplToJson(
      this,
    );
  }
}

abstract class _PollVote implements PollVote {
  const factory _PollVote(
          {required final int id,
          required final int option,
          @JsonKey(name: 'voted_at') required final DateTime votedAt}) =
      _$PollVoteImpl;

  factory _PollVote.fromJson(Map<String, dynamic> json) =
      _$PollVoteImpl.fromJson;

  @override
  int get id;
  @override
  int get option;
  @override
  @JsonKey(name: 'voted_at')
  DateTime get votedAt;
  @override
  @JsonKey(ignore: true)
  _$$PollVoteImplCopyWith<_$PollVoteImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreatePollOption _$CreatePollOptionFromJson(Map<String, dynamic> json) {
  return _CreatePollOption.fromJson(json);
}

/// @nodoc
mixin _$CreatePollOption {
  String get text => throw _privateConstructorUsedError;
  @JsonKey(name: 'image_url')
  String? get imageUrl => throw _privateConstructorUsedError;
  int get position => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreatePollOptionCopyWith<CreatePollOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreatePollOptionCopyWith<$Res> {
  factory $CreatePollOptionCopyWith(
          CreatePollOption value, $Res Function(CreatePollOption) then) =
      _$CreatePollOptionCopyWithImpl<$Res, CreatePollOption>;
  @useResult
  $Res call(
      {String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      int position});
}

/// @nodoc
class _$CreatePollOptionCopyWithImpl<$Res, $Val extends CreatePollOption>
    implements $CreatePollOptionCopyWith<$Res> {
  _$CreatePollOptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? imageUrl = freezed,
    Object? position = null,
  }) {
    return _then(_value.copyWith(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreatePollOptionImplCopyWith<$Res>
    implements $CreatePollOptionCopyWith<$Res> {
  factory _$$CreatePollOptionImplCopyWith(_$CreatePollOptionImpl value,
          $Res Function(_$CreatePollOptionImpl) then) =
      __$$CreatePollOptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      int position});
}

/// @nodoc
class __$$CreatePollOptionImplCopyWithImpl<$Res>
    extends _$CreatePollOptionCopyWithImpl<$Res, _$CreatePollOptionImpl>
    implements _$$CreatePollOptionImplCopyWith<$Res> {
  __$$CreatePollOptionImplCopyWithImpl(_$CreatePollOptionImpl _value,
      $Res Function(_$CreatePollOptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = null,
    Object? imageUrl = freezed,
    Object? position = null,
  }) {
    return _then(_$CreatePollOptionImpl(
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreatePollOptionImpl implements _CreatePollOption {
  const _$CreatePollOptionImpl(
      {required this.text,
      @JsonKey(name: 'image_url') this.imageUrl,
      this.position = 0});

  factory _$CreatePollOptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreatePollOptionImplFromJson(json);

  @override
  final String text;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey()
  final int position;

  @override
  String toString() {
    return 'CreatePollOption(text: $text, imageUrl: $imageUrl, position: $position)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreatePollOptionImpl &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, text, imageUrl, position);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreatePollOptionImplCopyWith<_$CreatePollOptionImpl> get copyWith =>
      __$$CreatePollOptionImplCopyWithImpl<_$CreatePollOptionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreatePollOptionImplToJson(
      this,
    );
  }
}

abstract class _CreatePollOption implements CreatePollOption {
  const factory _CreatePollOption(
      {required final String text,
      @JsonKey(name: 'image_url') final String? imageUrl,
      final int position}) = _$CreatePollOptionImpl;

  factory _CreatePollOption.fromJson(Map<String, dynamic> json) =
      _$CreatePollOptionImpl.fromJson;

  @override
  String get text;
  @override
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @override
  int get position;
  @override
  @JsonKey(ignore: true)
  _$$CreatePollOptionImplCopyWith<_$CreatePollOptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreatePoll _$CreatePollFromJson(Map<String, dynamic> json) {
  return _CreatePoll.fromJson(json);
}

/// @nodoc
mixin _$CreatePoll {
  String get question => throw _privateConstructorUsedError;
  @JsonKey(name: 'allow_multiple_choices')
  bool get allowMultipleChoices => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_results_immediately')
  bool get showResultsImmediately => throw _privateConstructorUsedError;
  @JsonKey(name: 'expires_at')
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_anonymous')
  bool get isAnonymous => throw _privateConstructorUsedError;
  List<CreatePollOption> get options => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreatePollCopyWith<CreatePoll> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreatePollCopyWith<$Res> {
  factory $CreatePollCopyWith(
          CreatePoll value, $Res Function(CreatePoll) then) =
      _$CreatePollCopyWithImpl<$Res, CreatePoll>;
  @useResult
  $Res call(
      {String question,
      @JsonKey(name: 'allow_multiple_choices') bool allowMultipleChoices,
      @JsonKey(name: 'show_results_immediately') bool showResultsImmediately,
      @JsonKey(name: 'expires_at') DateTime? expiresAt,
      @JsonKey(name: 'is_anonymous') bool isAnonymous,
      List<CreatePollOption> options});
}

/// @nodoc
class _$CreatePollCopyWithImpl<$Res, $Val extends CreatePoll>
    implements $CreatePollCopyWith<$Res> {
  _$CreatePollCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? allowMultipleChoices = null,
    Object? showResultsImmediately = null,
    Object? expiresAt = freezed,
    Object? isAnonymous = null,
    Object? options = null,
  }) {
    return _then(_value.copyWith(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      allowMultipleChoices: null == allowMultipleChoices
          ? _value.allowMultipleChoices
          : allowMultipleChoices // ignore: cast_nullable_to_non_nullable
              as bool,
      showResultsImmediately: null == showResultsImmediately
          ? _value.showResultsImmediately
          : showResultsImmediately // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isAnonymous: null == isAnonymous
          ? _value.isAnonymous
          : isAnonymous // ignore: cast_nullable_to_non_nullable
              as bool,
      options: null == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<CreatePollOption>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreatePollImplCopyWith<$Res>
    implements $CreatePollCopyWith<$Res> {
  factory _$$CreatePollImplCopyWith(
          _$CreatePollImpl value, $Res Function(_$CreatePollImpl) then) =
      __$$CreatePollImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String question,
      @JsonKey(name: 'allow_multiple_choices') bool allowMultipleChoices,
      @JsonKey(name: 'show_results_immediately') bool showResultsImmediately,
      @JsonKey(name: 'expires_at') DateTime? expiresAt,
      @JsonKey(name: 'is_anonymous') bool isAnonymous,
      List<CreatePollOption> options});
}

/// @nodoc
class __$$CreatePollImplCopyWithImpl<$Res>
    extends _$CreatePollCopyWithImpl<$Res, _$CreatePollImpl>
    implements _$$CreatePollImplCopyWith<$Res> {
  __$$CreatePollImplCopyWithImpl(
      _$CreatePollImpl _value, $Res Function(_$CreatePollImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? question = null,
    Object? allowMultipleChoices = null,
    Object? showResultsImmediately = null,
    Object? expiresAt = freezed,
    Object? isAnonymous = null,
    Object? options = null,
  }) {
    return _then(_$CreatePollImpl(
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      allowMultipleChoices: null == allowMultipleChoices
          ? _value.allowMultipleChoices
          : allowMultipleChoices // ignore: cast_nullable_to_non_nullable
              as bool,
      showResultsImmediately: null == showResultsImmediately
          ? _value.showResultsImmediately
          : showResultsImmediately // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isAnonymous: null == isAnonymous
          ? _value.isAnonymous
          : isAnonymous // ignore: cast_nullable_to_non_nullable
              as bool,
      options: null == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<CreatePollOption>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreatePollImpl implements _CreatePoll {
  const _$CreatePollImpl(
      {required this.question,
      @JsonKey(name: 'allow_multiple_choices')
      this.allowMultipleChoices = false,
      @JsonKey(name: 'show_results_immediately')
      this.showResultsImmediately = true,
      @JsonKey(name: 'expires_at') this.expiresAt,
      @JsonKey(name: 'is_anonymous') this.isAnonymous = false,
      required final List<CreatePollOption> options})
      : _options = options;

  factory _$CreatePollImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreatePollImplFromJson(json);

  @override
  final String question;
  @override
  @JsonKey(name: 'allow_multiple_choices')
  final bool allowMultipleChoices;
  @override
  @JsonKey(name: 'show_results_immediately')
  final bool showResultsImmediately;
  @override
  @JsonKey(name: 'expires_at')
  final DateTime? expiresAt;
  @override
  @JsonKey(name: 'is_anonymous')
  final bool isAnonymous;
  final List<CreatePollOption> _options;
  @override
  List<CreatePollOption> get options {
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_options);
  }

  @override
  String toString() {
    return 'CreatePoll(question: $question, allowMultipleChoices: $allowMultipleChoices, showResultsImmediately: $showResultsImmediately, expiresAt: $expiresAt, isAnonymous: $isAnonymous, options: $options)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreatePollImpl &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.allowMultipleChoices, allowMultipleChoices) ||
                other.allowMultipleChoices == allowMultipleChoices) &&
            (identical(other.showResultsImmediately, showResultsImmediately) ||
                other.showResultsImmediately == showResultsImmediately) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.isAnonymous, isAnonymous) ||
                other.isAnonymous == isAnonymous) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      question,
      allowMultipleChoices,
      showResultsImmediately,
      expiresAt,
      isAnonymous,
      const DeepCollectionEquality().hash(_options));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreatePollImplCopyWith<_$CreatePollImpl> get copyWith =>
      __$$CreatePollImplCopyWithImpl<_$CreatePollImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreatePollImplToJson(
      this,
    );
  }
}

abstract class _CreatePoll implements CreatePoll {
  const factory _CreatePoll(
      {required final String question,
      @JsonKey(name: 'allow_multiple_choices') final bool allowMultipleChoices,
      @JsonKey(name: 'show_results_immediately')
      final bool showResultsImmediately,
      @JsonKey(name: 'expires_at') final DateTime? expiresAt,
      @JsonKey(name: 'is_anonymous') final bool isAnonymous,
      required final List<CreatePollOption> options}) = _$CreatePollImpl;

  factory _CreatePoll.fromJson(Map<String, dynamic> json) =
      _$CreatePollImpl.fromJson;

  @override
  String get question;
  @override
  @JsonKey(name: 'allow_multiple_choices')
  bool get allowMultipleChoices;
  @override
  @JsonKey(name: 'show_results_immediately')
  bool get showResultsImmediately;
  @override
  @JsonKey(name: 'expires_at')
  DateTime? get expiresAt;
  @override
  @JsonKey(name: 'is_anonymous')
  bool get isAnonymous;
  @override
  List<CreatePollOption> get options;
  @override
  @JsonKey(ignore: true)
  _$$CreatePollImplCopyWith<_$CreatePollImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
