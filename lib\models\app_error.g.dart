// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_error.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppErrorImpl _$$AppErrorImplFromJson(Map<String, dynamic> json) =>
    _$AppErrorImpl(
      type: $enumDecode(_$AppErrorTypeEnumMap, json['type']),
      message: json['message'] as String,
      userMessage: json['userMessage'] as String,
      details: json['details'] as String?,
      suggestion: json['suggestion'] as String?,
      statusCode: (json['statusCode'] as num?)?.toInt(),
      isRetryable: json['isRetryable'] as bool?,
      isTemporary: json['isTemporary'] as bool? ?? false,
    );

Map<String, dynamic> _$$AppErrorImplToJson(_$AppErrorImpl instance) =>
    <String, dynamic>{
      'type': _$AppErrorTypeEnumMap[instance.type]!,
      'message': instance.message,
      'userMessage': instance.userMessage,
      'details': instance.details,
      'suggestion': instance.suggestion,
      'statusCode': instance.statusCode,
      'isRetryable': instance.isRetryable,
      'isTemporary': instance.isTemporary,
    };

const _$AppErrorTypeEnumMap = {
  AppErrorType.network: 'network',
  AppErrorType.server: 'server',
  AppErrorType.authentication: 'authentication',
  AppErrorType.authorization: 'authorization',
  AppErrorType.validation: 'validation',
  AppErrorType.notFound: 'notFound',
  AppErrorType.maintenance: 'maintenance',
  AppErrorType.featureDisabled: 'featureDisabled',
  AppErrorType.storage: 'storage',
  AppErrorType.unknown: 'unknown',
};
