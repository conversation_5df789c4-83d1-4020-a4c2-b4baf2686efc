import 'package:freezed_annotation/freezed_annotation.dart';

part 'voice_comment.freezed.dart';
part 'voice_comment.g.dart';

@freezed
class VoiceComment with _$VoiceComment {
  const factory VoiceComment({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'post_id') required int postId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id') required int userId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_name') required String userName,
    @Json<PERSON>ey(name: 'user_avatar') String? userAvatar,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'audio_url') required String audioUrl,
    @<PERSON><PERSON><PERSON>ey(name: 'transcription') String? transcription,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'duration_seconds') @Default(0) int durationSeconds,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'file_size') @Default(0) int fileSize,
    @<PERSON>son<PERSON>ey(name: 'is_transcribed') @Default(false) bool isTranscribed,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'like_count') @Default(0) int likeCount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_liked') @Default(false) bool isLiked,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
  }) = _VoiceComment;

  factory VoiceComment.fromJson(Map<String, dynamic> json) => _$VoiceCommentFromJson(json);
}

@freezed
class CreateVoiceComment with _$CreateVoiceComment {
  const factory CreateVoiceComment({
    @JsonKey(name: 'post_id') required int postId,
    @JsonKey(name: 'audio_file') required String audioFile, // Base64 encoded or file path
    @JsonKey(name: 'duration_seconds') required int durationSeconds,
    @JsonKey(name: 'transcription') String? transcription,
  }) = _CreateVoiceComment;

  factory CreateVoiceComment.fromJson(Map<String, dynamic> json) => _$CreateVoiceCommentFromJson(json);
}

@freezed
class VoiceCommentResponse with _$VoiceCommentResponse {
  const factory VoiceCommentResponse({
    @Default([]) List<VoiceComment> results,
    @Default(0) int count,
    String? next,
    String? previous,
  }) = _VoiceCommentResponse;

  factory VoiceCommentResponse.fromJson(Map<String, dynamic> json) => _$VoiceCommentResponseFromJson(json);
}

enum VoiceCommentStatus {
  recording,
  processing,
  transcribing,
  ready,
  error,
}

extension VoiceCommentStatusExtension on VoiceCommentStatus {
  String get displayName {
    switch (this) {
      case VoiceCommentStatus.recording:
        return 'Recording...';
      case VoiceCommentStatus.processing:
        return 'Processing...';
      case VoiceCommentStatus.transcribing:
        return 'Transcribing...';
      case VoiceCommentStatus.ready:
        return 'Ready';
      case VoiceCommentStatus.error:
        return 'Error';
    }
  }

  bool get isLoading {
    switch (this) {
      case VoiceCommentStatus.recording:
      case VoiceCommentStatus.processing:
      case VoiceCommentStatus.transcribing:
        return true;
      case VoiceCommentStatus.ready:
      case VoiceCommentStatus.error:
        return false;
    }
  }
}
