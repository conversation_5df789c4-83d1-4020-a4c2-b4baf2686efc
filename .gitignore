# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Django/Python related
*.sqlite3
*.db
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.env
.env.local

# Django media files (optional - comment out if you want to track media)
# trendy_web_and_api/trendy/media/

# Database backups (exclude from Git - too large)
database_backups/*.sqlite3
# But include JSON dumps for data sharing
!database_backups/*.json
!database_backups/README.md

# But include fixtures for data sharing
!trendy_web_and_api/trendy/fixtures/
trendy_web_and_api/trendy/fixtures/*.json

# Django static files (generated)
trendy_web_and_api/trendy/staticfiles/
trendy_web_and_api/trendy/static/collected/
