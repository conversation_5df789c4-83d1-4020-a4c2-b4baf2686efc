import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:trendy/theme/app_theme.dart';
import '../providers/theme_provider.dart' as theme_provider;

enum PostDisplayMode {
  preview,
  full,
  compact,
}

enum SortOrder {
  newest,
  oldest,
  mostLiked,
  mostViewed,
}

class ContentSettingsSheet extends ConsumerStatefulWidget {
  final PostDisplayMode currentDisplayMode;
  final SortOrder currentSortOrder;
  final int currentPageSize;
  final Function(PostDisplayMode) onDisplayModeChanged;
  final Function(SortOrder) onSortOrderChanged;
  final Function(int) onPageSizeChanged;

  const ContentSettingsSheet({
    super.key,
    required this.currentDisplayMode,
    required this.currentSortOrder,
    required this.currentPageSize,
    required this.onDisplayModeChanged,
    required this.onSortOrderChanged,
    required this.onPageSizeChanged,
  });

  @override
  ConsumerState<ContentSettingsSheet> createState() => _ContentSettingsSheetState();
}

class _ContentSettingsSheetState extends ConsumerState<ContentSettingsSheet> {
  late PostDisplayMode _displayMode;
  late SortOrder _sortOrder;
  late int _pageSize;

  @override
  void initState() {
    super.initState();
    _displayMode = widget.currentDisplayMode;
    _sortOrder = widget.currentSortOrder;
    _pageSize = widget.currentPageSize;
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.85; // Use 85% of screen height

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Fixed header
          Container(
            padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.tune,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Content Settings',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Scrollable content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

          // Display Mode Section
          _buildSection(
            'Display Mode',
            'Choose how posts are displayed',
            Column(
              children: [
                _buildDisplayModeOption(
                  PostDisplayMode.preview,
                  'Preview Mode',
                  'Show post previews with "Read More" button',
                  Icons.preview,
                ),
                _buildDisplayModeOption(
                  PostDisplayMode.full,
                  'Full Mode',
                  'Show complete posts in the feed',
                  Icons.article,
                ),
                _buildDisplayModeOption(
                  PostDisplayMode.compact,
                  'Compact Mode',
                  'Show minimal post information',
                  Icons.view_list,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Sort Order Section
          _buildSection(
            'Sort Order',
            'Choose how posts are ordered',
            Column(
              children: [
                _buildSortOption(SortOrder.newest, 'Newest First', Icons.schedule),
                _buildSortOption(SortOrder.oldest, 'Oldest First', Icons.history),
                _buildSortOption(SortOrder.mostLiked, 'Most Liked', Icons.favorite),
                _buildSortOption(SortOrder.mostViewed, 'Most Viewed', Icons.visibility),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Theme Section
          _buildSection(
            'Theme',
            'Choose your preferred theme',
            Column(
              children: [
                _buildThemeOption(theme_provider.ThemeMode.light, 'Light', 'Light theme for daytime use', Icons.light_mode),
                _buildThemeOption(theme_provider.ThemeMode.dark, 'Dark', 'Dark theme for nighttime use', Icons.dark_mode),
                _buildThemeOption(theme_provider.ThemeMode.system, 'System', 'Follow system theme settings', Icons.settings_brightness),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Page Size Section
          _buildSection(
            'Posts Per Load',
            'Number of posts to load at once',
            Column(
              children: [
                _buildPageSizeSlider(),
                const SizedBox(height: 8),
                Text(
                  '$_pageSize posts per load',
                  style: const TextStyle(
                    color: AppTheme.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Apply Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _applySettings,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Apply Settings',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, String subtitle, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textTertiary,
          ),
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  Widget _buildDisplayModeOption(PostDisplayMode mode, String title, String subtitle, IconData icon) {
    final isSelected = _displayMode == mode;
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => setState(() => _displayMode = mode),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textTertiary,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSortOption(SortOrder order, String title, IconData icon) {
    final isSelected = _sortOrder == order;
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => setState(() => _sortOrder = order),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
                ),
              ),
              const Spacer(),
              if (isSelected)
                Icon(
                  Icons.check,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPageSizeSlider() {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        activeTrackColor: AppTheme.primaryColor,
        inactiveTrackColor: AppTheme.primaryColor.withOpacity(0.3),
        thumbColor: AppTheme.primaryColor,
        overlayColor: AppTheme.primaryColor.withOpacity(0.2),
      ),
      child: Slider(
        value: _pageSize.toDouble(),
        min: 5,
        max: 20,
        divisions: 3,
        onChanged: (value) => setState(() => _pageSize = value.round()),
      ),
    );
  }

  Widget _buildThemeOption(theme_provider.ThemeMode themeMode, String title, String subtitle, IconData icon) {
    final currentThemeMode = ref.watch(theme_provider.themeProvider).themeMode;
    final isSelected = currentThemeMode == themeMode;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => ref.read(theme_provider.themeProvider.notifier).setThemeMode(themeMode),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.borderColor,
                width: isSelected ? 2 : 1,
              ),
              color: isSelected ? AppTheme.primaryColor.withOpacity(0.05) : Colors.transparent,
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppTheme.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  const Icon(
                    Icons.check_circle,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _applySettings() {
    widget.onDisplayModeChanged(_displayMode);
    widget.onSortOrderChanged(_sortOrder);
    widget.onPageSizeChanged(_pageSize);
    Navigator.pop(context);
  }
}
