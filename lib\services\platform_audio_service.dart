import 'dart:io';
import 'package:flutter/foundation.dart';

/// Platform-aware audio service that handles missing dependencies gracefully
class PlatformAudioService {
  static bool get isAudioSupported {
    // Audio is fully supported on mobile platforms
    if (Platform.isAndroid || Platform.isIOS) return true;
    
    // On desktop platforms, check if dependencies are available
    if (Platform.isLinux || Platform.isWindows || Platform.isMacOS) {
      // For now, assume desktop audio might not be available
      // In production, you could check for specific system dependencies
      return false;
    }
    
    return false;
  }
  
  static bool get isRecordingSupported {
    // Recording is supported on most platforms
    return Platform.isAndroid || Platform.isIOS || Platform.isMacOS;
  }
  
  static String get platformMessage {
    if (Platform.isLinux) {
      return 'Audio playback requires additional system dependencies on Linux. Please install gstreamer packages.';
    } else if (Platform.isWindows) {
      return 'Audio playback may require additional system configuration on Windows.';
    } else {
      return 'Audio playback is not supported on this platform.';
    }
  }
}

/// Exception thrown when audio features are not supported
class AudioNotSupportedException implements Exception {
  final String message;
  final String platform;
  
  const AudioNotSupportedException(this.message, this.platform);
  
  @override
  String toString() => 'AudioNotSupportedException: $message (Platform: $platform)';
}

/// Mixin to add platform-aware audio capabilities to services
mixin PlatformAudioMixin {
  void checkAudioSupport() {
    if (!PlatformAudioService.isAudioSupported) {
      throw AudioNotSupportedException(
        PlatformAudioService.platformMessage,
        Platform.operatingSystem,
      );
    }
  }
  
  void checkRecordingSupport() {
    if (!PlatformAudioService.isRecordingSupported) {
      throw AudioNotSupportedException(
        'Audio recording is not supported on ${Platform.operatingSystem}',
        Platform.operatingSystem,
      );
    }
  }
  
  Future<T> withAudioSupport<T>(Future<T> Function() audioOperation) async {
    try {
      checkAudioSupport();
      return await audioOperation();
    } on AudioNotSupportedException {
      rethrow;
    } catch (e) {
      // If audio operation fails, it might be due to missing system dependencies
      if (e.toString().contains('gstreamer') || 
          e.toString().contains('libsecret') ||
          e.toString().contains('CMake Error')) {
        throw AudioNotSupportedException(
          'Audio system dependencies are missing. ${PlatformAudioService.platformMessage}',
          Platform.operatingSystem,
        );
      }
      rethrow;
    }
  }
  
  Future<T> withRecordingSupport<T>(Future<T> Function() recordingOperation) async {
    try {
      checkRecordingSupport();
      return await recordingOperation();
    } on AudioNotSupportedException {
      rethrow;
    } catch (e) {
      throw AudioNotSupportedException(
        'Recording failed: ${e.toString()}',
        Platform.operatingSystem,
      );
    }
  }
}
