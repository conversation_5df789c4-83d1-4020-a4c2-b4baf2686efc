from django.urls import path
from . import views

app_name = 'payments'

urlpatterns = [
    # PayPal Profile Management
    path('paypal-profile/', views.get_paypal_profile, name='get-paypal-profile'),
    path('setup-paypal-profile/', views.setup_paypal_profile, name='setup-paypal-profile'),
    path('verify-paypal-profile/', views.verify_paypal_profile, name='verify-paypal-profile'),
    path('resend-paypal-verification/', views.resend_paypal_verification, name='resend-paypal-verification'),
    
    # Payment Processing
    path('create-order/', views.create_payment_order, name='create-payment-order'),
    path('capture-order/', views.capture_payment_order, name='capture-payment-order'),
    path('request-payout/', views.request_payout, name='request-payout'),
    
    # Payment History and Settings
    path('history/', views.payment_history, name='payment-history'),
    path('settings/', views.payment_settings, name='payment-settings'),
    path('status/<int:transaction_id>/', views.payment_status, name='payment-status'),
    
    # Webhooks
    path('webhook/', views.paypal_webhook, name='paypal-webhook'),
]
