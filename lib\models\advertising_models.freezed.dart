// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'advertising_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AdPlacement _$AdPlacementFromJson(Map<String, dynamic> json) {
  return _AdPlacement.fromJson(json);
}

/// @nodoc
mixin _$AdPlacement {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'placement_type')
  String get placementType => throw _privateConstructorUsedError;
  String get location => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool get isActive => throw _privateConstructorUsedError;
  int get frequency => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_per_session')
  int get maxPerSession => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_reward')
  int get pointsReward => throw _privateConstructorUsedError;
  @JsonKey(name: 'min_user_level')
  int get minUserLevel => throw _privateConstructorUsedError;
  @JsonKey(name: 'premium_users_only')
  bool get premiumUsersOnly => throw _privateConstructorUsedError;
  @JsonKey(name: 'free_users_only')
  bool get freeUsersOnly => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdPlacementCopyWith<AdPlacement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdPlacementCopyWith<$Res> {
  factory $AdPlacementCopyWith(
          AdPlacement value, $Res Function(AdPlacement) then) =
      _$AdPlacementCopyWithImpl<$Res, AdPlacement>;
  @useResult
  $Res call(
      {String id,
      String name,
      @JsonKey(name: 'placement_type') String placementType,
      String location,
      @JsonKey(name: 'is_active') bool isActive,
      int frequency,
      @JsonKey(name: 'max_per_session') int maxPerSession,
      @JsonKey(name: 'points_reward') int pointsReward,
      @JsonKey(name: 'min_user_level') int minUserLevel,
      @JsonKey(name: 'premium_users_only') bool premiumUsersOnly,
      @JsonKey(name: 'free_users_only') bool freeUsersOnly});
}

/// @nodoc
class _$AdPlacementCopyWithImpl<$Res, $Val extends AdPlacement>
    implements $AdPlacementCopyWith<$Res> {
  _$AdPlacementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? placementType = null,
    Object? location = null,
    Object? isActive = null,
    Object? frequency = null,
    Object? maxPerSession = null,
    Object? pointsReward = null,
    Object? minUserLevel = null,
    Object? premiumUsersOnly = null,
    Object? freeUsersOnly = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      placementType: null == placementType
          ? _value.placementType
          : placementType // ignore: cast_nullable_to_non_nullable
              as String,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      frequency: null == frequency
          ? _value.frequency
          : frequency // ignore: cast_nullable_to_non_nullable
              as int,
      maxPerSession: null == maxPerSession
          ? _value.maxPerSession
          : maxPerSession // ignore: cast_nullable_to_non_nullable
              as int,
      pointsReward: null == pointsReward
          ? _value.pointsReward
          : pointsReward // ignore: cast_nullable_to_non_nullable
              as int,
      minUserLevel: null == minUserLevel
          ? _value.minUserLevel
          : minUserLevel // ignore: cast_nullable_to_non_nullable
              as int,
      premiumUsersOnly: null == premiumUsersOnly
          ? _value.premiumUsersOnly
          : premiumUsersOnly // ignore: cast_nullable_to_non_nullable
              as bool,
      freeUsersOnly: null == freeUsersOnly
          ? _value.freeUsersOnly
          : freeUsersOnly // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdPlacementImplCopyWith<$Res>
    implements $AdPlacementCopyWith<$Res> {
  factory _$$AdPlacementImplCopyWith(
          _$AdPlacementImpl value, $Res Function(_$AdPlacementImpl) then) =
      __$$AdPlacementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      @JsonKey(name: 'placement_type') String placementType,
      String location,
      @JsonKey(name: 'is_active') bool isActive,
      int frequency,
      @JsonKey(name: 'max_per_session') int maxPerSession,
      @JsonKey(name: 'points_reward') int pointsReward,
      @JsonKey(name: 'min_user_level') int minUserLevel,
      @JsonKey(name: 'premium_users_only') bool premiumUsersOnly,
      @JsonKey(name: 'free_users_only') bool freeUsersOnly});
}

/// @nodoc
class __$$AdPlacementImplCopyWithImpl<$Res>
    extends _$AdPlacementCopyWithImpl<$Res, _$AdPlacementImpl>
    implements _$$AdPlacementImplCopyWith<$Res> {
  __$$AdPlacementImplCopyWithImpl(
      _$AdPlacementImpl _value, $Res Function(_$AdPlacementImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? placementType = null,
    Object? location = null,
    Object? isActive = null,
    Object? frequency = null,
    Object? maxPerSession = null,
    Object? pointsReward = null,
    Object? minUserLevel = null,
    Object? premiumUsersOnly = null,
    Object? freeUsersOnly = null,
  }) {
    return _then(_$AdPlacementImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      placementType: null == placementType
          ? _value.placementType
          : placementType // ignore: cast_nullable_to_non_nullable
              as String,
      location: null == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      frequency: null == frequency
          ? _value.frequency
          : frequency // ignore: cast_nullable_to_non_nullable
              as int,
      maxPerSession: null == maxPerSession
          ? _value.maxPerSession
          : maxPerSession // ignore: cast_nullable_to_non_nullable
              as int,
      pointsReward: null == pointsReward
          ? _value.pointsReward
          : pointsReward // ignore: cast_nullable_to_non_nullable
              as int,
      minUserLevel: null == minUserLevel
          ? _value.minUserLevel
          : minUserLevel // ignore: cast_nullable_to_non_nullable
              as int,
      premiumUsersOnly: null == premiumUsersOnly
          ? _value.premiumUsersOnly
          : premiumUsersOnly // ignore: cast_nullable_to_non_nullable
              as bool,
      freeUsersOnly: null == freeUsersOnly
          ? _value.freeUsersOnly
          : freeUsersOnly // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdPlacementImpl implements _AdPlacement {
  const _$AdPlacementImpl(
      {required this.id,
      required this.name,
      @JsonKey(name: 'placement_type') required this.placementType,
      required this.location,
      @JsonKey(name: 'is_active') required this.isActive,
      required this.frequency,
      @JsonKey(name: 'max_per_session') required this.maxPerSession,
      @JsonKey(name: 'points_reward') required this.pointsReward,
      @JsonKey(name: 'min_user_level') required this.minUserLevel,
      @JsonKey(name: 'premium_users_only') required this.premiumUsersOnly,
      @JsonKey(name: 'free_users_only') required this.freeUsersOnly});

  factory _$AdPlacementImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdPlacementImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  @JsonKey(name: 'placement_type')
  final String placementType;
  @override
  final String location;
  @override
  @JsonKey(name: 'is_active')
  final bool isActive;
  @override
  final int frequency;
  @override
  @JsonKey(name: 'max_per_session')
  final int maxPerSession;
  @override
  @JsonKey(name: 'points_reward')
  final int pointsReward;
  @override
  @JsonKey(name: 'min_user_level')
  final int minUserLevel;
  @override
  @JsonKey(name: 'premium_users_only')
  final bool premiumUsersOnly;
  @override
  @JsonKey(name: 'free_users_only')
  final bool freeUsersOnly;

  @override
  String toString() {
    return 'AdPlacement(id: $id, name: $name, placementType: $placementType, location: $location, isActive: $isActive, frequency: $frequency, maxPerSession: $maxPerSession, pointsReward: $pointsReward, minUserLevel: $minUserLevel, premiumUsersOnly: $premiumUsersOnly, freeUsersOnly: $freeUsersOnly)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdPlacementImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.placementType, placementType) ||
                other.placementType == placementType) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.frequency, frequency) ||
                other.frequency == frequency) &&
            (identical(other.maxPerSession, maxPerSession) ||
                other.maxPerSession == maxPerSession) &&
            (identical(other.pointsReward, pointsReward) ||
                other.pointsReward == pointsReward) &&
            (identical(other.minUserLevel, minUserLevel) ||
                other.minUserLevel == minUserLevel) &&
            (identical(other.premiumUsersOnly, premiumUsersOnly) ||
                other.premiumUsersOnly == premiumUsersOnly) &&
            (identical(other.freeUsersOnly, freeUsersOnly) ||
                other.freeUsersOnly == freeUsersOnly));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      placementType,
      location,
      isActive,
      frequency,
      maxPerSession,
      pointsReward,
      minUserLevel,
      premiumUsersOnly,
      freeUsersOnly);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdPlacementImplCopyWith<_$AdPlacementImpl> get copyWith =>
      __$$AdPlacementImplCopyWithImpl<_$AdPlacementImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdPlacementImplToJson(
      this,
    );
  }
}

abstract class _AdPlacement implements AdPlacement {
  const factory _AdPlacement(
      {required final String id,
      required final String name,
      @JsonKey(name: 'placement_type') required final String placementType,
      required final String location,
      @JsonKey(name: 'is_active') required final bool isActive,
      required final int frequency,
      @JsonKey(name: 'max_per_session') required final int maxPerSession,
      @JsonKey(name: 'points_reward') required final int pointsReward,
      @JsonKey(name: 'min_user_level') required final int minUserLevel,
      @JsonKey(name: 'premium_users_only') required final bool premiumUsersOnly,
      @JsonKey(name: 'free_users_only')
      required final bool freeUsersOnly}) = _$AdPlacementImpl;

  factory _AdPlacement.fromJson(Map<String, dynamic> json) =
      _$AdPlacementImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  @JsonKey(name: 'placement_type')
  String get placementType;
  @override
  String get location;
  @override
  @JsonKey(name: 'is_active')
  bool get isActive;
  @override
  int get frequency;
  @override
  @JsonKey(name: 'max_per_session')
  int get maxPerSession;
  @override
  @JsonKey(name: 'points_reward')
  int get pointsReward;
  @override
  @JsonKey(name: 'min_user_level')
  int get minUserLevel;
  @override
  @JsonKey(name: 'premium_users_only')
  bool get premiumUsersOnly;
  @override
  @JsonKey(name: 'free_users_only')
  bool get freeUsersOnly;
  @override
  @JsonKey(ignore: true)
  _$$AdPlacementImplCopyWith<_$AdPlacementImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AdNetwork _$AdNetworkFromJson(Map<String, dynamic> json) {
  return _AdNetwork.fromJson(json);
}

/// @nodoc
mixin _$AdNetwork {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'display_name')
  String get displayName => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool get isActive => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;
  @JsonKey(name: 'revenue_share_percentage')
  double get revenueSharePercentage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdNetworkCopyWith<AdNetwork> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdNetworkCopyWith<$Res> {
  factory $AdNetworkCopyWith(AdNetwork value, $Res Function(AdNetwork) then) =
      _$AdNetworkCopyWithImpl<$Res, AdNetwork>;
  @useResult
  $Res call(
      {String id,
      String name,
      @JsonKey(name: 'display_name') String displayName,
      @JsonKey(name: 'is_active') bool isActive,
      int priority,
      @JsonKey(name: 'revenue_share_percentage')
      double revenueSharePercentage});
}

/// @nodoc
class _$AdNetworkCopyWithImpl<$Res, $Val extends AdNetwork>
    implements $AdNetworkCopyWith<$Res> {
  _$AdNetworkCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? displayName = null,
    Object? isActive = null,
    Object? priority = null,
    Object? revenueSharePercentage = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      revenueSharePercentage: null == revenueSharePercentage
          ? _value.revenueSharePercentage
          : revenueSharePercentage // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdNetworkImplCopyWith<$Res>
    implements $AdNetworkCopyWith<$Res> {
  factory _$$AdNetworkImplCopyWith(
          _$AdNetworkImpl value, $Res Function(_$AdNetworkImpl) then) =
      __$$AdNetworkImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      @JsonKey(name: 'display_name') String displayName,
      @JsonKey(name: 'is_active') bool isActive,
      int priority,
      @JsonKey(name: 'revenue_share_percentage')
      double revenueSharePercentage});
}

/// @nodoc
class __$$AdNetworkImplCopyWithImpl<$Res>
    extends _$AdNetworkCopyWithImpl<$Res, _$AdNetworkImpl>
    implements _$$AdNetworkImplCopyWith<$Res> {
  __$$AdNetworkImplCopyWithImpl(
      _$AdNetworkImpl _value, $Res Function(_$AdNetworkImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? displayName = null,
    Object? isActive = null,
    Object? priority = null,
    Object? revenueSharePercentage = null,
  }) {
    return _then(_$AdNetworkImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      revenueSharePercentage: null == revenueSharePercentage
          ? _value.revenueSharePercentage
          : revenueSharePercentage // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdNetworkImpl implements _AdNetwork {
  const _$AdNetworkImpl(
      {required this.id,
      required this.name,
      @JsonKey(name: 'display_name') required this.displayName,
      @JsonKey(name: 'is_active') required this.isActive,
      required this.priority,
      @JsonKey(name: 'revenue_share_percentage')
      required this.revenueSharePercentage});

  factory _$AdNetworkImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdNetworkImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  @JsonKey(name: 'display_name')
  final String displayName;
  @override
  @JsonKey(name: 'is_active')
  final bool isActive;
  @override
  final int priority;
  @override
  @JsonKey(name: 'revenue_share_percentage')
  final double revenueSharePercentage;

  @override
  String toString() {
    return 'AdNetwork(id: $id, name: $name, displayName: $displayName, isActive: $isActive, priority: $priority, revenueSharePercentage: $revenueSharePercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdNetworkImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.revenueSharePercentage, revenueSharePercentage) ||
                other.revenueSharePercentage == revenueSharePercentage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, displayName, isActive,
      priority, revenueSharePercentage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdNetworkImplCopyWith<_$AdNetworkImpl> get copyWith =>
      __$$AdNetworkImplCopyWithImpl<_$AdNetworkImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdNetworkImplToJson(
      this,
    );
  }
}

abstract class _AdNetwork implements AdNetwork {
  const factory _AdNetwork(
      {required final String id,
      required final String name,
      @JsonKey(name: 'display_name') required final String displayName,
      @JsonKey(name: 'is_active') required final bool isActive,
      required final int priority,
      @JsonKey(name: 'revenue_share_percentage')
      required final double revenueSharePercentage}) = _$AdNetworkImpl;

  factory _AdNetwork.fromJson(Map<String, dynamic> json) =
      _$AdNetworkImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  @JsonKey(name: 'display_name')
  String get displayName;
  @override
  @JsonKey(name: 'is_active')
  bool get isActive;
  @override
  int get priority;
  @override
  @JsonKey(name: 'revenue_share_percentage')
  double get revenueSharePercentage;
  @override
  @JsonKey(ignore: true)
  _$$AdNetworkImplCopyWith<_$AdNetworkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardedAdSession _$RewardedAdSessionFromJson(Map<String, dynamic> json) {
  return _RewardedAdSession.fromJson(json);
}

/// @nodoc
mixin _$RewardedAdSession {
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'session_id')
  String get sessionId => throw _privateConstructorUsedError;
  AdPlacement get placement => throw _privateConstructorUsedError;
  @JsonKey(name: 'ad_network')
  AdNetwork get adNetwork => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_offered')
  int get pointsOffered => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_awarded')
  int get pointsAwarded => throw _privateConstructorUsedError;
  @JsonKey(name: 'reward_claimed')
  bool get rewardClaimed => throw _privateConstructorUsedError;
  @JsonKey(name: 'started_at')
  DateTime get startedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'completed_at')
  DateTime? get completedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardedAdSessionCopyWith<RewardedAdSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardedAdSessionCopyWith<$Res> {
  factory $RewardedAdSessionCopyWith(
          RewardedAdSession value, $Res Function(RewardedAdSession) then) =
      _$RewardedAdSessionCopyWithImpl<$Res, RewardedAdSession>;
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'session_id') String sessionId,
      AdPlacement placement,
      @JsonKey(name: 'ad_network') AdNetwork adNetwork,
      String status,
      @JsonKey(name: 'points_offered') int pointsOffered,
      @JsonKey(name: 'points_awarded') int pointsAwarded,
      @JsonKey(name: 'reward_claimed') bool rewardClaimed,
      @JsonKey(name: 'started_at') DateTime startedAt,
      @JsonKey(name: 'completed_at') DateTime? completedAt});

  $AdPlacementCopyWith<$Res> get placement;
  $AdNetworkCopyWith<$Res> get adNetwork;
}

/// @nodoc
class _$RewardedAdSessionCopyWithImpl<$Res, $Val extends RewardedAdSession>
    implements $RewardedAdSessionCopyWith<$Res> {
  _$RewardedAdSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sessionId = null,
    Object? placement = null,
    Object? adNetwork = null,
    Object? status = null,
    Object? pointsOffered = null,
    Object? pointsAwarded = null,
    Object? rewardClaimed = null,
    Object? startedAt = null,
    Object? completedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      placement: null == placement
          ? _value.placement
          : placement // ignore: cast_nullable_to_non_nullable
              as AdPlacement,
      adNetwork: null == adNetwork
          ? _value.adNetwork
          : adNetwork // ignore: cast_nullable_to_non_nullable
              as AdNetwork,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      pointsOffered: null == pointsOffered
          ? _value.pointsOffered
          : pointsOffered // ignore: cast_nullable_to_non_nullable
              as int,
      pointsAwarded: null == pointsAwarded
          ? _value.pointsAwarded
          : pointsAwarded // ignore: cast_nullable_to_non_nullable
              as int,
      rewardClaimed: null == rewardClaimed
          ? _value.rewardClaimed
          : rewardClaimed // ignore: cast_nullable_to_non_nullable
              as bool,
      startedAt: null == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AdPlacementCopyWith<$Res> get placement {
    return $AdPlacementCopyWith<$Res>(_value.placement, (value) {
      return _then(_value.copyWith(placement: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AdNetworkCopyWith<$Res> get adNetwork {
    return $AdNetworkCopyWith<$Res>(_value.adNetwork, (value) {
      return _then(_value.copyWith(adNetwork: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RewardedAdSessionImplCopyWith<$Res>
    implements $RewardedAdSessionCopyWith<$Res> {
  factory _$$RewardedAdSessionImplCopyWith(_$RewardedAdSessionImpl value,
          $Res Function(_$RewardedAdSessionImpl) then) =
      __$$RewardedAdSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(name: 'session_id') String sessionId,
      AdPlacement placement,
      @JsonKey(name: 'ad_network') AdNetwork adNetwork,
      String status,
      @JsonKey(name: 'points_offered') int pointsOffered,
      @JsonKey(name: 'points_awarded') int pointsAwarded,
      @JsonKey(name: 'reward_claimed') bool rewardClaimed,
      @JsonKey(name: 'started_at') DateTime startedAt,
      @JsonKey(name: 'completed_at') DateTime? completedAt});

  @override
  $AdPlacementCopyWith<$Res> get placement;
  @override
  $AdNetworkCopyWith<$Res> get adNetwork;
}

/// @nodoc
class __$$RewardedAdSessionImplCopyWithImpl<$Res>
    extends _$RewardedAdSessionCopyWithImpl<$Res, _$RewardedAdSessionImpl>
    implements _$$RewardedAdSessionImplCopyWith<$Res> {
  __$$RewardedAdSessionImplCopyWithImpl(_$RewardedAdSessionImpl _value,
      $Res Function(_$RewardedAdSessionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? sessionId = null,
    Object? placement = null,
    Object? adNetwork = null,
    Object? status = null,
    Object? pointsOffered = null,
    Object? pointsAwarded = null,
    Object? rewardClaimed = null,
    Object? startedAt = null,
    Object? completedAt = freezed,
  }) {
    return _then(_$RewardedAdSessionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      placement: null == placement
          ? _value.placement
          : placement // ignore: cast_nullable_to_non_nullable
              as AdPlacement,
      adNetwork: null == adNetwork
          ? _value.adNetwork
          : adNetwork // ignore: cast_nullable_to_non_nullable
              as AdNetwork,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      pointsOffered: null == pointsOffered
          ? _value.pointsOffered
          : pointsOffered // ignore: cast_nullable_to_non_nullable
              as int,
      pointsAwarded: null == pointsAwarded
          ? _value.pointsAwarded
          : pointsAwarded // ignore: cast_nullable_to_non_nullable
              as int,
      rewardClaimed: null == rewardClaimed
          ? _value.rewardClaimed
          : rewardClaimed // ignore: cast_nullable_to_non_nullable
              as bool,
      startedAt: null == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RewardedAdSessionImpl implements _RewardedAdSession {
  const _$RewardedAdSessionImpl(
      {required this.id,
      @JsonKey(name: 'session_id') required this.sessionId,
      required this.placement,
      @JsonKey(name: 'ad_network') required this.adNetwork,
      required this.status,
      @JsonKey(name: 'points_offered') required this.pointsOffered,
      @JsonKey(name: 'points_awarded') required this.pointsAwarded,
      @JsonKey(name: 'reward_claimed') required this.rewardClaimed,
      @JsonKey(name: 'started_at') required this.startedAt,
      @JsonKey(name: 'completed_at') this.completedAt});

  factory _$RewardedAdSessionImpl.fromJson(Map<String, dynamic> json) =>
      _$$RewardedAdSessionImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(name: 'session_id')
  final String sessionId;
  @override
  final AdPlacement placement;
  @override
  @JsonKey(name: 'ad_network')
  final AdNetwork adNetwork;
  @override
  final String status;
  @override
  @JsonKey(name: 'points_offered')
  final int pointsOffered;
  @override
  @JsonKey(name: 'points_awarded')
  final int pointsAwarded;
  @override
  @JsonKey(name: 'reward_claimed')
  final bool rewardClaimed;
  @override
  @JsonKey(name: 'started_at')
  final DateTime startedAt;
  @override
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;

  @override
  String toString() {
    return 'RewardedAdSession(id: $id, sessionId: $sessionId, placement: $placement, adNetwork: $adNetwork, status: $status, pointsOffered: $pointsOffered, pointsAwarded: $pointsAwarded, rewardClaimed: $rewardClaimed, startedAt: $startedAt, completedAt: $completedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RewardedAdSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.placement, placement) ||
                other.placement == placement) &&
            (identical(other.adNetwork, adNetwork) ||
                other.adNetwork == adNetwork) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.pointsOffered, pointsOffered) ||
                other.pointsOffered == pointsOffered) &&
            (identical(other.pointsAwarded, pointsAwarded) ||
                other.pointsAwarded == pointsAwarded) &&
            (identical(other.rewardClaimed, rewardClaimed) ||
                other.rewardClaimed == rewardClaimed) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      sessionId,
      placement,
      adNetwork,
      status,
      pointsOffered,
      pointsAwarded,
      rewardClaimed,
      startedAt,
      completedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RewardedAdSessionImplCopyWith<_$RewardedAdSessionImpl> get copyWith =>
      __$$RewardedAdSessionImplCopyWithImpl<_$RewardedAdSessionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RewardedAdSessionImplToJson(
      this,
    );
  }
}

abstract class _RewardedAdSession implements RewardedAdSession {
  const factory _RewardedAdSession(
          {required final String id,
          @JsonKey(name: 'session_id') required final String sessionId,
          required final AdPlacement placement,
          @JsonKey(name: 'ad_network') required final AdNetwork adNetwork,
          required final String status,
          @JsonKey(name: 'points_offered') required final int pointsOffered,
          @JsonKey(name: 'points_awarded') required final int pointsAwarded,
          @JsonKey(name: 'reward_claimed') required final bool rewardClaimed,
          @JsonKey(name: 'started_at') required final DateTime startedAt,
          @JsonKey(name: 'completed_at') final DateTime? completedAt}) =
      _$RewardedAdSessionImpl;

  factory _RewardedAdSession.fromJson(Map<String, dynamic> json) =
      _$RewardedAdSessionImpl.fromJson;

  @override
  String get id;
  @override
  @JsonKey(name: 'session_id')
  String get sessionId;
  @override
  AdPlacement get placement;
  @override
  @JsonKey(name: 'ad_network')
  AdNetwork get adNetwork;
  @override
  String get status;
  @override
  @JsonKey(name: 'points_offered')
  int get pointsOffered;
  @override
  @JsonKey(name: 'points_awarded')
  int get pointsAwarded;
  @override
  @JsonKey(name: 'reward_claimed')
  bool get rewardClaimed;
  @override
  @JsonKey(name: 'started_at')
  DateTime get startedAt;
  @override
  @JsonKey(name: 'completed_at')
  DateTime? get completedAt;
  @override
  @JsonKey(ignore: true)
  _$$RewardedAdSessionImplCopyWith<_$RewardedAdSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SponsoredContent _$SponsoredContentFromJson(Map<String, dynamic> json) {
  return _SponsoredContent.fromJson(json);
}

/// @nodoc
mixin _$SponsoredContent {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  @JsonKey(name: 'sponsor_name')
  String get sponsorName => throw _privateConstructorUsedError;
  @JsonKey(name: 'sponsor_type')
  String get sponsorType => throw _privateConstructorUsedError;
  @JsonKey(name: 'sponsor_logo')
  String? get sponsorLogo => throw _privateConstructorUsedError;
  @JsonKey(name: 'image_url')
  String? get imageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'video_url')
  String? get videoUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'call_to_action')
  String get callToAction => throw _privateConstructorUsedError;
  @JsonKey(name: 'target_url')
  String get targetUrl => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'start_date')
  DateTime get startDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'end_date')
  DateTime get endDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_impressions')
  int get totalImpressions => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_clicks')
  int get totalClicks => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SponsoredContentCopyWith<SponsoredContent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SponsoredContentCopyWith<$Res> {
  factory $SponsoredContentCopyWith(
          SponsoredContent value, $Res Function(SponsoredContent) then) =
      _$SponsoredContentCopyWithImpl<$Res, SponsoredContent>;
  @useResult
  $Res call(
      {String id,
      String title,
      String content,
      @JsonKey(name: 'sponsor_name') String sponsorName,
      @JsonKey(name: 'sponsor_type') String sponsorType,
      @JsonKey(name: 'sponsor_logo') String? sponsorLogo,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'video_url') String? videoUrl,
      @JsonKey(name: 'call_to_action') String callToAction,
      @JsonKey(name: 'target_url') String targetUrl,
      String status,
      @JsonKey(name: 'start_date') DateTime startDate,
      @JsonKey(name: 'end_date') DateTime endDate,
      @JsonKey(name: 'total_impressions') int totalImpressions,
      @JsonKey(name: 'total_clicks') int totalClicks});
}

/// @nodoc
class _$SponsoredContentCopyWithImpl<$Res, $Val extends SponsoredContent>
    implements $SponsoredContentCopyWith<$Res> {
  _$SponsoredContentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? sponsorName = null,
    Object? sponsorType = null,
    Object? sponsorLogo = freezed,
    Object? imageUrl = freezed,
    Object? videoUrl = freezed,
    Object? callToAction = null,
    Object? targetUrl = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? totalImpressions = null,
    Object? totalClicks = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      sponsorName: null == sponsorName
          ? _value.sponsorName
          : sponsorName // ignore: cast_nullable_to_non_nullable
              as String,
      sponsorType: null == sponsorType
          ? _value.sponsorType
          : sponsorType // ignore: cast_nullable_to_non_nullable
              as String,
      sponsorLogo: freezed == sponsorLogo
          ? _value.sponsorLogo
          : sponsorLogo // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      callToAction: null == callToAction
          ? _value.callToAction
          : callToAction // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalImpressions: null == totalImpressions
          ? _value.totalImpressions
          : totalImpressions // ignore: cast_nullable_to_non_nullable
              as int,
      totalClicks: null == totalClicks
          ? _value.totalClicks
          : totalClicks // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SponsoredContentImplCopyWith<$Res>
    implements $SponsoredContentCopyWith<$Res> {
  factory _$$SponsoredContentImplCopyWith(_$SponsoredContentImpl value,
          $Res Function(_$SponsoredContentImpl) then) =
      __$$SponsoredContentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String content,
      @JsonKey(name: 'sponsor_name') String sponsorName,
      @JsonKey(name: 'sponsor_type') String sponsorType,
      @JsonKey(name: 'sponsor_logo') String? sponsorLogo,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'video_url') String? videoUrl,
      @JsonKey(name: 'call_to_action') String callToAction,
      @JsonKey(name: 'target_url') String targetUrl,
      String status,
      @JsonKey(name: 'start_date') DateTime startDate,
      @JsonKey(name: 'end_date') DateTime endDate,
      @JsonKey(name: 'total_impressions') int totalImpressions,
      @JsonKey(name: 'total_clicks') int totalClicks});
}

/// @nodoc
class __$$SponsoredContentImplCopyWithImpl<$Res>
    extends _$SponsoredContentCopyWithImpl<$Res, _$SponsoredContentImpl>
    implements _$$SponsoredContentImplCopyWith<$Res> {
  __$$SponsoredContentImplCopyWithImpl(_$SponsoredContentImpl _value,
      $Res Function(_$SponsoredContentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? sponsorName = null,
    Object? sponsorType = null,
    Object? sponsorLogo = freezed,
    Object? imageUrl = freezed,
    Object? videoUrl = freezed,
    Object? callToAction = null,
    Object? targetUrl = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? totalImpressions = null,
    Object? totalClicks = null,
  }) {
    return _then(_$SponsoredContentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      sponsorName: null == sponsorName
          ? _value.sponsorName
          : sponsorName // ignore: cast_nullable_to_non_nullable
              as String,
      sponsorType: null == sponsorType
          ? _value.sponsorType
          : sponsorType // ignore: cast_nullable_to_non_nullable
              as String,
      sponsorLogo: freezed == sponsorLogo
          ? _value.sponsorLogo
          : sponsorLogo // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      callToAction: null == callToAction
          ? _value.callToAction
          : callToAction // ignore: cast_nullable_to_non_nullable
              as String,
      targetUrl: null == targetUrl
          ? _value.targetUrl
          : targetUrl // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalImpressions: null == totalImpressions
          ? _value.totalImpressions
          : totalImpressions // ignore: cast_nullable_to_non_nullable
              as int,
      totalClicks: null == totalClicks
          ? _value.totalClicks
          : totalClicks // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SponsoredContentImpl implements _SponsoredContent {
  const _$SponsoredContentImpl(
      {required this.id,
      required this.title,
      required this.content,
      @JsonKey(name: 'sponsor_name') required this.sponsorName,
      @JsonKey(name: 'sponsor_type') required this.sponsorType,
      @JsonKey(name: 'sponsor_logo') this.sponsorLogo,
      @JsonKey(name: 'image_url') this.imageUrl,
      @JsonKey(name: 'video_url') this.videoUrl,
      @JsonKey(name: 'call_to_action') required this.callToAction,
      @JsonKey(name: 'target_url') required this.targetUrl,
      required this.status,
      @JsonKey(name: 'start_date') required this.startDate,
      @JsonKey(name: 'end_date') required this.endDate,
      @JsonKey(name: 'total_impressions') required this.totalImpressions,
      @JsonKey(name: 'total_clicks') required this.totalClicks});

  factory _$SponsoredContentImpl.fromJson(Map<String, dynamic> json) =>
      _$$SponsoredContentImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String content;
  @override
  @JsonKey(name: 'sponsor_name')
  final String sponsorName;
  @override
  @JsonKey(name: 'sponsor_type')
  final String sponsorType;
  @override
  @JsonKey(name: 'sponsor_logo')
  final String? sponsorLogo;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey(name: 'video_url')
  final String? videoUrl;
  @override
  @JsonKey(name: 'call_to_action')
  final String callToAction;
  @override
  @JsonKey(name: 'target_url')
  final String targetUrl;
  @override
  final String status;
  @override
  @JsonKey(name: 'start_date')
  final DateTime startDate;
  @override
  @JsonKey(name: 'end_date')
  final DateTime endDate;
  @override
  @JsonKey(name: 'total_impressions')
  final int totalImpressions;
  @override
  @JsonKey(name: 'total_clicks')
  final int totalClicks;

  @override
  String toString() {
    return 'SponsoredContent(id: $id, title: $title, content: $content, sponsorName: $sponsorName, sponsorType: $sponsorType, sponsorLogo: $sponsorLogo, imageUrl: $imageUrl, videoUrl: $videoUrl, callToAction: $callToAction, targetUrl: $targetUrl, status: $status, startDate: $startDate, endDate: $endDate, totalImpressions: $totalImpressions, totalClicks: $totalClicks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SponsoredContentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.sponsorName, sponsorName) ||
                other.sponsorName == sponsorName) &&
            (identical(other.sponsorType, sponsorType) ||
                other.sponsorType == sponsorType) &&
            (identical(other.sponsorLogo, sponsorLogo) ||
                other.sponsorLogo == sponsorLogo) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.callToAction, callToAction) ||
                other.callToAction == callToAction) &&
            (identical(other.targetUrl, targetUrl) ||
                other.targetUrl == targetUrl) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.totalImpressions, totalImpressions) ||
                other.totalImpressions == totalImpressions) &&
            (identical(other.totalClicks, totalClicks) ||
                other.totalClicks == totalClicks));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      content,
      sponsorName,
      sponsorType,
      sponsorLogo,
      imageUrl,
      videoUrl,
      callToAction,
      targetUrl,
      status,
      startDate,
      endDate,
      totalImpressions,
      totalClicks);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SponsoredContentImplCopyWith<_$SponsoredContentImpl> get copyWith =>
      __$$SponsoredContentImplCopyWithImpl<_$SponsoredContentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SponsoredContentImplToJson(
      this,
    );
  }
}

abstract class _SponsoredContent implements SponsoredContent {
  const factory _SponsoredContent(
      {required final String id,
      required final String title,
      required final String content,
      @JsonKey(name: 'sponsor_name') required final String sponsorName,
      @JsonKey(name: 'sponsor_type') required final String sponsorType,
      @JsonKey(name: 'sponsor_logo') final String? sponsorLogo,
      @JsonKey(name: 'image_url') final String? imageUrl,
      @JsonKey(name: 'video_url') final String? videoUrl,
      @JsonKey(name: 'call_to_action') required final String callToAction,
      @JsonKey(name: 'target_url') required final String targetUrl,
      required final String status,
      @JsonKey(name: 'start_date') required final DateTime startDate,
      @JsonKey(name: 'end_date') required final DateTime endDate,
      @JsonKey(name: 'total_impressions') required final int totalImpressions,
      @JsonKey(name: 'total_clicks')
      required final int totalClicks}) = _$SponsoredContentImpl;

  factory _SponsoredContent.fromJson(Map<String, dynamic> json) =
      _$SponsoredContentImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get content;
  @override
  @JsonKey(name: 'sponsor_name')
  String get sponsorName;
  @override
  @JsonKey(name: 'sponsor_type')
  String get sponsorType;
  @override
  @JsonKey(name: 'sponsor_logo')
  String? get sponsorLogo;
  @override
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @override
  @JsonKey(name: 'video_url')
  String? get videoUrl;
  @override
  @JsonKey(name: 'call_to_action')
  String get callToAction;
  @override
  @JsonKey(name: 'target_url')
  String get targetUrl;
  @override
  String get status;
  @override
  @JsonKey(name: 'start_date')
  DateTime get startDate;
  @override
  @JsonKey(name: 'end_date')
  DateTime get endDate;
  @override
  @JsonKey(name: 'total_impressions')
  int get totalImpressions;
  @override
  @JsonKey(name: 'total_clicks')
  int get totalClicks;
  @override
  @JsonKey(ignore: true)
  _$$SponsoredContentImplCopyWith<_$SponsoredContentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AdSettings _$AdSettingsFromJson(Map<String, dynamic> json) {
  return _AdSettings.fromJson(json);
}

/// @nodoc
mixin _$AdSettings {
  @JsonKey(name: 'ads_enabled')
  bool get adsEnabled => throw _privateConstructorUsedError;
  @JsonKey(name: 'rewarded_ads_enabled')
  bool get rewardedAdsEnabled => throw _privateConstructorUsedError;
  @JsonKey(name: 'sponsored_content_enabled')
  bool get sponsoredContentEnabled => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_ads_per_session')
  int get maxAdsPerSession => throw _privateConstructorUsedError;
  @JsonKey(name: 'min_time_between_ads')
  int get minTimeBetweenAds => throw _privateConstructorUsedError;
  @JsonKey(name: 'skip_ads_for_premium')
  bool get skipAdsForPremium => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_points_per_ad')
  int get basePointsPerAd => throw _privateConstructorUsedError;
  @JsonKey(name: 'bonus_points_multiplier')
  double get bonusPointsMultiplier => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_daily_ad_points')
  int get maxDailyAdPoints => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdSettingsCopyWith<AdSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdSettingsCopyWith<$Res> {
  factory $AdSettingsCopyWith(
          AdSettings value, $Res Function(AdSettings) then) =
      _$AdSettingsCopyWithImpl<$Res, AdSettings>;
  @useResult
  $Res call(
      {@JsonKey(name: 'ads_enabled') bool adsEnabled,
      @JsonKey(name: 'rewarded_ads_enabled') bool rewardedAdsEnabled,
      @JsonKey(name: 'sponsored_content_enabled') bool sponsoredContentEnabled,
      @JsonKey(name: 'max_ads_per_session') int maxAdsPerSession,
      @JsonKey(name: 'min_time_between_ads') int minTimeBetweenAds,
      @JsonKey(name: 'skip_ads_for_premium') bool skipAdsForPremium,
      @JsonKey(name: 'base_points_per_ad') int basePointsPerAd,
      @JsonKey(name: 'bonus_points_multiplier') double bonusPointsMultiplier,
      @JsonKey(name: 'max_daily_ad_points') int maxDailyAdPoints});
}

/// @nodoc
class _$AdSettingsCopyWithImpl<$Res, $Val extends AdSettings>
    implements $AdSettingsCopyWith<$Res> {
  _$AdSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? adsEnabled = null,
    Object? rewardedAdsEnabled = null,
    Object? sponsoredContentEnabled = null,
    Object? maxAdsPerSession = null,
    Object? minTimeBetweenAds = null,
    Object? skipAdsForPremium = null,
    Object? basePointsPerAd = null,
    Object? bonusPointsMultiplier = null,
    Object? maxDailyAdPoints = null,
  }) {
    return _then(_value.copyWith(
      adsEnabled: null == adsEnabled
          ? _value.adsEnabled
          : adsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      rewardedAdsEnabled: null == rewardedAdsEnabled
          ? _value.rewardedAdsEnabled
          : rewardedAdsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      sponsoredContentEnabled: null == sponsoredContentEnabled
          ? _value.sponsoredContentEnabled
          : sponsoredContentEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      maxAdsPerSession: null == maxAdsPerSession
          ? _value.maxAdsPerSession
          : maxAdsPerSession // ignore: cast_nullable_to_non_nullable
              as int,
      minTimeBetweenAds: null == minTimeBetweenAds
          ? _value.minTimeBetweenAds
          : minTimeBetweenAds // ignore: cast_nullable_to_non_nullable
              as int,
      skipAdsForPremium: null == skipAdsForPremium
          ? _value.skipAdsForPremium
          : skipAdsForPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      basePointsPerAd: null == basePointsPerAd
          ? _value.basePointsPerAd
          : basePointsPerAd // ignore: cast_nullable_to_non_nullable
              as int,
      bonusPointsMultiplier: null == bonusPointsMultiplier
          ? _value.bonusPointsMultiplier
          : bonusPointsMultiplier // ignore: cast_nullable_to_non_nullable
              as double,
      maxDailyAdPoints: null == maxDailyAdPoints
          ? _value.maxDailyAdPoints
          : maxDailyAdPoints // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdSettingsImplCopyWith<$Res>
    implements $AdSettingsCopyWith<$Res> {
  factory _$$AdSettingsImplCopyWith(
          _$AdSettingsImpl value, $Res Function(_$AdSettingsImpl) then) =
      __$$AdSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'ads_enabled') bool adsEnabled,
      @JsonKey(name: 'rewarded_ads_enabled') bool rewardedAdsEnabled,
      @JsonKey(name: 'sponsored_content_enabled') bool sponsoredContentEnabled,
      @JsonKey(name: 'max_ads_per_session') int maxAdsPerSession,
      @JsonKey(name: 'min_time_between_ads') int minTimeBetweenAds,
      @JsonKey(name: 'skip_ads_for_premium') bool skipAdsForPremium,
      @JsonKey(name: 'base_points_per_ad') int basePointsPerAd,
      @JsonKey(name: 'bonus_points_multiplier') double bonusPointsMultiplier,
      @JsonKey(name: 'max_daily_ad_points') int maxDailyAdPoints});
}

/// @nodoc
class __$$AdSettingsImplCopyWithImpl<$Res>
    extends _$AdSettingsCopyWithImpl<$Res, _$AdSettingsImpl>
    implements _$$AdSettingsImplCopyWith<$Res> {
  __$$AdSettingsImplCopyWithImpl(
      _$AdSettingsImpl _value, $Res Function(_$AdSettingsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? adsEnabled = null,
    Object? rewardedAdsEnabled = null,
    Object? sponsoredContentEnabled = null,
    Object? maxAdsPerSession = null,
    Object? minTimeBetweenAds = null,
    Object? skipAdsForPremium = null,
    Object? basePointsPerAd = null,
    Object? bonusPointsMultiplier = null,
    Object? maxDailyAdPoints = null,
  }) {
    return _then(_$AdSettingsImpl(
      adsEnabled: null == adsEnabled
          ? _value.adsEnabled
          : adsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      rewardedAdsEnabled: null == rewardedAdsEnabled
          ? _value.rewardedAdsEnabled
          : rewardedAdsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      sponsoredContentEnabled: null == sponsoredContentEnabled
          ? _value.sponsoredContentEnabled
          : sponsoredContentEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      maxAdsPerSession: null == maxAdsPerSession
          ? _value.maxAdsPerSession
          : maxAdsPerSession // ignore: cast_nullable_to_non_nullable
              as int,
      minTimeBetweenAds: null == minTimeBetweenAds
          ? _value.minTimeBetweenAds
          : minTimeBetweenAds // ignore: cast_nullable_to_non_nullable
              as int,
      skipAdsForPremium: null == skipAdsForPremium
          ? _value.skipAdsForPremium
          : skipAdsForPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      basePointsPerAd: null == basePointsPerAd
          ? _value.basePointsPerAd
          : basePointsPerAd // ignore: cast_nullable_to_non_nullable
              as int,
      bonusPointsMultiplier: null == bonusPointsMultiplier
          ? _value.bonusPointsMultiplier
          : bonusPointsMultiplier // ignore: cast_nullable_to_non_nullable
              as double,
      maxDailyAdPoints: null == maxDailyAdPoints
          ? _value.maxDailyAdPoints
          : maxDailyAdPoints // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdSettingsImpl implements _AdSettings {
  const _$AdSettingsImpl(
      {@JsonKey(name: 'ads_enabled') required this.adsEnabled,
      @JsonKey(name: 'rewarded_ads_enabled') required this.rewardedAdsEnabled,
      @JsonKey(name: 'sponsored_content_enabled')
      required this.sponsoredContentEnabled,
      @JsonKey(name: 'max_ads_per_session') required this.maxAdsPerSession,
      @JsonKey(name: 'min_time_between_ads') required this.minTimeBetweenAds,
      @JsonKey(name: 'skip_ads_for_premium') required this.skipAdsForPremium,
      @JsonKey(name: 'base_points_per_ad') required this.basePointsPerAd,
      @JsonKey(name: 'bonus_points_multiplier')
      required this.bonusPointsMultiplier,
      @JsonKey(name: 'max_daily_ad_points') required this.maxDailyAdPoints});

  factory _$AdSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdSettingsImplFromJson(json);

  @override
  @JsonKey(name: 'ads_enabled')
  final bool adsEnabled;
  @override
  @JsonKey(name: 'rewarded_ads_enabled')
  final bool rewardedAdsEnabled;
  @override
  @JsonKey(name: 'sponsored_content_enabled')
  final bool sponsoredContentEnabled;
  @override
  @JsonKey(name: 'max_ads_per_session')
  final int maxAdsPerSession;
  @override
  @JsonKey(name: 'min_time_between_ads')
  final int minTimeBetweenAds;
  @override
  @JsonKey(name: 'skip_ads_for_premium')
  final bool skipAdsForPremium;
  @override
  @JsonKey(name: 'base_points_per_ad')
  final int basePointsPerAd;
  @override
  @JsonKey(name: 'bonus_points_multiplier')
  final double bonusPointsMultiplier;
  @override
  @JsonKey(name: 'max_daily_ad_points')
  final int maxDailyAdPoints;

  @override
  String toString() {
    return 'AdSettings(adsEnabled: $adsEnabled, rewardedAdsEnabled: $rewardedAdsEnabled, sponsoredContentEnabled: $sponsoredContentEnabled, maxAdsPerSession: $maxAdsPerSession, minTimeBetweenAds: $minTimeBetweenAds, skipAdsForPremium: $skipAdsForPremium, basePointsPerAd: $basePointsPerAd, bonusPointsMultiplier: $bonusPointsMultiplier, maxDailyAdPoints: $maxDailyAdPoints)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdSettingsImpl &&
            (identical(other.adsEnabled, adsEnabled) ||
                other.adsEnabled == adsEnabled) &&
            (identical(other.rewardedAdsEnabled, rewardedAdsEnabled) ||
                other.rewardedAdsEnabled == rewardedAdsEnabled) &&
            (identical(
                    other.sponsoredContentEnabled, sponsoredContentEnabled) ||
                other.sponsoredContentEnabled == sponsoredContentEnabled) &&
            (identical(other.maxAdsPerSession, maxAdsPerSession) ||
                other.maxAdsPerSession == maxAdsPerSession) &&
            (identical(other.minTimeBetweenAds, minTimeBetweenAds) ||
                other.minTimeBetweenAds == minTimeBetweenAds) &&
            (identical(other.skipAdsForPremium, skipAdsForPremium) ||
                other.skipAdsForPremium == skipAdsForPremium) &&
            (identical(other.basePointsPerAd, basePointsPerAd) ||
                other.basePointsPerAd == basePointsPerAd) &&
            (identical(other.bonusPointsMultiplier, bonusPointsMultiplier) ||
                other.bonusPointsMultiplier == bonusPointsMultiplier) &&
            (identical(other.maxDailyAdPoints, maxDailyAdPoints) ||
                other.maxDailyAdPoints == maxDailyAdPoints));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      adsEnabled,
      rewardedAdsEnabled,
      sponsoredContentEnabled,
      maxAdsPerSession,
      minTimeBetweenAds,
      skipAdsForPremium,
      basePointsPerAd,
      bonusPointsMultiplier,
      maxDailyAdPoints);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdSettingsImplCopyWith<_$AdSettingsImpl> get copyWith =>
      __$$AdSettingsImplCopyWithImpl<_$AdSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdSettingsImplToJson(
      this,
    );
  }
}

abstract class _AdSettings implements AdSettings {
  const factory _AdSettings(
      {@JsonKey(name: 'ads_enabled') required final bool adsEnabled,
      @JsonKey(name: 'rewarded_ads_enabled')
      required final bool rewardedAdsEnabled,
      @JsonKey(name: 'sponsored_content_enabled')
      required final bool sponsoredContentEnabled,
      @JsonKey(name: 'max_ads_per_session') required final int maxAdsPerSession,
      @JsonKey(name: 'min_time_between_ads')
      required final int minTimeBetweenAds,
      @JsonKey(name: 'skip_ads_for_premium')
      required final bool skipAdsForPremium,
      @JsonKey(name: 'base_points_per_ad') required final int basePointsPerAd,
      @JsonKey(name: 'bonus_points_multiplier')
      required final double bonusPointsMultiplier,
      @JsonKey(name: 'max_daily_ad_points')
      required final int maxDailyAdPoints}) = _$AdSettingsImpl;

  factory _AdSettings.fromJson(Map<String, dynamic> json) =
      _$AdSettingsImpl.fromJson;

  @override
  @JsonKey(name: 'ads_enabled')
  bool get adsEnabled;
  @override
  @JsonKey(name: 'rewarded_ads_enabled')
  bool get rewardedAdsEnabled;
  @override
  @JsonKey(name: 'sponsored_content_enabled')
  bool get sponsoredContentEnabled;
  @override
  @JsonKey(name: 'max_ads_per_session')
  int get maxAdsPerSession;
  @override
  @JsonKey(name: 'min_time_between_ads')
  int get minTimeBetweenAds;
  @override
  @JsonKey(name: 'skip_ads_for_premium')
  bool get skipAdsForPremium;
  @override
  @JsonKey(name: 'base_points_per_ad')
  int get basePointsPerAd;
  @override
  @JsonKey(name: 'bonus_points_multiplier')
  double get bonusPointsMultiplier;
  @override
  @JsonKey(name: 'max_daily_ad_points')
  int get maxDailyAdPoints;
  @override
  @JsonKey(ignore: true)
  _$$AdSettingsImplCopyWith<_$AdSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AdImpression _$AdImpressionFromJson(Map<String, dynamic> json) {
  return _AdImpression.fromJson(json);
}

/// @nodoc
mixin _$AdImpression {
  String get id => throw _privateConstructorUsedError;
  AdPlacement get placement => throw _privateConstructorUsedError;
  @JsonKey(name: 'ad_network')
  AdNetwork get adNetwork => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'session_id')
  String? get sessionId => throw _privateConstructorUsedError;
  @JsonKey(name: 'was_clicked')
  bool get wasClicked => throw _privateConstructorUsedError;
  @JsonKey(name: 'points_awarded')
  int get pointsAwarded => throw _privateConstructorUsedError;
  @JsonKey(name: 'revenue_generated')
  double get revenueGenerated => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdImpressionCopyWith<AdImpression> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdImpressionCopyWith<$Res> {
  factory $AdImpressionCopyWith(
          AdImpression value, $Res Function(AdImpression) then) =
      _$AdImpressionCopyWithImpl<$Res, AdImpression>;
  @useResult
  $Res call(
      {String id,
      AdPlacement placement,
      @JsonKey(name: 'ad_network') AdNetwork adNetwork,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'session_id') String? sessionId,
      @JsonKey(name: 'was_clicked') bool wasClicked,
      @JsonKey(name: 'points_awarded') int pointsAwarded,
      @JsonKey(name: 'revenue_generated') double revenueGenerated,
      @JsonKey(name: 'created_at') DateTime createdAt});

  $AdPlacementCopyWith<$Res> get placement;
  $AdNetworkCopyWith<$Res> get adNetwork;
}

/// @nodoc
class _$AdImpressionCopyWithImpl<$Res, $Val extends AdImpression>
    implements $AdImpressionCopyWith<$Res> {
  _$AdImpressionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? placement = null,
    Object? adNetwork = null,
    Object? userId = freezed,
    Object? sessionId = freezed,
    Object? wasClicked = null,
    Object? pointsAwarded = null,
    Object? revenueGenerated = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      placement: null == placement
          ? _value.placement
          : placement // ignore: cast_nullable_to_non_nullable
              as AdPlacement,
      adNetwork: null == adNetwork
          ? _value.adNetwork
          : adNetwork // ignore: cast_nullable_to_non_nullable
              as AdNetwork,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      wasClicked: null == wasClicked
          ? _value.wasClicked
          : wasClicked // ignore: cast_nullable_to_non_nullable
              as bool,
      pointsAwarded: null == pointsAwarded
          ? _value.pointsAwarded
          : pointsAwarded // ignore: cast_nullable_to_non_nullable
              as int,
      revenueGenerated: null == revenueGenerated
          ? _value.revenueGenerated
          : revenueGenerated // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AdPlacementCopyWith<$Res> get placement {
    return $AdPlacementCopyWith<$Res>(_value.placement, (value) {
      return _then(_value.copyWith(placement: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AdNetworkCopyWith<$Res> get adNetwork {
    return $AdNetworkCopyWith<$Res>(_value.adNetwork, (value) {
      return _then(_value.copyWith(adNetwork: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AdImpressionImplCopyWith<$Res>
    implements $AdImpressionCopyWith<$Res> {
  factory _$$AdImpressionImplCopyWith(
          _$AdImpressionImpl value, $Res Function(_$AdImpressionImpl) then) =
      __$$AdImpressionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      AdPlacement placement,
      @JsonKey(name: 'ad_network') AdNetwork adNetwork,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'session_id') String? sessionId,
      @JsonKey(name: 'was_clicked') bool wasClicked,
      @JsonKey(name: 'points_awarded') int pointsAwarded,
      @JsonKey(name: 'revenue_generated') double revenueGenerated,
      @JsonKey(name: 'created_at') DateTime createdAt});

  @override
  $AdPlacementCopyWith<$Res> get placement;
  @override
  $AdNetworkCopyWith<$Res> get adNetwork;
}

/// @nodoc
class __$$AdImpressionImplCopyWithImpl<$Res>
    extends _$AdImpressionCopyWithImpl<$Res, _$AdImpressionImpl>
    implements _$$AdImpressionImplCopyWith<$Res> {
  __$$AdImpressionImplCopyWithImpl(
      _$AdImpressionImpl _value, $Res Function(_$AdImpressionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? placement = null,
    Object? adNetwork = null,
    Object? userId = freezed,
    Object? sessionId = freezed,
    Object? wasClicked = null,
    Object? pointsAwarded = null,
    Object? revenueGenerated = null,
    Object? createdAt = null,
  }) {
    return _then(_$AdImpressionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      placement: null == placement
          ? _value.placement
          : placement // ignore: cast_nullable_to_non_nullable
              as AdPlacement,
      adNetwork: null == adNetwork
          ? _value.adNetwork
          : adNetwork // ignore: cast_nullable_to_non_nullable
              as AdNetwork,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      wasClicked: null == wasClicked
          ? _value.wasClicked
          : wasClicked // ignore: cast_nullable_to_non_nullable
              as bool,
      pointsAwarded: null == pointsAwarded
          ? _value.pointsAwarded
          : pointsAwarded // ignore: cast_nullable_to_non_nullable
              as int,
      revenueGenerated: null == revenueGenerated
          ? _value.revenueGenerated
          : revenueGenerated // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdImpressionImpl implements _AdImpression {
  const _$AdImpressionImpl(
      {required this.id,
      required this.placement,
      @JsonKey(name: 'ad_network') required this.adNetwork,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'session_id') this.sessionId,
      @JsonKey(name: 'was_clicked') required this.wasClicked,
      @JsonKey(name: 'points_awarded') required this.pointsAwarded,
      @JsonKey(name: 'revenue_generated') required this.revenueGenerated,
      @JsonKey(name: 'created_at') required this.createdAt});

  factory _$AdImpressionImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdImpressionImplFromJson(json);

  @override
  final String id;
  @override
  final AdPlacement placement;
  @override
  @JsonKey(name: 'ad_network')
  final AdNetwork adNetwork;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'session_id')
  final String? sessionId;
  @override
  @JsonKey(name: 'was_clicked')
  final bool wasClicked;
  @override
  @JsonKey(name: 'points_awarded')
  final int pointsAwarded;
  @override
  @JsonKey(name: 'revenue_generated')
  final double revenueGenerated;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  String toString() {
    return 'AdImpression(id: $id, placement: $placement, adNetwork: $adNetwork, userId: $userId, sessionId: $sessionId, wasClicked: $wasClicked, pointsAwarded: $pointsAwarded, revenueGenerated: $revenueGenerated, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdImpressionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.placement, placement) ||
                other.placement == placement) &&
            (identical(other.adNetwork, adNetwork) ||
                other.adNetwork == adNetwork) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.wasClicked, wasClicked) ||
                other.wasClicked == wasClicked) &&
            (identical(other.pointsAwarded, pointsAwarded) ||
                other.pointsAwarded == pointsAwarded) &&
            (identical(other.revenueGenerated, revenueGenerated) ||
                other.revenueGenerated == revenueGenerated) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, placement, adNetwork, userId,
      sessionId, wasClicked, pointsAwarded, revenueGenerated, createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdImpressionImplCopyWith<_$AdImpressionImpl> get copyWith =>
      __$$AdImpressionImplCopyWithImpl<_$AdImpressionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdImpressionImplToJson(
      this,
    );
  }
}

abstract class _AdImpression implements AdImpression {
  const factory _AdImpression(
          {required final String id,
          required final AdPlacement placement,
          @JsonKey(name: 'ad_network') required final AdNetwork adNetwork,
          @JsonKey(name: 'user_id') final String? userId,
          @JsonKey(name: 'session_id') final String? sessionId,
          @JsonKey(name: 'was_clicked') required final bool wasClicked,
          @JsonKey(name: 'points_awarded') required final int pointsAwarded,
          @JsonKey(name: 'revenue_generated')
          required final double revenueGenerated,
          @JsonKey(name: 'created_at') required final DateTime createdAt}) =
      _$AdImpressionImpl;

  factory _AdImpression.fromJson(Map<String, dynamic> json) =
      _$AdImpressionImpl.fromJson;

  @override
  String get id;
  @override
  AdPlacement get placement;
  @override
  @JsonKey(name: 'ad_network')
  AdNetwork get adNetwork;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'session_id')
  String? get sessionId;
  @override
  @JsonKey(name: 'was_clicked')
  bool get wasClicked;
  @override
  @JsonKey(name: 'points_awarded')
  int get pointsAwarded;
  @override
  @JsonKey(name: 'revenue_generated')
  double get revenueGenerated;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$AdImpressionImplCopyWith<_$AdImpressionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
