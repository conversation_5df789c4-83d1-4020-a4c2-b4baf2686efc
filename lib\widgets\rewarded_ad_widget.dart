  import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/advertising_provider.dart';
import '../theme/app_theme.dart';
import '../models/advertising_models.dart';

class RewardedAdWidget extends ConsumerStatefulWidget {
  final String? placementLocation;
  final VoidCallback? onAdCompleted;
  final VoidCallback? onAdFailed;

  const RewardedAdWidget({
    super.key,
    this.placementLocation,
    this.onAdCompleted,
    this.onAdFailed,
  });

  @override
  ConsumerState<RewardedAdWidget> createState() => _RewardedAdWidgetState();
}

class _RewardedAdWidgetState extends ConsumerState<RewardedAdWidget> {
  bool _isWatchingAd = false;
  RewardedAdSession? _currentSession;

  @override
  Widget build(BuildContext context) {
    final advertisingState = ref.watch(advertisingProvider);
    final canWatchAds = advertisingState.canWatchAds;
    final rewardedPlacements = ref.read(advertisingProvider.notifier).getRewardedAdPlacements();

    if (!canWatchAds || rewardedPlacements.isEmpty) {
      return const SizedBox.shrink();
    }

    // Find appropriate placement
    final placement = widget.placementLocation != null
        ? rewardedPlacements.firstWhere(
            (p) => p.location == widget.placementLocation,
            orElse: () => rewardedPlacements.first,
          )
        : rewardedPlacements.first;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.1),
            AppTheme.secondaryColor.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.play_circle_filled,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Watch Ad & Earn Points',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Earn ${placement.pointsReward} points by watching a short video',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _isWatchingAd
                    ? Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color: AppTheme.textSecondary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 8),
                            Text('Loading Ad...'),
                          ],
                        ),
                      )
                    : ElevatedButton.icon(
                        onPressed: () => _watchAd(placement),
                        icon: const Icon(Icons.play_arrow),
                        label: Text('Watch Ad (+${placement.pointsReward} pts)'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
              ),
            ],
          ),
          if (advertisingState.dailyAdPoints > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Today: ${advertisingState.dailyAdPoints} points earned from ads',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _watchAd(AdPlacement placement) async {
    setState(() {
      _isWatchingAd = true;
    });

    try {
      // Start ad session
      final session = await ref.read(advertisingProvider.notifier).startRewardedAd(placement.id);
      
      if (session != null) {
        _currentSession = session;
        
        // Simulate ad watching (in real app, this would integrate with ad SDK)
        await _simulateAdWatching(session);
        
        // Complete ad session
        final completedSession = await ref.read(advertisingProvider.notifier).completeRewardedAd(session.sessionId);
        
        if (completedSession != null && completedSession.rewardClaimed) {
          _showRewardDialog(completedSession.pointsAwarded);
          widget.onAdCompleted?.call();
        } else {
          _showErrorDialog('Failed to claim reward');
          widget.onAdFailed?.call();
        }
      } else {
        _showErrorDialog('Failed to start ad');
        widget.onAdFailed?.call();
      }
    } catch (e) {
      _showErrorDialog('Error watching ad: $e');
      widget.onAdFailed?.call();
    } finally {
      setState(() {
        _isWatchingAd = false;
        _currentSession = null;
      });
    }
  }

  Future<void> _simulateAdWatching(RewardedAdSession session) async {
    // Show ad watching dialog
    if (mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => _AdWatchingDialog(
          session: session,
          onCompleted: () => Navigator.of(context).pop(),
        ),
      );
    }
  }

  void _showRewardDialog(int points) {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.celebration, color: AppTheme.successColor),
              SizedBox(width: 8),
              Text('Reward Earned!'),
            ],
          ),
          content: Text(
            'Congratulations! You earned $points points for watching the ad.',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Awesome!'),
            ),
          ],
        ),
      );
    }
  }

  void _showErrorDialog(String message) {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: AppTheme.errorColor),
              SizedBox(width: 8),
              Text('Ad Error'),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}

class _AdWatchingDialog extends StatefulWidget {
  final RewardedAdSession session;
  final VoidCallback onCompleted;

  const _AdWatchingDialog({
    required this.session,
    required this.onCompleted,
  });

  @override
  State<_AdWatchingDialog> createState() => _AdWatchingDialogState();
}

class _AdWatchingDialogState extends State<_AdWatchingDialog>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _countdown = 15; // 15 second ad

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: _countdown),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);

    _controller.forward();
    _startCountdown();
  }

  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _countdown > 0) {
        setState(() {
          _countdown--;
        });
        _startCountdown();
      } else if (mounted && _countdown == 0) {
        widget.onCompleted();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.tv,
              size: 64,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Watching Ad',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Please wait while the ad plays...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return LinearProgressIndicator(
                  value: _animation.value,
                  backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
                  valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                );
              },
            ),
            const SizedBox(height: 16),
            Text(
              '$_countdown seconds remaining',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Earn ${widget.session.pointsOffered} points',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
