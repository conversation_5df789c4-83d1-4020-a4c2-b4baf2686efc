import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'paginated_response.freezed.dart';
part 'paginated_response.g.dart';

@Freezed(genericArgumentFactories: true)
class PaginatedResponse<T> with _$PaginatedResponse<T> {
  const factory PaginatedResponse({
    required int count,
    String? next,
    String? previous,
    required List<T> results,
  }) = _PaginatedResponse<T>;

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResponseFromJson(json, fromJsonT);
}

// Extension to add convenience getters for test compatibility
extension PaginatedResponseExtension<T> on PaginatedResponse<T> {
  bool get isSuccess => true; // Assume success if object was created
  PaginatedResponse<T>? get data => this; // Return self as data
}