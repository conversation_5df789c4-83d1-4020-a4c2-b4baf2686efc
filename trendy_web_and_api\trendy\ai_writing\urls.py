from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create router for ViewSets
router = DefaultRouter()
router.register(r'preferences', views.AIWritingPreferencesViewSet, basename='ai-preferences')
router.register(r'prompts', views.ContentPromptViewSet, basename='content-prompts')
router.register(r'sessions', views.AIWritingSessionViewSet, basename='ai-sessions')
router.register(r'suggestions', views.ContentSuggestionViewSet, basename='content-suggestions')
router.register(r'templates', views.ContentTemplateViewSet, basename='content-templates')

app_name = 'ai_writing'

urlpatterns = [
    # ViewSet URLs
    path('', include(router.urls)),
    
    # AI Writing Assistant API endpoints
    path('generate/ideas/', views.GenerateContentIdeasView.as_view(), name='generate-ideas'),
    path('generate/outline/', views.GenerateContentOutlineView.as_view(), name='generate-outline'),
    path('improve/grammar/', views.ImproveGrammarView.as_view(), name='improve-grammar'),
    path('generate/seo/', views.GenerateSEOSuggestionsView.as_view(), name='generate-seo'),
    path('complete/text/', views.CompleteTextView.as_view(), name='complete-text'),
    path('analyze/readability/', views.AnalyzeReadabilityView.as_view(), name='analyze-readability'),
    
    # Utility endpoints
    path('user/preferences/', views.get_user_preferences, name='user-preferences'),
    path('session/create/', views.create_writing_session, name='create-session'),
    path('analytics/usage/', views.get_usage_analytics, name='usage-analytics'),

    # AI Configuration
    path('config/info/', views.get_ai_config_info, name='ai-config-info'),
]
