from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import UserWallet

User = get_user_model()


@receiver(post_save, sender=User)
def create_user_wallet(sender, instance, created, **kwargs):
    """Create a wallet when a new user is created"""
    if created:
        UserWallet.objects.create(
            user=instance,
            balance=0.00,
            is_active=True,
            is_verified=getattr(instance, 'is_email_verified', False)
        )
