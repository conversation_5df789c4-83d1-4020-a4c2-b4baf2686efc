from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from monetization.models import UserReferralCode

User = get_user_model()


class Command(BaseCommand):
    help = 'Generate referral codes for existing users who don\'t have them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regenerate codes for all users (including those who already have codes)',
        )

    def handle(self, *args, **options):
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS('🔧 Generating referral codes for users...')
        )
        
        users = User.objects.all()
        total_users = users.count()
        created_count = 0
        updated_count = 0
        skipped_count = 0
        
        for user in users:
            try:
                # Check if user already has a referral code
                existing_code = UserReferralCode.objects.filter(user=user).first()
                
                if existing_code and not force:
                    self.stdout.write(f'   ⏭️  {user.username}: Already has code {existing_code.code}')
                    skipped_count += 1
                    continue
                
                if existing_code and force:
                    # Delete existing code and create new one
                    old_code = existing_code.code
                    existing_code.delete()
                    new_code = UserReferralCode.get_or_create_for_user(user)
                    self.stdout.write(
                        self.style.WARNING(f'   🔄 {user.username}: Updated {old_code} → {new_code.code}')
                    )
                    updated_count += 1
                else:
                    # Create new code
                    new_code = UserReferralCode.get_or_create_for_user(user)
                    self.stdout.write(
                        self.style.SUCCESS(f'   ✅ {user.username}: Created code {new_code.code}')
                    )
                    created_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'   ❌ {user.username}: Error - {str(e)}')
                )
        
        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('📊 Summary:'))
        self.stdout.write(f'   👥 Total users: {total_users}')
        self.stdout.write(f'   ✅ Codes created: {created_count}')
        self.stdout.write(f'   🔄 Codes updated: {updated_count}')
        self.stdout.write(f'   ⏭️  Codes skipped: {skipped_count}')
        
        # Verify uniqueness
        all_codes = UserReferralCode.objects.values_list('code', flat=True)
        unique_codes = set(all_codes)
        
        if len(all_codes) == len(unique_codes):
            self.stdout.write(
                self.style.SUCCESS(f'   🔒 All {len(all_codes)} codes are unique')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'   ⚠️  Found duplicate codes! {len(all_codes)} total, {len(unique_codes)} unique')
            )
        
        self.stdout.write('\n🚀 Referral code generation complete!')
