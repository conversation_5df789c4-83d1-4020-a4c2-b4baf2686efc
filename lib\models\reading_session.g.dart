// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reading_session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReadingSessionImpl _$$ReadingSessionImplFromJson(Map<String, dynamic> json) =>
    _$ReadingSessionImpl(
      id: (json['id'] as num).toInt(),
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      post: Post.fromJson(json['post'] as Map<String, dynamic>),
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] == null
          ? null
          : DateTime.parse(json['end_time'] as String),
      progressPercentage:
          (json['progress_percentage'] as num?)?.toDouble() ?? 0.0,
      readingSpeedWpm: (json['reading_speed_wpm'] as num?)?.toInt(),
      sessionDuration: (json['session_duration'] as num?)?.toInt() ?? 0,
      scrollDepth: (json['scroll_depth'] as num?)?.toDouble() ?? 0.0,
      isCompleted: json['is_completed'] as bool? ?? false,
      deviceType: json['device_type'] as String? ?? 'unknown',
    );

Map<String, dynamic> _$$ReadingSessionImplToJson(
        _$ReadingSessionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user': instance.user,
      'post': instance.post,
      'start_time': instance.startTime.toIso8601String(),
      'end_time': instance.endTime?.toIso8601String(),
      'progress_percentage': instance.progressPercentage,
      'reading_speed_wpm': instance.readingSpeedWpm,
      'session_duration': instance.sessionDuration,
      'scroll_depth': instance.scrollDepth,
      'is_completed': instance.isCompleted,
      'device_type': instance.deviceType,
    };
