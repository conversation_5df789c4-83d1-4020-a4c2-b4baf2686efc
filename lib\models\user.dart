import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:trendy/models/country.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const User._();

  const factory User({
    required int id,
    required String username,
    @Default('') String email, // Made optional with default empty string
    @Json<PERSON>ey(name: 'first_name') @Default('') String firstName,
    @Json<PERSON>ey(name: 'last_name') @Default('') String lastName,
    String? bio,
    @<PERSON>son<PERSON><PERSON>(name: 'avatar_url') String? avatarUrl,
    @Json<PERSON><PERSON>(name: 'phone_number') String? phoneNumber,
    @Json<PERSON>ey(name: 'date_of_birth') String? dateOfBirth,
    String? location,
    String? website,
    @Json<PERSON>ey(name: 'twitter_url') String? twitterUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'linkedin_url') String? linkedinUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'github_url') String? githubUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_email_verified') @Default(false) bool isEmailVerified,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'receive_email_notifications')
    @Default(true)
    bool receiveEmailNotifications,
    @JsonKey(name: 'receive_push_notifications')
    @Default(true)
    bool receivePushNotifications,
    @JsonKey(name: 'is_profile_public') @Default(true) bool isProfilePublic,
    @JsonKey(name: 'is_staff') @Default(false) bool isStaff,
    @JsonKey(name: 'is_superuser') @Default(false) bool isSuperuser,
    @JsonKey(name: 'date_joined') String? dateJoined,
    @JsonKey(name: 'updated_at') String? updatedAt,
    // Regional preferences
    @JsonKey(name: 'preferred_country') Country? preferredCountry,
    @JsonKey(name: 'detected_country') Country? detectedCountry,
    @JsonKey(name: 'show_global_content') @Default(true) bool showGlobalContent,
    @JsonKey(name: 'auto_detect_location')
    @Default(true)
    bool autoDetectLocation,
    // Role-based properties
    @Default('regular_user') String role,
    @JsonKey(name: 'is_content_creator') @Default(false) bool isContentCreator,
    @JsonKey(name: 'is_regular_user') @Default(true) bool isRegularUser,
    @JsonKey(name: 'can_create_content') @Default(false) bool canCreateContent,
    @JsonKey(name: 'can_moderate_content')
    @Default(false)
    bool canModerateContent,
    @Default(<String>[]) List<String> groups,
    @Default(<String, bool>{}) Map<String, bool> permissions,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  String get fullName {
    if (firstName.trim().isNotEmpty && lastName.trim().isNotEmpty) {
      return '$firstName $lastName';
    }
    return username;
  }

  /// Get the effective country for content filtering
  Country? get effectiveCountry => preferredCountry ?? detectedCountry;
}

extension UserExtension on User {
  String get fullName {
    if (firstName.isNotEmpty && lastName.isNotEmpty) {
      return '$firstName $lastName';
    }
    return username;
  }
}

@freezed
class UserProfile with _$UserProfile {
  const UserProfile._();

  const factory UserProfile({
    required int id,
    required String username,
    @Default('') String email, // Made optional with default empty string
    @JsonKey(name: 'first_name') @Default('') String firstName,
    @JsonKey(name: 'last_name') @Default('') String lastName,
    String? bio,
    @JsonKey(name: 'avatar_url') String? avatarUrl,
    String? avatar,
    String? location,
    String? website,
    @JsonKey(name: 'is_email_verified') @Default(false) bool isEmailVerified,
    @JsonKey(name: 'is_profile_public') @Default(true) bool isProfilePublic,
    @JsonKey(name: 'date_joined') String? dateJoined,
    // Social fields
    @JsonKey(name: 'followers_count') @Default(0) int followersCount,
    @JsonKey(name: 'following_count') @Default(0) int followingCount,
    @JsonKey(name: 'posts_count') @Default(0) int postsCount,
    @JsonKey(name: 'is_following') @Default(false) bool isFollowing,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  String get fullName {
    if (firstName.trim().isNotEmpty && lastName.trim().isNotEmpty) {
      return '$firstName $lastName';
    }
    return username;
  }

  String get effectiveAvatarUrl {
    return avatar ?? avatarUrl ?? '';
  }
}
