import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/provider.dart' as provider;
import '../providers/auth_provider.dart';
import 'reading_debug_overlay.dart';

class ReadingEngagementTracker extends ConsumerStatefulWidget {
  final String postId;
  final Widget child;
  final VoidCallback? onPointsAwarded;
  final VoidCallback? onEngagementComplete;

  const ReadingEngagementTracker({
    Key? key,
    required this.postId,
    required this.child,
    this.onPointsAwarded,
    this.onEngagementComplete,
  }) : super(key: key);

  @override
  ConsumerState<ReadingEngagementTracker> createState() => _ReadingEngagementTrackerState();
}

class _ReadingEngagementTrackerState extends ConsumerState<ReadingEngagementTracker> {
  Timer? _engagementTimer;
  Timer? _scrollTrackingTimer;
  ScrollController? _scrollController;
  
  // Engagement tracking
  int _timeSpentSeconds = 0;
  double _maxScrollPercentage = 0.0;
  bool _hasAwardedPoints = false;
  bool _isEngagementActive = false;
  
  // Configuration
  static const int _minEngagementTimeSeconds = 10;
  static const double _minScrollPercentage = 30.0;

  @override
  void initState() {
    super.initState();
    _startEngagementTracking();
  }

  @override
  void dispose() {
    _engagementTimer?.cancel();
    _scrollTrackingTimer?.cancel();
    super.dispose();
  }

  void _startEngagementTracking() {
    // Only track for authenticated users
    final authState = ref.read(enhancedAuthProvider);
    if (!authState.isAuthenticated) {
      print('📖 Reading engagement: User not authenticated, skipping tracking');
      return;
    }

    print('📖 Starting reading engagement tracking for post ${widget.postId}');
    _isEngagementActive = true;

    // Start time tracking (increment every second)
    _engagementTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isEngagementActive) {
        timer.cancel();
        return;
      }

      _timeSpentSeconds++;
      print('📖 Time tracking: ${_timeSpentSeconds}s, Scroll: ${_maxScrollPercentage.toStringAsFixed(1)}%');

      // Check if user has met minimum engagement criteria
      if (!_hasAwardedPoints && _shouldAwardPoints()) {
        print('📖 Criteria met! Attempting to award points...');
        _awardReadingPoints();
      }
    });

    // Start scroll tracking (check every 500ms)
    _scrollTrackingTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!_isEngagementActive) {
        timer.cancel();
        return;
      }
      _updateScrollProgress();
    });
  }

  void _updateScrollProgress() {
    if (_scrollController == null || !_scrollController!.hasClients) {
      print('📖 Scroll tracking: No scroll controller or clients');
      return;
    }

    final maxScrollExtent = _scrollController!.position.maxScrollExtent;
    if (maxScrollExtent <= 0) {
      print('📖 Scroll tracking: Max scroll extent is 0');
      return;
    }

    final currentScroll = _scrollController!.offset;
    final scrollPercentage = (currentScroll / maxScrollExtent * 100).clamp(0.0, 100.0);

    if (scrollPercentage > _maxScrollPercentage) {
      _maxScrollPercentage = scrollPercentage;
      print('📖 Scroll progress updated: ${_maxScrollPercentage.toStringAsFixed(1)}%');
    }
  }

  bool _shouldAwardPoints() {
    return _timeSpentSeconds >= _minEngagementTimeSeconds && 
           _maxScrollPercentage >= _minScrollPercentage;
  }

  Future<void> _awardReadingPoints() async {
    if (_hasAwardedPoints) return;

    try {
      print('📖 Attempting to award reading points - Time: ${_timeSpentSeconds}s, Scroll: ${_maxScrollPercentage.toStringAsFixed(1)}%');
      
      final apiService = ref.read(provider.apiServiceProvider);
      final result = await apiService.awardReadingPoints(
        widget.postId,
        timeSpent: _timeSpentSeconds,
        scrollPercentage: _maxScrollPercentage,
      );

      if (result['success'] == true) {
        _hasAwardedPoints = true;
        print('✅ Reading points awarded successfully!');
        widget.onPointsAwarded?.call();
        
        // Show success message
        if (mounted) {
          _showPointsAwardedSnackBar(result['points_awarded'] ?? 5);
        }
      } else {
        print('⚠️ Reading points not awarded: ${result['message']}');
      }
    } catch (e) {
      print('❌ Error awarding reading points: $e');
    }
  }

  void _showPointsAwardedSnackBar(int points) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.stars, color: Colors.amber, size: 20),
            const SizedBox(width: 8),
            Text('Earned $points reading points!'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _pauseEngagement() {
    _isEngagementActive = false;
    print('📖 Reading engagement paused');
  }

  void _resumeEngagement() {
    if (!_hasAwardedPoints) {
      _isEngagementActive = true;
      print('📖 Reading engagement resumed');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ReadingDebugOverlay(
      postId: widget.postId,
      timeSpent: _timeSpentSeconds,
      scrollPercentage: _maxScrollPercentage,
      hasAwardedPoints: _hasAwardedPoints,
      isEngagementActive: _isEngagementActive,
      child: NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollStartNotification) {
            _resumeEngagement();
          } else if (scrollNotification is ScrollEndNotification) {
            // Don't pause immediately, user might continue reading
          }

          // Update scroll progress directly from scroll notifications
          if (scrollNotification.metrics.maxScrollExtent > 0) {
            final scrollPercentage = (scrollNotification.metrics.pixels /
                scrollNotification.metrics.maxScrollExtent * 100).clamp(0.0, 100.0);
            if (scrollPercentage > _maxScrollPercentage) {
              _maxScrollPercentage = scrollPercentage;
              print('📖 Scroll notification: ${_maxScrollPercentage.toStringAsFixed(1)}%');
            }
          }

          return false;
        },
        child: Builder(
          builder: (context) {
            // Try to find a scroll controller in the widget tree
            final scrollable = Scrollable.maybeOf(context);
            if (scrollable != null && _scrollController != scrollable.widget.controller) {
              _scrollController = scrollable.widget.controller;
              print('📖 Scroll controller found and attached');
            }

            return widget.child;
          },
        ),
      ),
    );
  }
}
