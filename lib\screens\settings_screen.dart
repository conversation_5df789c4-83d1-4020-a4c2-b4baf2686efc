import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../providers/auth_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/theme_provider.dart' as theme_provider;
import '../theme/app_theme.dart';
import '../widgets/settings_section.dart';
import '../widgets/settings_tile.dart';
import '../screens/profile_edit_screen.dart';
import '../screens/change_password_screen.dart';
import '../screens/notifications_screen.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;

  @override
  void initState() {
    super.initState();
    // Load settings when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(settingsProvider.notifier).loadSettings();
    });

    // Listen to scroll changes to show/hide scroll-to-top button
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 200 && !_showScrollToTop) {
      setState(() {
        _showScrollToTop = true;
      });
    } else if (_scrollController.offset <= 200 && _showScrollToTop) {
      setState(() {
        _showScrollToTop = false;
      });
    }
  }

  // Calculate responsive padding based on screen size
  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    // Base padding
    double horizontalPadding = 16.0;
    double topPadding = 16.0;
    double bottomPaddingExtra = 32.0;

    // Adjust for larger screens
    if (screenHeight > 800) {
      horizontalPadding = 20.0;
      topPadding = 20.0;
      bottomPaddingExtra = 48.0;
    }

    // Account for keyboard if visible
    if (keyboardHeight > 0) {
      bottomPaddingExtra = 16.0;
    }

    return EdgeInsets.only(
      left: horizontalPadding,
      right: horizontalPadding,
      top: topPadding,
      bottom: bottomPadding + bottomPaddingExtra,
    );
  }

  // Scroll to top of the settings list
  void _scrollToTop() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final settingsState = ref.watch(settingsProvider);
    final themeState = ref.watch(theme_provider.themeProvider);
    final user = authState.user;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: AppTheme.textPrimary,
        actions: [
          if (settingsState.isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: user == null
          ? _buildNotLoggedIn()
          : _buildSettingsContent(user, settingsState, themeState),
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton(
              onPressed: _scrollToTop,
              backgroundColor: AppTheme.primaryColor,
              child: const Icon(
                Icons.keyboard_arrow_up,
                color: Colors.white,
              ),
            )
          : null,
    );
  }

  Widget _buildNotLoggedIn() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Please log in to access settings',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            key: const ValueKey('settings_login_button'),
            onPressed: () {
              Navigator.pushNamed(context, '/auth');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text('Login'),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent(user, settingsState, themeState) {
    final settings = settingsState.settings;

    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          await ref.read(settingsProvider.notifier).loadSettings();
        },
        color: AppTheme.primaryColor,
        child: AnimationLimiter(
          child: ListView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(
              parent: BouncingScrollPhysics(),
            ),
            padding: _getResponsivePadding(context),
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
            // Profile Section
            SettingsSection(
              title: 'Profile',
              icon: Icons.person_outline,
              children: [
                _buildProfileHeader(user),
                const SizedBox(height: 16),
                SettingsTile(
                  title: 'Edit Profile',
                  subtitle: 'Update your personal information',
                  icon: Icons.edit_outlined,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ProfileEditScreen(),
                    ),
                  ),
                ),
                SettingsTile(
                  title: 'Change Password',
                  subtitle: 'Update your account password',
                  icon: Icons.lock_outline,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ChangePasswordScreen(),
                    ),
                  ),
                ),
                if (!user.isEmailVerified)
                  SettingsTile(
                    title: 'Verify Email',
                    subtitle: 'Verify your email address',
                    icon: Icons.email_outlined,
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Pending',
                        style: TextStyle(
                          color: Colors.orange[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    onTap: () => _resendVerification(),
                  ),
              ],
            ),

            const SizedBox(height: 24),

            // Notifications Section
            SettingsSection(
              title: 'Notifications',
              icon: Icons.notifications_outlined,
              children: [
                SettingsTile(
                  title: 'Notification Center',
                  subtitle: 'View all your notifications',
                  icon: Icons.inbox_outlined,
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationsScreen(),
                    ),
                  ),
                ),
                if (settings != null) ...[
                  SettingsTile(
                    title: 'Email Notifications',
                    subtitle: 'Receive notifications via email',
                    icon: Icons.email_outlined,
                    trailing: Switch(
                      value: settings.emailNotifications,
                      onChanged: (value) => _updateNotificationSetting(
                        'emailNotifications',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                  SettingsTile(
                    title: 'Push Notifications',
                    subtitle: 'Receive push notifications',
                    icon: Icons.notifications_active_outlined,
                    trailing: Switch(
                      value: settings.pushNotifications,
                      onChanged: (value) => _updateNotificationSetting(
                        'pushNotifications',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                  SettingsTile(
                    title: 'Comment Notifications',
                    subtitle: 'Get notified when someone comments',
                    icon: Icons.comment_outlined,
                    trailing: Switch(
                      value: settings.commentNotifications,
                      onChanged: (value) => _updateNotificationSetting(
                        'commentNotifications',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                  SettingsTile(
                    title: 'Like Notifications',
                    subtitle: 'Get notified when someone likes your posts',
                    icon: Icons.favorite_outline,
                    trailing: Switch(
                      value: settings.likeNotifications,
                      onChanged: (value) => _updateNotificationSetting(
                        'likeNotifications',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 24),

            // Privacy Section
            SettingsSection(
              title: 'Privacy',
              icon: Icons.privacy_tip_outlined,
              children: [
                if (settings != null) ...[
                  SettingsTile(
                    title: 'Profile Visibility',
                    subtitle: settings.profileVisibilityDisplayName,
                    icon: Icons.visibility_outlined,
                    onTap: () => _showProfileVisibilityDialog(),
                  ),
                  SettingsTile(
                    title: 'Show Email',
                    subtitle: 'Display email on your profile',
                    icon: Icons.email_outlined,
                    trailing: Switch(
                      value: settings.showEmail,
                      onChanged: (value) => _updatePrivacySetting(
                        'showEmail',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                  SettingsTile(
                    title: 'Show Phone',
                    subtitle: 'Display phone number on your profile',
                    icon: Icons.phone_outlined,
                    trailing: Switch(
                      value: settings.showPhone,
                      onChanged: (value) => _updatePrivacySetting(
                        'showPhone',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 24),

            // Content Section
            SettingsSection(
              title: 'Content',
              icon: Icons.article_outlined,
              children: [
                if (settings != null) ...[
                  SettingsTile(
                    title: 'Posts Per Page',
                    subtitle: '${settings.postsPerPage} posts',
                    icon: Icons.view_list_outlined,
                    onTap: () => _showPostsPerPageDialog(),
                  ),
                  SettingsTile(
                    title: 'Auto-play Videos',
                    subtitle: 'Automatically play videos in feed',
                    icon: Icons.play_circle_outline,
                    trailing: Switch(
                      value: settings.autoPlayVideos,
                      onChanged: (value) => _updateContentSetting(
                        'autoPlayVideos',
                        value,
                      ),
                      activeColor: AppTheme.primaryColor,
                    ),
                  ),
                  SettingsTile(
                    title: 'Theme',
                    subtitle: themeState.themeModeDisplayName,
                    icon: Icons.palette_outlined,
                    onTap: () => _showThemeDialog(),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 24),

            // Account Section
            SettingsSection(
              title: 'Account',
              icon: Icons.account_circle_outlined,
              children: [
                SettingsTile(
                  title: 'Logout',
                  subtitle: 'Sign out of your account',
                  icon: Icons.logout,
                  iconColor: Colors.red,
                  titleColor: Colors.red,
                  onTap: () => _showLogoutDialog(),
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    ),
    ),
    );
  }

  Widget _buildProfileHeader(user) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white.withOpacity(0.2),
            backgroundImage: user.avatarUrl != null ? NetworkImage(user.avatarUrl!) : null,
            child: user.avatarUrl == null
                ? Text(
                    user.username.isNotEmpty ? user.username[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.firstName.isNotEmpty && user.lastName.isNotEmpty
                      ? '${user.firstName} ${user.lastName}'
                      : user.username,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user.email,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
                if (user.bio != null && user.bio!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    user.bio!,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          if (!user.isEmailVerified)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Unverified',
                style: TextStyle(
                  color: Colors.orange[700],
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _updateNotificationSetting(String setting, bool value) async {
    try {
      switch (setting) {
        case 'emailNotifications':
          await ref.read(settingsProvider.notifier).updateNotificationSettings(
            emailNotifications: value,
          );
          break;
        case 'pushNotifications':
          await ref.read(settingsProvider.notifier).updateNotificationSettings(
            pushNotifications: value,
          );
          break;
        case 'commentNotifications':
          await ref.read(settingsProvider.notifier).updateNotificationSettings(
            commentNotifications: value,
          );
          break;
        case 'likeNotifications':
          await ref.read(settingsProvider.notifier).updateNotificationSettings(
            likeNotifications: value,
          );
          break;
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update notification settings: ${e.toString()}');
    }
  }

  Future<void> _updatePrivacySetting(String setting, bool value) async {
    try {
      switch (setting) {
        case 'showEmail':
          await ref.read(settingsProvider.notifier).updatePrivacySettings(
            showEmail: value,
          );
          break;
        case 'showPhone':
          await ref.read(settingsProvider.notifier).updatePrivacySettings(
            showPhone: value,
          );
          break;
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update privacy settings: ${e.toString()}');
    }
  }

  Future<void> _updateContentSetting(String setting, dynamic value) async {
    try {
      switch (setting) {
        case 'autoPlayVideos':
          await ref.read(settingsProvider.notifier).updateContentSettings(
            autoPlayVideos: value as bool,
          );
          break;
        case 'postsPerPage':
          await ref.read(settingsProvider.notifier).updateContentSettings(
            postsPerPage: value as int,
          );
          break;
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update content settings: ${e.toString()}');
    }
  }

  Future<void> _resendVerification() async {
    try {
      await ref.read(enhancedAuthProvider.notifier).resendEmailVerification();
      _showSuccessSnackBar('Verification email sent successfully!');
    } catch (e) {
      _showErrorSnackBar('Failed to send verification email: ${e.toString()}');
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            key: const ValueKey('settings_logout_button'),
            onPressed: () async {
              final navigator = Navigator.of(context);
              navigator.pop();
              await ref.read(enhancedAuthProvider.notifier).logout();
              if (mounted) {
                navigator.pushNamedAndRemoveUntil('/', (route) => false);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showProfileVisibilityDialog() {
    // Implementation will be added
  }



  void _showPostsPerPageDialog() {
    // Implementation will be added
  }

  void _showThemeDialog() {
    final currentThemeMode = ref.read(theme_provider.themeProvider).themeMode;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Theme'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildThemeDialogOption(
              theme_provider.ThemeMode.light,
              'Light',
              'Light theme for daytime use',
              Icons.light_mode,
              currentThemeMode,
            ),
            _buildThemeDialogOption(
              theme_provider.ThemeMode.dark,
              'Dark',
              'Dark theme for nighttime use',
              Icons.dark_mode,
              currentThemeMode,
            ),
            _buildThemeDialogOption(
              theme_provider.ThemeMode.system,
              'System',
              'Follow system theme settings',
              Icons.settings_brightness,
              currentThemeMode,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeDialogOption(
    theme_provider.ThemeMode themeMode,
    String title,
    String subtitle,
    IconData icon,
    theme_provider.ThemeMode currentThemeMode,
  ) {
    final isSelected = currentThemeMode == themeMode;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondary,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? AppTheme.primaryColor : AppTheme.textPrimary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 12,
          color: AppTheme.textTertiary,
        ),
      ),
      trailing: isSelected
          ? const Icon(
              Icons.check_circle,
              color: AppTheme.primaryColor,
            )
          : null,
      onTap: () {
        Navigator.pop(context);
        ref.read(theme_provider.themeProvider.notifier).setThemeMode(themeMode);
      },
    );
  }
}
