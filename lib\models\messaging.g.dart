// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'messaging.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MessageImpl _$$MessageImplFromJson(Map<String, dynamic> json) =>
    _$MessageImpl(
      id: (json['id'] as num).toInt(),
      content: json['content'] as String,
      sender: User.fromJson(json['sender'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isRead: json['is_read'] as bool,
      isDeleted: json['is_deleted'] as bool,
      isOwnMessage: json['is_own_message'] as bool,
    );

Map<String, dynamic> _$$MessageImplToJson(_$MessageImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'content': instance.content,
      'sender': instance.sender,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'is_read': instance.isRead,
      'is_deleted': instance.isDeleted,
      'is_own_message': instance.isOwnMessage,
    };

_$ConversationImpl _$$ConversationImplFromJson(Map<String, dynamic> json) =>
    _$ConversationImpl(
      id: (json['id'] as num).toInt(),
      participants: (json['participants'] as List<dynamic>)
          .map((e) => User.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool,
      lastMessage: json['last_message'] == null
          ? null
          : Message.fromJson(json['last_message'] as Map<String, dynamic>),
      unreadCount: (json['unread_count'] as num).toInt(),
      otherParticipant: json['other_participant'] == null
          ? null
          : User.fromJson(json['other_participant'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ConversationImplToJson(_$ConversationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'participants': instance.participants,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'is_active': instance.isActive,
      'last_message': instance.lastMessage,
      'unread_count': instance.unreadCount,
      'other_participant': instance.otherParticipant,
    };

_$ConversationResponseImpl _$$ConversationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ConversationResponseImpl(
      count: (json['count'] as num).toInt(),
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: (json['results'] as List<dynamic>)
          .map((e) => Conversation.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ConversationResponseImplToJson(
        _$ConversationResponseImpl instance) =>
    <String, dynamic>{
      'count': instance.count,
      'next': instance.next,
      'previous': instance.previous,
      'results': instance.results,
    };

_$MessageListResponseImpl _$$MessageListResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$MessageListResponseImpl(
      count: (json['count'] as num).toInt(),
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: (json['results'] as List<dynamic>)
          .map((e) => Message.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$MessageListResponseImplToJson(
        _$MessageListResponseImpl instance) =>
    <String, dynamic>{
      'count': instance.count,
      'next': instance.next,
      'previous': instance.previous,
      'results': instance.results,
    };

_$StartConversationRequestImpl _$$StartConversationRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$StartConversationRequestImpl(
      username: json['username'] as String,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$StartConversationRequestImplToJson(
        _$StartConversationRequestImpl instance) =>
    <String, dynamic>{
      'username': instance.username,
      'message': instance.message,
    };

_$StartConversationResponseImpl _$$StartConversationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$StartConversationResponseImpl(
      conversation:
          Conversation.fromJson(json['conversation'] as Map<String, dynamic>),
      created: json['created'] as bool,
      message: json['message'] as String,
    );

Map<String, dynamic> _$$StartConversationResponseImplToJson(
        _$StartConversationResponseImpl instance) =>
    <String, dynamic>{
      'conversation': instance.conversation,
      'created': instance.created,
      'message': instance.message,
    };

_$SendMessageRequestImpl _$$SendMessageRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$SendMessageRequestImpl(
      content: json['content'] as String,
    );

Map<String, dynamic> _$$SendMessageRequestImplToJson(
        _$SendMessageRequestImpl instance) =>
    <String, dynamic>{
      'content': instance.content,
    };

_$UnreadCountResponseImpl _$$UnreadCountResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$UnreadCountResponseImpl(
      unreadCount: (json['unread_count'] as num).toInt(),
    );

Map<String, dynamic> _$$UnreadCountResponseImplToJson(
        _$UnreadCountResponseImpl instance) =>
    <String, dynamic>{
      'unread_count': instance.unreadCount,
    };
