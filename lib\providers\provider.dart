import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/category.dart';
import '../models/comment.dart';
import '../models/paginated_response.dart';
import '../models/post.dart';
import '../models/post_filter.dart';
import '../models/user.dart';
import '../services/api_service.dart';
import 'auth_provider.dart' as auth_provider;
import 'like_provider.dart';
import 'regional_provider.dart';

// API Service Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

// Auth Provider
final authProvider =
    StateNotifierProvider<AuthNotifier, AsyncValue<User?>>((ref) {
  return AuthNotifier(ref.read(apiServiceProvider));
});

// Posts Provider
final postsProvider =
    StateNotifierProvider<PostsNotifier, AsyncValue<PaginatedResponse<Post>>>(
        (ref) {
  return PostsNotifier(
    ref.read(apiServiceProvider),
    ref.read(auth_provider.enhancedAuthProvider.notifier),
    ref.read(likeProvider.notifier),
  );
});

// Regional Posts Provider - automatically applies regional filtering
final regionalPostsProvider = StateNotifierProvider<RegionalPostsNotifier,
    AsyncValue<PaginatedResponse<Post>>>((ref) {
  return RegionalPostsNotifier(
    ref.read(apiServiceProvider),
    ref.read(auth_provider.enhancedAuthProvider.notifier),
    ref.read(likeProvider.notifier),
    ref,
  );
});

// Individual Post Provider
final postProvider =
    StateNotifierProvider.family<PostNotifier, AsyncValue<Post>, String>(
        (ref, slug) {
  return PostNotifier(
      ref.watch(apiServiceProvider),
      ref.watch(auth_provider.enhancedAuthProvider.notifier),
      ref.watch(likeProvider.notifier),
      slug);
});

// Comments Provider
final commentsProvider = StateNotifierProvider.family<CommentsNotifier,
    AsyncValue<PaginatedResponse<Comment>>, String>((ref, pk) {
  return CommentsNotifier(ref.watch(apiServiceProvider), pk);
});

// Categories Provider
final categoriesProvider = StateNotifierProvider<CategoriesNotifier,
    AsyncValue<PaginatedResponse<Category>>>((ref) {
  return CategoriesNotifier(ref.read(apiServiceProvider));
});

// Profile Provider
final profileProvider =
    StateNotifierProvider<ProfileNotifier, AsyncValue<User?>>((ref) {
  return ProfileNotifier(ref.watch(apiServiceProvider));
});

// Filtered Posts Provider
final filteredPostsProvider = Provider.family<
    AsyncValue<PaginatedResponse<Post>>,
    ({String? category, String? searchQuery})>((ref, params) {
  final postsNotifier = ref.watch(postsProvider.notifier);

  // Trigger a new fetch with the filters
  postsNotifier.getPosts(
    category: params.category,
    search: params.searchQuery,
  );

  // Return the current state
  return ref.watch(postsProvider);
});

// Auth Notifier
class AuthNotifier extends StateNotifier<AsyncValue<User?>> {
  final ApiService _apiService;

  AuthNotifier(this._apiService) : super(const AsyncValue.loading()) {
    _init();
  }

  Future<void> _init() async {
    try {
      final user = await _apiService.getCurrentUser();
      state = AsyncValue.data(user);
    } catch (e) {
      state = const AsyncValue.data(null);
    }
  }

  Future<void> login(String username, String password) async {
    try {
      state = const AsyncValue.loading();
      final user = await _apiService.login(username, password);
      state = AsyncValue.data(user);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> register(String username, String email, String password,
      String firstName, String lastName) async {
    try {
      state = const AsyncValue.loading();
      final user = await _apiService.register(
          username, email, password, firstName, lastName);
      state = AsyncValue.data(user);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> logout() async {
    try {
      await _apiService.logout();
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Posts Notifier
class PostsNotifier extends StateNotifier<AsyncValue<PaginatedResponse<Post>>> {
  final ApiService _apiService;
  final auth_provider.AuthNotifier _authNotifier;
  final LikeNotifier _likeNotifier;
  int _currentPage = 1;
  bool _isLoadingMore = false;
  String? _currentCategory;
  String? _currentSearch;

  PostsNotifier(this._apiService, this._authNotifier, this._likeNotifier)
      : super(const AsyncValue.loading()) {
    getPosts();
  }

  bool get isLoadingMore => _isLoadingMore;
  bool get hasMorePages {
    final currentState = state.value;
    return currentState?.next != null;
  }

  Future<void> getPosts({
    int page = 1,
    int pageSize = 10,
    String? category,
    String? search,
    String? country,
    bool? showGlobal,
    bool refresh = false,
  }) async {
    try {
      if (refresh || page == 1) {
        state = const AsyncValue.loading();
        _currentPage = 1;
      }

      _currentCategory = category;
      _currentSearch = search;

      final posts = await _apiService.getPosts(
        page: page,
        pageSize: pageSize,
        category: category,
        search: search,
        country: country,
        showGlobal: showGlobal,
      );

      if (page == 1 || refresh) {
        state = AsyncValue.data(posts);
        // Initialize like state from posts
        _likeNotifier.initializeFromPosts(posts.results);
      } else {
        // Append to existing posts for infinite scroll
        final currentState = state.value;
        if (currentState != null) {
          final updatedPosts = PaginatedResponse<Post>(
            count: posts.count,
            next: posts.next,
            previous: posts.previous,
            results: [...currentState.results, ...posts.results],
          );
          state = AsyncValue.data(updatedPosts);
          // Initialize like state from new posts
          _likeNotifier.initializeFromPosts(posts.results);
        }
      }
      _currentPage = page;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> loadMorePosts() async {
    if (_isLoadingMore || !hasMorePages) return;

    try {
      _isLoadingMore = true;
      await getPosts(
        page: _currentPage + 1,
        category: _currentCategory,
        search: _currentSearch,
      );
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> refreshPosts() async {
    await getPosts(
      category: _currentCategory,
      search: _currentSearch,
      refresh: true,
    );
  }

  Future<void> toggleLike(int postId) async {
    try {
      final authState = _authNotifier.state;
      if (!authState.isAuthenticated) {
        throw Exception('Please log in to like posts');
      }

      // Use centralized like provider
      await _likeNotifier.togglePostLike(postId);

      // Update local state to reflect like changes
      final currentState = state.value;
      if (currentState != null) {
        final updatedPosts = currentState.results.map((post) {
          if (post.id == postId) {
            return post.copyWith(
              isLiked: _likeNotifier.state.isPostLiked(postId),
              likeCount: _likeNotifier.state.getPostLikeCount(postId),
            );
          }
          return post;
        }).toList();
        state = AsyncValue.data(currentState.copyWith(results: updatedPosts));
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Post Notifier
class PostNotifier extends StateNotifier<AsyncValue<Post>> {
  final ApiService _apiService;
  final auth_provider.AuthNotifier _authNotifier;
  final LikeNotifier _likeNotifier;
  final String _pk;

  PostNotifier(
      this._apiService, this._authNotifier, this._likeNotifier, this._pk)
      : super(const AsyncValue.loading()) {
    getPost();
  }

  Future<void> getPost() async {
    try {
      state = const AsyncValue.loading();
      final post = await _apiService.getPost(_pk);
      state = AsyncValue.data(post);

      // Initialize like state for this post
      _likeNotifier.updatePostLike(post.id, post.isLiked, post.likeCount);

      // Track view count when post is loaded (don't await to avoid blocking UI)
      _apiService.incrementPostView(_pk).then((_) {
        print('✅ View count incremented for post $_pk');
      }).catchError((e) {
        print('❌ Failed to increment view count: $e');
      });
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> toggleLike() async {
    try {
      final authState = _authNotifier.state;
      if (!authState.isAuthenticated) {
        throw Exception('Please log in to like posts');
      }

      final currentState = state.value;
      if (currentState == null) return;

      // Use centralized like provider
      await _likeNotifier.togglePostLike(currentState.id);

      // Update local state to reflect like changes
      state = AsyncValue.data(currentState.copyWith(
        isLiked: _likeNotifier.state.isPostLiked(currentState.id),
        likeCount: _likeNotifier.state.getPostLikeCount(currentState.id),
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Comments Notifier
class CommentsNotifier
    extends StateNotifier<AsyncValue<PaginatedResponse<Comment>>> {
  final ApiService _apiService;
  final String _pk;
  int _currentPage = 1;

  CommentsNotifier(this._apiService, this._pk)
      : super(const AsyncValue.loading()) {
    getComments();
  }

  Future<void> getComments() async {
    try {
      print('CommentsNotifier: Fetching comments for post $_pk');
      final comments = await _apiService.getComments(_pk, page: _currentPage);
      print(
          'CommentsNotifier: Received ${comments.results.length} top-level comments');

      // The API now returns properly nested comments, so we can use them directly
      for (var comment in comments.results) {
        print(
            'CommentsNotifier: Comment ${comment.id} has ${comment.replies.length} replies');
      }

      print('CommentsNotifier: Comments loaded successfully');

      // Update the state with the comments (already properly nested from API)
      state = AsyncValue.data(comments);
    } catch (e) {
      print('CommentsNotifier: Error fetching comments: $e');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> loadMore() async {
    final currentState = state;
    if (currentState is! AsyncData<PaginatedResponse<Comment>>) return;
    if (currentState.value.next == null) return;

    try {
      _currentPage++;
      final response = await _apiService.getComments(_pk, page: _currentPage);
      state = AsyncValue.data(PaginatedResponse(
        count: response.count,
        next: response.next,
        previous: response.previous,
        results: [...currentState.value.results, ...response.results],
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> createComment(String content, {int? parentId}) async {
    try {
      await _apiService.createComment(_pk, content, parentId: parentId);

      // After creating a comment, refresh the entire comment list to get the updated structure
      await getComments();
    } catch (e) {
      print('CommentsNotifier: Error creating comment: $e');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> deleteComment(int commentId) async {
    try {
      final user = await _apiService.getCurrentUser();
      if (user == null) {
        throw Exception('Please log in to delete comments');
      }

      await _apiService.deleteComment(_pk, commentId);
      final currentState = state;
      if (currentState is! AsyncData<PaginatedResponse<Comment>>) return;

      state = AsyncValue.data(PaginatedResponse(
        count: currentState.value.count - 1,
        next: currentState.value.next,
        previous: currentState.value.previous,
        results: currentState.value.results
            .where((comment) => comment.id != commentId)
            .toList(),
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> toggleCommentLike(int commentId) async {
    try {
      final user = await _apiService.getCurrentUser();
      if (user == null) {
        throw Exception('Please log in to like comments');
      }

      final currentState = state;
      if (currentState is! AsyncData<PaginatedResponse<Comment>>) return;

      void updateCommentLike(List<Comment> comments, int targetId) {
        for (int i = 0; i < comments.length; i++) {
          if (comments[i].id == targetId) {
            comments[i] = comments[i].copyWith(
              isLiked: !comments[i].isLiked,
              likeCount: comments[i].isLiked
                  ? comments[i].likeCount - 1
                  : comments[i].likeCount + 1,
            );
            return;
          }
          if (comments[i].replies.isNotEmpty) {
            updateCommentLike(comments[i].replies, targetId);
          }
        }
      }

      final updatedComments = [...currentState.value.results];
      updateCommentLike(updatedComments, commentId);

      state = AsyncValue.data(currentState.value.copyWith(
        results: updatedComments,
      ));

      await _apiService.toggleCommentLike(commentId);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Categories Notifier
class CategoriesNotifier
    extends StateNotifier<AsyncValue<PaginatedResponse<Category>>> {
  final ApiService _apiService;

  CategoriesNotifier(this._apiService) : super(const AsyncValue.loading()) {
    getCategories();
  }

  Future<void> getCategories() async {
    try {
      state = const AsyncValue.loading();
      final categories = await _apiService.getCategories();
      state = AsyncValue.data(categories);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Profile Notifier
class ProfileNotifier extends StateNotifier<AsyncValue<User?>> {
  final ApiService _apiService;

  ProfileNotifier(this._apiService) : super(const AsyncValue.loading()) {
    getCurrentUser();
  }

  Future<void> getCurrentUser() async {
    try {
      final user = await _apiService.getCurrentUser();
      state = AsyncValue.data(user);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final user = await _apiService.updateUserProfile(profileData);
      state = AsyncValue.data(user);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

// Regional Posts Notifier - extends PostsNotifier with regional filtering
class RegionalPostsNotifier
    extends StateNotifier<AsyncValue<PaginatedResponse<Post>>> {
  final ApiService _apiService;
  final auth_provider.AuthNotifier _authNotifier;
  final LikeNotifier _likeNotifier;
  final Ref _ref;
  int _currentPage = 1;
  bool _isLoadingMore = false;
  String? _currentCategory;
  String? _currentSearch;

  RegionalPostsNotifier(
      this._apiService, this._authNotifier, this._likeNotifier, this._ref)
      : super(const AsyncValue.loading()) {
    getPosts();

    // Listen to regional preferences changes and refresh posts
    _ref.listen(regionalPostsFilterProvider, (previous, next) {
      if (previous != next) {
        print('🔄 Regional filter changed: $previous -> $next');
        getPosts(refresh: true);
      }
    });
  }

  bool get isLoadingMore => _isLoadingMore;
  bool get hasMorePages {
    final currentState = state.value;
    return currentState?.next != null;
  }

  Future<void> getPosts({
    int page = 1,
    int pageSize = 10,
    String? category,
    String? search,
    bool refresh = false,
  }) async {
    try {
      if (refresh || page == 1) {
        state = const AsyncValue.loading();
        _currentPage = 1;
      }

      _currentCategory = category;
      _currentSearch = search;

      // Get regional filtering parameters
      final regionalFilters = _ref.read(regionalPostsFilterProvider);
      final country = regionalFilters['country'] as String?;
      final showGlobal = regionalFilters['show_global'] as bool?;

      print(
          '📡 API Call: getPosts(country: $country, showGlobal: $showGlobal, page: $page)');

      final posts = await _apiService.getPosts(
        page: page,
        pageSize: pageSize,
        category: category,
        search: search,
        country: country,
        showGlobal: showGlobal,
      );

      print(
          '📊 API Response: ${posts.count} total posts, ${posts.results.length} in this page');

      if (page == 1 || refresh) {
        state = AsyncValue.data(posts);
        // Initialize like state from posts
        _likeNotifier.initializeFromPosts(posts.results);
      } else {
        // Load more posts
        final currentState = state.value;
        if (currentState != null) {
          final updatedPosts = PaginatedResponse<Post>(
            count: posts.count,
            next: posts.next,
            previous: posts.previous,
            results: [...currentState.results, ...posts.results],
          );
          state = AsyncValue.data(updatedPosts);
          // Initialize like state from new posts
          _likeNotifier.initializeFromPosts(posts.results);
        }
      }
      _currentPage = page;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> loadMorePosts() async {
    if (_isLoadingMore || !hasMorePages) return;

    try {
      _isLoadingMore = true;
      await getPosts(
        page: _currentPage + 1,
        category: _currentCategory,
        search: _currentSearch,
      );
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> refreshPosts() async {
    await getPosts(
      category: _currentCategory,
      search: _currentSearch,
      refresh: true,
    );
  }

  // Refresh posts when regional preferences change
  void refreshForRegionalChange() {
    getPosts(refresh: true);
  }

  Future<void> toggleLike(int postId) async {
    try {
      final authState = _authNotifier.state;
      if (!authState.isAuthenticated) {
        throw Exception('You must be logged in to like posts');
      }

      await _likeNotifier.togglePostLike(postId);

      // Update the post in the current state
      final currentState = state.value;
      if (currentState != null) {
        final updatedPosts = currentState.results.map((post) {
          if (post.id == postId) {
            return post.copyWith(
              isLiked: _likeNotifier.state.isPostLiked(postId),
              likeCount: _likeNotifier.state.getPostLikeCount(postId),
            );
          }
          return post;
        }).toList();
        state = AsyncValue.data(currentState.copyWith(results: updatedPosts));
      }
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}
