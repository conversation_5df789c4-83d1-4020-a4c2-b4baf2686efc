# 🔗 Referral System & Email Functionality Guide

## **🎯 REFERRAL CODE SYSTEM**

### **Where Users Enter Referral Codes** 📝

Users can enter referral codes during registration in the **Enhanced Auth Screen**:

#### **Registration Form Location:**
- **Screen**: `lib/screens/enhanced_auth_screen.dart`
- **Field**: "Referral Code (Optional)" 
- **Position**: Below password confirmation, before register button
- **Icon**: Gift card icon (🎁)
- **Validation**: Optional field, shows checkmark when filled

#### **Referral Code Format:**
```
TRENDY_USERNAME_USERID
Example: TRENDY_SARAH_JOHNSON_123
```

#### **How It Works:**
1. **User Registration**: New user enters friend's referral code
2. **Code Validation**: System validates code format and user existence
3. **Relationship Creation**: Creates referral relationship in database
4. **Reward Distribution**: Awards points/money to both users
5. **Email Notifications**: Sends confirmation emails

### **Referral Code Generation** 🔗

Every user automatically gets a referral code when they join:

```dart
// Format: TRENDY_USERNAME_USERID
String referralCode = "TRENDY_${user.username.toUpperCase()}_${user.id}";
```

#### **Where Users Find Their Code:**
- **Profile Screen**: Display prominently
- **Referral Section**: Dedicated referral page
- **Welcome Email**: Included in verification email
- **Share Feature**: Easy copy/share functionality

### **Referral Rewards System** 💰

#### **Reward Structure:**
```
📊 Referral Earnings:
├── Join Bonus: $2 (when friend registers)
├── Level 5 Bonus: $2 (when friend reaches level 5)
└── Premium Bonus: $5 (when friend upgrades to premium)

Total Potential: $9 per successful referral
```

#### **Reward Processing:**
1. **Immediate**: Join bonus awarded instantly
2. **Milestone**: Level/premium bonuses when achieved
3. **Email Notification**: Both users notified
4. **Wallet Credit**: Money added to referrer's wallet

## **📧 EMAIL FUNCTIONALITY**

### **Email Types Implemented** ✉️

#### **1. Account Verification Email** ✅
- **Trigger**: User registration
- **Purpose**: Verify email address
- **Content**: Welcome message + verification link
- **Expiry**: 24 hours
- **Template**: Beautiful HTML with gradient design

#### **2. Password Reset Email** 🔑
- **Trigger**: Forgot password request
- **Purpose**: Secure password reset
- **Content**: Reset link with security notice
- **Expiry**: 1 hour
- **Security**: Token-based authentication

#### **3. Welcome Email** 🎉
- **Trigger**: Email verification completion
- **Purpose**: Onboard new users
- **Content**: How to earn, referral code, features
- **Includes**: User's personal referral code

#### **4. PayPal Verification Email** 💳
- **Trigger**: PayPal email setup
- **Purpose**: Verify PayPal for rewards
- **Content**: 6-digit verification code
- **Expiry**: 15 minutes

#### **5. Referral Reward Email** 🎁
- **Trigger**: Referral milestone achieved
- **Purpose**: Notify about earned rewards
- **Content**: Reward amount, milestone details
- **Variants**: Join, Level 5, Premium bonuses

#### **6. Reward Notification Emails** 💰
- **Triggers**: Reward claimed/approved/paid
- **Purpose**: Update on reward status
- **Content**: Amount, status, next steps
- **Types**: Claimed, Approved, Paid

### **Email Configuration** ⚙️

#### **Development Mode:**
```python
# Console backend - emails appear in terminal
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

#### **Production Mode:**
```python
# SMTP backend - real email sending
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'
```

#### **Environment Variables:**
```bash
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
FRONTEND_URL=http://localhost:3000
```

### **Email Templates** 🎨

#### **Design Features:**
- **Responsive**: Works on all devices
- **Gradient Headers**: Beautiful visual design
- **Clear CTAs**: Prominent action buttons
- **Security Notices**: Important warnings highlighted
- **Brand Consistent**: Trendy app styling

#### **Template Structure:**
```html
<!DOCTYPE html>
<html>
<head>
    <style>/* Responsive CSS */</style>
</head>
<body>
    <div class="container">
        <div class="header"><!-- Gradient header --></div>
        <div class="content"><!-- Main content --></div>
        <div class="footer"><!-- Footer info --></div>
    </div>
</body>
</html>
```

## **🧹 SYSTEM CLEANUP SCRIPT**

### **Reset System Script** 🔄

#### **Purpose:**
Complete system reset with fresh data for development

#### **Usage:**
```bash
./reset_system.sh
```

#### **What It Does:**
1. **🗑️ Deletes** all migrations and database
2. **🆕 Creates** fresh migrations and schema
3. **👥 Adds** 6 users (1 admin + 5 test users)
4. **💰 Funds** each wallet with $100
5. **📝 Creates** 5 sample blog posts
6. **📂 Adds** 7 content categories
7. **🎮 Sets up** gamification (150 points each)
8. **⚙️ Configures** monetization settings
9. **💾 Creates** backup of fresh database

#### **Created Users:**
```
Admin: admin / admin123
Users: sarah_johnson, mike_chen, alex_rivera, testuser, demo_user / password123
```

#### **Sample Data:**
- **Categories**: Technology, Lifestyle, Business, Health, Travel, Entertainment, Education
- **Posts**: Welcome guide, earning tips, wallet features, referral program, community guidelines
- **Wallets**: $100 starting balance for all users
- **Points**: 150 points and Level 2 for all users

### **Email Testing Script** 📧

#### **Purpose:**
Test all email functionality

#### **Usage:**
```bash
./test_email.sh
```

#### **Tests Performed:**
1. **Email Configuration**: Verify settings
2. **Basic Sending**: Test SMTP connection
3. **Registration Email**: Account verification
4. **Password Reset**: Reset token email
5. **Welcome Email**: Post-verification welcome
6. **Referral Reward**: Referral bonus notification

## **🚀 QUICK START GUIDE**

### **Fresh Development Setup:**
```bash
# 1. Reset system with fresh data
./reset_system.sh

# 2. Test email functionality
./test_email.sh

# 3. Start Django server
cd trendy_web_and_api/trendy
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000

# 4. Start Flutter app
cd ../../
flutter run
```

### **Testing Referral System:**
1. **Register** new user with referral code: `TRENDY_ADMIN_1`
2. **Check** admin wallet for $2 join bonus
3. **Level up** new user to Level 5 for $2 bonus
4. **Upgrade** new user to premium for $5 bonus
5. **Verify** email notifications sent

### **Testing Email System:**
1. **Register** new user (verification email sent)
2. **Request** password reset (reset email sent)
3. **Complete** verification (welcome email sent)
4. **Add** PayPal email (verification code sent)
5. **Check** terminal console for email content

## **🔧 TROUBLESHOOTING**

### **Common Issues:**

#### **Referral Code Not Working:**
- Check code format: `TRENDY_USERNAME_USERID`
- Verify referrer user exists
- Ensure code is entered during registration

#### **Emails Not Sending:**
- Check EMAIL_BACKEND setting
- Verify SMTP credentials in production
- Look for emails in terminal console (development)

#### **Database Issues:**
- Run `./reset_system.sh` for clean start
- Check migrations with `python manage.py showmigrations`
- Verify virtual environment is activated

### **Support Commands:**
```bash
# Check email configuration
python manage.py shell -c "from django.conf import settings; print(settings.EMAIL_BACKEND)"

# Test database connection
python manage.py check --database default

# View migration status
python manage.py showmigrations

# Create backup
./backup_database.sh

# Restore backup
./restore_database.sh latest
```

## **🎉 SUMMARY**

**Referral System & Email Functionality Complete!** ✅

- **✅ Referral codes** work during registration
- **✅ Email verification** with beautiful templates
- **✅ Password reset** functionality
- **✅ Welcome emails** with referral codes
- **✅ PayPal verification** emails
- **✅ Referral reward** notifications
- **✅ System cleanup** script for fresh starts
- **✅ Email testing** script for validation

Everything is ready for development and testing! 🚀💪✨
