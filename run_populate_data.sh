#!/bin/bash

# Trendy App Data Population Script Runner
# This script sets up the environment and runs the data population script

echo "🚀 Trendy App Data Population"
echo "=============================="

# Change to the Django project directory
cd "$(dirname "$0")/trendy_web_and_api/trendy"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install requirements if needed
if [ ! -f "requirements_installed.flag" ]; then
    echo "📥 Installing requirements..."
    pip install -r requirements.txt
    touch requirements_installed.flag
fi

# Run migrations
echo "🗄️  Running database migrations..."
python manage.py makemigrations
python manage.py migrate

# Create superuser if it doesn't exist
echo "👤 Creating superuser (if needed)..."
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ Superuser created: admin/admin123')
else:
    print('✅ Superuser already exists')
"

# Run the data population script
echo "📊 Populating database with sample data..."
cd ../../
python populate_data.py

echo ""
echo "✅ Data population completed!"
echo ""
echo "🔗 You can now:"
echo "   1. Start the Django server: cd trendy_web_and_api/trendy && python manage.py runserver"
echo "   2. Access admin panel: http://127.0.0.1:8000/admin (admin/admin123)"
echo "   3. Test the Flutter app with populated data"
echo ""
echo "💰 Wallet Features:"
echo "   - All users have wallets with initial balances"
echo "   - Sample transactions are created"
echo "   - PayPal integration is configured"
echo ""
echo "🎮 Gamification Features:"
echo "   - Achievements are created and some are awarded to users"
echo "   - Point system is active"
echo "   - User stats are populated"
echo ""
echo "🛒 Monetization Features:"
echo "   - Virtual items are available for purchase"
echo "   - Point boost packages are configured"
echo "   - Premium subscription options are set up"
