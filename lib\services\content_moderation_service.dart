import 'package:flutter/material.dart';

class ContentModerationService {
  // Prohibited words/phrases for basic content filtering
  static const List<String> _prohibitedWords = [
    // Add appropriate content filters here
    'spam', 'scam', 'fake', 'illegal', 'drugs', 'violence',
    // Note: Add more based on your community guidelines
  ];

  // Content categories for reporting
  static const List<String> reportCategories = [
    'Inappropriate content',
    'Spam or misleading information',
    'Harassment or bullying',
    'Hate speech or discrimination',
    'Violence or dangerous content',
    'Copyright infringement',
    'Privacy violation',
    'Fake news or misinformation',
    'Adult content',
    'Other',
  ];

  // Check if content is appropriate (basic filtering)
  static ContentModerationResult moderateContent(String content) {
    final lowercaseContent = content.toLowerCase();
    
    // Check for prohibited words
    for (final word in _prohibitedWords) {
      if (lowercaseContent.contains(word.toLowerCase())) {
        return ContentModerationResult(
          isApproved: false,
          reason: 'Content contains prohibited terms',
          flaggedWords: [word],
        );
      }
    }

    // Check content length
    if (content.length > 5000) {
      return ContentModerationResult(
        isApproved: false,
        reason: 'Content exceeds maximum length',
      );
    }

    // Check for excessive caps (potential spam)
    final capsCount = content.replaceAll(RegExp(r'[^A-Z]'), '').length;
    final totalLetters = content.replaceAll(RegExp(r'[^a-zA-Z]'), '').length;
    
    if (totalLetters > 0 && (capsCount / totalLetters) > 0.7) {
      return ContentModerationResult(
        isApproved: false,
        reason: 'Excessive use of capital letters',
      );
    }

    // Check for repeated characters (spam detection)
    if (RegExp(r'(.)\1{4,}').hasMatch(content)) {
      return ContentModerationResult(
        isApproved: false,
        reason: 'Excessive repeated characters',
      );
    }

    return ContentModerationResult(isApproved: true);
  }

  // Report content to moderation team
  static Future<bool> reportContent({
    required String contentId,
    required String contentType,
    required String reporterId,
    required String reason,
    String? additionalInfo,
  }) async {
    try {
      // TODO: Implement actual API call to report content
      final reportData = {
        'content_id': contentId,
        'content_type': contentType,
        'reporter_id': reporterId,
        'reason': reason,
        'additional_info': additionalInfo,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Log the report (in production, send to your backend)
      print('Content reported: $reportData');
      
      return true;
    } catch (e) {
      print('Error reporting content: $e');
      return false;
    }
  }

  // Block user functionality
  static Future<bool> blockUser({
    required String userId,
    required String blockerId,
    String? reason,
  }) async {
    try {
      // TODO: Implement actual API call to block user
      final blockData = {
        'user_id': userId,
        'blocker_id': blockerId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Log the block (in production, send to your backend)
      print('User blocked: $blockData');
      
      return true;
    } catch (e) {
      print('Error blocking user: $e');
      return false;
    }
  }

  // Show content reporting dialog
  static Future<void> showReportDialog(
    BuildContext context, {
    required String contentId,
    required String contentType,
    required String reporterId,
  }) async {
    String? selectedReason;
    String additionalInfo = '';

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Report Content'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Why are you reporting this content?',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    ...reportCategories.map((category) {
                      return RadioListTile<String>(
                        title: Text(category),
                        value: category,
                        groupValue: selectedReason,
                        onChanged: (value) {
                          setState(() {
                            selectedReason = value;
                          });
                        },
                        dense: true,
                      );
                    }).toList(),
                    const SizedBox(height: 16),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'Additional information (optional)',
                        hintText: 'Provide more details about the issue...',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onChanged: (value) {
                        additionalInfo = value;
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: selectedReason != null
                      ? () async {
                          final success = await reportContent(
                            contentId: contentId,
                            contentType: contentType,
                            reporterId: reporterId,
                            reason: selectedReason!,
                            additionalInfo: additionalInfo.isNotEmpty ? additionalInfo : null,
                          );

                          if (context.mounted) {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  success
                                      ? 'Content reported successfully. Thank you for helping keep our community safe.'
                                      : 'Failed to report content. Please try again.',
                                ),
                                backgroundColor: success ? Colors.green : Colors.red,
                              ),
                            );
                          }
                        }
                      : null,
                  child: const Text('Report'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Show user blocking dialog
  static Future<void> showBlockUserDialog(
    BuildContext context, {
    required String userId,
    required String username,
    required String blockerId,
  }) async {
    String reason = '';

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Block @$username'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Are you sure you want to block @$username?'),
              const SizedBox(height: 8),
              const Text(
                'When you block someone:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Text('• You won\'t see their posts or comments'),
              const Text('• They won\'t be able to message you'),
              const Text('• They won\'t be notified that you blocked them'),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Reason (optional)',
                  hintText: 'Why are you blocking this user?',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                onChanged: (value) {
                  reason = value;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final success = await blockUser(
                  userId: userId,
                  blockerId: blockerId,
                  reason: reason.isNotEmpty ? reason : null,
                );

                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        success
                            ? '@$username has been blocked.'
                            : 'Failed to block user. Please try again.',
                      ),
                      backgroundColor: success ? Colors.orange : Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Block User'),
            ),
          ],
        );
      },
    );
  }

  // Get community guidelines text
  static String getCommunityGuidelines() {
    return '''
TRENDY COMMUNITY GUIDELINES

Welcome to Trendy! To ensure a safe and positive experience for everyone, please follow these guidelines:

RESPECT OTHERS
• Treat all community members with respect and kindness
• No harassment, bullying, or personal attacks
• Respect different opinions and perspectives

APPROPRIATE CONTENT
• Share content that is appropriate for all ages (13+)
• No adult content, violence, or disturbing imagery
• No hate speech or discriminatory content

AUTHENTIC ENGAGEMENT
• Be genuine in your interactions
• No spam, fake accounts, or misleading information
• Don't manipulate the reward system

LEGAL COMPLIANCE
• Don't share copyrighted content without permission
• No illegal activities or content
• Respect privacy and don't share personal information

BLOCKCHAIN FEATURES
• Use TRD tokens responsibly
• Don't attempt to manipulate token values
• Understand that tokens are for platform rewards only

REPORTING
• Report inappropriate content or behavior
• Help us maintain a safe community
• False reports may result in account restrictions

CONSEQUENCES
Violations may result in:
• Content removal
• Account warnings
• Temporary suspensions
• Permanent account termination

Thank you for helping make Trendy a great place for everyone!
''';
  }
}

class ContentModerationResult {
  final bool isApproved;
  final String? reason;
  final List<String>? flaggedWords;

  ContentModerationResult({
    required this.isApproved,
    this.reason,
    this.flaggedWords,
  });
}
