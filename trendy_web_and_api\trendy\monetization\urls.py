from django.urls import path
from . import views

app_name = 'monetization'

urlpatterns = [
    # Premium subscription
    path('premium-status/', views.premium_status, name='premium-status'),
    path('premium-subscribe/', views.premium_subscribe, name='premium-subscribe'),
    path('subscribe/', views.subscribe_to_premium, name='subscribe_to_premium'),
    path('confirm-payment/', views.confirm_subscription_payment, name='confirm_subscription_payment'),
    path('cancel-subscription/', views.cancel_pending_subscription, name='cancel_pending_subscription'),
    
    # Virtual items
    path('virtual-items/', views.virtual_items_list, name='virtual-items-list'),
    path('virtual-items/<int:item_id>/purchase/', views.purchase_virtual_item, name='purchase-virtual-item'),
    path('user-virtual-items/', views.user_virtual_items, name='user-virtual-items'),
    
    # Point boosts
    path('point-boosts/', views.point_boosts_list, name='point-boosts-list'),
    path('point-boosts/<int:boost_id>/purchase/', views.purchase_point_boost, name='purchase-point-boost'),
    path('point-boosts/confirm-payment/', views.confirm_point_boost_payment, name='confirm-point-boost-payment'),
    
    # Referral system
    path('referral-data/', views.referral_data, name='referral-data'),
    path('referral-code/', views.get_referral_code, name='get-referral-code'),
    path('validate-referral-code/', views.validate_referral_code, name='validate-referral-code'),
    
    # Monetization settings
    path('settings/', views.monetization_settings, name='monetization-settings'),
]
