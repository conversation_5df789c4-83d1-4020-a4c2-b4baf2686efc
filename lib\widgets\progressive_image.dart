/// Progressive image loading widget with optimization support
/// Provides smooth loading experience with placeholder -> low -> high quality progression

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/media_models.dart';

class ProgressiveImage extends StatefulWidget {
  final OptimizedMedia media;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool enableProgressiveLoading;
  final Duration fadeInDuration;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;
  final String quality;

  const ProgressiveImage({
    Key? key,
    required this.media,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.enableProgressiveLoading = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.placeholder,
    this.errorWidget,
    this.onTap,
    this.quality = 'medium',
  }) : super(key: key);

  @override
  State<ProgressiveImage> createState() => _ProgressiveImageState();
}

class _ProgressiveImageState extends State<ProgressiveImage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  String? _currentImageUrl;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: widget.fadeInDuration,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _loadImage();
  }

  @override
  void didUpdateWidget(ProgressiveImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.media.id != widget.media.id ||
        oldWidget.quality != widget.quality) {
      _loadImage();
    }
  }

  void _loadImage() {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    _currentImageUrl = widget.media.getBestUrl(widget.quality);
    
    if (_currentImageUrl?.isNotEmpty == true) {
      _fadeController.forward();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  Widget _buildPlaceholder() {
    if (widget.placeholder != null) {
      return widget.placeholder!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (widget.errorWidget != null) {
      return widget.errorWidget!;
    }

    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildOptimizedImage() {
    if (_currentImageUrl == null || _currentImageUrl!.isEmpty) {
      return _buildErrorWidget();
    }

    return CachedNetworkImage(
      imageUrl: _currentImageUrl!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      fadeInDuration: widget.fadeInDuration,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      imageBuilder: (context, imageProvider) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: imageProvider,
                fit: widget.fit,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressiveImage() {
    if (!widget.media.isOptimized || !widget.enableProgressiveLoading) {
      return _buildOptimizedImage();
    }

    return Stack(
      children: [
        // Placeholder/thumbnail layer
        if (widget.media.responsive?.placeholder != null)
          CachedNetworkImage(
            imageUrl: widget.media.responsive!.placeholder!,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            placeholder: (context, url) => _buildPlaceholder(),
            errorWidget: (context, url, error) => _buildPlaceholder(),
          ),
        
        // Main image layer
        _buildOptimizedImage(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = _buildProgressiveImage();

    if (widget.onTap != null) {
      imageWidget = GestureDetector(
        onTap: widget.onTap,
        child: imageWidget,
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: imageWidget,
    );
  }
}

/// Responsive image widget that adapts to different screen sizes
class ResponsiveImage extends StatelessWidget {
  final OptimizedMedia media;
  final BoxFit fit;
  final VoidCallback? onTap;

  const ResponsiveImage({
    Key? key,
    required this.media,
    this.fit = BoxFit.cover,
    this.onTap,
  }) : super(key: key);

  String _getQualityForScreenSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final effectiveWidth = screenWidth * devicePixelRatio;

    if (effectiveWidth < 800) {
      return 'small';
    } else if (effectiveWidth < 1600) {
      return 'medium';
    } else {
      return 'large';
    }
  }

  @override
  Widget build(BuildContext context) {
    final quality = _getQualityForScreenSize(context);
    
    return ProgressiveImage(
      media: media,
      quality: quality,
      fit: fit,
      onTap: onTap,
    );
  }
}

/// Hero image widget for smooth transitions
class HeroProgressiveImage extends StatelessWidget {
  final OptimizedMedia media;
  final String heroTag;
  final double? width;
  final double? height;
  final BoxFit fit;
  final VoidCallback? onTap;

  const HeroProgressiveImage({
    Key? key,
    required this.media,
    required this.heroTag,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: heroTag,
      child: ProgressiveImage(
        media: media,
        width: width,
        height: height,
        fit: fit,
        onTap: onTap,
        quality: 'large',
      ),
    );
  }
}

/// Thumbnail image widget for gallery previews
class ThumbnailImage extends StatelessWidget {
  final OptimizedMedia media;
  final double size;
  final VoidCallback? onTap;
  final bool isSelected;

  const ThumbnailImage({
    Key? key,
    required this.media,
    this.size = 60,
    this.onTap,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                )
              : null,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: ProgressiveImage(
            media: media,
            width: size,
            height: size,
            quality: 'thumbnail',
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}
