# 🗄️ Database Setup Guide for Multiple Machines

## **Problem with SQLite + Git**

❌ **Don't commit `db.sqlite3` to Git because:**
- Binary files cause merge conflicts
- Large file sizes slow down repository
- Multiple developers can't work simultaneously
- Git merges can corrupt the database

## **✅ Recommended Solution: Use Fixtures**

### **1. Export Data (On Source Machine)**

```bash
cd trendy/trendy_web_and_api/trendy
source venv/bin/activate

# Export all data
python manage.py dumpdata --indent=2 --output=fixtures/initial_data.json

# Or export by app (recommended)
python manage.py dumpdata accounts --indent=2 --output=fixtures/users_data.json
python manage.py dumpdata wallet --indent=2 --output=fixtures/wallet_data.json
python manage.py dumpdata blog --indent=2 --output=fixtures/blog_data.json
python manage.py dumpdata gamification --indent=2 --output=fixtures/gamification_data.json
python manage.py dumpdata monetization --indent=2 --output=fixtures/monetization_data.json
```

### **2. Setup on New Machine**

```bash
# Clone repository
git clone <repository-url>
cd trendy/trendy_web_and_api/trendy

# Setup virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Load development data
python manage.py setup_dev_data

# Create superuser (optional)
python manage.py createsuperuser
```

### **3. Quick Setup Command**

```bash
# Reset database and load fresh data
python manage.py setup_dev_data --reset
```

## **Alternative Solutions**

### **Option A: PostgreSQL with Docker**

```yaml
# docker-compose.yml
version: '3.8'
services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: trendy_db
      POSTGRES_USER: trendy_user
      POSTGRES_PASSWORD: trendy_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'trendy_db',
        'USER': 'trendy_user',
        'PASSWORD': 'trendy_pass',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### **Option B: Environment-Based Database**

```python
# settings.py
import os

if os.environ.get('USE_POSTGRES'):
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('DB_NAME', 'trendy_db'),
            'USER': os.environ.get('DB_USER', 'trendy_user'),
            'PASSWORD': os.environ.get('DB_PASSWORD', 'trendy_pass'),
            'HOST': os.environ.get('DB_HOST', 'localhost'),
            'PORT': os.environ.get('DB_PORT', '5432'),
        }
    }
else:
    # Default SQLite for development
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
```

## **Git Configuration**

### **Update .gitignore**

```gitignore
# Database files
*.sqlite3
*.db

# But include fixtures
!fixtures/
fixtures/*.json

# Environment files
.env
.env.local
```

## **Development Workflow**

### **Daily Development**
1. **Work with local SQLite** for development
2. **Export important data** to fixtures when needed
3. **Commit fixtures** to Git (not database files)
4. **Other developers load fixtures** on their machines

### **Data Sharing**
```bash
# When you have new important data
python manage.py dumpdata accounts wallet blog --indent=2 --output=fixtures/latest_data.json
git add fixtures/latest_data.json
git commit -m "Add latest development data"
git push

# Other developers update their data
git pull
python manage.py setup_dev_data --reset
```

## **Production Deployment**

### **Use PostgreSQL/MySQL in Production**
```python
# Production settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DATABASE_URL'),
        # ... other production settings
    }
}
```

### **Migration Strategy**
1. **Development**: SQLite + Fixtures
2. **Staging**: PostgreSQL + Migrations
3. **Production**: PostgreSQL + Migrations

## **Best Practices**

### **✅ Do:**
- Use fixtures for sharing development data
- Commit fixture files to Git
- Use PostgreSQL for production
- Keep database files in .gitignore
- Document data setup process

### **❌ Don't:**
- Commit SQLite database files
- Share database files via email/Slack
- Use SQLite in production
- Ignore database migrations
- Forget to backup production data

## **Troubleshooting**

### **Fixture Loading Errors**
```bash
# If fixtures fail to load
python manage.py migrate
python manage.py flush --noinput
python manage.py migrate
python manage.py loaddata fixtures/users_data.json
```

### **Permission Errors**
```bash
# Fix file permissions
chmod +x manage.py
chmod -R 755 fixtures/
```

### **Missing Dependencies**
```bash
# Reinstall requirements
pip install -r requirements.txt
```

This approach ensures consistent development environments across all machines! 🚀
