from django.db import transaction
from django.utils import timezone
from django.db.models import F
from .models import (
    UserLevel, PointTransaction, Badge, UserBadge, Challenge, ChallengeParticipation,
    EngagementSettings, PostReadingHistory, EngagementHistory,
    UserEngagementTracker, SuspiciousActivityLog
)
from datetime import date, timedelta, datetime

class GamificationService:
    """Service for handling gamification logic"""
    
    @staticmethod
    def get_or_create_user_level(user):
        """Get or create user level object"""
        user_level, created = UserLevel.objects.get_or_create(
            user=user,
            defaults={
                'total_points': 0,
                'current_level': 1,
                'points_to_next_level': 100,
            }
        )
        return user_level
    
    @staticmethod
    def award_points(user, points, transaction_type, description, related_object_type=None, related_object_id=None, check_badges=True):
        """Award points to a user and handle level progression"""
        print(f"🔍 DEBUG: award_points called - user: {user.username}, points: {points}, type: {transaction_type}")
        with transaction.atomic():
            # Create point transaction
            transaction_obj = PointTransaction.objects.create(
                user=user,
                transaction_type=transaction_type,
                points=points,
                description=description,
                related_object_type=related_object_type or '',
                related_object_id=related_object_id
            )
            print(f"🔍 DEBUG: PointTransaction created with ID: {transaction_obj.id}")

            # Invalidate user points cache when points change
            try:
                from .performance_utils import DataCache
                DataCache.invalidate_user_points_cache(user.id)
            except ImportError:
                pass  # Cache utils not available

            # Update user level
            user_level = GamificationService.get_or_create_user_level(user)
            old_points = user_level.total_points
            user_level.total_points = F('total_points') + points
            user_level.save(update_fields=['total_points'])
            print(f"🔍 DEBUG: User level points updated from {old_points} (adding {points} points)")

            # Refresh from database to get updated total_points
            user_level.refresh_from_db()

            # Calculate points_to_next_level safely
            next_level_points = UserLevel.get_points_for_level(user_level.current_level + 1)
            points_needed = next_level_points - user_level.total_points
            user_level.points_to_next_level = max(0, points_needed)
            user_level.save(update_fields=['points_to_next_level'])

            # Refresh again to get updated points_to_next_level
            user_level.refresh_from_db()

            # Check for level up
            levels_gained = 0
            while user_level.points_to_next_level <= 0:
                levels_gained += 1
                user_level.current_level += 1

                # Calculate points for next level
                next_level_points = UserLevel.get_points_for_level(user_level.current_level + 1)
                current_level_points = UserLevel.get_points_for_level(user_level.current_level)
                points_needed = next_level_points - user_level.total_points

                # Ensure points_to_next_level is never negative
                user_level.points_to_next_level = max(0, points_needed)

                user_level.save(update_fields=['current_level', 'points_to_next_level'])

            # Check for badges (but not during badge point awards to prevent recursion)
            if check_badges:
                GamificationService.check_and_award_badges(user)

            return user_level, levels_gained
    
    @staticmethod
    def update_reading_activity(user, post_id=None, time_spent=0, scroll_percentage=0.0):
        """Update reading activity with fraud prevention"""
        print(f"🔍 DEBUG: update_reading_activity called for user {user.username}, post {post_id}")

        if not post_id:
            return False, "Post ID is required"

        # Check if user can earn points for reading this post
        can_earn, reason = GamificationService.can_earn_reading_points(user, post_id, time_spent, scroll_percentage)
        print(f"🔍 DEBUG: can_earn_reading_points returned: {can_earn}, reason: {reason}")

        if not can_earn:
            return False, reason

        user_level = GamificationService.get_or_create_user_level(user)
        today = date.today()

        with transaction.atomic():
            # Update or create reading history
            print(f"🔍 DEBUG: Creating/getting reading history for user {user.username}, post {post_id}")
            reading_history, created = PostReadingHistory.objects.get_or_create(
                user=user,
                post_id=post_id,
                defaults={
                    'time_spent_seconds': time_spent,
                    'scroll_percentage': scroll_percentage,
                    'points_awarded': 5,
                    'reward_given': True
                }
            )
            print(f"🔍 DEBUG: Reading history created={created}, record_id={reading_history.id if reading_history else 'None'}")

            if not created:
                # Update existing record but don't award points again
                print(f"🔍 DEBUG: Record already exists, updating read count from {reading_history.read_count}")
                reading_history.read_count = F('read_count') + 1
                reading_history.last_read_at = timezone.now()
                reading_history.time_spent_seconds += time_spent
                reading_history.scroll_percentage = max(reading_history.scroll_percentage, scroll_percentage)
                reading_history.save()
                return False, "Points already awarded for reading this post"

            # Update reading stats
            user_level.total_posts_read = F('total_posts_read') + 1

            # Update reading streak
            if user_level.last_reading_date == today - timedelta(days=1):
                # Continue streak
                user_level.reading_streak = F('reading_streak') + 1
            elif user_level.last_reading_date != today:
                # Start new streak
                user_level.reading_streak = 1

            user_level.last_reading_date = today
            user_level.save(update_fields=['total_posts_read', 'reading_streak', 'last_reading_date'])

            # Update engagement tracker
            GamificationService.update_engagement_tracker(user, 'reading')

            # Award points
            GamificationService.award_points(
                user=user,
                points=5,
                transaction_type='reading',
                description='Read a post',
                related_object_type='post',
                related_object_id=post_id
            )

            return True, "Points awarded for reading"
    
    @staticmethod
    def update_writing_activity(user, post_id=None):
        """Update writing activity and streaks"""
        user_level = GamificationService.get_or_create_user_level(user)
        today = date.today()
        
        with transaction.atomic():
            # Update writing stats
            user_level.total_posts_written = F('total_posts_written') + 1
            
            # Update writing streak
            if user_level.last_writing_date == today - timedelta(days=1):
                user_level.writing_streak = F('writing_streak') + 1
            elif user_level.last_writing_date != today:
                user_level.writing_streak = 1
            
            user_level.last_writing_date = today
            user_level.save(update_fields=['total_posts_written', 'writing_streak', 'last_writing_date'])
            
            # Award points
            GamificationService.award_points(
                user=user,
                points=25,
                transaction_type='writing',
                description='Published a post',
                related_object_type='post',
                related_object_id=post_id
            )
    
    @staticmethod
    def update_engagement_activity(user, activity_type, object_id=None, target_type='post'):
        """Update engagement activity with fraud prevention"""
        if not object_id:
            return False, "Object ID is required"

        # Check if user can earn points for this engagement
        can_earn, reason = GamificationService.can_earn_engagement_points(
            user, activity_type, object_id, target_type
        )
        if not can_earn:
            return False, reason

        user_level = GamificationService.get_or_create_user_level(user)
        today = date.today()

        points_map = {
            'comment': 10,
            'like': 2,
            'voice_comment': 15,
            'poll_vote': 5,
            'quiz_complete': 20,
        }

        with transaction.atomic():
            # Check for duplicate engagement
            existing_engagement = EngagementHistory.objects.filter(
                user=user,
                engagement_type=activity_type,
                target_type=target_type,
                target_id=object_id
            ).first()

            if existing_engagement:
                return False, f"Points already awarded for {activity_type} on this {target_type}"

            # Create engagement history record
            EngagementHistory.objects.create(
                user=user,
                engagement_type=activity_type,
                target_type=target_type,
                target_id=object_id,
                points_awarded=points_map.get(activity_type, 5),
                is_valid=True
            )

            # Update engagement stats
            if activity_type == 'comment':
                user_level.total_comments_made = F('total_comments_made') + 1
            elif activity_type == 'like':
                user_level.total_likes_given = F('total_likes_given') + 1
            elif activity_type == 'voice_comment':
                user_level.total_voice_comments = F('total_voice_comments') + 1

            # Update engagement streak
            if user_level.last_engagement_date == today - timedelta(days=1):
                user_level.engagement_streak = F('engagement_streak') + 1
            elif user_level.last_engagement_date != today:
                user_level.engagement_streak = 1

            user_level.last_engagement_date = today
            user_level.save()

            # Update engagement tracker
            GamificationService.update_engagement_tracker(user, activity_type)

            # Award points
            points = points_map.get(activity_type, 5)
            GamificationService.award_points(
                user=user,
                points=points,
                transaction_type='engagement',
                description=f'{activity_type.replace("_", " ").title()} activity',
                related_object_type=activity_type,
                related_object_id=object_id
            )

            # Update challenge progress for quiz/poll activities
            GamificationService.update_interactive_challenge_progress(user, activity_type)

            return True, f"Points awarded for {activity_type}"
    
    @staticmethod
    def check_and_award_badges(user):
        """Check if user qualifies for any badges and award them"""
        user_level = GamificationService.get_or_create_user_level(user)
        
        # Get badges user hasn't earned yet
        earned_badge_ids = UserBadge.objects.filter(user=user).values_list('badge_id', flat=True)
        available_badges = Badge.objects.filter(is_active=True).exclude(id__in=earned_badge_ids)
        
        newly_earned = []
        
        for badge in available_badges:
            if GamificationService.check_badge_requirements(user, user_level, badge):
                # Award badge
                user_badge = UserBadge.objects.create(
                    user=user,
                    badge=badge,
                    progress_data={'earned_automatically': True}
                )
                newly_earned.append(user_badge)
                
                # Award badge points (without checking badges again to prevent recursion)
                GamificationService.award_points(
                    user=user,
                    points=badge.points_reward,
                    transaction_type='badge',
                    description=f'Earned badge: {badge.name}',
                    related_object_type='badge',
                    related_object_id=badge.id,
                    check_badges=False
                )
        
        return newly_earned
    
    @staticmethod
    def check_badge_requirements(user, user_level, badge):
        """Check if user meets badge requirements"""
        requirements = badge.requirements
        
        # Level-based badges
        if 'min_level' in requirements:
            if user_level.current_level < requirements['min_level']:
                return False
        
        # Points-based badges
        if 'min_points' in requirements:
            if user_level.total_points < requirements['min_points']:
                return False
        
        # Activity-based badges
        if 'min_posts_read' in requirements:
            if user_level.total_posts_read < requirements['min_posts_read']:
                return False
        
        if 'min_posts_written' in requirements:
            if user_level.total_posts_written < requirements['min_posts_written']:
                return False
        
        if 'min_comments' in requirements:
            if user_level.total_comments_made < requirements['min_comments']:
                return False
        
        # Streak-based badges
        if 'min_reading_streak' in requirements:
            if user_level.reading_streak < requirements['min_reading_streak']:
                return False
        
        if 'min_writing_streak' in requirements:
            if user_level.writing_streak < requirements['min_writing_streak']:
                return False
        
        return True
    
    @staticmethod
    def update_challenge_progress(user, challenge_id, progress_data):
        """Update user's progress in a challenge"""
        try:
            participation = ChallengeParticipation.objects.get(
                user=user,
                challenge_id=challenge_id,
                is_active=True
            )
            
            # Update progress
            participation.progress.update(progress_data)
            
            # Calculate completion percentage
            challenge = participation.challenge
            completion = GamificationService.calculate_challenge_completion(
                challenge.requirements,
                participation.progress
            )
            
            participation.completion_percentage = completion
            
            # Check if completed
            if completion >= 100.0 and not participation.is_completed:
                participation.is_completed = True
                participation.completed_at = timezone.now()
                
                # Award challenge points
                GamificationService.award_points(
                    user=user,
                    points=challenge.points_reward,
                    transaction_type='challenge',
                    description=f'Completed challenge: {challenge.title}',
                    related_object_type='challenge',
                    related_object_id=challenge.id
                )
                
                # Award badge if specified
                if challenge.badge_reward:
                    UserBadge.objects.get_or_create(
                        user=user,
                        badge=challenge.badge_reward,
                        defaults={'progress_data': {'challenge_completion': True}}
                    )
            
            participation.save()
            return participation
            
        except ChallengeParticipation.DoesNotExist:
            return None
    
    @staticmethod
    def calculate_challenge_completion(requirements, progress):
        """Calculate challenge completion percentage"""
        if not requirements:
            return 0.0
        
        total_weight = 0
        completed_weight = 0
        
        for req_key, req_value in requirements.items():
            weight = 1  # Equal weight for all requirements
            total_weight += weight
            
            progress_value = progress.get(req_key, 0)
            completion_ratio = min(progress_value / req_value, 1.0) if req_value > 0 else 0
            completed_weight += completion_ratio * weight
        
        return (completed_weight / total_weight * 100) if total_weight > 0 else 0.0

    @staticmethod
    def update_interactive_challenge_progress(user, activity_type):
        """Update progress for interactive challenges (quiz/poll participation)"""
        from .models import ChallengeParticipation

        # Get active interactive challenges for the user
        active_participations = ChallengeParticipation.objects.filter(
            user=user,
            is_active=True,
            is_completed=False,
            challenge__challenge_type__in=['quiz', 'poll', 'interactive'],
            challenge__is_active=True
        )

        for participation in active_participations:
            challenge = participation.challenge
            progress = participation.progress.copy()

            # Update progress based on activity type and challenge requirements
            if activity_type == 'quiz_complete' and challenge.challenge_type in ['quiz', 'interactive']:
                # Update quiz completion count
                progress['quizzes_completed'] = progress.get('quizzes_completed', 0) + 1

            elif activity_type == 'poll_vote' and challenge.challenge_type in ['poll', 'interactive']:
                # Update poll vote count
                progress['polls_voted'] = progress.get('polls_voted', 0) + 1

            # Update the participation progress
            GamificationService.update_challenge_progress(
                user=user,
                challenge_id=challenge.id,
                progress_data=progress
            )

    @staticmethod
    def can_earn_reading_points(user, post_id, time_spent=0, scroll_percentage=0.0):
        """Check if user can earn points for reading a post"""
        settings = EngagementSettings.get_settings()

        # Check if fraud detection is enabled
        if not settings.enable_fraud_detection:
            return True, "Fraud detection disabled"

        # Check if user has already read this post
        existing_reading = PostReadingHistory.objects.filter(
            user=user,
            post_id=post_id,
            reward_given=True
        ).first()

        if existing_reading:
            return False, "Points already awarded for reading this post"

        # Check minimum reading requirements for genuine engagement
        if time_spent < settings.min_reading_time_seconds:
            # Don't log as suspicious for initial views (time_spent = 0)
            # Only log if user is trying to game with very short times
            if time_spent > 0:
                GamificationService.log_suspicious_activity(
                    user, 'rapid_reading',
                    f"Read post {post_id} in {time_spent} seconds (minimum: {settings.min_reading_time_seconds})",
                    'post', post_id
                )
            return False, f"Minimum reading time not met ({settings.min_reading_time_seconds}s required)"

        if scroll_percentage < settings.min_scroll_percentage:
            GamificationService.log_suspicious_activity(
                user, 'rapid_reading',
                f"Read post {post_id} with {scroll_percentage}% scroll (minimum: {settings.min_scroll_percentage}%)",
                'post', post_id
            )
            return False, f"Minimum scroll percentage not met ({settings.min_scroll_percentage}% required)"

        # Check daily limits
        tracker = GamificationService.get_or_create_engagement_tracker(user)
        tracker.reset_daily_counters_if_needed()

        if tracker.posts_read_today >= settings.max_posts_read_per_day:
            return False, f"Daily reading limit reached ({settings.max_posts_read_per_day} posts)"

        # Check rate limiting
        if not GamificationService.check_rate_limit(user, settings):
            return False, "Rate limit exceeded"

        return True, "Can earn reading points"

    @staticmethod
    def can_earn_engagement_points(user, activity_type, object_id, target_type):
        """Check if user can earn points for engagement activity"""
        settings = EngagementSettings.get_settings()

        # Check if fraud detection is enabled
        if not settings.enable_fraud_detection:
            return True, "Fraud detection disabled"

        # Check for duplicate engagement
        existing_engagement = EngagementHistory.objects.filter(
            user=user,
            engagement_type=activity_type,
            target_type=target_type,
            target_id=object_id
        ).first()

        if existing_engagement:
            return False, f"Points already awarded for {activity_type} on this {target_type}"

        # Check daily limits
        tracker = GamificationService.get_or_create_engagement_tracker(user)
        tracker.reset_daily_counters_if_needed()

        if activity_type == 'comment' and tracker.comments_today >= settings.max_comments_per_day:
            return False, f"Daily comment limit reached ({settings.max_comments_per_day} comments)"

        if activity_type == 'like' and tracker.likes_today >= settings.max_likes_per_day:
            return False, f"Daily like limit reached ({settings.max_likes_per_day} likes)"

        # Check cooldown periods
        last_engagement = EngagementHistory.objects.filter(
            user=user,
            engagement_type=activity_type
        ).order_by('-created_at').first()

        if last_engagement:
            time_since_last = timezone.now() - last_engagement.created_at
            cooldown_seconds = settings.like_cooldown if activity_type == 'like' else settings.comment_cooldown

            if time_since_last.total_seconds() < cooldown_seconds:
                return False, f"Cooldown period active ({cooldown_seconds}s required)"

        # Check rate limiting
        if not GamificationService.check_rate_limit(user, settings):
            return False, "Rate limit exceeded"

        return True, f"Can earn {activity_type} points"

    @staticmethod
    def get_or_create_engagement_tracker(user):
        """Get or create engagement tracker for user"""
        tracker, created = UserEngagementTracker.objects.get_or_create(
            user=user,
            defaults={
                'posts_read_today': 0,
                'comments_today': 0,
                'likes_today': 0,
                'is_flagged': False,
                'activity_count_last_minute': 0
            }
        )
        return tracker

    @staticmethod
    def update_engagement_tracker(user, activity_type):
        """Update engagement tracker counters"""
        tracker = GamificationService.get_or_create_engagement_tracker(user)
        tracker.reset_daily_counters_if_needed()

        if activity_type == 'reading':
            tracker.posts_read_today = F('posts_read_today') + 1
        elif activity_type == 'comment':
            tracker.comments_today = F('comments_today') + 1
        elif activity_type == 'like':
            tracker.likes_today = F('likes_today') + 1

        tracker.save()

    @staticmethod
    def check_rate_limit(user, settings):
        """Check if user is within rate limits"""
        tracker = GamificationService.get_or_create_engagement_tracker(user)
        now = timezone.now()

        # Check if it's been more than a minute since last activity
        if tracker.last_activity_timestamp:
            time_diff = now - tracker.last_activity_timestamp
            if time_diff.total_seconds() >= 60:
                # Reset minute counter
                tracker.activity_count_last_minute = 0

        # Increment activity count
        tracker.activity_count_last_minute = F('activity_count_last_minute') + 1
        tracker.save()

        # Refresh to get updated count
        tracker.refresh_from_db()

        if tracker.activity_count_last_minute > settings.max_actions_per_minute:
            GamificationService.log_suspicious_activity(
                user, 'rate_limit_exceeded',
                f"Exceeded rate limit: {tracker.activity_count_last_minute} actions in last minute"
            )
            return False

        return True

    @staticmethod
    def log_suspicious_activity(user, activity_type, description, related_object_type=None, related_object_id=None):
        """Log suspicious activity for admin review"""
        settings = EngagementSettings.get_settings()

        if not settings.enable_fraud_detection:
            return

        # Determine severity based on activity type
        severity_map = {
            'rapid_reading': 'medium',
            'excessive_likes': 'high',
            'spam_comments': 'high',
            'bot_behavior': 'critical',
            'duplicate_rewards': 'high',
            'rate_limit_exceeded': 'medium',
        }

        severity = severity_map.get(activity_type, 'medium')

        # Create suspicious activity log
        SuspiciousActivityLog.objects.create(
            user=user,
            activity_type=activity_type,
            description=description,
            related_object_type=related_object_type or '',
            related_object_id=related_object_id,
            severity=severity
        )

        # Auto-flag user if enabled and severity is high
        if settings.auto_flag_suspicious and severity in ['high', 'critical']:
            tracker = GamificationService.get_or_create_engagement_tracker(user)
            if not tracker.is_flagged:
                tracker.is_flagged = True
                tracker.flag_reason = f"Auto-flagged for {activity_type}"
                tracker.flagged_at = timezone.now()
                tracker.save()

    @staticmethod
    def convert_gamification_to_store_points(user, gamification_points):
        """Convert gamification points to store points with strategic rates"""
        from .models import PointConversionSettings, UserStorePoints, PointConversionTransaction

        try:
            with transaction.atomic():
                settings = PointConversionSettings.get_settings()

                # Check if conversion is enabled
                if not settings.conversion_enabled:
                    return False, "Point conversion is currently disabled"

                if settings.maintenance_mode:
                    return False, "Point conversion is under maintenance"

                # Validate amount
                if gamification_points < settings.minimum_conversion_amount:
                    return False, f"Minimum conversion amount is {settings.minimum_conversion_amount} points"

                if gamification_points > settings.maximum_conversion_amount:
                    return False, f"Maximum conversion amount is {settings.maximum_conversion_amount} points"

                # Check user's gamification points balance
                user_level = GamificationService.get_or_create_user_level(user)
                if user_level.total_points < gamification_points:
                    return False, "Insufficient gamification points"

                # Calculate conversion details
                conversion_details = settings.calculate_total_cost(gamification_points, user)
                total_cost = conversion_details['total_cost']
                store_points = conversion_details['store_points']

                if store_points <= 0:
                    return False, "Conversion amount too small to generate store points"

                # Check if user has enough points including fees
                if user_level.total_points < total_cost:
                    return False, f"Insufficient points. Need {total_cost} points (including fees)"

                # Get or create user store points
                user_store_points, created = UserStorePoints.objects.get_or_create(
                    user=user,
                    defaults={'balance': 0}
                )

                # Check daily limits
                if not user_store_points.can_convert_today(store_points):
                    remaining = settings.daily_conversion_limit - user_store_points.daily_conversions_today
                    return False, f"Daily conversion limit exceeded. You can convert {remaining} more store points today"

                # Check premium status
                is_premium = False
                try:
                    from monetization.services import MonetizationService
                    premium_status = MonetizationService.get_user_premium_status(user)
                    is_premium = premium_status.get('is_premium', False)
                except:
                    pass

                # Create conversion transaction
                conversion_transaction = PointConversionTransaction.objects.create(
                    user=user,
                    gamification_points_spent=total_cost,
                    store_points_received=store_points,
                    conversion_rate=conversion_details['conversion_rate'],
                    percentage_fee=conversion_details['percentage_fee'],
                    fixed_fee=conversion_details['fixed_fee'],
                    total_fee=conversion_details['percentage_fee'] + conversion_details['fixed_fee'],
                    user_level_at_conversion=user_level.current_level,
                    user_total_points_at_conversion=user_level.total_points,
                    was_premium=is_premium,
                    status='pending'
                )

                # Deduct gamification points
                GamificationService.award_points(
                    user=user,
                    points=-total_cost,
                    transaction_type='conversion',
                    description=f'Converted to {store_points} store points',
                    related_object_type='conversion',
                    related_object_id=conversion_transaction.id
                )

                # Add store points
                user_store_points.add_points(store_points, 'conversion')

                # Mark transaction as completed
                conversion_transaction.status = 'completed'
                conversion_transaction.completed_at = timezone.now()
                conversion_transaction.save()

                return True, {
                    'message': f'Successfully converted {total_cost} gamification points to {store_points} store points',
                    'store_points_received': store_points,
                    'gamification_points_spent': total_cost,
                    'conversion_rate': conversion_details['conversion_rate'],
                    'fees_paid': conversion_details['percentage_fee'] + conversion_details['fixed_fee'],
                    'new_store_balance': user_store_points.balance,
                    'transaction_id': conversion_transaction.id
                }

        except Exception as e:
            return False, f"Conversion failed: {str(e)}"

    @staticmethod
    def get_conversion_preview(user, gamification_points):
        """Get preview of conversion without executing it"""
        from .models import PointConversionSettings, UserStorePoints

        try:
            settings = PointConversionSettings.get_settings()

            # Check if conversion is enabled
            if not settings.conversion_enabled:
                return False, "Point conversion is currently disabled"

            # Validate amount
            if gamification_points < settings.minimum_conversion_amount:
                return False, f"Minimum conversion amount is {settings.minimum_conversion_amount} points"

            if gamification_points > settings.maximum_conversion_amount:
                return False, f"Maximum conversion amount is {settings.maximum_conversion_amount} points"

            # Calculate conversion details
            conversion_details = settings.calculate_total_cost(gamification_points, user)

            # Get user's current balances
            user_level = GamificationService.get_or_create_user_level(user)
            user_store_points, created = UserStorePoints.objects.get_or_create(
                user=user,
                defaults={'balance': 0}
            )

            # Check daily limits
            can_convert_today = user_store_points.can_convert_today(conversion_details['store_points'])
            remaining_daily = settings.daily_conversion_limit - user_store_points.daily_conversions_today

            return True, {
                'conversion_details': conversion_details,
                'user_gamification_balance': user_level.total_points,
                'user_store_balance': user_store_points.balance,
                'can_afford': user_level.total_points >= conversion_details['total_cost'],
                'can_convert_today': can_convert_today,
                'remaining_daily_conversions': remaining_daily,
                'user_level': user_level.current_level,
                'is_premium': False  # Will be updated if monetization service available
            }

        except Exception as e:
            return False, f"Preview failed: {str(e)}"

    @staticmethod
    def get_user_store_points_balance(user):
        """Get user's store points balance"""
        from .models import UserStorePoints

        user_store_points, created = UserStorePoints.objects.get_or_create(
            user=user,
            defaults={'balance': 0}
        )
        return user_store_points.balance
