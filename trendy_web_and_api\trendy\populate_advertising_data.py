#!/usr/bin/env python
"""
Script to populate initial advertising data
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from django.utils import timezone
from advertising.models import (
    AdNetwork, AdPlacement, AdSettings, RewardedAd, SponsoredPost
)


def create_ad_networks():
    """Create sample ad networks"""
    networks = [
        {
            'name': 'Google AdMob',
            'network_type': 'google_admob',
            'is_active': True,
            'priority': 1,
            'revenue_share_percentage': Decimal('70.00')
        },
        {
            'name': 'Facebook Audience Network',
            'network_type': 'facebook_audience',
            'is_active': True,
            'priority': 2,
            'revenue_share_percentage': Decimal('65.00')
        },
        {
            'name': 'Unity Ads',
            'network_type': 'unity_ads',
            'is_active': True,
            'priority': 3,
            'revenue_share_percentage': Decimal('60.00')
        }
    ]
    
    for network_data in networks:
        network, created = AdNetwork.objects.get_or_create(
            name=network_data['name'],
            defaults=network_data
        )
        if created:
            print(f"✅ Created ad network: {network.name}")
        else:
            print(f"📋 Ad network already exists: {network.name}")


def create_ad_placements():
    """Create sample ad placements"""
    placements = [
        {
            'name': 'Home Feed Banner',
            'location': 'home_feed_top',
            'placement_type': 'banner',
            'is_active': True,
            'frequency_cap': 3,
            'estimated_cpm': Decimal('2.50')
        },
        {
            'name': 'Post Detail Interstitial',
            'location': 'post_detail_bottom',
            'placement_type': 'interstitial',
            'is_active': True,
            'frequency_cap': 2,
            'estimated_cpm': Decimal('4.00')
        },
        {
            'name': 'Rewarded Video - Rewards Page',
            'location': 'rewards_page',
            'placement_type': 'rewarded_video',
            'is_active': True,
            'frequency_cap': 5,
            'estimated_cpm': Decimal('8.00')
        },
        {
            'name': 'Rewarded Video - Before Reward',
            'location': 'before_reward_claim',
            'placement_type': 'rewarded_video',
            'is_active': True,
            'frequency_cap': 3,
            'estimated_cpm': Decimal('6.00')
        },
        {
            'name': 'Native Ad - Feed Middle',
            'location': 'home_feed_middle',
            'placement_type': 'native',
            'is_active': True,
            'frequency_cap': 2,
            'estimated_cpm': Decimal('3.00')
        }
    ]
    
    for placement_data in placements:
        placement, created = AdPlacement.objects.get_or_create(
            name=placement_data['name'],
            defaults=placement_data
        )
        if created:
            print(f"✅ Created ad placement: {placement.name}")
        else:
            print(f"📋 Ad placement already exists: {placement.name}")


def create_rewarded_ads():
    """Create sample rewarded ads"""
    # Get rewarded video placements
    rewards_placement = AdPlacement.objects.filter(placement_type='rewarded_video').first()

    if not rewards_placement:
        print("⚠️ No rewarded video placement found, skipping rewarded ads")
        return

    rewarded_ads = [
        {
            'name': 'Daily Points Boost',
            'description': 'Watch a video to earn bonus points',
            'reward_type': 'points',
            'points_reward': 10,
            'ad_placement': rewards_placement,
            'is_active': True
        },
        {
            'name': 'Premium Trial',
            'description': 'Watch a video to get 1-day premium trial',
            'reward_type': 'premium_trial',
            'points_reward': 0,
            'premium_trial_days': 1,
            'ad_placement': rewards_placement,
            'is_active': True
        },
        {
            'name': 'Streak Protection',
            'description': 'Watch a video to protect your daily streak',
            'reward_type': 'streak_protection',
            'points_reward': 5,
            'ad_placement': rewards_placement,
            'is_active': True
        }
    ]

    for ad_data in rewarded_ads:
        ad, created = RewardedAd.objects.get_or_create(
            name=ad_data['name'],
            defaults=ad_data
        )
        if created:
            print(f"✅ Created rewarded ad: {ad.name}")
        else:
            print(f"📋 Rewarded ad already exists: {ad.name}")


def create_sponsored_content():
    """Create sample sponsored content"""
    now = timezone.now()
    sponsored_posts = [
        {
            'title': 'Discover Amazing Apps',
            'content': 'Find the best apps for productivity and entertainment.',
            'sponsor_name': 'TechCorp',
            'sponsor_type': 'brand',
            'status': 'active',
            'total_budget': Decimal('1000.00'),
            'target_url': 'https://example.com/apps',
            'start_date': now,
            'end_date': now + timedelta(days=30)
        },
        {
            'title': 'Learn New Skills Online',
            'content': 'Master new skills with our comprehensive online courses.',
            'sponsor_name': 'EduPlatform',
            'sponsor_type': 'service',
            'status': 'active',
            'total_budget': Decimal('500.00'),
            'target_url': 'https://example.com/courses',
            'start_date': now,
            'end_date': now + timedelta(days=15)
        },
        {
            'title': 'Support Local Businesses',
            'content': 'Help your community thrive by supporting local businesses.',
            'sponsor_name': 'Community Foundation',
            'sponsor_type': 'affiliate',
            'status': 'active',
            'total_budget': Decimal('250.00'),
            'target_url': 'https://example.com/local',
            'start_date': now,
            'end_date': now + timedelta(days=45)
        }
    ]
    
    for post_data in sponsored_posts:
        post, created = SponsoredPost.objects.get_or_create(
            title=post_data['title'],
            defaults=post_data
        )
        if created:
            print(f"✅ Created sponsored post: {post.title}")
        else:
            print(f"📋 Sponsored post already exists: {post.title}")


def create_ad_settings():
    """Create or update ad settings"""
    settings_data = {
        'ads_enabled': True,
        'show_ads_to_premium': False,
        'max_ads_per_session': 10,
        'min_time_between_ads': 30,
        'rewarded_ads_enabled': True,
        'max_rewarded_ads_per_day': 5,
        'rewarded_ad_cooldown': 300,
        'revenue_share_with_users': Decimal('10.00'),
        'minimum_payout_threshold': Decimal('1.00'),
        'sponsored_posts_enabled': True,
        'sponsored_post_frequency': 5,
        'enable_user_targeting': True,
        'enable_behavioral_targeting': True
    }
    
    settings, created = AdSettings.objects.get_or_create(
        pk=1,
        defaults=settings_data
    )
    
    if created:
        print("✅ Created ad settings")
    else:
        # Update existing settings
        for key, value in settings_data.items():
            setattr(settings, key, value)
        settings.save()
        print("📋 Updated ad settings")


def main():
    """Main function to populate advertising data"""
    print("🚀 Populating advertising data...")
    print("=" * 50)
    
    try:
        create_ad_networks()
        print()
        
        create_ad_placements()
        print()
        
        create_rewarded_ads()
        print()
        
        create_sponsored_content()
        print()
        
        create_ad_settings()
        print()
        
        print("=" * 50)
        print("✅ Advertising data population completed successfully!")
        print()
        print("📊 Summary:")
        print(f"   • Ad Networks: {AdNetwork.objects.count()}")
        print(f"   • Ad Placements: {AdPlacement.objects.count()}")
        print(f"   • Rewarded Ads: {RewardedAd.objects.count()}")
        print(f"   • Sponsored Posts: {SponsoredPost.objects.count()}")
        print(f"   • Ad Settings: {'Configured' if AdSettings.objects.exists() else 'Not configured'}")
        
    except Exception as e:
        print(f"❌ Error populating advertising data: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
