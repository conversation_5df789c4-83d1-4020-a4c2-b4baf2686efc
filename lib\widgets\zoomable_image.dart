/// Zoomable image widget with pinch-to-zoom and pan support
/// Optimized for full-screen media viewing with smooth gestures

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ZoomableImage extends StatefulWidget {
  final String imageUrl;
  final String? heroTag;
  final double minScale;
  final double maxScale;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final Duration animationDuration;

  const ZoomableImage({
    Key? key,
    required this.imageUrl,
    this.heroTag,
    this.minScale = 0.5,
    this.maxScale = 3.0,
    this.onTap,
    this.onDoubleTap,
    this.animationDuration = const Duration(milliseconds: 200),
  }) : super(key: key);

  @override
  State<ZoomableImage> createState() => _ZoomableImageState();
}

class _ZoomableImageState extends State<ZoomableImage>
    with TickerProviderStateMixin {
  late TransformationController _transformationController;
  late AnimationController _animationController;
  late Animation<Matrix4> _animation;
  
  TapDownDetails? _doubleTapDetails;
  bool _isZoomed = false;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _animation = Matrix4Tween(
      begin: Matrix4.identity(),
      end: Matrix4.identity(),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animation.addListener(() {
      _transformationController.value = _animation.value;
    });
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _handleDoubleTapDown(TapDownDetails details) {
    _doubleTapDetails = details;
  }

  void _handleDoubleTap() {
    if (_doubleTapDetails == null) return;

    final position = _doubleTapDetails!.localPosition;
    
    if (_isZoomed) {
      // Zoom out to fit
      _animateToMatrix(Matrix4.identity());
      _isZoomed = false;
    } else {
      // Zoom in to 2x at tap position
      final double scale = 2.0;
      final Matrix4 matrix = Matrix4.identity()
        ..translate(-position.dx * (scale - 1), -position.dy * (scale - 1))
        ..scale(scale);
      
      _animateToMatrix(matrix);
      _isZoomed = true;
    }

    widget.onDoubleTap?.call();
  }

  void _animateToMatrix(Matrix4 targetMatrix) {
    _animation = Matrix4Tween(
      begin: _transformationController.value,
      end: targetMatrix,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward(from: 0);
  }

  void _handleInteractionEnd(ScaleEndDetails details) {
    // Check if we need to animate back to bounds
    final Matrix4 matrix = _transformationController.value;
    final double scale = matrix.getMaxScaleOnAxis();
    
    if (scale < widget.minScale) {
      _animateToMatrix(Matrix4.identity());
      _isZoomed = false;
    } else if (scale > widget.maxScale) {
      final Matrix4 clampedMatrix = Matrix4.identity()
        ..setFrom(matrix)
        ..scale(widget.maxScale / scale);
      _animateToMatrix(clampedMatrix);
    } else {
      _isZoomed = scale > 1.0;
    }
  }

  Widget _buildImage() {
    return CachedNetworkImage(
      imageUrl: widget.imageUrl,
      fit: BoxFit.contain,
      placeholder: (context, url) => Container(
        color: Colors.black,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
            strokeWidth: 2,
          ),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.white,
                size: 48,
              ),
              SizedBox(height: 16),
              Text(
                'Failed to load image',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = InteractiveViewer(
      transformationController: _transformationController,
      minScale: widget.minScale,
      maxScale: widget.maxScale,
      onInteractionEnd: _handleInteractionEnd,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: _buildImage(),
      ),
    );

    // Add gesture detection for tap and double tap
    imageWidget = GestureDetector(
      onTap: widget.onTap,
      onDoubleTapDown: _handleDoubleTapDown,
      onDoubleTap: _handleDoubleTap,
      child: imageWidget,
    );

    // Wrap with Hero if heroTag is provided
    if (widget.heroTag != null) {
      imageWidget = Hero(
        tag: widget.heroTag!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

/// Simple zoomable widget for any child
class ZoomableWidget extends StatefulWidget {
  final Widget child;
  final double minScale;
  final double maxScale;
  final VoidCallback? onTap;

  const ZoomableWidget({
    Key? key,
    required this.child,
    this.minScale = 0.5,
    this.maxScale = 3.0,
    this.onTap,
  }) : super(key: key);

  @override
  State<ZoomableWidget> createState() => _ZoomableWidgetState();
}

class _ZoomableWidgetState extends State<ZoomableWidget> {
  late TransformationController _transformationController;

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: InteractiveViewer(
        transformationController: _transformationController,
        minScale: widget.minScale,
        maxScale: widget.maxScale,
        child: widget.child,
      ),
    );
  }
}

/// Photo view widget with advanced zoom controls
class PhotoView extends StatefulWidget {
  final String imageUrl;
  final String? heroTag;
  final BoxFit fit;
  final VoidCallback? onTap;

  const PhotoView({
    Key? key,
    required this.imageUrl,
    this.heroTag,
    this.fit = BoxFit.contain,
    this.onTap,
  }) : super(key: key);

  @override
  State<PhotoView> createState() => _PhotoViewState();
}

class _PhotoViewState extends State<PhotoView> {
  late TransformationController _controller;
  double _currentScale = 1.0;

  @override
  void initState() {
    super.initState();
    _controller = TransformationController();
    _controller.addListener(_onTransformationChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onTransformationChanged);
    _controller.dispose();
    super.dispose();
  }

  void _onTransformationChanged() {
    setState(() {
      _currentScale = _controller.value.getMaxScaleOnAxis();
    });
  }

  void _resetZoom() {
    _controller.value = Matrix4.identity();
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = InteractiveViewer(
      transformationController: _controller,
      minScale: 0.1,
      maxScale: 5.0,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: CachedNetworkImage(
          imageUrl: widget.imageUrl,
          fit: widget.fit,
          placeholder: (context, url) => const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
          errorWidget: (context, url, error) => const Center(
            child: Icon(Icons.error, color: Colors.white, size: 48),
          ),
        ),
      ),
    );

    if (widget.onTap != null) {
      imageWidget = GestureDetector(
        onTap: widget.onTap,
        child: imageWidget,
      );
    }

    if (widget.heroTag != null) {
      imageWidget = Hero(
        tag: widget.heroTag!,
        child: imageWidget,
      );
    }

    return Stack(
      children: [
        imageWidget,
        
        // Zoom indicator
        if (_currentScale != 1.0)
          Positioned(
            top: 50,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${(_currentScale * 100).round()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: _resetZoom,
                    child: const Icon(
                      Icons.refresh,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
