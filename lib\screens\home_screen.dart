import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:shimmer/shimmer.dart';
import '../models/post.dart';
import '../models/category.dart';
import '../providers/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/regional_provider.dart';
import '../providers/advertising_provider.dart';
import '../widgets/post_preview_card.dart';
import '../widgets/content_settings_sheet.dart';
import '../widgets/gamification/user_level_widget.dart';
import '../widgets/gamification/challenges_widget.dart';
import '../widgets/rewarded_ad_widget.dart';
import '../widgets/sponsored_content_widget.dart';
import '../widgets/collapsible_header.dart';
import '../widgets/regional/country_selector.dart';
import '../widgets/regional/regional_indicator.dart';
import '../widgets/role_based_widget.dart';
import '../widgets/maintenance_banner.dart';
import '../widgets/connection_status_widgets.dart';
import '../providers/offline_provider.dart';
import '../models/app_error.dart';
import '../services/error_reporting_service.dart';
import '../theme/app_theme.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  String? _selectedCategory;
  String _searchQuery = '';
  final ScrollController _scrollController = ScrollController();
  PostDisplayMode _displayMode = PostDisplayMode.preview;
  SortOrder _sortOrder = SortOrder.newest;
  int _pageSize = 10;

  // Animation controllers for collapsible header
  late AnimationController _headerAnimationController;
  late Animation<double> _headerAnimation;
  late AnimationController _gamificationAnimationController;
  late Animation<double> _gamificationAnimation;

  // Scroll tracking
  bool _isHeaderVisible = true;

  @override
  void initState() {
    super.initState();

    // Initialize offline services
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeOfflineServices();
    });

    // Initialize animation controllers with improved timing
    _headerAnimationController = AnimationController(
      duration: const Duration(
        milliseconds: 400,
      ), // Slightly slower for smoother feel
      vsync: this,
    );
    _headerAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: Curves.easeInOutCubic, // More natural curve
      ),
    );

    _gamificationAnimationController = AnimationController(
      duration: const Duration(
        milliseconds: 350,
      ), // Slightly slower for better coordination
      vsync: this,
    );
    _gamificationAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _gamificationAnimationController,
        curve: Curves.easeInOutCubic, // More natural curve
      ),
    );

    _scrollController.addListener(_onScroll);

    // Initialize advertising provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('HomeScreen: Initializing advertising provider...');
      ref.read(advertisingProvider.notifier).loadAdvertisingData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _headerAnimationController.dispose();
    _gamificationAnimationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final currentScrollOffset = _scrollController.position.pixels;

    // Load more posts when user is near the bottom
    if (currentScrollOffset >=
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(regionalPostsProvider.notifier).loadMorePosts();
    }

    // Reversed header visibility logic - hide when at top, show when scrolling down
    if (currentScrollOffset <= 10) {
      // Hide header when at the very top (within 10px of top)
      _hideHeader();
    } else if (currentScrollOffset > 80 && !_isHeaderVisible) {
      // Show header when scrolled down more than 80px from top
      _showHeader();
    }
  }

  void _showHeader() {
    if (!_isHeaderVisible) {
      setState(() {
        _isHeaderVisible = true;
      });
      // Show search bar first
      _headerAnimationController.reverse();
      // Show gamification card with slight delay for staggered effect
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && _isHeaderVisible) {
          _gamificationAnimationController.reverse();
        }
      });
    }
  }

  void _hideHeader() {
    if (_isHeaderVisible) {
      setState(() {
        _isHeaderVisible = false;
      });
      // Hide gamification card first
      _gamificationAnimationController.forward();
      // Hide search bar with slight delay for staggered effect
      Future.delayed(const Duration(milliseconds: 50), () {
        if (mounted && !_isHeaderVisible) {
          _headerAnimationController.forward();
        }
      });
    }
  }

  Future<void> _initializeOfflineServices() async {
    try {
      // Initialize connectivity and storage services
      final connectivityService = ref.read(connectivityServiceProvider);
      final storageService = ref.read(offlineStorageServiceProvider);

      await connectivityService.initialize();
      await storageService.initialize();

      print('🚀 Offline services initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize offline services: $e');
    }
  }

  void _refreshData() {
    // Refresh both posts and categories
    ref.read(regionalPostsProvider.notifier).refreshPosts();
    ref.refresh(categoriesProvider);

    // Refresh advertising data
    ref.read(advertisingProvider.notifier).loadAdvertisingData();

    // Force connectivity check
    ref.read(connectivityServiceProvider).forceCheck();

    // Show a snackbar to indicate refresh
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.refresh, color: Colors.white),
            SizedBox(width: 8),
            Text('Refreshing content...'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showContentSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ContentSettingsSheet(
        currentDisplayMode: _displayMode,
        currentSortOrder: _sortOrder,
        currentPageSize: _pageSize,
        onDisplayModeChanged: (mode) {
          setState(() {
            _displayMode = mode;
          });
        },
        onSortOrderChanged: (order) {
          setState(() {
            _sortOrder = order;
          });
        },
        onPageSizeChanged: (size) {
          setState(() {
            _pageSize = size;
          });
          // Update the posts provider with new page size
          ref
              .read(regionalPostsProvider.notifier)
              .getPosts(pageSize: size, refresh: true);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final postsState = ref.watch(regionalPostsProvider);
    final categoriesState = ref.watch(categoriesProvider);
    final authState = ref.watch(enhancedAuthProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.trending_up,
                color: Colors.white,
                size: 22,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Trendy',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w800,
                    color: AppTheme.textPrimary,
                    fontSize: 24,
                  ),
            ),
          ],
        ),
        actions: [
          // Connection status indicator
          const ConnectionStatusIndicator(showText: false),
          const SizedBox(width: 8),
          // Maintenance status indicator
          const MaintenanceStatusIndicator(showText: false),
          // Settings button
          IconButton(
            icon: const Icon(Icons.tune, color: AppTheme.primaryColor),
            onPressed: _showContentSettings,
            tooltip: 'Content Settings',
          ),
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh, color: AppTheme.primaryColor),
            onPressed: () {
              _refreshData();
            },
            tooltip: 'Refresh',
          ),
          !authState.isAuthenticated
              ? Container(
                  margin: const EdgeInsets.only(right: 16),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: TextButton(
                      onPressed: () {
                        Navigator.pushNamed(context, '/auth');
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: const Text('Join Trendy'),
                    ),
                  ),
                )
              : PopupMenuButton<String>(
                  icon: CircleAvatar(
                    backgroundColor: AppTheme.primaryColor,
                    child: Text(
                      authState.user?.username.substring(0, 1).toUpperCase() ??
                          'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  onSelected: (value) {
                    if (value == 'logout') {
                      ref.read(enhancedAuthProvider.notifier).logout();
                      // Stay on main page after logout - just refresh the UI
                    } else if (value == 'profile') {
                      Navigator.pushNamed(context, '/profile');
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'profile',
                      child: Row(
                        children: [
                          Icon(Icons.person_outline),
                          SizedBox(width: 8),
                          Text('Profile'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'logout',
                      child: Row(
                        children: [
                          Icon(Icons.logout, color: AppTheme.errorColor),
                          SizedBox(width: 8),
                          Text(
                            'Logout',
                            style: TextStyle(color: AppTheme.errorColor),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
        ],
      ),
      // Floating Action Button for quick search when header is hidden
      floatingActionButton: CollapsibleSearchFAB(
        animation: _headerAnimation,
        onPressed: () {
          _showHeader();
          // Optional: Focus on search field after showing header
        },
        tooltip: 'Show Search & Gamification',
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _refreshData();
          // Wait a bit for the refresh to complete
          await Future.delayed(const Duration(milliseconds: 500));
        },
        color: AppTheme.primaryColor,
        child: Column(
          children: [
            // Connection status banner
            const ConnectionStatusBanner(),

            // Collapsible Header with Search Bar and Gamification
            CollapsibleHeader(
              animation: _headerAnimation,
              searchQuery: _searchQuery,
              onSearchChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              onSearchClear: () {
                setState(() {
                  _searchQuery = '';
                });
              },
              gamificationWidget: authState.isAuthenticated
                  ? UserLevelWidget(
                      onTap: () {
                        Navigator.pushNamed(context, '/gamification');
                      },
                    )
                  : null,
              gamificationAnimation:
                  authState.isAuthenticated ? _gamificationAnimation : null,
            ),

            // Regional Indicator
            if (authState.isAuthenticated)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: RegionalIndicator(
                  showChangeButton: true,
                  onChangePressed: () {
                    Navigator.pushNamed(context, '/regional-settings');
                  },
                ),
              ),

            // Role Badge and Content Creation Prompt
            if (authState.isAuthenticated) ...[
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    const RoleBadge(showDescription: true),
                    const Spacer(),
                    RoleBasedWidget(
                      requireContentCreator: true,
                      fallback: const SizedBox.shrink(),
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, '/create-post');
                        },
                        icon: const Icon(Icons.add, size: 16),
                        label: const Text('Create Post'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Content Creation Prompt for non-creators
              const ContentCreationPrompt(
                message:
                    'Want to share your thoughts? Request content creator permissions to start posting!',
              ),
            ],

            // Categories
            Container(
              height: 50,
              margin: const EdgeInsets.only(bottom: 16),
              child: categoriesState.when(
                data: (categories) {
                  return ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: categories.results.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return _buildCategoryChip(
                          'All',
                          _selectedCategory == null,
                          () {
                            setState(() {
                              _selectedCategory = null;
                            });
                          },
                        );
                      }
                      final category = categories.results[index - 1];
                      return _buildCategoryChip(
                        category.name,
                        _selectedCategory == category.slug,
                        () {
                          setState(() {
                            _selectedCategory =
                                _selectedCategory == category.slug
                                    ? null
                                    : category.slug;
                          });
                        },
                      );
                    },
                  );
                },
                loading: () => _buildCategoriesShimmer(),
                error: (error, stack) => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    ErrorReportingService.getUserFriendlyMessage(error),
                    style: TextStyle(color: AppTheme.errorColor),
                  ),
                ),
              ),
            ),
            // Posts
            Expanded(
              child: postsState.when(
                data: (posts) {
                  final filteredPosts = posts.results.where((post) {
                    final matchesCategory = _selectedCategory == null ||
                        post.category.slug == _selectedCategory;
                    final matchesSearch = _searchQuery.isEmpty ||
                        post.title.toLowerCase().contains(
                              _searchQuery.toLowerCase(),
                            ) ||
                        post.content.toLowerCase().contains(
                              _searchQuery.toLowerCase(),
                            );
                    return matchesCategory && matchesSearch;
                  }).toList();

                  if (filteredPosts.isEmpty) {
                    return _buildEmptyState();
                  }

                  final postsNotifier = ref.read(
                    regionalPostsProvider.notifier,
                  );
                  final hasMorePages = postsNotifier.hasMorePages;
                  final isLoadingMore = postsNotifier.isLoadingMore;

                  return AnimationLimiter(
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: filteredPosts.length + (hasMorePages ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Show loading indicator at the bottom
                        if (index == filteredPosts.length) {
                          return Container(
                            padding: const EdgeInsets.all(16),
                            child: Center(
                              child: isLoadingMore
                                  ? const CircularProgressIndicator()
                                  : ElevatedButton(
                                      onPressed: () =>
                                          postsNotifier.loadMorePosts(),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: AppTheme.primaryColor,
                                        foregroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                        ),
                                      ),
                                      child: const Text('Load More Posts'),
                                    ),
                            ),
                          );
                        }

                        final post = filteredPosts[index];

                        // Insert advertising content at strategic intervals
                        if (index > 0 && index % 3 == 0) {
                          // Every 3rd post, show advertising content (for testing)
                          return Column(
                            children: [
                              // Show rewarded ad widget every 6th post
                              if (index % 6 == 0)
                                const RewardedAdWidget(
                                  placementLocation: 'feed',
                                ),

                              // Show sponsored content every 3rd post
                              const SponsoredContentWidget(
                                contentType: 'feed',
                                showAsCard: false,
                              ),

                              const SizedBox(height: 8),

                              // Show the actual post
                              AnimationConfiguration.staggeredList(
                                position: index,
                                duration: const Duration(milliseconds: 375),
                                child: SlideAnimation(
                                  verticalOffset: 50.0,
                                  child: FadeInAnimation(
                                    child: _buildPostWidget(post),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }

                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: SlideAnimation(
                            verticalOffset: 50.0,
                            child: FadeInAnimation(
                              child: _buildPostWidget(post),
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
                loading: () => _buildPostsShimmer(),
                error: (error, stack) => ErrorDisplayWidget(
                  error: error is AppError
                      ? error
                      : AppErrorHandler.handleError(error),
                  onRetry: () {
                    _refreshData();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostWidget(dynamic post) {
    switch (_displayMode) {
      case PostDisplayMode.preview:
        return PostPreviewCard(
          post: post,
          maxLines: 3,
          showFullContent: false,
          onLike: () {
            ref.read(regionalPostsProvider.notifier).toggleLike(post.id);
          },
          onComment: () {
            Navigator.pushNamed(
              context,
              '/post',
              arguments: post.id.toString(),
            );
          },
        );
      case PostDisplayMode.full:
        return PostPreviewCard(
          post: post,
          showFullContent: true,
          onLike: () {
            ref.read(regionalPostsProvider.notifier).toggleLike(post.id);
          },
          onComment: () {
            Navigator.pushNamed(
              context,
              '/post',
              arguments: post.id.toString(),
            );
          },
        );
      case PostDisplayMode.compact:
        return _buildCompactPostCard(post);
    }
  }

  Widget _buildCompactPostCard(dynamic post) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(context, '/post', arguments: post.id.toString());
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Category indicator
              Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      post.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          post.author.username,
                          style: TextStyle(
                            color: AppTheme.textTertiary,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            post.category.name,
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Stats
              Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.favorite,
                        size: 14,
                        color:
                            post.isLiked ? Colors.red : AppTheme.textTertiary,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        post.likeCount.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textTertiary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.visibility,
                        size: 14,
                        color: AppTheme.textTertiary,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        post.views.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String label, bool isSelected, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(30),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.primaryGradient : null,
              color: isSelected ? null : Colors.white,
              borderRadius: BorderRadius.circular(30),
              border: isSelected
                  ? null
                  : Border.all(color: AppTheme.dividerColor, width: 1.5),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.04),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
            ),
            child: Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesShimmer() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(right: 12),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 80,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPostsShimmer() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: 3,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                Icons.search_off,
                size: 60,
                color: AppTheme.textTertiary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No posts found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppTheme.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or category filter',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppTheme.textTertiary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.errorColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                Icons.error_outline,
                size: 60,
                color: AppTheme.errorColor,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppTheme.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppTheme.textTertiary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                ref.refresh(regionalPostsProvider);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}
