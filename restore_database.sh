#!/bin/bash

# 🔄 SQLite Database Restore Script
# This script restores your SQLite database from a backup

set -e  # Exit on any error

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 SQLite Database Restore Script${NC}"
echo "==================================="

# Configuration
DB_PATH="trendy_web_and_api/trendy/db.sqlite3"
BACKUP_DIR="database_backups"

# Check if backup file is provided
if [ $# -eq 0 ]; then
    echo -e "${BLUE}📋 Available backups:${NC}"
    if [ -d "$BACKUP_DIR" ]; then
        ls -la "$BACKUP_DIR"/*.sqlite3 2>/dev/null || echo "No backup files found"
    else
        echo "No backup directory found"
    fi
    echo ""
    echo -e "${YELLOW}Usage: $0 <backup_filename>${NC}"
    echo -e "${BLUE}Example: $0 db_backup_20241223_143022.sqlite3${NC}"
    echo -e "${BLUE}Or use: $0 latest${NC}"
    exit 1
fi

# Determine backup file
if [ "$1" = "latest" ]; then
    BACKUP_FILE="${BACKUP_DIR}/latest_backup.sqlite3"
    echo -e "${BLUE}🔗 Using latest backup${NC}"
else
    BACKUP_FILE="${BACKUP_DIR}/$1"
fi

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo -e "${RED}❌ Backup file not found: $BACKUP_FILE${NC}"
    echo -e "${BLUE}📋 Available backups:${NC}"
    ls -la "$BACKUP_DIR"/*.sqlite3 2>/dev/null || echo "No backup files found"
    exit 1
fi

echo -e "${BLUE}📦 Backup file: $BACKUP_FILE${NC}"
echo -e "${BLUE}📍 Target: $DB_PATH${NC}"

# Create backup of current database if it exists
if [ -f "$DB_PATH" ]; then
    CURRENT_BACKUP="${BACKUP_DIR}/pre_restore_backup_$(date +"%Y%m%d_%H%M%S").sqlite3"
    echo -e "${YELLOW}⚠️ Creating backup of current database...${NC}"
    cp "$DB_PATH" "$CURRENT_BACKUP"
    echo -e "${GREEN}✅ Current database backed up to: $CURRENT_BACKUP${NC}"
fi

# Restore database
echo -e "${BLUE}🔄 Restoring database...${NC}"
cp "$BACKUP_FILE" "$DB_PATH"

# Verify restoration
if [ -f "$DB_PATH" ]; then
    BACKUP_SIZE=$(stat -f%z "$BACKUP_FILE" 2>/dev/null || stat -c%s "$BACKUP_FILE" 2>/dev/null)
    RESTORED_SIZE=$(stat -f%z "$DB_PATH" 2>/dev/null || stat -c%s "$DB_PATH" 2>/dev/null)
    
    echo -e "${GREEN}✅ Database restored successfully!${NC}"
    echo -e "${BLUE}📊 Backup size: ${BACKUP_SIZE} bytes${NC}"
    echo -e "${BLUE}📊 Restored size: ${RESTORED_SIZE} bytes${NC}"
    
    # Test database integrity
    echo -e "${BLUE}🔍 Testing database integrity...${NC}"
    cd trendy_web_and_api/trendy
    
    # Check if virtual environment exists and activate it
    if [ -d "venv" ]; then
        source venv/bin/activate
        echo -e "${GREEN}✅ Virtual environment activated${NC}"
    else
        echo -e "${YELLOW}⚠️ Virtual environment not found, using system Python${NC}"
    fi
    
    # Test Django database connection
    if python manage.py check --database default 2>/dev/null; then
        echo -e "${GREEN}✅ Database integrity check passed${NC}"
        
        # Show some basic stats
        echo -e "\n${BLUE}📊 Database Statistics:${NC}"
        python manage.py shell -c "
from django.contrib.auth import get_user_model
from django.apps import apps

User = get_user_model()
print(f'👥 Users: {User.objects.count()}')

try:
    from wallet.models import UserWallet
    print(f'💰 Wallets: {UserWallet.objects.count()}')
except:
    pass

try:
    from blog.models import Post
    print(f'📝 Posts: {Post.objects.count()}')
except:
    pass
" 2>/dev/null || echo "Could not retrieve statistics"
        
    else
        echo -e "${YELLOW}⚠️ Database integrity check failed, but file was restored${NC}"
        echo -e "${BLUE}💡 You may need to run migrations: python manage.py migrate${NC}"
    fi
    
else
    echo -e "${RED}❌ Database restoration failed!${NC}"
    exit 1
fi

echo -e "\n${GREEN}🎉 Database restoration completed!${NC}"
echo -e "${BLUE}💡 Next steps:${NC}"
echo -e "  1. cd trendy_web_and_api/trendy"
echo -e "  2. source venv/bin/activate"
echo -e "  3. python manage.py migrate (if needed)"
echo -e "  4. python manage.py runserver"
