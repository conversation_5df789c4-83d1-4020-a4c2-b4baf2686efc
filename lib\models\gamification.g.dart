// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gamification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BadgeImpl _$$BadgeImplFromJson(Map<String, dynamic> json) => _$BadgeImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String,
      badgeType: json['badge_type'] as String,
      typeDisplay: json['type_display'] as String,
      rarity: json['rarity'] as String,
      rarityDisplay: json['rarity_display'] as String,
      icon: json['icon'] as String,
      color: json['color'] as String,
      imageUrl: json['image_url'] as String?,
      pointsReward: (json['points_reward'] as num).toInt(),
      isSecret: json['is_secret'] as bool? ?? false,
      requirements: json['requirements'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$BadgeImplToJson(_$BadgeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'badge_type': instance.badgeType,
      'type_display': instance.typeDisplay,
      'rarity': instance.rarity,
      'rarity_display': instance.rarityDisplay,
      'icon': instance.icon,
      'color': instance.color,
      'image_url': instance.imageUrl,
      'points_reward': instance.pointsReward,
      'is_secret': instance.isSecret,
      'requirements': instance.requirements,
    };

_$UserBadgeImpl _$$UserBadgeImplFromJson(Map<String, dynamic> json) =>
    _$UserBadgeImpl(
      id: (json['id'] as num).toInt(),
      badge: Badge.fromJson(json['badge'] as Map<String, dynamic>),
      earnedAt: DateTime.parse(json['earned_at'] as String),
      progressData: json['progress_data'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$UserBadgeImplToJson(_$UserBadgeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'badge': instance.badge,
      'earned_at': instance.earnedAt.toIso8601String(),
      'progress_data': instance.progressData,
    };

_$ChallengeImpl _$$ChallengeImplFromJson(Map<String, dynamic> json) =>
    _$ChallengeImpl(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      description: json['description'] as String,
      challengeType: json['challenge_type'] as String,
      typeDisplay: json['type_display'] as String,
      difficulty: json['difficulty'] as String,
      difficultyDisplay: json['difficulty_display'] as String,
      requirements: json['requirements'] as Map<String, dynamic>? ?? const {},
      pointsReward: (json['points_reward'] as num).toInt(),
      badgeReward: json['badge_reward'] == null
          ? null
          : Badge.fromJson(json['badge_reward'] as Map<String, dynamic>),
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      durationDays: (json['duration_days'] as num).toInt(),
      maxParticipants: (json['max_participants'] as num?)?.toInt(),
      isOngoing: json['is_ongoing'] as bool? ?? false,
      participantCount: (json['participant_count'] as num?)?.toInt() ?? 0,
      userParticipation: json['user_participation'] == null
          ? null
          : ChallengeParticipation.fromJson(
              json['user_participation'] as Map<String, dynamic>),
      isFeatured: json['is_featured'] as bool? ?? false,
    );

Map<String, dynamic> _$$ChallengeImplToJson(_$ChallengeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'challenge_type': instance.challengeType,
      'type_display': instance.typeDisplay,
      'difficulty': instance.difficulty,
      'difficulty_display': instance.difficultyDisplay,
      'requirements': instance.requirements,
      'points_reward': instance.pointsReward,
      'badge_reward': instance.badgeReward,
      'start_date': instance.startDate.toIso8601String(),
      'end_date': instance.endDate.toIso8601String(),
      'duration_days': instance.durationDays,
      'max_participants': instance.maxParticipants,
      'is_ongoing': instance.isOngoing,
      'participant_count': instance.participantCount,
      'user_participation': instance.userParticipation,
      'is_featured': instance.isFeatured,
    };

_$ChallengeParticipationImpl _$$ChallengeParticipationImplFromJson(
        Map<String, dynamic> json) =>
    _$ChallengeParticipationImpl(
      id: (json['id'] as num).toInt(),
      challengeTitle: json['challenge_title'] as String?,
      progress: json['progress'] as Map<String, dynamic>? ?? const {},
      completionPercentage:
          (json['completion_percentage'] as num?)?.toDouble() ?? 0.0,
      isCompleted: json['is_completed'] as bool? ?? false,
      completedAt: json['completed_at'] == null
          ? null
          : DateTime.parse(json['completed_at'] as String),
      joinedAt: DateTime.parse(json['joined_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$ChallengeParticipationImplToJson(
        _$ChallengeParticipationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'challenge_title': instance.challengeTitle,
      'progress': instance.progress,
      'completion_percentage': instance.completionPercentage,
      'is_completed': instance.isCompleted,
      'completed_at': instance.completedAt?.toIso8601String(),
      'joined_at': instance.joinedAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_$UserLevelImpl _$$UserLevelImplFromJson(Map<String, dynamic> json) =>
    _$UserLevelImpl(
      totalPoints: (json['total_points'] as num?)?.toInt() ?? 0,
      currentLevel: (json['current_level'] as num?)?.toInt() ?? 1,
      pointsToNextLevel: (json['points_to_next_level'] as num?)?.toInt() ?? 100,
      levelProgressPercentage:
          (json['level_progress_percentage'] as num?)?.toDouble() ?? 0.0,
      nextLevelPoints: (json['next_level_points'] as num?)?.toInt() ?? 100,
      readingStreak: (json['reading_streak'] as num?)?.toInt() ?? 0,
      writingStreak: (json['writing_streak'] as num?)?.toInt() ?? 0,
      engagementStreak: (json['engagement_streak'] as num?)?.toInt() ?? 0,
      totalPostsRead: (json['total_posts_read'] as num?)?.toInt() ?? 0,
      totalPostsWritten: (json['total_posts_written'] as num?)?.toInt() ?? 0,
      totalCommentsMade: (json['total_comments_made'] as num?)?.toInt() ?? 0,
      totalLikesGiven: (json['total_likes_given'] as num?)?.toInt() ?? 0,
      totalVoiceComments: (json['total_voice_comments'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$UserLevelImplToJson(_$UserLevelImpl instance) =>
    <String, dynamic>{
      'total_points': instance.totalPoints,
      'current_level': instance.currentLevel,
      'points_to_next_level': instance.pointsToNextLevel,
      'level_progress_percentage': instance.levelProgressPercentage,
      'next_level_points': instance.nextLevelPoints,
      'reading_streak': instance.readingStreak,
      'writing_streak': instance.writingStreak,
      'engagement_streak': instance.engagementStreak,
      'total_posts_read': instance.totalPostsRead,
      'total_posts_written': instance.totalPostsWritten,
      'total_comments_made': instance.totalCommentsMade,
      'total_likes_given': instance.totalLikesGiven,
      'total_voice_comments': instance.totalVoiceComments,
    };

_$PointTransactionImpl _$$PointTransactionImplFromJson(
        Map<String, dynamic> json) =>
    _$PointTransactionImpl(
      id: (json['id'] as num).toInt(),
      transactionType: json['transaction_type'] as String,
      typeDisplay: json['type_display'] as String,
      points: (json['points'] as num).toInt(),
      description: json['description'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$$PointTransactionImplToJson(
        _$PointTransactionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'transaction_type': instance.transactionType,
      'type_display': instance.typeDisplay,
      'points': instance.points,
      'description': instance.description,
      'created_at': instance.createdAt.toIso8601String(),
    };

_$LeaderboardEntryImpl _$$LeaderboardEntryImplFromJson(
        Map<String, dynamic> json) =>
    _$LeaderboardEntryImpl(
      userId: (json['user_id'] as num).toInt(),
      username: json['username'] as String,
      totalPoints: (json['total_points'] as num).toInt(),
      currentLevel: (json['current_level'] as num).toInt(),
      rank: (json['rank'] as num).toInt(),
      badgeCount: (json['badge_count'] as num).toInt(),
    );

Map<String, dynamic> _$$LeaderboardEntryImplToJson(
        _$LeaderboardEntryImpl instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'username': instance.username,
      'total_points': instance.totalPoints,
      'current_level': instance.currentLevel,
      'rank': instance.rank,
      'badge_count': instance.badgeCount,
    };

_$GamificationUserProfileImpl _$$GamificationUserProfileImplFromJson(
        Map<String, dynamic> json) =>
    _$GamificationUserProfileImpl(
      id: (json['id'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String,
      dateJoined: DateTime.parse(json['date_joined'] as String),
      level: json['level'] == null
          ? null
          : UserLevel.fromJson(json['level'] as Map<String, dynamic>),
      earnedBadges: (json['earned_badges'] as List<dynamic>?)
              ?.map((e) => UserBadge.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      recentTransactions: (json['recent_transactions'] as List<dynamic>?)
              ?.map((e) => PointTransaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      activeChallenges: (json['active_challenges'] as List<dynamic>?)
              ?.map((e) =>
                  ChallengeParticipation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$GamificationUserProfileImplToJson(
        _$GamificationUserProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'date_joined': instance.dateJoined.toIso8601String(),
      'level': instance.level,
      'earned_badges': instance.earnedBadges,
      'recent_transactions': instance.recentTransactions,
      'active_challenges': instance.activeChallenges,
    };
