# Database Management & System Maintenance

This document describes the comprehensive database management and system maintenance system for the Trendy application.

## 🚀 Quick Start

### 1. Setup the System
```bash
# Navigate to Django project
cd trendy_web_and_api/trendy

# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Setup the maintenance system
python manage.py setup_maintenance_system
```

### 2. Access Admin Interface
- Visit: `http://your-server/admin/core/`
- Login with superuser credentials
- Manage maintenance and feature toggles

## 📦 Database Backup Commands

### Create Backup
```bash
# Basic backup
python manage.py backup_database

# Compressed backup with media files
python manage.py backup_database --compress --include-media

# Custom backup location and name
python manage.py backup_database --output-dir /path/to/backups --name my_backup
```

**Backup Features:**
- ✅ Raw database dumps (SQLite, PostgreSQL, MySQL)
- ✅ Django fixtures for all models
- ✅ Media files (optional)
- ✅ Configuration templates
- ✅ Compression support
- ✅ Metadata and restore scripts
- ✅ Automatic timestamping

### Restore Database
```bash
# Restore from backup
python manage.py restore_database backup_name

# Restore with media files
python manage.py restore_database backup_name --restore-media

# Force restore without confirmation
python manage.py restore_database backup_name --force

# Dry run to see what would be restored
python manage.py restore_database backup_name --dry-run
```

**Restore Features:**
- ✅ Automatic backup validation
- ✅ Pre-restore backup creation
- ✅ Database engine compatibility checks
- ✅ Media file restoration
- ✅ Migration execution
- ✅ Static file collection

### Clear Database
```bash
# Clear database with confirmation
python manage.py clear_database

# Create backup before clearing
python manage.py clear_database --create-backup

# Preserve superuser accounts
python manage.py clear_database --preserve-users

# Preserve groups and permissions
python manage.py clear_database --preserve-groups

# Force clear without confirmation
python manage.py clear_database --force

# Dry run to see what would be deleted
python manage.py clear_database --dry-run
```

**Clear Features:**
- ✅ Safe data deletion with confirmations
- ✅ Preserve essential data (users, groups)
- ✅ Automatic backup creation
- ✅ Database sequence reset
- ✅ Dependency-aware deletion order

## 🔧 System Maintenance

### Admin Interface Features

#### System Maintenance
- **Schedule Maintenance Windows**: Set start/end times
- **Maintenance Types**: Full, Partial, Database, Upgrade, Emergency
- **Access Control**: Allow admin/staff access during maintenance
- **Custom Messages**: Public and internal messages
- **Status Tracking**: Scheduled → Active → Completed
- **Activity Logging**: All maintenance actions logged

#### Feature Toggles
- **Granular Control**: Enable/disable individual features
- **User-Based Access**: Different permissions for admin/staff/users
- **Timed Toggles**: Schedule feature enable/disable
- **Feature Types**: API, UI, Service, Integration, Experimental
- **Custom Messages**: Feature-specific disabled messages

#### Maintenance Logs
- **Activity Tracking**: All system changes logged
- **User Attribution**: Track who made changes
- **Detailed Information**: JSON metadata for complex operations
- **Search & Filter**: Find specific activities

### Predefined Features

The system includes toggles for:
- 🔐 **User Registration**: New account creation
- 📝 **Post Creation**: Content publishing
- 💬 **Commenting System**: User comments
- 🗳️ **Voting System**: Post and poll voting
- 💌 **Messaging System**: Private messages
- 💳 **Payment Processing**: Financial transactions
- 🤖 **AI Writing Assistant**: Content creation help
- 🎤 **Voice Features**: Text-to-speech functionality
- ⛓️ **Blockchain Features**: Crypto and NFT functionality
- 📊 **Analytics Tracking**: User behavior tracking

## 🛡️ Middleware Protection

### Maintenance Middleware
- **Automatic Enforcement**: Blocks access during maintenance
- **User Exceptions**: Allow admin/staff access
- **API & Web Support**: JSON and HTML responses
- **Custom Templates**: Branded maintenance pages

### Feature Toggle Middleware
- **URL Pattern Matching**: Automatic feature detection
- **User-Based Permissions**: Role-aware feature access
- **API Protection**: Consistent error responses
- **Template Integration**: Feature status in templates

## 📋 Usage Examples

### Schedule Maintenance
1. Go to Admin → Core → System Maintenance
2. Click "Add System Maintenance"
3. Set title, description, and timing
4. Configure access permissions
5. Activate when ready

### Disable Feature
1. Go to Admin → Core → Feature Toggles
2. Find the feature to disable
3. Uncheck "Is enabled"
4. Set custom disabled message
5. Save changes

### Emergency Maintenance
```bash
# Quick backup and maintenance activation
python manage.py backup_database --compress
# Then activate maintenance in admin interface
```

### System Upgrade Process
```bash
# 1. Create backup
python manage.py backup_database --compress --include-media --name pre_upgrade

# 2. Activate maintenance mode (via admin)

# 3. Perform upgrades
git pull origin main
pip install -r requirements.txt
python manage.py migrate

# 4. Test system

# 5. Deactivate maintenance mode (via admin)
```

## 🔍 Monitoring & Logs

### View Logs
- **Admin Interface**: Core → Maintenance Logs
- **Filter Options**: By type, date, user, related object
- **Search**: Find specific activities
- **Export**: Download log data

### System Status
```bash
# Check current status
python manage.py setup_maintenance_system --force
```

## 🚨 Emergency Procedures

### Emergency Shutdown
1. Admin → Core → System Maintenance
2. Create "Emergency Maintenance"
3. Set type to "Emergency"
4. Activate immediately
5. All users blocked except admins

### Quick Recovery
```bash
# If system is broken, restore from backup
python manage.py restore_database latest_backup --force

# Or clear and start fresh
python manage.py clear_database --create-backup --force
python manage.py migrate
python manage.py createsuperuser
```

## 📁 File Structure

```
trendy_web_and_api/trendy/
├── core/                           # Core maintenance app
│   ├── models.py                   # Maintenance & feature models
│   ├── admin.py                    # Admin interface
│   ├── middleware.py               # Protection middleware
│   └── management/commands/        # Management commands
│       ├── backup_database.py      # Database backup
│       ├── restore_database.py     # Database restore
│       ├── clear_database.py       # Database clearing
│       └── setup_maintenance_system.py
├── backups/                        # Default backup location
│   ├── pre_restore/               # Pre-restore backups
│   └── pre_clear/                 # Pre-clear backups
└── trendyblog/settings.py         # Updated with core app
```

## 🔒 Security Considerations

- **Backup Encryption**: Consider encrypting sensitive backups
- **Access Control**: Limit admin access to maintenance features
- **Audit Trail**: All actions are logged with user attribution
- **Safe Defaults**: Confirmations required for destructive operations
- **Environment Isolation**: Separate backup locations per environment

## 🎯 Best Practices

1. **Regular Backups**: Schedule automated daily backups
2. **Test Restores**: Regularly test backup restoration
3. **Maintenance Windows**: Schedule during low-traffic periods
4. **Feature Rollouts**: Use toggles for gradual feature deployment
5. **Documentation**: Keep maintenance logs and procedures updated
6. **Monitoring**: Set up alerts for maintenance mode activation
7. **Communication**: Notify users of scheduled maintenance

## 🆘 Support

For issues or questions:
1. Check maintenance logs in admin interface
2. Review backup/restore command output
3. Verify middleware configuration in settings
4. Test with dry-run options first
5. Contact system administrator if needed

## 📱 Flutter App Integration

### **Maintenance Notifications**
The Flutter app now includes comprehensive maintenance status checking:

#### **Automatic Maintenance Detection**
- **System Status Provider**: Checks maintenance status every 2 minutes
- **Maintenance Screen**: Full-screen maintenance notification with details
- **Maintenance Banner**: Collapsible banner shown on all screens during maintenance
- **Status Indicators**: Small maintenance indicators in app bars

#### **API Endpoints for Flutter**
```bash
# System status (comprehensive)
GET /api/v1/system/status/

# Health check (lightweight)
GET /api/v1/system/health/

# Feature status
GET /api/v1/system/feature/<feature_name>/

# All features
GET /api/v1/system/features/
```

#### **Flutter Components**
- **MaintenanceBanner**: Shows maintenance info at top of screens
- **MaintenanceStatusIndicator**: Small indicator for app bars
- **MaintenanceScreen**: Full-screen maintenance page
- **FeatureWrapper**: Wraps features that can be disabled
- **FeatureAwareButton**: Buttons that check feature status

#### **User Experience**
- **Automatic Detection**: App checks maintenance status on startup and periodically
- **Graceful Degradation**: Features are disabled gracefully with user-friendly messages
- **Real-time Updates**: Status updates without app restart
- **Informative Messages**: Clear communication about maintenance duration and type

#### **Testing Flutter Integration**
1. Launch the Flutter app
2. Check that maintenance banner appears when maintenance is active
3. Test feature toggles by disabling features in admin
4. Verify that disabled features show appropriate messages in the app

---

**System Status**: ✅ Ready for Production (Web + Mobile)
**Last Updated**: 2025-08-04
**Version**: 1.1.0 (Added Flutter Integration)
