// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'post.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Post _$PostFromJson(Map<String, dynamic> json) {
  return _Post.fromJson(json);
}

/// @nodoc
mixin _$Post {
  int get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get slug => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  User get author => throw _privateConstructorUsedError;
  Category get category => throw _privateConstructorUsedError;
  List<Tag> get tags => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt => throw _privateConstructorUsedError;
  int get views => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_featured')
  bool get isFeatured => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  List<dynamic> get reference => throw _privateConstructorUsedError;
  @JsonKey(name: 'comment_count')
  int get commentCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'like_count')
  int get likeCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_liked')
  bool get isLiked => throw _privateConstructorUsedError;
  @JsonKey(name: 'media_items')
  List<PostMedia> get mediaItems =>
      throw _privateConstructorUsedError; // Regional targeting fields
  @JsonKey(name: 'is_global')
  bool get isGlobal => throw _privateConstructorUsedError;
  @JsonKey(name: 'target_countries')
  List<Country> get targetCountries => throw _privateConstructorUsedError;
  @JsonKey(name: 'regional_priority')
  int get regionalPriority => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PostCopyWith<Post> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PostCopyWith<$Res> {
  factory $PostCopyWith(Post value, $Res Function(Post) then) =
      _$PostCopyWithImpl<$Res, Post>;
  @useResult
  $Res call(
      {int id,
      String title,
      String slug,
      String content,
      User author,
      Category category,
      List<Tag> tags,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      int views,
      @JsonKey(name: 'is_featured') bool isFeatured,
      String status,
      List<dynamic> reference,
      @JsonKey(name: 'comment_count') int commentCount,
      @JsonKey(name: 'like_count') int likeCount,
      @JsonKey(name: 'is_liked') bool isLiked,
      @JsonKey(name: 'media_items') List<PostMedia> mediaItems,
      @JsonKey(name: 'is_global') bool isGlobal,
      @JsonKey(name: 'target_countries') List<Country> targetCountries,
      @JsonKey(name: 'regional_priority') int regionalPriority});

  $UserCopyWith<$Res> get author;
  $CategoryCopyWith<$Res> get category;
}

/// @nodoc
class _$PostCopyWithImpl<$Res, $Val extends Post>
    implements $PostCopyWith<$Res> {
  _$PostCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? slug = null,
    Object? content = null,
    Object? author = null,
    Object? category = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? views = null,
    Object? isFeatured = null,
    Object? status = null,
    Object? reference = null,
    Object? commentCount = null,
    Object? likeCount = null,
    Object? isLiked = null,
    Object? mediaItems = null,
    Object? isGlobal = null,
    Object? targetCountries = null,
    Object? regionalPriority = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      author: null == author
          ? _value.author
          : author // ignore: cast_nullable_to_non_nullable
              as User,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as Category,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<Tag>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      views: null == views
          ? _value.views
          : views // ignore: cast_nullable_to_non_nullable
              as int,
      isFeatured: null == isFeatured
          ? _value.isFeatured
          : isFeatured // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      reference: null == reference
          ? _value.reference
          : reference // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      commentCount: null == commentCount
          ? _value.commentCount
          : commentCount // ignore: cast_nullable_to_non_nullable
              as int,
      likeCount: null == likeCount
          ? _value.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int,
      isLiked: null == isLiked
          ? _value.isLiked
          : isLiked // ignore: cast_nullable_to_non_nullable
              as bool,
      mediaItems: null == mediaItems
          ? _value.mediaItems
          : mediaItems // ignore: cast_nullable_to_non_nullable
              as List<PostMedia>,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
      targetCountries: null == targetCountries
          ? _value.targetCountries
          : targetCountries // ignore: cast_nullable_to_non_nullable
              as List<Country>,
      regionalPriority: null == regionalPriority
          ? _value.regionalPriority
          : regionalPriority // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get author {
    return $UserCopyWith<$Res>(_value.author, (value) {
      return _then(_value.copyWith(author: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CategoryCopyWith<$Res> get category {
    return $CategoryCopyWith<$Res>(_value.category, (value) {
      return _then(_value.copyWith(category: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PostImplCopyWith<$Res> implements $PostCopyWith<$Res> {
  factory _$$PostImplCopyWith(
          _$PostImpl value, $Res Function(_$PostImpl) then) =
      __$$PostImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String title,
      String slug,
      String content,
      User author,
      Category category,
      List<Tag> tags,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      int views,
      @JsonKey(name: 'is_featured') bool isFeatured,
      String status,
      List<dynamic> reference,
      @JsonKey(name: 'comment_count') int commentCount,
      @JsonKey(name: 'like_count') int likeCount,
      @JsonKey(name: 'is_liked') bool isLiked,
      @JsonKey(name: 'media_items') List<PostMedia> mediaItems,
      @JsonKey(name: 'is_global') bool isGlobal,
      @JsonKey(name: 'target_countries') List<Country> targetCountries,
      @JsonKey(name: 'regional_priority') int regionalPriority});

  @override
  $UserCopyWith<$Res> get author;
  @override
  $CategoryCopyWith<$Res> get category;
}

/// @nodoc
class __$$PostImplCopyWithImpl<$Res>
    extends _$PostCopyWithImpl<$Res, _$PostImpl>
    implements _$$PostImplCopyWith<$Res> {
  __$$PostImplCopyWithImpl(_$PostImpl _value, $Res Function(_$PostImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? slug = null,
    Object? content = null,
    Object? author = null,
    Object? category = null,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? views = null,
    Object? isFeatured = null,
    Object? status = null,
    Object? reference = null,
    Object? commentCount = null,
    Object? likeCount = null,
    Object? isLiked = null,
    Object? mediaItems = null,
    Object? isGlobal = null,
    Object? targetCountries = null,
    Object? regionalPriority = null,
  }) {
    return _then(_$PostImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      slug: null == slug
          ? _value.slug
          : slug // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      author: null == author
          ? _value.author
          : author // ignore: cast_nullable_to_non_nullable
              as User,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as Category,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<Tag>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      views: null == views
          ? _value.views
          : views // ignore: cast_nullable_to_non_nullable
              as int,
      isFeatured: null == isFeatured
          ? _value.isFeatured
          : isFeatured // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      reference: null == reference
          ? _value._reference
          : reference // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      commentCount: null == commentCount
          ? _value.commentCount
          : commentCount // ignore: cast_nullable_to_non_nullable
              as int,
      likeCount: null == likeCount
          ? _value.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int,
      isLiked: null == isLiked
          ? _value.isLiked
          : isLiked // ignore: cast_nullable_to_non_nullable
              as bool,
      mediaItems: null == mediaItems
          ? _value._mediaItems
          : mediaItems // ignore: cast_nullable_to_non_nullable
              as List<PostMedia>,
      isGlobal: null == isGlobal
          ? _value.isGlobal
          : isGlobal // ignore: cast_nullable_to_non_nullable
              as bool,
      targetCountries: null == targetCountries
          ? _value._targetCountries
          : targetCountries // ignore: cast_nullable_to_non_nullable
              as List<Country>,
      regionalPriority: null == regionalPriority
          ? _value.regionalPriority
          : regionalPriority // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PostImpl implements _Post {
  const _$PostImpl(
      {required this.id,
      required this.title,
      required this.slug,
      required this.content,
      required this.author,
      required this.category,
      final List<Tag> tags = const [],
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt,
      this.views = 0,
      @JsonKey(name: 'is_featured') this.isFeatured = false,
      this.status = 'draft',
      final List<dynamic> reference = const [],
      @JsonKey(name: 'comment_count') this.commentCount = 0,
      @JsonKey(name: 'like_count') this.likeCount = 0,
      @JsonKey(name: 'is_liked') this.isLiked = false,
      @JsonKey(name: 'media_items') final List<PostMedia> mediaItems = const [],
      @JsonKey(name: 'is_global') this.isGlobal = false,
      @JsonKey(name: 'target_countries')
      final List<Country> targetCountries = const [],
      @JsonKey(name: 'regional_priority') this.regionalPriority = 0})
      : _tags = tags,
        _reference = reference,
        _mediaItems = mediaItems,
        _targetCountries = targetCountries;

  factory _$PostImpl.fromJson(Map<String, dynamic> json) =>
      _$$PostImplFromJson(json);

  @override
  final int id;
  @override
  final String title;
  @override
  final String slug;
  @override
  final String content;
  @override
  final User author;
  @override
  final Category category;
  final List<Tag> _tags;
  @override
  @JsonKey()
  List<Tag> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @override
  @JsonKey()
  final int views;
  @override
  @JsonKey(name: 'is_featured')
  final bool isFeatured;
  @override
  @JsonKey()
  final String status;
  final List<dynamic> _reference;
  @override
  @JsonKey()
  List<dynamic> get reference {
    if (_reference is EqualUnmodifiableListView) return _reference;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_reference);
  }

  @override
  @JsonKey(name: 'comment_count')
  final int commentCount;
  @override
  @JsonKey(name: 'like_count')
  final int likeCount;
  @override
  @JsonKey(name: 'is_liked')
  final bool isLiked;
  final List<PostMedia> _mediaItems;
  @override
  @JsonKey(name: 'media_items')
  List<PostMedia> get mediaItems {
    if (_mediaItems is EqualUnmodifiableListView) return _mediaItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mediaItems);
  }

// Regional targeting fields
  @override
  @JsonKey(name: 'is_global')
  final bool isGlobal;
  final List<Country> _targetCountries;
  @override
  @JsonKey(name: 'target_countries')
  List<Country> get targetCountries {
    if (_targetCountries is EqualUnmodifiableListView) return _targetCountries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_targetCountries);
  }

  @override
  @JsonKey(name: 'regional_priority')
  final int regionalPriority;

  @override
  String toString() {
    return 'Post(id: $id, title: $title, slug: $slug, content: $content, author: $author, category: $category, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt, views: $views, isFeatured: $isFeatured, status: $status, reference: $reference, commentCount: $commentCount, likeCount: $likeCount, isLiked: $isLiked, mediaItems: $mediaItems, isGlobal: $isGlobal, targetCountries: $targetCountries, regionalPriority: $regionalPriority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PostImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.slug, slug) || other.slug == slug) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.author, author) || other.author == author) &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.views, views) || other.views == views) &&
            (identical(other.isFeatured, isFeatured) ||
                other.isFeatured == isFeatured) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality()
                .equals(other._reference, _reference) &&
            (identical(other.commentCount, commentCount) ||
                other.commentCount == commentCount) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.isLiked, isLiked) || other.isLiked == isLiked) &&
            const DeepCollectionEquality()
                .equals(other._mediaItems, _mediaItems) &&
            (identical(other.isGlobal, isGlobal) ||
                other.isGlobal == isGlobal) &&
            const DeepCollectionEquality()
                .equals(other._targetCountries, _targetCountries) &&
            (identical(other.regionalPriority, regionalPriority) ||
                other.regionalPriority == regionalPriority));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        slug,
        content,
        author,
        category,
        const DeepCollectionEquality().hash(_tags),
        createdAt,
        updatedAt,
        views,
        isFeatured,
        status,
        const DeepCollectionEquality().hash(_reference),
        commentCount,
        likeCount,
        isLiked,
        const DeepCollectionEquality().hash(_mediaItems),
        isGlobal,
        const DeepCollectionEquality().hash(_targetCountries),
        regionalPriority
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PostImplCopyWith<_$PostImpl> get copyWith =>
      __$$PostImplCopyWithImpl<_$PostImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PostImplToJson(
      this,
    );
  }
}

abstract class _Post implements Post {
  const factory _Post(
      {required final int id,
      required final String title,
      required final String slug,
      required final String content,
      required final User author,
      required final Category category,
      final List<Tag> tags,
      @JsonKey(name: 'created_at') required final DateTime createdAt,
      @JsonKey(name: 'updated_at') required final DateTime updatedAt,
      final int views,
      @JsonKey(name: 'is_featured') final bool isFeatured,
      final String status,
      final List<dynamic> reference,
      @JsonKey(name: 'comment_count') final int commentCount,
      @JsonKey(name: 'like_count') final int likeCount,
      @JsonKey(name: 'is_liked') final bool isLiked,
      @JsonKey(name: 'media_items') final List<PostMedia> mediaItems,
      @JsonKey(name: 'is_global') final bool isGlobal,
      @JsonKey(name: 'target_countries') final List<Country> targetCountries,
      @JsonKey(name: 'regional_priority')
      final int regionalPriority}) = _$PostImpl;

  factory _Post.fromJson(Map<String, dynamic> json) = _$PostImpl.fromJson;

  @override
  int get id;
  @override
  String get title;
  @override
  String get slug;
  @override
  String get content;
  @override
  User get author;
  @override
  Category get category;
  @override
  List<Tag> get tags;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  @override
  int get views;
  @override
  @JsonKey(name: 'is_featured')
  bool get isFeatured;
  @override
  String get status;
  @override
  List<dynamic> get reference;
  @override
  @JsonKey(name: 'comment_count')
  int get commentCount;
  @override
  @JsonKey(name: 'like_count')
  int get likeCount;
  @override
  @JsonKey(name: 'is_liked')
  bool get isLiked;
  @override
  @JsonKey(name: 'media_items')
  List<PostMedia> get mediaItems;
  @override // Regional targeting fields
  @JsonKey(name: 'is_global')
  bool get isGlobal;
  @override
  @JsonKey(name: 'target_countries')
  List<Country> get targetCountries;
  @override
  @JsonKey(name: 'regional_priority')
  int get regionalPriority;
  @override
  @JsonKey(ignore: true)
  _$$PostImplCopyWith<_$PostImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
