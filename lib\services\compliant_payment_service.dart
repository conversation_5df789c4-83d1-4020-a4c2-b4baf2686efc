import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'compliance_service.dart';
import 'api_service.dart';
import 'error_reporting_service.dart';

/// Compliant payment service that ensures app store compliance
/// Separates digital goods (must use store payments) from physical goods (can use external payments)
class CompliantPaymentService {
  static final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  static final ApiService _apiService = ApiService();

  // Product IDs for in-app purchases (configure these in app store consoles)
  static const Map<String, String> digitalProductIds = {
    'premium_monthly': 'trendy_premium_monthly',
    'premium_yearly': 'trendy_premium_yearly',
    'point_boost_small': 'trendy_points_100',
    'point_boost_medium': 'trendy_points_500',
    'point_boost_large': 'trendy_points_1000',
    'streak_protection': 'trendy_streak_protection',
    'virtual_item_badge': 'trendy_virtual_badge',
  };

  /// Initialize in-app purchase system
  static Future<bool> initialize() async {
    try {
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        print('In-app purchases not available on this device');
        return false;
      }
      return true;
    } catch (e) {
      print('Error initializing in-app purchases: $e');
      return false;
    }
  }

  /// Purchase premium subscription (DIGITAL GOOD - must use store payment)
  static Future<bool> purchasePremiumSubscription(
    BuildContext context, {
    required String plan, // 'monthly' or 'yearly'
  }) async {
    try {
      // Show compliance disclaimer
      final accepted = await ComplianceService.showPaymentDisclaimer(context);
      if (!accepted) return false;

      // Get product ID
      final productId = digitalProductIds['premium_$plan'];
      if (productId == null) {
        throw Exception('Invalid premium plan: $plan');
      }

      // Use store payment system (REQUIRED for digital goods)
      final success = await ComplianceService.purchaseDigitalContent(
        productId: productId,
        context: context,
      );

      if (success) {
        // Notify backend of successful purchase
        await _apiService.confirmSubscriptionPayment(productId, 'completed');

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Premium subscription activated!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      return success;
    } catch (e) {
      if (context.mounted) {
        ErrorReportingService.showPaymentErrorSnackbar(context, e,
            errorContext: 'premium_subscription');
      }
      return false;
    }
  }

  /// Purchase point boost (DIGITAL GOOD - must use store payment)
  static Future<bool> purchasePointBoost(
    BuildContext context, {
    required String packageId, // 'small', 'medium', 'large'
  }) async {
    try {
      // Show compliance disclaimer
      final accepted = await ComplianceService.showPaymentDisclaimer(context);
      if (!accepted) return false;

      // Get product ID
      final productId = digitalProductIds['point_boost_$packageId'];
      if (productId == null) {
        throw Exception('Invalid point boost package: $packageId');
      }

      // Use store payment system (REQUIRED for digital goods)
      final success = await ComplianceService.purchaseDigitalContent(
        productId: productId,
        context: context,
      );

      if (success) {
        // Notify backend of successful purchase
        await _apiService.confirmPointBoostPayment(
            productId, packageId, 'completed');

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Point boost purchased successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      return success;
    } catch (e) {
      if (context.mounted) {
        ErrorReportingService.showPaymentErrorSnackbar(context, e,
            errorContext: 'point_boost');
      }
      return false;
    }
  }

  /// Purchase virtual item (DIGITAL GOOD - must use store payment)
  static Future<bool> purchaseVirtualItem(
    BuildContext context, {
    required String itemId,
    required String itemType,
  }) async {
    try {
      // Show compliance disclaimer
      final accepted = await ComplianceService.showPaymentDisclaimer(context);
      if (!accepted) return false;

      // Get product ID based on item type
      final productId = digitalProductIds['virtual_item_$itemType'];
      if (productId == null) {
        throw Exception('Invalid virtual item type: $itemType');
      }

      // Use store payment system (REQUIRED for digital goods)
      final success = await ComplianceService.purchaseDigitalContent(
        productId: productId,
        context: context,
      );

      if (success) {
        // Notify backend of successful purchase
        await _apiService.purchaseVirtualItem(itemId);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Virtual item purchased!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      return success;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// Request PayPal payout (PHYSICAL GOOD - can use external payment)
  static Future<bool> requestPayPalPayout(
    BuildContext context, {
    required double amount,
    required String paypalEmail,
  }) async {
    try {
      // This is a payout TO the user, not a purchase FROM the user
      // This is allowed to use external payment systems
      final success = await ComplianceService.purchasePhysicalGood(
        amount: amount,
        description: 'PayPal payout to $paypalEmail',
        context: context,
      );

      if (success) {
        // Process the payout through backend
        await _apiService.requestPayout(amount);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payout request submitted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      return success;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payout request failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// Claim PayPal reward (PHYSICAL GOOD - this is a payout, not a purchase)
  static Future<bool> claimPayPalReward(
    BuildContext context, {
    required String rewardId,
    required String paypalEmail,
  }) async {
    try {
      // This is claiming earned rewards - not a purchase
      // Backend handles the actual PayPal transfer
      final result = await _apiService.claimPayPalReward(rewardId, paypalEmail);

      if (result['success'] == true) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Reward claimed! Payment will be processed within 24 hours.'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return true;
      } else {
        throw Exception(result['message'] ?? 'Failed to claim reward');
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to claim reward: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// Wallet deposit (DIGITAL GOOD - must use store payment for adding money to wallet)
  static Future<bool> depositToWallet(
    BuildContext context, {
    required double amount,
  }) async {
    try {
      // Show compliance disclaimer
      final accepted = await ComplianceService.showPaymentDisclaimer(context);
      if (!accepted) return false;

      // Wallet deposits are digital goods - must use store payment
      // Create a dynamic product for wallet deposit
      final productId = 'trendy_wallet_deposit_${amount.toInt()}';

      final success = await ComplianceService.purchaseDigitalContent(
        productId: productId,
        context: context,
      );

      if (success) {
        // Notify backend to add money to wallet
        await _apiService.createWalletDeposit(amount, 'in_app_purchase');

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('\$${amount.toStringAsFixed(2)} added to your wallet!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      return success;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deposit failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  /// Get available products for purchase
  static Future<List<ProductDetails>> getAvailableProducts() async {
    try {
      final Set<String> productIds = digitalProductIds.values.toSet();
      final ProductDetailsResponse response =
          await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        print('Error fetching products: ${response.error}');
        return [];
      }

      return response.productDetails;
    } catch (e) {
      print('Error getting available products: $e');
      return [];
    }
  }

  /// Check if a product is a digital good (requires store payment)
  static bool isDigitalGood(String productType) {
    const digitalGoodTypes = [
      'premium_subscription',
      'point_boost',
      'virtual_item',
      'streak_protection',
      'wallet_deposit',
    ];

    return digitalGoodTypes.any((type) => productType.contains(type));
  }

  /// Check if a product is a physical good (can use external payment)
  static bool isPhysicalGood(String productType) {
    const physicalGoodTypes = [
      'payout',
      'withdrawal',
      'reward_claim',
      'physical_merchandise',
    ];

    return physicalGoodTypes.any((type) => productType.contains(type));
  }

  /// Show payment method selection dialog (compliance-aware)
  static Future<String?> showPaymentMethodDialog(
    BuildContext context, {
    required String productType,
    required double amount,
  }) async {
    if (isDigitalGood(productType)) {
      // Digital goods must use store payment
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Payment Method'),
          content: const Text(
            'Digital content purchases use your device\'s payment system (Apple Pay, Google Pay, etc.) for security and compliance.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
      return 'store_payment';
    } else if (isPhysicalGood(productType)) {
      // Physical goods can offer choice
      return await showDialog<String>(
        context: context,
        builder: (context) => SimpleDialog(
          title: const Text('Select Payment Method'),
          children: [
            SimpleDialogOption(
              onPressed: () => Navigator.of(context).pop('paypal'),
              child: const ListTile(
                leading: Icon(Icons.payment),
                title: Text('PayPal'),
                subtitle: Text('For payouts and withdrawals'),
              ),
            ),
            SimpleDialogOption(
              onPressed: () => Navigator.of(context).pop('wallet'),
              child: const ListTile(
                leading: Icon(Icons.account_balance_wallet),
                title: Text('Wallet Balance'),
                subtitle: Text('Use your app wallet'),
              ),
            ),
          ],
        ),
      );
    }

    return null;
  }
}
