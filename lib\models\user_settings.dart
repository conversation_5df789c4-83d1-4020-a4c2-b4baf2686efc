import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_settings.freezed.dart';
part 'user_settings.g.dart';

@freezed
class UserSettings with _$UserSettings {
  const UserSettings._();

  const factory UserSettings({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email_notifications') @Default(true) bool emailNotifications,
    @<PERSON>son<PERSON>ey(name: 'push_notifications') @Default(true) bool pushNotifications,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'comment_notifications') @Default(true) bool commentNotifications,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'like_notifications') @Default(true) bool likeNotifications,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'follow_notifications') @Default(true) bool followNotifications,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_visibility') @Default('public') String profileVisibility,
    @<PERSON>son<PERSON>ey(name: 'show_email') @Default(false) bool showEmail,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'show_phone') @Default(false) bool showPhone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'content_language') @Default('en') String contentLanguage,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'posts_per_page') @Default(10) int postsPerPage,
    @<PERSON>son<PERSON>ey(name: 'auto_play_videos') @Default(true) bool autoPlayVideos,
    @Default('auto') String theme,
  }) = _UserSettings;

  factory UserSettings.fromJson(Map<String, dynamic> json) => _$UserSettingsFromJson(json);

  String get profileVisibilityDisplayName {
    switch (profileVisibility) {
      case 'public':
        return 'Public';
      case 'private':
        return 'Private';
      case 'friends':
        return 'Friends Only';
      default:
        return 'Public';
    }
  }

  String get themeDisplayName {
    switch (theme) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'auto':
        return 'Auto';
      default:
        return 'Auto';
    }
  }
}
