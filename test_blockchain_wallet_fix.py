#!/usr/bin/env python3
"""
Test script to verify the blockchain wallet creation fix
"""

import requests
import json

# API Configuration
API_BASE = "http://127.0.0.1:8000"

def test_blockchain_wallet_flow():
    """Test the complete blockchain wallet flow"""
    print("🧪 Testing Blockchain Wallet Creation Fix")
    print("=" * 50)
    
    # Step 1: Login
    print("1. 🔐 Logging in...")
    login_data = {
        'email_or_username': '<EMAIL>',
        'password': 'admin123'
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/v1/accounts/login/", json=login_data)
        if response.status_code == 200:
            token = response.json()['token']
            print(f"   ✅ Login successful! Token: {token[:20]}...")
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return
    
    headers = {'Authorization': f'Token {token}'}
    
    # Step 2: Try to get existing wallet
    print("\n2. 🔍 Checking for existing wallet...")
    try:
        response = requests.get(f"{API_BASE}/api/v1/blockchain/wallet/", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                wallet = data.get('wallet')
                print(f"   ✅ Existing wallet found: {wallet.get('address')}")
                print(f"   📍 Network: {wallet.get('network')}")
                existing_wallet = True
            else:
                print(f"   ℹ️ No existing wallet found")
                existing_wallet = False
        else:
            print(f"   ⚠️ Error getting wallet: {response.status_code}")
            existing_wallet = False
            
    except Exception as e:
        print(f"   ❌ Error checking wallet: {e}")
        existing_wallet = False
    
    # Step 3: Try to create wallet (should handle existing wallet gracefully)
    print("\n3. 🔨 Testing wallet creation...")
    try:
        response = requests.post(f"{API_BASE}/api/v1/blockchain/wallet/create/", headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                wallet = data.get('wallet')
                print(f"   ✅ New wallet created: {wallet.get('address')}")
            else:
                print(f"   ⚠️ Wallet creation failed: {data.get('message')}")
        elif response.status_code == 400:
            data = response.json()
            if 'already exists' in data.get('message', '').lower():
                print(f"   ✅ Wallet already exists (expected): {data.get('message')}")
                print(f"   💡 Flutter app should handle this by fetching existing wallet")
            else:
                print(f"   ❌ Unexpected 400 error: {data.get('message')}")
        else:
            print(f"   ❌ Unexpected error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error creating wallet: {e}")
    
    # Step 4: Verify wallet access after creation attempt
    print("\n4. ✅ Final wallet verification...")
    try:
        response = requests.get(f"{API_BASE}/api/v1/blockchain/wallet/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                wallet = data.get('wallet')
                print(f"   ✅ Wallet accessible: {wallet.get('address')}")
                print(f"   📍 Network: {wallet.get('network')}")
                print(f"   🔑 Primary: {wallet.get('is_primary')}")
            else:
                print(f"   ❌ Wallet not accessible: {data}")
        else:
            print(f"   ❌ Error accessing wallet: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error verifying wallet: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("✅ The Flutter app should now handle wallet creation properly")
    print("✅ If wallet exists, it should fetch the existing one")
    print("✅ If wallet doesn't exist, it should create a new one")
    print("✅ No more 'Failed to create blockchain wallet' errors")

if __name__ == "__main__":
    test_blockchain_wallet_flow()
