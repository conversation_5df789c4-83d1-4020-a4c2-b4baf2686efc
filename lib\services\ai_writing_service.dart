import 'package:dio/dio.dart';
import '../config/api_config.dart';
import '../models/ai_writing_models.dart';
import 'auth_service.dart';

// Legacy models for backward compatibility
class WritingSuggestion {
  final SuggestionType type;
  final String title;
  final String description;
  final String suggestion;

  WritingSuggestion({
    required this.type,
    required this.title,
    required this.description,
    required this.suggestion,
  });
}

enum SuggestionType { grammar, style, structure, engagement, expansion, seo }

class SEOSuggestion {
  final String title;
  final String description;
  final SEOPriority priority;

  SEOSuggestion({
    required this.title,
    required this.description,
    required this.priority,
  });
}

enum SEOPriority { low, medium, high }

class AIWritingService {
  static final AIWritingService _instance = AIWritingService._internal();
  factory AIWritingService() => _instance;
  AIWritingService._internal();

  late final Dio _dio;
  bool _isInitialized = false;

  void initialize() {
    if (_isInitialized) return; // Prevent multiple initializations

    _dio = Dio(
      BaseOptions(
        baseUrl: ApiConfig.baseUrl,
        connectTimeout: ApiConfig.connectTimeout,
        receiveTimeout: ApiConfig.receiveTimeout,
        sendTimeout: ApiConfig.sendTimeout,
      ),
    );
    _isInitialized = true;
  }

  // Get authentication options
  Future<Options> _getAuthOptions() async {
    final token = await AuthService.getToken();
    return Options(
      headers: {
        'Authorization': 'Token $token',
        'Content-Type': 'application/json',
      },
    );
  }

  // Get user AI writing preferences
  Future<AIWritingPreferences> getUserPreferences() async {
    try {
      final response = await _dio.get(
        '/api/v1/ai-writing/preferences/user_preferences/',
        options: await _getAuthOptions(),
      );
      return AIWritingPreferences.fromJson(response.data);
    } catch (e) {
      // Return default preferences if API fails
      return const AIWritingPreferences();
    }
  }

  // Save user AI writing preferences
  Future<AIWritingPreferences> saveUserPreferences(
    AIWritingPreferences preferences,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/preferences/user_preferences/',
        data: preferences.toJson(),
        options: await _getAuthOptions(),
      );
      return AIWritingPreferences.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to save preferences: $e');
    }
  }

  // Generate content ideas
  Future<List<String>> generateContentIdeas(
    String topic, {
    int count = 5,
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/generate/ideas/',
        data: ContentIdeaRequest(topic: topic, count: count).toJson(),
        options: await _getAuthOptions(),
      );
      return List<String>.from(response.data['ideas']);
    } catch (e) {
      return _generateMockIdeas(topic, count);
    }
  }

  // Generate content outline
  Future<ContentOutline?> generateContentOutline(String title) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/generate/outline/',
        data: ContentOutlineRequest(title: title).toJson(),
        options: await _getAuthOptions(),
      );
      return ContentOutline.fromJson(response.data);
    } catch (e) {
      return _generateMockOutline(title);
    }
  }

  // Improve grammar and style
  Future<GrammarImprovement?> improveGrammar(String text) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/improve/grammar/',
        data: GrammarImprovementRequest(text: text).toJson(),
        options: await _getAuthOptions(),
      );
      return GrammarImprovement.fromJson(response.data);
    } catch (e) {
      return _generateMockImprovement(text);
    }
  }

  // Generate SEO suggestions
  Future<SEOSuggestions?> generateSEOSuggestions(
    String content, {
    String? title,
  }) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/generate/seo/',
        data: SEOSuggestionsRequest(content: content, title: title).toJson(),
        options: await _getAuthOptions(),
      );
      return SEOSuggestions.fromJson(response.data);
    } catch (e) {
      return _generateMockSEOSuggestions(content, title);
    }
  }

  // Complete text
  Future<String> completeText(String partialText, {String? context}) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/complete/text/',
        data: TextCompletionRequest(
          partialText: partialText,
          context: context,
        ).toJson(),
        options: await _getAuthOptions(),
      );
      return response.data['completed_text'];
    } catch (e) {
      return _generateMockCompletion(partialText);
    }
  }

  // Analyze readability
  Future<ReadabilityAnalysis?> analyzeReadability(String content) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/analyze/readability/',
        data: ReadabilityAnalysisRequest(content: content).toJson(),
        options: await _getAuthOptions(),
      );
      return ReadabilityAnalysis.fromJson(response.data);
    } catch (e) {
      return _generateMockReadabilityAnalysis(content);
    }
  }

  // Create writing session
  Future<AIWritingSession?> createWritingSession({String? postId}) async {
    try {
      final response = await _dio.post(
        '/api/v1/ai-writing/session/create/',
        data: {'post_id': postId},
        options: await _getAuthOptions(),
      );
      return AIWritingSession.fromJson(response.data);
    } catch (e) {
      return null;
    }
  }

  // Get usage analytics
  Future<List<AIUsageAnalytics>> getUsageAnalytics() async {
    try {
      final response = await _dio.get(
        '/api/v1/ai-writing/analytics/usage/',
        options: await _getAuthOptions(),
      );
      return (response.data as List)
          .map((item) => AIUsageAnalytics.fromJson(item))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Get writing suggestions based on current content (legacy method)
  Future<List<WritingSuggestion>> getWritingSuggestions(String content) async {
    try {
      // Use grammar improvement as suggestions
      final improvement = await improveGrammar(content);
      if (improvement != null) {
        return improvement.changes
            .map(
              (change) => WritingSuggestion(
                type: SuggestionType.grammar,
                title: 'Grammar Improvement',
                description: change.reason,
                suggestion:
                    'Change "${change.original}" to "${change.improved}"',
              ),
            )
            .toList();
      }
      return _generateMockSuggestions(content);
    } catch (e) {
      return _generateMockSuggestions(content);
    }
  }

  // Improve text (legacy method)
  Future<String> improveText(String text) async {
    try {
      final improvement = await improveGrammar(text);
      return improvement?.improvedText ?? text;
    } catch (e) {
      return text;
    }
  }

  // Generate SEO suggestions (legacy method)
  Future<List<SEOSuggestion>> getSEOSuggestions(String content) async {
    try {
      final seoSuggestions = await generateSEOSuggestions(content);
      if (seoSuggestions != null) {
        // Convert SEOSuggestions to List<SEOSuggestion>
        final suggestions = <SEOSuggestion>[];

        // Add title suggestions
        for (final title in seoSuggestions.titleSuggestions) {
          suggestions.add(
            SEOSuggestion(
              title: 'Title Suggestion',
              description: title,
              priority: SEOPriority.high,
            ),
          );
        }

        // Add content suggestions
        for (final contentSuggestion in seoSuggestions.contentSuggestions) {
          suggestions.add(
            SEOSuggestion(
              title: 'Content Improvement',
              description: contentSuggestion,
              priority: SEOPriority.medium,
            ),
          );
        }

        return suggestions;
      }
      return [];
    } catch (e) {
      print('Error getting SEO suggestions: $e');
      return [];
    }
  }

  // Mock implementations (replace with real AI service calls)
  List<WritingSuggestion> _generateMockSuggestions(String content) {
    final suggestions = <WritingSuggestion>[];

    if (content.length < 100) {
      suggestions.add(
        WritingSuggestion(
          type: SuggestionType.expansion,
          title: 'Expand your introduction',
          description: 'Consider adding more context to engage your readers',
          suggestion:
              'Try adding a compelling hook or interesting statistic to draw readers in.',
        ),
      );
    }

    if (!content.contains('?') && content.length > 200) {
      suggestions.add(
        WritingSuggestion(
          type: SuggestionType.engagement,
          title: 'Add a question',
          description: 'Questions can increase reader engagement',
          suggestion:
              'Consider adding a thought-provoking question to encourage reader interaction.',
        ),
      );
    }

    if (content.split('.').length < 3) {
      suggestions.add(
        WritingSuggestion(
          type: SuggestionType.structure,
          title: 'Break up long sentences',
          description: 'Shorter sentences improve readability',
          suggestion:
              'Try breaking longer sentences into shorter, more digestible ones.',
        ),
      );
    }

    return suggestions;
  }

  String _generateMockCompletion(String partialText) {
    final completions = [
      ' and this opens up new possibilities for innovation.',
      ' which demonstrates the importance of user experience.',
      ' leading to better outcomes for everyone involved.',
      ' while maintaining the highest standards of quality.',
      ' creating opportunities for meaningful collaboration.',
    ];

    return completions[partialText.length % completions.length];
  }

  // Mock methods for fallback
  List<String> _generateMockIdeas(String topic, int count) {
    final ideas = [
      'The Ultimate Guide to $topic',
      '10 Common Mistakes in $topic (And How to Avoid Them)',
      'Why $topic Matters More Than Ever in 2025',
      'A Beginner\'s Journey into $topic',
      'The Future of $topic: Trends and Predictions',
      'How to Master $topic in 30 Days',
      '$topic: Best Practices and Expert Tips',
      'The Complete $topic Checklist',
      'Understanding $topic: A Comprehensive Overview',
      'Advanced $topic Techniques for Professionals',
    ];
    return ideas.take(count).toList();
  }

  ContentOutline _generateMockOutline(String title) {
    return ContentOutline(
      introduction: [
        'Hook the reader with an engaging opening',
        'Introduce the main topic: $title',
        'Preview the key points to be covered',
      ],
      mainSections: [
        OutlineSection(
          title: 'Background and Context',
          points: [
            'Historical perspective',
            'Current situation',
            'Key challenges',
          ],
        ),
        OutlineSection(
          title: 'Main Concepts',
          points: [
            'Core principles',
            'Important definitions',
            'Key frameworks',
          ],
        ),
        OutlineSection(
          title: 'Practical Applications',
          points: [
            'Real-world examples',
            'Implementation strategies',
            'Best practices',
          ],
        ),
      ],
      conclusion: [
        'Summarize the key takeaways',
        'Provide actionable next steps',
        'Encourage reader engagement',
      ],
      estimatedWordCount: 800,
    );
  }

  GrammarImprovement _generateMockImprovement(String text) {
    return GrammarImprovement(
      improvedText: text, // In real implementation, this would be improved
      changes: [
        TextChange(
          original: 'This is a example',
          improved: 'This is an example',
          reason: 'Use "an" before words starting with vowel sounds',
        ),
      ],
      readabilityScore: 7.5,
    );
  }

  SEOSuggestions _generateMockSEOSuggestions(String content, String? title) {
    return SEOSuggestions(
      titleSuggestions: [
        '${title ?? 'Your Topic'} - Complete Guide 2025',
        'Ultimate ${title ?? 'Guide'}: Everything You Need to Know',
        'Master ${title ?? 'This Topic'} with Expert Tips',
      ],
      metaDescription:
          'Learn everything about ${title?.toLowerCase() ?? 'this topic'} with our comprehensive guide. Expert tips, practical advice, and actionable insights.',
      keywords: ['guide', 'tips', 'tutorial', 'how-to', '2025'],
      contentSuggestions: [
        'Add more subheadings to improve structure',
        'Include relevant keywords naturally',
        'Add internal and external links',
        'Use bullet points for better readability',
      ],
      readabilityIssues: [
        'Consider shorter paragraphs',
        'Use more transition words',
      ],
    );
  }

  ReadabilityAnalysis _generateMockReadabilityAnalysis(String content) {
    final wordCount = content.split(' ').length;
    final sentenceCount = content
        .split('.')
        .length
        .clamp(1, double.infinity)
        .toInt();
    final paragraphCount = content
        .split('\n\n')
        .length
        .clamp(1, double.infinity)
        .toInt();

    return ReadabilityAnalysis(
      wordCount: wordCount,
      sentenceCount: sentenceCount,
      paragraphCount: paragraphCount,
      avgWordsPerSentence: wordCount / sentenceCount,
      avgSentencesPerParagraph: sentenceCount / paragraphCount,
      readabilityScore: 75.0,
      readingLevel: 'Standard',
      estimatedReadingTime: (wordCount / 200).ceil(),
      suggestions: [
        'Consider breaking up long sentences',
        'Add more paragraph breaks',
        'Use simpler vocabulary where possible',
      ],
    );
  }
}
