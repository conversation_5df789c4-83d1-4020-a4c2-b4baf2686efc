import 'dart:async';
import 'package:flutter/foundation.dart';
import 'api_service.dart';
import 'error_reporting_service.dart';

/// Service for tracking payment status and handling payment-related operations
class PaymentStatusService {
  static final PaymentStatusService _instance = PaymentStatusService._internal();
  factory PaymentStatusService() => _instance;
  PaymentStatusService._internal();

  final ApiService _apiService = ApiService();
  final Map<String, StreamController<PaymentStatus>> _statusControllers = {};
  Timer? _statusCheckTimer;

  /// Start tracking payment status for a transaction
  Stream<PaymentStatus> trackPaymentStatus(String transactionId) {
    if (_statusControllers.containsKey(transactionId)) {
      return _statusControllers[transactionId]!.stream;
    }

    final controller = StreamController<PaymentStatus>.broadcast();
    _statusControllers[transactionId] = controller;

    // Start periodic status checks
    _startStatusChecking(transactionId);

    return controller.stream;
  }

  /// Stop tracking payment status for a transaction
  void stopTracking(String transactionId) {
    final controller = _statusControllers.remove(transactionId);
    controller?.close();

    // Stop timer if no more transactions are being tracked
    if (_statusControllers.isEmpty) {
      _statusCheckTimer?.cancel();
      _statusCheckTimer = null;
    }
  }

  /// Start periodic status checking
  void _startStatusChecking(String transactionId) {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkPaymentStatus(transactionId);
    });

    // Initial check
    _checkPaymentStatus(transactionId);
  }

  /// Check payment status from backend
  Future<void> _checkPaymentStatus(String transactionId) async {
    try {
      final response = await _apiService.getPaymentStatus(transactionId);
      
      if (response['success'] == true && response['transaction'] != null) {
        final transactionData = response['transaction'];
        final status = PaymentStatus.fromJson(transactionData);
        
        final controller = _statusControllers[transactionId];
        if (controller != null && !controller.isClosed) {
          controller.add(status);
          
          // Stop tracking if payment is completed or failed
          if (status.isCompleted || status.isFailed) {
            stopTracking(transactionId);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking payment status: $e');
      }
      
      final controller = _statusControllers[transactionId];
      if (controller != null && !controller.isClosed) {
        controller.addError(e);
      }
    }
  }

  /// Validate payment before processing
  static Future<PaymentValidationResult> validatePayment({
    required double amount,
    required String paymentMethod,
    String? currency,
  }) async {
    try {
      // Basic validation
      if (amount <= 0) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Amount must be greater than zero',
        );
      }

      if (amount > 10000) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Amount exceeds maximum limit of \$10,000',
        );
      }

      // Payment method validation
      if (paymentMethod.isEmpty) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Payment method is required',
        );
      }

      // Currency validation
      final validCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];
      final paymentCurrency = currency ?? 'USD';
      if (!validCurrencies.contains(paymentCurrency)) {
        return PaymentValidationResult(
          isValid: false,
          error: 'Unsupported currency: $paymentCurrency',
        );
      }

      return PaymentValidationResult(isValid: true);
    } catch (e) {
      return PaymentValidationResult(
        isValid: false,
        error: 'Payment validation failed: ${e.toString()}',
      );
    }
  }

  /// Get payment history for user
  static Future<List<PaymentHistoryItem>> getPaymentHistory() async {
    try {
      final apiService = ApiService();
      final response = await apiService.getPaymentHistory();
      
      if (response['success'] == true && response['data'] != null) {
        final historyData = response['data']['transactions'] as List;
        return historyData
            .map((item) => PaymentHistoryItem.fromJson(item))
            .toList();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching payment history: $e');
      }
      return [];
    }
  }

  /// Dispose of all resources
  void dispose() {
    _statusCheckTimer?.cancel();
    for (final controller in _statusControllers.values) {
      controller.close();
    }
    _statusControllers.clear();
  }
}

/// Payment status model
class PaymentStatus {
  final String transactionId;
  final String status;
  final double amount;
  final String currency;
  final String paymentMethod;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? errorMessage;

  PaymentStatus({
    required this.transactionId,
    required this.status,
    required this.amount,
    required this.currency,
    required this.paymentMethod,
    required this.createdAt,
    this.completedAt,
    this.errorMessage,
  });

  bool get isPending => status == 'pending';
  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed' || status == 'canceled';
  bool get isProcessing => status == 'processing';

  factory PaymentStatus.fromJson(Map<String, dynamic> json) {
    return PaymentStatus(
      transactionId: json['id']?.toString() ?? '',
      status: json['status']?.toString() ?? 'unknown',
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      currency: json['currency']?.toString() ?? 'USD',
      paymentMethod: json['payment_method']?.toString() ?? '',
      createdAt: DateTime.tryParse(json['created_at']?.toString() ?? '') ?? DateTime.now(),
      completedAt: json['completed_at'] != null 
          ? DateTime.tryParse(json['completed_at'].toString())
          : null,
      errorMessage: json['error_message']?.toString(),
    );
  }
}

/// Payment validation result
class PaymentValidationResult {
  final bool isValid;
  final String? error;

  PaymentValidationResult({
    required this.isValid,
    this.error,
  });
}

/// Payment history item
class PaymentHistoryItem {
  final String id;
  final String type;
  final double amount;
  final String currency;
  final String status;
  final String description;
  final DateTime createdAt;

  PaymentHistoryItem({
    required this.id,
    required this.type,
    required this.amount,
    required this.currency,
    required this.status,
    required this.description,
    required this.createdAt,
  });

  factory PaymentHistoryItem.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryItem(
      id: json['id']?.toString() ?? '',
      type: json['transaction_type']?.toString() ?? '',
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      currency: json['currency']?.toString() ?? 'USD',
      status: json['status']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      createdAt: DateTime.tryParse(json['created_at']?.toString() ?? '') ?? DateTime.now(),
    );
  }
}
