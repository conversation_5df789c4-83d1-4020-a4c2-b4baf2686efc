{% extends 'blog/base.html' %}
{% load crispy_forms_tags %}

{% block title %}Home - Trendy Blog{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section py-7 mb-7">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 order-lg-1 order-2">
                <h1 class="display-3 fw-bold mb-4 gradient-text animate__animated animate__fadeInUp" data-scroll>
                    Stay Curious, Stay Inspired
                </h1>
                <p class="lead text-muted mb-4 fs-5 animate__animated animate__fadeInUp animate__delay-1s" data-scroll>
                    Discover fresh perspectives and thought-provoking ideas from our vibrant community.
                </p>
                <form method="get" action="{% url 'home' %}" class="search-form animate__animated animate__fadeInUp animate__delay-2s" data-scroll>
                    <div class="input-group input-group-lg shadow-lg rounded-pill overflow-hidden">
                        <input type="text" name="q" class="form-control border-0 ps-4" 
                               placeholder="Search articles..." value="{{ query }}">
                        <button type="submit" class="btn btn-gradient px-4">
                            <i class="fas fa-search fa-lg"></i>
                        </button>
                    </div>
                </form>
                <div class="trending-tags mt-4 animate__animated animate__fadeInUp animate__delay-3s" data-scroll>
                    <span class="text-muted me-2">Trending:</span>
                    {% for tag in tags|slice:":3" %}
                    <a href="{% url 'home' %}?tag={{ tag.slug }}" class="btn btn-sm btn-tag rounded-pill me-2">
                        #{{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            <div class="col-lg-6 order-lg-2 order-1 mb-lg-0 mb-4">
                <div class="hero-carousel position-relative" data-scroll>
                    {% comment %} <div class="gradient-overlay"></div> {% endcomment %}
                    <div class="hero-images">
                        {% for hero_image in hero_images %}
                        <div class="hero-image {% if forloop.first %}active{% endif %}" 
                            data-interval="{{ hero_image.display_duration|default:5000 }}">
                            <img src="{% if hero_image.image %}{{ hero_image.image.url }}{% else %}{{ hero_image.image_url }}{% endif %}" 
                                alt="Hero Image" class="img-fluid">
                        </div>
                    {% empty %}
                        <div class="hero-image active">
                            <img src="https://images.unsplash.com/photo-1499750310107-5fef28a66643?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" 
                                alt="Default Hero Image" class="img-fluid">
                        </div>
                    {% endfor %}

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Posts -->
<section class="featured-posts mb-7">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2 class="section-title fw-bold display-5">Featured Stories</h2>
            <a href="{% url 'home' %}" class="btn btn-link text-decoration-none">
                Explore All <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
        <div class="row g-4">
            {% for post in featured_posts %}
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-hover">
                    {% if post.media_items.exists %}
                    <div class="card-image-container">
                        {% with first_media=post.media_items.first %}
                        <img src="{% if first_media.image %}{{ first_media.image.url }}{% else %}{{ first_media.image_url }}{% endif %}"
                             class="card-img-top"
                             alt="{{ post.title }}">
                        {% endwith %}
                        <div class="card-badge">
                            <span class="badge bg-white text-dark"><i class="fas fa-fire me-2"></i>Featured</span>
                        </div>
                    </div>
                    {% endif %}
                    <div class="card-body">
                        <div class="d-flex align-items-center gap-2 mb-3">
                            <a href="{% url 'home' %}?category={{ post.category.slug }}" 
                               class="badge bg-gradient text-decoration-none">{{ post.category.name }}</a>
                            <small class="text-muted">{{ post.created_at|timesince }} ago</small>
                        </div>
                        <h5 class="card-title fw-bold mb-2">{{ post.title }}</h5>
                        <p class="card-text text-muted mb-3">{{ post.content|striptags|truncatewords:20 }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="author-info d-flex align-items-center">
                                <div class="avatar me-2">
                                    <img src="{{ post.author.profile.image.url }}" 
                                         alt="{{ post.author.username }}" 
                                         class="rounded-circle"
                                         width="36"
                                         height="36">
                                </div>
                                <div>
                                    <small class="d-block fw-medium">{{ post.author.get_full_name|default:post.author.username }}</small>
                                    <small class="text-muted">{{ post.author.profile.title|default:"Writer" }}</small>
                                </div>
                            </div>
                            <a href="{% url 'post-detail' slug=post.slug %}" class="btn btn-icon">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<div class="container">
    <div class="row g-5">
        <!-- Main Content -->
        <main class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-5">
                <h2 class="section-title fw-bold display-5">Latest Updates</h2>
                {% if user.is_authenticated %}
                <a href="{% url 'post-create' %}" class="btn btn-gradient rounded-pill">
                    <i class="fas fa-plus me-2"></i>New Story
                </a>
                {% endif %}
            </div>

            <!-- Posts Grid -->
            <div class="row g-4">
                {% for post in posts %}
                <div class="col-md-6">
                    <article class="card h-100 border-0 shadow-sm hover-lift">
                        {% if post.media_items.exists %}
                        {% with first_media=post.media_items.first %}
                        <img src="{% if first_media.image %}{{ first_media.image.url }}{% else %}{{ first_media.image_url }}{% endif %}"
                             class="card-img-top"
                             alt="{{ post.title }}"
                             style="height: 180px; object-fit: cover;">
                        {% endwith %}
                        {% endif %}
                        <div class="card-body">
                            <div class="d-flex align-items-center gap-2 mb-2">
                                <span class="badge bg-gradient">{{ post.category.name }}</span>
                                <small class="text-muted">{{ post.created_at|date:"M j, Y" }}</small>
                            </div>
                            <h5 class="card-title fw-bold mb-2">{{ post.title }}</h5>
                            <p class="card-text text-muted mb-3">{{ post.content|truncatewords:15 }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center gap-2">
                                    <div class="avatar-xs">
                                        {% if post.author.profile.image %}
                                            <img src="{{ post.author.profile.image.url }}" 
                                                 alt="{{ post.author.username }}" 
                                                 class="rounded-circle"
                                                 width="32"
                                                 height="32">
                                        {% else %}
                                            <div class="avatar-initials bg-gradient text-white rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 32px; height: 32px;">
                                                {{ post.author.get_full_name|default:post.author.username|slice:":2"|upper }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">{{ post.author.get_full_name|default:post.author.username }}</small>
                                </div>
                                <a href="{% url 'post-detail' slug=post.slug %}" class="btn btn-link p-0">
                                    Read More <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </article>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="empty-state text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <p class="text-muted mb-0">No posts found.</p>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if posts.has_other_pages %}
            <nav class="mt-6">
                <ul class="pagination justify-content-center">
                    {% if posts.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ posts.previous_page_number }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in posts.paginator.page_range %}
                    <li class="page-item {% if posts.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endfor %}
                    
                    {% if posts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ posts.next_page_number }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </main>

        <!-- Sidebar -->
        <aside class="col-lg-4">
            <!-- Categories -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="fw-bold mb-3">Explore Topics</h5>
                    <div class="category-cloud">
                        {% for category in categories %}
                        <a href="{% url 'home' %}?category={{ category.slug }}" 
                           class="category-pill {% if category_slug == category.slug %}active{% endif %}">
                            {{ category.name }}
                            <span class="badge bg-white text-dark ms-2">{{ category.post_set.count }}</span>
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Newsletter -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body newsletter-card">
                    <div class="newsletter-icon mb-4">
                        <i class="fas fa-envelope-open-text fa-3x text-gradient"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Join Our Newsletter</h5>
                    <p class="text-muted mb-4">Get weekly updates and curated resources directly to your inbox.</p>
                    <form method="post" action="{% url 'newsletter-subscribe' %}" class="newsletter-form">
                        {% csrf_token %}
                        <div class="input-group shadow-sm rounded-pill overflow-hidden">
                            <input type="email" name="email" class="form-control border-0 ps-4" 
                                   placeholder="Your email address" required>
                            <button type="submit" class="btn btn-gradient px-4">
                                Join
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Featured Author -->
            {% if featured_author %}
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="fw-bold mb-4">Featured Writer</h5>
                    <div class="author-card text-center">
                        <div class="avatar mb-3">
                            <img src="{{ featured_author.profile.image.url }}" 
                                alt="{{ featured_author.username }}" 
                                class="rounded-circle shadow"
                                width="80"
                                height="80">
                        </div>
                        <h6 class="fw-bold mb-1">{{ featured_author.get_full_name }}</h6>
                        <p class="text-muted small mb-3">{{ featured_author.profile.title|default:"Writer" }}</p>
                        <p class="text-muted small mb-3">{{ featured_author.profile.bio|truncatewords:15 }}</p>
                        <a href="{% url 'author-posts' featured_author.username %}" 
                        class="btn btn-sm btn-outline-gradient rounded-pill">
                            View Profile
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </aside>
    </div>
</div>

<style>

    .hero-carousel {
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
        min-height: 400px;
    }

    .hero-images {
        position: relative;
        padding-top: 75%; /* 4:3 aspect ratio */
    }

    .hero-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 1.5s ease-in-out;
        background-size: cover;
        background-position: center;
    }

    .hero-image.active {
        opacity: 1;
    }

    {% comment %} .gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.1) 100%);
        z-index: 1;
    } {% endcomment %}
    .gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2); /* Reduce opacity (0.2) or remove this line */
        z-index: 1;
    }
    

    /* Fallback image styling */
    .hero-image[data-image] {
        background-size: cover;
        background-position: center;
    }

    [data-scroll] {
        transition: all 1s ease;
        opacity: 0;
        transform: translateY(30px);
    }

    [data-scroll].scrolled {
        opacity: 1;
        transform: translateY(0);
    }
    :root {
        --gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%);
        --primary-color: #6366f1;
        --text-color: #1e293b;
    }

    .hero-section {
        background: linear-gradient(150deg, #f8f9fa 0%, #ffffff 100%);
    }

    .gradient-text {
        background-image: var(--gradient);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .btn-gradient {
        background-image: var(--gradient);
        color: white !important;
        border: none;
        transition: all 0.3s ease;
    }

    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px -10px rgba(99, 102, 241, 0.5);
    }

    .shadow-hover {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .shadow-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .category-pill {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        background: #f1f5f9;
        border-radius: 2rem;
        color: var(--text-color);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .category-pill.active, .category-pill:hover {
        background: var(--gradient);
        color: white !important;
    }

    .category-pill.active .badge, .category-pill:hover .badge {
        background: rgba(255, 255, 255, 0.2) !important;
        color: white !important;
    }

    .post-card {
        transition: all 0.3s ease;
        border-radius: 1rem;
    }

    .post-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .newsletter-card {
        background: var(--gradient);
        color: white;
        border-radius: 1rem;
        overflow: hidden;
        position: relative;
    }

    .newsletter-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
        pointer-events: none;
    }

    .pagination .page-item.active .page-link {
        background: var(--gradient);
        border-color: transparent;
        color: white;
    }

    .avatar img {
        object-fit: cover;
    }

    @media (max-width: 768px) {
        .hero-section {
            padding-top: 4rem !important;
            padding-bottom: 4rem !important;
        }

        .display-3 {
            font-size: 2.5rem;
        }
    }
</style>

<script>
    // Image rotation
    document.addEventListener('DOMContentLoaded', function() {
        const images = document.querySelectorAll('.hero-image');
        let current = 0;

        function nextImage() {
            images[current].classList.remove('active');
            current = (current + 1) % images.length;
            images[current].classList.add('active');
            
            const interval = parseInt(images[current].dataset.interval) || 5000;
            setTimeout(nextImage, interval);
        }

        if(images.length > 1) {
            const interval = parseInt(images[0].dataset.interval) || 5000;
            setTimeout(nextImage, interval);
        }

        // Scroll animations
        const scrollElements = document.querySelectorAll('[data-scroll]');
        const elementInView = (el) => {
            const elementTop = el.getBoundingClientRect().top;
            return elementTop <= (window.innerHeight || document.documentElement.clientHeight);
        };

        const displayScrollElement = (element) => {
            element.classList.add('scrolled');
        };

        const handleScrollAnimation = () => {
            scrollElements.forEach((el) => {
                if (elementInView(el)) {
                    displayScrollElement(el);
                }
            });
        };

        window.addEventListener('scroll', handleScrollAnimation);
        handleScrollAnimation(); // Initial check

        // Track views when clicking on post links
        document.querySelectorAll('a[href*="/posts/"]').forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                const postSlugMatch = href.match(/\/posts\/([^\/]+)\//);
                if (postSlugMatch) {
                    const postSlug = postSlugMatch[1];
                    // Get post ID from data attribute or make API call to get post by slug
                    // For now, we'll track the view in the post detail page itself
                }
            });
        });
    });
</script>
{% endblock %}