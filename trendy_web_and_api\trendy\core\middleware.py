"""
Middleware for system maintenance and feature toggle enforcement
"""
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.conf import settings
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import get_user_model
from .models import SystemMaintenance, FeatureToggle
import json
import re

User = get_user_model()


class MaintenanceMiddleware(MiddlewareMixin):
    """
    Middleware to handle system maintenance mode
    """
    
    # URLs that should always be accessible during maintenance
    ALLOWED_PATHS = [
        '/admin/',
        '/api/v1/auth/login/',
        '/api/v1/auth/logout/',
        '/static/',
        '/media/',
        '/favicon.ico',
    ]
    
    def process_request(self, request):
        # Skip maintenance check for allowed paths
        if self._is_path_allowed(request.path):
            return None
        
        # Check if maintenance is active
        maintenance = SystemMaintenance.get_active_maintenance()
        if not maintenance:
            return None
        
        # Check if user can access during maintenance
        if request.user.is_authenticated and maintenance.can_user_access(request.user):
            return None
        
        # Return maintenance response
        return self._maintenance_response(request, maintenance)
    
    def _is_path_allowed(self, path):
        """Check if path is allowed during maintenance"""
        for allowed_path in self.ALLOWED_PATHS:
            if path.startswith(allowed_path):
                return True
        return False
    
    def _maintenance_response(self, request, maintenance):
        """Return appropriate maintenance response"""
        if request.path.startswith('/api/'):
            # API response
            return JsonResponse({
                'error': 'maintenance_mode',
                'message': maintenance.public_message,
                'maintenance_type': maintenance.maintenance_type,
                'scheduled_end': maintenance.scheduled_end.isoformat() if maintenance.scheduled_end else None,
                'status_code': 503
            }, status=503)
        else:
            # Web response
            context = {
                'maintenance': maintenance,
                'title': 'System Maintenance',
                'message': maintenance.public_message,
            }
            
            # Try to render custom maintenance template
            try:
                return render(request, 'maintenance.html', context, status=503)
            except:
                # Fallback to simple HTML response
                html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>System Maintenance</title>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1">
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px; 
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            min-height: 100vh;
                            margin: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }}
                        .container {{
                            background: rgba(255,255,255,0.1);
                            padding: 40px;
                            border-radius: 10px;
                            backdrop-filter: blur(10px);
                            max-width: 500px;
                        }}
                        h1 {{ color: #fff; margin-bottom: 20px; }}
                        p {{ font-size: 18px; line-height: 1.6; }}
                        .icon {{ font-size: 64px; margin-bottom: 20px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="icon">🔧</div>
                        <h1>System Maintenance</h1>
                        <p>{maintenance.public_message}</p>
                        {f'<p><strong>Expected completion:</strong> {maintenance.scheduled_end.strftime("%Y-%m-%d %H:%M UTC")}</p>' if maintenance.scheduled_end else ''}
                        <p>We apologize for any inconvenience.</p>
                    </div>
                </body>
                </html>
                """
                return HttpResponse(html, status=503)


class FeatureToggleMiddleware(MiddlewareMixin):
    """
    Middleware to handle feature toggles
    """
    
    # Feature mappings for different URL patterns
    FEATURE_URL_MAPPINGS = {
        r'/api/v1/accounts/register/': 'user_registration',
        r'/api/v1/posts/create/': 'post_creation',
        r'/api/v1/posts/.*/comments/': 'commenting',
        r'/api/v1/posts/.*/vote/': 'voting',
        r'/api/v1/polls/.*/vote/': 'voting',
        r'/api/v1/messaging/': 'messaging',
        r'/api/v1/payments/': 'payments',
        r'/api/v1/ai-writing/': 'ai_writing',
        r'/api/v1/voice/': 'voice_features',
        r'/api/v1/blockchain/': 'blockchain',
        r'/api/v1/analytics/': 'analytics',
    }
    
    def process_request(self, request):
        # Skip for admin and static files
        if request.path.startswith('/admin/') or request.path.startswith('/static/'):
            return None
        
        # Check feature toggles for API endpoints
        if request.path.startswith('/api/'):
            return self._check_api_features(request)
        
        return None
    
    def _check_api_features(self, request):
        """Check if API endpoint features are enabled"""
        for url_pattern, feature_name in self.FEATURE_URL_MAPPINGS.items():
            if re.match(url_pattern, request.path):
                if not FeatureToggle.is_feature_enabled(feature_name, request.user):
                    return self._feature_disabled_response(request, feature_name)
        
        return None
    
    def _feature_disabled_response(self, request, feature_name):
        """Return response when feature is disabled"""
        try:
            feature = FeatureToggle.objects.get(name=feature_name)
            message = feature.disabled_message
        except FeatureToggle.DoesNotExist:
            message = "This feature is temporarily unavailable."
        
        return JsonResponse({
            'error': 'feature_disabled',
            'feature': feature_name,
            'message': message,
            'status_code': 503
        }, status=503)


class FeatureToggleContextMiddleware(MiddlewareMixin):
    """
    Middleware to add feature toggle context to templates
    """
    
    def process_template_response(self, request, response):
        if hasattr(response, 'context_data') and response.context_data is not None:
            # Add feature toggles to template context
            response.context_data['feature_toggles'] = self._get_feature_context(request.user)
            
            # Add maintenance status
            response.context_data['maintenance_active'] = SystemMaintenance.is_maintenance_active()
            
        return response
    
    def _get_feature_context(self, user):
        """Get feature toggle context for templates"""
        features = {}
        
        # Get all feature toggles
        for feature in FeatureToggle.objects.all():
            features[feature.name] = {
                'enabled': feature.is_enabled_for_user(user) if user.is_authenticated else feature.is_currently_enabled,
                'display_name': feature.display_name,
                'disabled_message': feature.disabled_message,
            }
        
        return features


# Utility functions for views and templates
def require_feature(feature_name):
    """
    Decorator to require a feature to be enabled
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not FeatureToggle.is_feature_enabled(feature_name, request.user):
                if request.path.startswith('/api/'):
                    try:
                        feature = FeatureToggle.objects.get(name=feature_name)
                        message = feature.disabled_message
                    except FeatureToggle.DoesNotExist:
                        message = "This feature is temporarily unavailable."
                    
                    return JsonResponse({
                        'error': 'feature_disabled',
                        'feature': feature_name,
                        'message': message,
                        'status_code': 503
                    }, status=503)
                else:
                    # Redirect to appropriate page or show error
                    from django.shortcuts import render
                    return render(request, 'feature_disabled.html', {
                        'feature_name': feature_name,
                        'message': "This feature is temporarily unavailable."
                    }, status=503)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def is_feature_enabled(feature_name, user=None):
    """
    Template tag / utility function to check if feature is enabled
    """
    return FeatureToggle.is_feature_enabled(feature_name, user)


# Template tags
from django import template

register = template.Library()

@register.simple_tag
def feature_enabled(feature_name, user=None):
    """Template tag to check if feature is enabled"""
    return FeatureToggle.is_feature_enabled(feature_name, user)

@register.simple_tag
def maintenance_active():
    """Template tag to check if maintenance is active"""
    return SystemMaintenance.is_maintenance_active()

@register.inclusion_tag('core/feature_toggle.html')
def feature_toggle_widget(feature_name, user=None):
    """Template tag to render feature toggle widget"""
    try:
        feature = FeatureToggle.objects.get(name=feature_name)
        enabled = feature.is_enabled_for_user(user) if user else feature.is_currently_enabled
        return {
            'feature': feature,
            'enabled': enabled,
            'user': user,
        }
    except FeatureToggle.DoesNotExist:
        return {
            'feature': None,
            'enabled': True,
            'user': user,
        }
