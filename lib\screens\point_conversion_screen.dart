import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/points_provider.dart';
import '../theme/app_theme.dart';

class PointConversionScreen extends ConsumerStatefulWidget {
  const PointConversionScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<PointConversionScreen> createState() => _PointConversionScreenState();
}

class _PointConversionScreenState extends ConsumerState<PointConversionScreen> {
  final TextEditingController _amountController = TextEditingController();
  Map<String, dynamic>? _conversionPreview;
  bool _isLoadingPreview = false;

  @override
  void initState() {
    super.initState();
    // Load data when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(unifiedPointsProvider.notifier).refreshAll();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _getPreview() async {
    final amount = int.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      setState(() {
        _conversionPreview = null;
      });
      return;
    }

    setState(() {
      _isLoadingPreview = true;
    });

    final preview = await ref.read(unifiedPointsProvider.notifier).getConversionPreview(amount);
    
    setState(() {
      _conversionPreview = preview;
      _isLoadingPreview = false;
    });
  }

  Future<void> _convertPoints() async {
    final amount = int.tryParse(_amountController.text);
    if (amount == null || amount <= 0) return;

    final success = await ref.read(unifiedPointsProvider.notifier).convertPoints(amount);
    
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Points converted successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      _amountController.clear();
      setState(() {
        _conversionPreview = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final pointsState = ref.watch(unifiedPointsProvider);
    final canConvert = ref.watch(canConvertPointsProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          '💱 Convert Points',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              ref.read(unifiedPointsProvider.notifier).refreshAll();
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: pointsState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Points Balance Cards
                  Row(
                    children: [
                      Expanded(
                        child: _buildPointsCard(
                          'Gamification Points',
                          pointsState.gamificationPoints,
                          Icons.stars,
                          AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildPointsCard(
                          'Store Points',
                          pointsState.storePoints,
                          Icons.shopping_cart,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Conversion Info
                  if (pointsState.conversionSettings != null) ...[
                    _buildConversionInfo(pointsState.conversionSettings!),
                    const SizedBox(height: 24),
                  ],

                  // Conversion Form
                  if (canConvert) ...[
                    _buildConversionForm(),
                    const SizedBox(height: 24),
                  ] else ...[
                    _buildConversionDisabled(pointsState),
                    const SizedBox(height: 24),
                  ],

                  // Conversion Preview
                  if (_conversionPreview != null) ...[
                    _buildConversionPreview(),
                    const SizedBox(height: 24),
                  ],

                  // Conversion History
                  if (pointsState.conversionHistory.isNotEmpty) ...[
                    _buildConversionHistory(pointsState.conversionHistory),
                  ],

                  // Error Display
                  if (pointsState.error != null) ...[
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade600),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              pointsState.error!,
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              ref.read(unifiedPointsProvider.notifier).clearError();
                            },
                            icon: const Icon(Icons.close),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildPointsCard(String title, int points, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            points.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversionInfo(Map<String, dynamic> settings) {
    final baseRate = settings['base_conversion_rate'] ?? 10;
    final dailyLimit = settings['daily_conversion_limit'] ?? 500;
    final minAmount = settings['minimum_conversion_amount'] ?? 50;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue.shade600),
              const SizedBox(width: 8),
              const Text(
                'Conversion Information',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text('• Base Rate: $baseRate gamification points = 1 store point'),
          Text('• Daily Limit: $dailyLimit store points per day'),
          Text('• Minimum Amount: $minAmount gamification points'),
          Text('• Remaining Today: ${ref.watch(unifiedPointsProvider).dailyConversionsRemaining} store points'),
        ],
      ),
    );
  }

  Widget _buildConversionForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Convert Gamification Points',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              labelText: 'Gamification Points to Convert',
              hintText: 'Enter amount',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              suffixIcon: IconButton(
                onPressed: _getPreview,
                icon: const Icon(Icons.preview),
              ),
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                _getPreview();
              } else {
                setState(() {
                  _conversionPreview = null;
                });
              }
            },
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _conversionPreview != null ? _convertPoints : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoadingPreview
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Convert Points',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversionDisabled(UnifiedPointsState state) {
    String reason = 'Conversion not available';
    
    if (state.conversionSettings != null) {
      final settings = state.conversionSettings!;
      if (!(settings['conversion_enabled'] ?? false)) {
        reason = 'Point conversion is currently disabled';
      } else if (settings['maintenance_mode'] ?? false) {
        reason = 'Point conversion is under maintenance';
      } else if (state.gamificationPoints < (settings['minimum_conversion_amount'] ?? 0)) {
        reason = 'You need at least ${settings['minimum_conversion_amount']} gamification points to convert';
      } else if (state.dailyConversionsRemaining <= 0) {
        reason = 'You have reached your daily conversion limit';
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(Icons.block, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              reason,
              style: TextStyle(color: Colors.grey.shade700),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversionPreview() {
    final preview = _conversionPreview!;
    final details = preview['conversion_details'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.preview, color: Colors.green.shade600),
              const SizedBox(width: 8),
              const Text(
                'Conversion Preview',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Store Points:'),
              Text(
                '${details['store_points']}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Total Cost:'),
              Text(
                '${details['total_cost']} gamification points',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Conversion Rate:'),
              Text('${details['conversion_rate']}:1'),
            ],
          ),
          if (details['percentage_fee'] > 0 || details['fixed_fee'] > 0) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Fees:'),
                Text('${details['percentage_fee'] + details['fixed_fee']} points'),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildConversionHistory(List<dynamic> history) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Conversions',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          ...history.take(5).map((transaction) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${transaction['gamification_points_spent']} → ${transaction['store_points_received']}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    DateTime.parse(transaction['created_at']).toString().split(' ')[0],
                    style: const TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}
