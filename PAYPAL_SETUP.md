# PayPal Integration Setup for Trendy App

## Overview
This guide explains how to set up PayPal integration for wallet deposits and payments in the Trendy app.

## 1. PayPal Developer Account Setup

### Step 1: Create PayPal Developer Account
1. Go to [PayPal Developer](https://developer.paypal.com/)
2. Sign in with your PayPal account or create a new one
3. Navigate to "My Apps & Credentials"

### Step 2: Create Application
1. Click "Create App"
2. Choose "Default Application" 
3. Select "Sandbox" for development
4. Note down your:
   - Client ID
   - Client Secret

### Step 3: Configure Sandbox Accounts
1. Go to "Sandbox" > "Accounts"
2. Create test buyer and seller accounts
3. Note down the credentials for testing

## 2. Django Backend Configuration

### Environment Variables
Add these to your `.env` file or environment:

```bash
# PayPal Configuration
PAYPAL_CLIENT_ID=your_sandbox_client_id
PAYPAL_CLIENT_SECRET=your_sandbox_client_secret
PAYPAL_MODE=sandbox  # Use 'live' for production
PAYPAL_WEBHOOK_ID=your_webhook_id
```

### Django Settings
Add to `settings.py`:

```python
# PayPal Configuration
PAYPAL_CLIENT_ID = os.environ.get('PAYPAL_CLIENT_ID')
PAYPAL_CLIENT_SECRET = os.environ.get('PAYPAL_CLIENT_SECRET')
PAYPAL_MODE = os.environ.get('PAYPAL_MODE', 'sandbox')
PAYPAL_WEBHOOK_ID = os.environ.get('PAYPAL_WEBHOOK_ID')

# PayPal API URLs
if PAYPAL_MODE == 'sandbox':
    PAYPAL_BASE_URL = 'https://api.sandbox.paypal.com'
else:
    PAYPAL_BASE_URL = 'https://api.paypal.com'
```

## 3. Testing Wallet Deposits

### Test User Credentials (Sandbox)
- **Buyer Account**: <EMAIL>
- **Password**: Test1234
- **Seller Account**: <EMAIL>  
- **Password**: Test1234

### Test Deposit Flow
1. Login to the Flutter app
2. Go to Wallet section
3. Click "Add Money"
4. Enter amount (e.g., $25.00)
5. Select PayPal payment method
6. Complete PayPal checkout with test credentials
7. Verify wallet balance is updated

## 4. API Endpoints for Wallet

### Create Deposit Request
```
POST /api/v1/wallet/deposit/create/
{
    "amount": 25.00,
    "payment_method": "paypal"
}
```

### Confirm Deposit
```
POST /api/v1/wallet/deposit/confirm/
{
    "deposit_request_id": "uuid",
    "transaction_id": "paypal_transaction_id"
}
```

### Get Wallet Overview
```
GET /api/v1/wallet/overview/
```

## 5. Flutter App Configuration

### Update API Configuration
Ensure your `api_config.dart` has the correct base URL:

```dart
static const String baseUrl = 'http://192.168.100.57:8000';
```

### Test Wallet Features
1. **View Balance**: Check current wallet balance
2. **Add Money**: Test PayPal deposit flow
3. **Transaction History**: View all transactions
4. **Spend Money**: Test purchasing virtual items

## 6. Troubleshooting

### Common Issues

#### 1. Connection Timeout
- **Problem**: API requests timing out
- **Solution**: Increased timeout to 60 seconds in `api_config.dart`

#### 2. PayPal Sandbox Issues
- **Problem**: PayPal payments not working
- **Solution**: 
  - Verify sandbox credentials
  - Check PayPal developer console for errors
  - Ensure webhook URLs are correct

#### 3. Wallet Balance Not Updating
- **Problem**: Deposit completed but balance unchanged
- **Solution**:
  - Check transaction status in Django admin
  - Verify deposit confirmation API call
  - Check wallet transaction logs

### Debug Commands

#### Check Wallet Status
```bash
cd trendy_web_and_api/trendy
python manage.py shell
```

```python
from wallet.models import UserWallet
from django.contrib.auth import get_user_model

User = get_user_model()
user = User.objects.get(username='your_username')
wallet = UserWallet.objects.get(user=user)
print(f"Balance: {wallet.balance}")
print(f"Transactions: {wallet.transactions.count()}")
```

#### Manual Deposit (for testing)
```python
from wallet.services import WalletService
from decimal import Decimal

# Add $50 to user's wallet
success, result = WalletService.process_deposit_direct(
    user=user,
    amount=Decimal('50.00'),
    description='Manual test deposit'
)
print(f"Success: {success}, Result: {result}")
```

## 7. Production Deployment

### Before Going Live
1. Switch PayPal mode to 'live'
2. Update PayPal credentials to production values
3. Configure production webhook URLs
4. Test with real PayPal accounts
5. Set up proper SSL certificates
6. Configure production database

### Security Considerations
- Never expose PayPal credentials in client-side code
- Use HTTPS for all payment-related endpoints
- Implement proper webhook verification
- Log all payment transactions for audit
- Set up monitoring for failed payments

## 8. Sample Test Data

The data population script creates:
- 15 users with wallets
- Initial balances ranging from $25-$150
- Sample transactions for each user
- Wallet settings configured for testing

### Test Scenarios
1. **Small Deposit**: $5.00 (minimum)
2. **Large Deposit**: $500.00 (maximum)
3. **Multiple Deposits**: Test consecutive deposits
4. **Spending**: Purchase virtual items
5. **Withdrawal**: Request PayPal withdrawal

## Support

For issues with PayPal integration:
1. Check PayPal Developer documentation
2. Review Django logs for errors
3. Test with PayPal sandbox tools
4. Contact PayPal developer support if needed
