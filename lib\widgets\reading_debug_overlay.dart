import 'package:flutter/material.dart';

class ReadingDebugOverlay extends StatefulWidget {
  final Widget child;
  final String postId;
  final int timeSpent;
  final double scrollPercentage;
  final bool hasAwardedPoints;
  final bool isEngagementActive;

  const ReadingDebugOverlay({
    Key? key,
    required this.child,
    required this.postId,
    required this.timeSpent,
    required this.scrollPercentage,
    required this.hasAwardedPoints,
    required this.isEngagementActive,
  }) : super(key: key);

  @override
  State<ReadingDebugOverlay> createState() => _ReadingDebugOverlayState();
}

class _ReadingDebugOverlayState extends State<ReadingDebugOverlay> {
  bool _showDebug = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_showDebug)
          Positioned(
            top: 100,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Reading Debug',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Post ID: ${widget.postId}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  Text(
                    'Time: ${widget.timeSpent}s',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  Text(
                    'Scroll: ${widget.scrollPercentage.toStringAsFixed(1)}%',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  Text(
                    'Active: ${widget.isEngagementActive}',
                    style: TextStyle(
                      color: widget.isEngagementActive ? Colors.green : Colors.red,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    'Awarded: ${widget.hasAwardedPoints}',
                    style: TextStyle(
                      color: widget.hasAwardedPoints ? Colors.green : Colors.orange,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Min: 10s + 30%',
                    style: const TextStyle(color: Colors.grey, fontSize: 10),
                  ),
                  Text(
                    'Ready: ${widget.timeSpent >= 10 && widget.scrollPercentage >= 30}',
                    style: TextStyle(
                      color: (widget.timeSpent >= 10 && widget.scrollPercentage >= 30) 
                          ? Colors.green 
                          : Colors.orange,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        Positioned(
          top: 50,
          right: 16,
          child: FloatingActionButton.small(
            onPressed: () {
              setState(() {
                _showDebug = !_showDebug;
              });
            },
            backgroundColor: Colors.blue,
            child: Icon(
              _showDebug ? Icons.visibility_off : Icons.bug_report,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
