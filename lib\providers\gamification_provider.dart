import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../models/gamification.dart';
import '../services/performance_service.dart';

// Gamification state
class GamificationState {
  final UserLevel? userLevel;
  final List<UserBadge> badges;
  final List<PointTransaction> transactions;
  final bool isLoading;
  final String? error;

  const GamificationState({
    this.userLevel,
    this.badges = const [],
    this.transactions = const [],
    this.isLoading = false,
    this.error,
  });

  GamificationState copyWith({
    UserLevel? userLevel,
    List<UserBadge>? badges,
    List<PointTransaction>? transactions,
    bool? isLoading,
    String? error,
  }) {
    return GamificationState(
      userLevel: userLevel ?? this.userLevel,
      badges: badges ?? this.badges,
      transactions: transactions ?? this.transactions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Gamification notifier
class GamificationNotifier extends StateNotifier<GamificationState> {
  final ApiService _apiService;

  GamificationNotifier(this._apiService) : super(const GamificationState());

  // Load user level
  Future<void> loadUserLevel() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the actual backend API
      final response = await _apiService.getUserLevel();

      // Calculate level progress percentage
      final totalPoints = response['total_points'] ?? 0;
      final currentLevel = response['current_level'] ?? 1;
      final pointsToNext = response['points_to_next_level'] ?? 100;

      // Calculate next level points (simple formula: level * 1000)
      final nextLevelPoints = currentLevel * 1000;
      final currentLevelPoints = (currentLevel - 1) * 1000;
      final progressInLevel = totalPoints - currentLevelPoints;
      final levelProgressPercentage = (progressInLevel / (nextLevelPoints - currentLevelPoints)) * 100;

      final userLevel = UserLevel(
        totalPoints: totalPoints,
        currentLevel: currentLevel,
        pointsToNextLevel: pointsToNext,
        levelProgressPercentage: levelProgressPercentage.clamp(0.0, 100.0),
        nextLevelPoints: nextLevelPoints,
        readingStreak: response['reading_streak'] ?? 0,
        writingStreak: response['writing_streak'] ?? 0,
        engagementStreak: response['engagement_streak'] ?? 0,
        totalPostsRead: response['total_posts_read'] ?? 0,
        totalPostsWritten: response['total_posts_written'] ?? 0,
        totalCommentsMade: response['total_comments_made'] ?? 0,
        totalLikesGiven: response['total_likes_given'] ?? 0,
        totalVoiceComments: response['total_voice_comments'] ?? 0,
      );

      state = state.copyWith(
        userLevel: userLevel,
        isLoading: false,
      );
    } catch (e) {
      print('Error loading user level: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load user level: ${e.toString()}',
      );
    }
  }

  // Load user badges
  Future<void> loadUserBadges() async {
    try {
      // Call the actual backend API
      final response = await _apiService.getUserBadges();

      final badges = response.map((badgeData) {
        final badge = badgeData['badge'];
        return UserBadge(
          id: badgeData['id'],
          badge: Badge(
            id: badge['id'],
            name: badge['name'],
            description: badge['description'],
            badgeType: badge['badge_type'],
            typeDisplay: _getBadgeTypeDisplay(badge['badge_type']),
            rarity: badge['rarity'],
            rarityDisplay: _getRarityDisplay(badge['rarity']),
            icon: badge['icon'] ?? '🏆',
            color: badge['color'] ?? '#FFD700',
            pointsReward: badge['points_reward'] ?? 0,
          ),
          earnedAt: DateTime.parse(badgeData['earned_at']),
        );
      }).toList();

      state = state.copyWith(badges: badges);
    } catch (e) {
      print('Error loading user badges: $e');
      state = state.copyWith(error: 'Failed to load badges: ${e.toString()}');
    }
  }

  String _getBadgeTypeDisplay(String type) {
    switch (type) {
      case 'reading': return 'Reading Achievement';
      case 'writing': return 'Writing Achievement';
      case 'engagement': return 'Engagement Achievement';
      case 'milestone': return 'Milestone Achievement';
      case 'special': return 'Special Achievement';
      default: return 'Achievement';
    }
  }

  String _getRarityDisplay(String rarity) {
    switch (rarity) {
      case 'common': return 'Common';
      case 'uncommon': return 'Uncommon';
      case 'rare': return 'Rare';
      case 'epic': return 'Epic';
      case 'legendary': return 'Legendary';
      default: return 'Common';
    }
  }

  // Load point transactions
  Future<void> loadPointTransactions() async {
    try {
      // Call the actual backend API
      final response = await _apiService.getUserTransactions();

      final transactions = (response as List).map((transactionData) {
        return PointTransaction(
          id: transactionData['id'],
          transactionType: transactionData['transaction_type'],
          typeDisplay: _getTransactionTypeDisplay(transactionData['transaction_type']),
          points: transactionData['points'],
          description: transactionData['description'],
          createdAt: DateTime.parse(transactionData['created_at']),
        );
      }).toList();

      state = state.copyWith(transactions: transactions);
    } catch (e) {
      print('Error loading point transactions: $e');
      state = state.copyWith(error: 'Failed to load transactions: ${e.toString()}');
    }
  }

  String _getTransactionTypeDisplay(String type) {
    switch (type) {
      case 'reading': return 'Reading Activity';
      case 'writing': return 'Writing Activity';
      case 'engagement': return 'Engagement Activity';
      case 'badge': return 'Badge Earned';
      case 'challenge': return 'Challenge Completed';
      case 'bonus': return 'Bonus Points';
      case 'daily': return 'Daily Bonus';
      case 'referral': return 'Referral Bonus';
      case 'purchase': return 'Points Purchased';
      case 'spent': return 'Points Spent';
      default: return 'Points Activity';
    }
  }

  // Add points (for when user earns points)
  Future<void> addPoints(int points, String description) async {
    try {
      final currentLevel = state.userLevel;
      if (currentLevel == null) return;

      // Create new transaction
      final transaction = PointTransaction(
        id: DateTime.now().millisecondsSinceEpoch,
        transactionType: 'earned',
        typeDisplay: 'Points Earned',
        points: points,
        description: description,
        createdAt: DateTime.now(),
      );

      // Update user level with new points
      final newTotalPoints = currentLevel.totalPoints + points;
      final newLevel = (newTotalPoints / 1000).floor() + 1; // Simple level calculation
      final pointsToNext = (newLevel * 1000) - newTotalPoints;

      final updatedLevel = currentLevel.copyWith(
        totalPoints: newTotalPoints,
        currentLevel: newLevel,
        pointsToNextLevel: pointsToNext,
        levelProgressPercentage: ((newTotalPoints % 1000) / 1000) * 100,
      );

      // Update state
      state = state.copyWith(
        userLevel: updatedLevel,
        transactions: [transaction, ...state.transactions],
      );
    } catch (e) {
      print('Error adding points: $e');
    }
  }

  // Get current user points
  int getCurrentPoints() {
    return state.userLevel?.totalPoints ?? 0;
  }

  // Get current user level
  int getCurrentLevel() {
    return state.userLevel?.currentLevel ?? 1;
  }

  // Get total points earned
  int getTotalPointsEarned() {
    return state.userLevel?.totalPoints ?? 0;
  }

  // Load all gamification data using unified endpoint (Performance Optimized)
  Future<void> loadAllDataOptimized() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Use unified endpoint to get all points data in one request
      final response = await _apiService.dio.get('/api/v1/gamification/points/unified/');

      if (response.statusCode == 200 && response.data['success'] == true) {
        final data = response.data['data'];

        // Extract gamification data
        final gamificationData = data['gamification'];
        final userLevel = UserLevel(
          totalPoints: gamificationData['total_points'],
          currentLevel: gamificationData['current_level'],
          pointsToNextLevel: gamificationData['points_to_next_level'],
          levelProgressPercentage: gamificationData['level_progress_percentage']?.toDouble() ?? 0.0,
          readingStreak: gamificationData['reading_streak'],
          writingStreak: gamificationData['writing_streak'],
          engagementStreak: gamificationData['engagement_streak'],
          totalPostsRead: gamificationData['total_posts_read'],
          totalPostsWritten: gamificationData['total_posts_written'],
          totalCommentsMade: gamificationData['total_comments_made'],
          totalLikesGiven: gamificationData['total_likes_given'],
        );

        // Extract recent transactions
        final transactionsList = data['recent_transactions'] as List;
        final transactions = transactionsList.map((transactionData) {
          return PointTransaction(
            id: transactionData['id'],
            transactionType: transactionData['type'],
            typeDisplay: transactionData['type'],
            points: transactionData['points'],
            description: transactionData['description'],
            createdAt: DateTime.parse(transactionData['created_at']),
          );
        }).toList();

        state = state.copyWith(
          userLevel: userLevel,
          transactions: transactions,
          isLoading: false,
          error: null,
        );

        // Load badges separately (less frequently needed)
        loadUserBadges();

      } else {
        throw Exception('Failed to load unified data');
      }
    } catch (e) {
      print('Error loading unified data: $e');
      // Fallback to individual requests
      await loadAllData();
    }
  }

  // Load all gamification data (fallback method)
  Future<void> loadAllData() async {
    await Future.wait([
      loadUserLevel(),
      loadUserBadges(),
      loadPointTransactions(),
    ]);
  }

  // Spend points (for rewards, purchases, etc.)
  Future<void> spendPoints(int points, String description) async {
    try {
      final currentLevel = state.userLevel;
      if (currentLevel == null) {
        throw Exception('User level not loaded');
      }

      if (currentLevel.totalPoints < points) {
        throw Exception('Insufficient points. You have ${currentLevel.totalPoints} but need $points.');
      }

      // Create negative point transaction for spending
      final transaction = PointTransaction(
        id: DateTime.now().millisecondsSinceEpoch,
        transactionType: 'spend',
        typeDisplay: 'Spent',
        points: -points, // Negative for spending
        description: description,
        createdAt: DateTime.now(),
      );

      // Update user level with reduced points
      final newTotalPoints = currentLevel.totalPoints - points;
      final newLevel = (newTotalPoints / 1000).floor() + 1; // Simple level calculation
      final pointsToNext = (newLevel * 1000) - newTotalPoints;

      final updatedLevel = currentLevel.copyWith(
        totalPoints: newTotalPoints,
        currentLevel: newLevel,
        pointsToNextLevel: pointsToNext,
        levelProgressPercentage: ((newTotalPoints % 1000) / 1000) * 100,
      );

      // Update state
      state = state.copyWith(
        userLevel: updatedLevel,
        transactions: [transaction, ...state.transactions],
      );

      // Call backend API to record the spending
      try {
        await _apiService.spendUserPoints(points, description);
      } catch (e) {
        print('Error recording point spending on backend: $e');
        // Continue anyway since we've updated the local state
      }
    } catch (e) {
      print('Error spending points: $e');
      rethrow;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final gamificationProvider = StateNotifierProvider<GamificationNotifier, GamificationState>((ref) {
  return GamificationNotifier(ApiService());
});

// Convenience providers
final userLevelProvider = Provider<UserLevel?>((ref) {
  return ref.watch(gamificationProvider).userLevel;
});

final userBadgesProvider = Provider<List<UserBadge>>((ref) {
  return ref.watch(gamificationProvider).badges;
});

final pointTransactionsProvider = Provider<List<PointTransaction>>((ref) {
  return ref.watch(gamificationProvider).transactions;
});

final currentPointsProvider = Provider<int>((ref) {
  final notifier = ref.read(gamificationProvider.notifier);
  return notifier.getCurrentPoints();
});

final currentLevelProvider = Provider<int>((ref) {
  final notifier = ref.read(gamificationProvider.notifier);
  return notifier.getCurrentLevel();
});

final totalPointsEarnedProvider = Provider<int>((ref) {
  final notifier = ref.read(gamificationProvider.notifier);
  return notifier.getTotalPointsEarned();
});
