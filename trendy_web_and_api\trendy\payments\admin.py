from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum, Count
from .models import (
    PayPalAccount, PaymentTransaction, PayPalWebhook, 
    PayPalPayoutBatch, PaymentSettings, UserPayPalProfile
)


@admin.register(PayPalAccount)
class PayPalAccountAdmin(admin.ModelAdmin):
    list_display = ['name', 'account_type', 'environment', 'business_email', 'is_active']
    list_filter = ['account_type', 'environment', 'is_active']
    search_fields = ['name', 'business_email', 'business_name']
    readonly_fields = ['created_at', 'updated_at']
    actions = ['activate_accounts', 'deactivate_accounts']
    
    fieldsets = [
        ('Account Information', {
            'fields': ['name', 'account_type', 'environment', 'is_active']
        }),
        ('PayPal API Credentials', {
            'fields': ['client_id', 'client_secret'],
            'classes': ['collapse']
        }),
        ('Business Details', {
            'fields': ['business_email', 'business_name']
        }),
        ('Webhook Configuration', {
            'fields': ['webhook_id', 'webhook_url'],
            'classes': ['collapse']
        }),
        ('Settings', {
            'fields': ['auto_accept_payments']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    def activate_accounts(self, request, queryset):
        # Deactivate all other accounts first
        PayPalAccount.objects.update(is_active=False)
        # Activate selected account
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} PayPal account (others deactivated)")
    activate_accounts.short_description = "Activate selected account (deactivate others)"
    
    def deactivate_accounts(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} PayPal accounts")
    deactivate_accounts.short_description = "Deactivate selected accounts"


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    list_display = ['user', 'transaction_type', 'payment_purpose', 'amount', 'status', 'created_at', 'completed_at']
    list_filter = ['transaction_type', 'payment_purpose', 'status', 'created_at']
    search_fields = ['user__username', 'reference_id', 'paypal_payment_id', 'payer_email', 'payee_email']
    readonly_fields = ['created_at', 'processed_at', 'completed_at']
    date_hierarchy = 'created_at'
    actions = ['mark_completed', 'mark_failed', 'retry_failed_transactions']
    
    fieldsets = [
        ('Transaction Details', {
            'fields': ['user', 'transaction_type', 'payment_purpose', 'status']
        }),
        ('Amount Information', {
            'fields': ['amount', 'currency', 'description']
        }),
        ('PayPal Details', {
            'fields': ['paypal_payment_id', 'paypal_payer_id', 'paypal_batch_id', 'paypal_item_id']
        }),
        ('Email Addresses', {
            'fields': ['payer_email', 'payee_email']
        }),
        ('Reference & Tracking', {
            'fields': ['reference_id']
        }),
        ('Timestamps', {
            'fields': ['created_at', 'processed_at', 'completed_at']
        }),
        ('Error Handling', {
            'fields': ['error_message', 'retry_count'],
            'classes': ['collapse']
        }),
    ]
    
    def mark_completed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(status__in=['pending', 'processing']).update(
            status='completed',
            completed_at=timezone.now()
        )
        self.message_user(request, f"Marked {updated} transactions as completed")
    mark_completed.short_description = "Mark as completed"
    
    def mark_failed(self, request, queryset):
        updated = queryset.filter(status__in=['pending', 'processing']).update(status='failed')
        self.message_user(request, f"Marked {updated} transactions as failed")
    mark_failed.short_description = "Mark as failed"
    
    def retry_failed_transactions(self, request, queryset):
        updated = queryset.filter(status='failed').update(
            status='pending',
            error_message='',
            retry_count=models.F('retry_count') + 1
        )
        self.message_user(request, f"Queued {updated} transactions for retry")
    retry_failed_transactions.short_description = "Retry failed transactions"
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(PayPalWebhook)
class PayPalWebhookAdmin(admin.ModelAdmin):
    list_display = ['event_type', 'resource_id', 'processed', 'received_at', 'processed_at']
    list_filter = ['event_type', 'processed', 'received_at']
    search_fields = ['event_id', 'resource_id', 'webhook_id']
    readonly_fields = ['received_at', 'processed_at', 'raw_data']
    date_hierarchy = 'received_at'
    actions = ['mark_processed', 'reprocess_webhooks']
    
    fieldsets = [
        ('Webhook Information', {
            'fields': ['webhook_id', 'event_type', 'event_id']
        }),
        ('Resource Details', {
            'fields': ['resource_id', 'resource_type']
        }),
        ('Processing Status', {
            'fields': ['processed', 'processed_at', 'payment_transaction']
        }),
        ('Raw Data', {
            'fields': ['raw_data'],
            'classes': ['collapse']
        }),
        ('Metadata', {
            'fields': ['received_at']
        }),
    ]
    
    def mark_processed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(processed=False).update(
            processed=True,
            processed_at=timezone.now()
        )
        self.message_user(request, f"Marked {updated} webhooks as processed")
    mark_processed.short_description = "Mark as processed"
    
    def reprocess_webhooks(self, request, queryset):
        from .paypal_service import PayPalService
        
        processed_count = 0
        for webhook in queryset.filter(processed=False):
            try:
                paypal_service = PayPalService()
                result = paypal_service.process_webhook(webhook.raw_data)
                if result['success']:
                    processed_count += 1
            except Exception as e:
                continue
        
        self.message_user(request, f"Reprocessed {processed_count} webhooks")
    reprocess_webhooks.short_description = "Reprocess selected webhooks"


@admin.register(PayPalPayoutBatch)
class PayPalPayoutBatchAdmin(admin.ModelAdmin):
    list_display = ['batch_id', 'paypal_batch_id', 'batch_status', 'total_amount', 'total_items', 'created_at', 'completed_at']
    list_filter = ['batch_status', 'created_at']
    search_fields = ['batch_id', 'paypal_batch_id']
    readonly_fields = ['created_at', 'submitted_at', 'completed_at']
    actions = ['check_batch_status', 'mark_completed']
    
    fieldsets = [
        ('Batch Information', {
            'fields': ['batch_id', 'paypal_batch_id', 'batch_status']
        }),
        ('Batch Details', {
            'fields': ['total_amount', 'total_items', 'currency']
        }),
        ('Processing Timeline', {
            'fields': ['created_at', 'submitted_at', 'completed_at']
        }),
        ('Error Information', {
            'fields': ['error_message'],
            'classes': ['collapse']
        }),
    ]
    
    def check_batch_status(self, request, queryset):
        # In a real implementation, this would check PayPal API for batch status
        self.message_user(request, "Batch status check initiated (implement PayPal API call)")
    check_batch_status.short_description = "Check batch status with PayPal"
    
    def mark_completed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(batch_status='processing').update(
            batch_status='success',
            completed_at=timezone.now()
        )
        self.message_user(request, f"Marked {updated} batches as completed")
    mark_completed.short_description = "Mark as completed"


@admin.register(UserPayPalProfile)
class UserPayPalProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'paypal_email', 'paypal_account_verified', 'total_payments_received', 'total_payments_made', 'successful_transactions']
    list_filter = ['paypal_account_verified', 'verified_at']
    search_fields = ['user__username', 'paypal_email']
    readonly_fields = ['verification_sent_at', 'verified_at', 'created_at', 'updated_at']
    actions = ['verify_accounts', 'unverify_accounts', 'send_verification']
    
    fieldsets = [
        ('User Information', {
            'fields': ['user', 'paypal_email']
        }),
        ('Verification Status', {
            'fields': ['paypal_account_verified', 'verification_code', 'verification_sent_at', 'verified_at']
        }),
        ('Payment Preferences', {
            'fields': ['auto_accept_payments', 'notification_preferences']
        }),
        ('Statistics', {
            'fields': ['total_payments_received', 'total_payments_made', 'successful_transactions', 'failed_transactions']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    def verify_accounts(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            paypal_account_verified=True,
            verified_at=timezone.now()
        )
        self.message_user(request, f"Verified {updated} PayPal accounts")
    verify_accounts.short_description = "Verify selected accounts"
    
    def unverify_accounts(self, request, queryset):
        updated = queryset.update(
            paypal_account_verified=False,
            verified_at=None
        )
        self.message_user(request, f"Unverified {updated} PayPal accounts")
    unverify_accounts.short_description = "Unverify selected accounts"
    
    def send_verification(self, request, queryset):
        import random
        from django.utils import timezone
        
        sent_count = 0
        for profile in queryset.filter(paypal_account_verified=False):
            verification_code = str(random.randint(100000, 999999))
            profile.verification_code = verification_code
            profile.verification_sent_at = timezone.now()
            profile.save()
            
            # In real implementation, send email here
            sent_count += 1
        
        self.message_user(request, f"Sent verification codes to {sent_count} users")
    send_verification.short_description = "Send verification codes"


@admin.register(PaymentSettings)
class PaymentSettingsAdmin(admin.ModelAdmin):
    fieldsets = [
        ('PayPal Configuration', {
            'fields': ['paypal_mode']
        }),
        ('Payment Limits', {
            'fields': ['minimum_payment', 'maximum_payment']
        }),
        ('Payout Settings', {
            'fields': ['minimum_payout', 'maximum_daily_payout', 'payout_processing_fee']
        }),
        ('Batch Processing', {
            'fields': ['auto_process_payouts', 'batch_size', 'batch_frequency_hours']
        }),
        ('Security Settings', {
            'fields': ['require_email_verification', 'webhook_verification']
        }),
        ('Notifications', {
            'fields': ['admin_email_notifications', 'user_email_notifications']
        }),
        ('System Controls', {
            'fields': ['payments_enabled', 'payouts_enabled']
        }),
        ('Metadata', {
            'fields': ['created_at', 'updated_at'],
            'classes': ['collapse']
        }),
    ]
    
    readonly_fields = ['created_at', 'updated_at']
    
    def has_add_permission(self, request):
        # Only allow one settings instance
        return not PaymentSettings.objects.exists()
    
    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False


# Custom admin view for payment analytics
class PaymentAnalyticsAdmin(admin.ModelAdmin):
    """Custom admin view for payment analytics dashboard"""
    change_list_template = 'admin/payments/payment_analytics.html'
    
    def changelist_view(self, request, extra_context=None):
        from django.utils import timezone
        from datetime import timedelta
        
        # Get date range (last 30 days by default)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)
        
        # Calculate analytics
        transactions = PaymentTransaction.objects.filter(
            created_at__range=[start_date, end_date]
        )
        
        # Incoming payments (users paying admin)
        incoming = transactions.filter(transaction_type='incoming', status='completed')
        incoming_revenue = incoming.aggregate(total=Sum('amount'))['total'] or 0
        incoming_count = incoming.count()
        
        # Outgoing payments (admin paying users)
        outgoing = transactions.filter(transaction_type='outgoing', status='completed')
        outgoing_total = outgoing.aggregate(total=Sum('amount'))['total'] or 0
        outgoing_count = outgoing.count()
        
        # Net revenue
        net_revenue = incoming_revenue - outgoing_total
        
        # Success rates
        total_transactions = transactions.count()
        successful_transactions = transactions.filter(status='completed').count()
        success_rate = (successful_transactions / total_transactions * 100) if total_transactions > 0 else 0
        
        # Top payment purposes
        top_purposes = incoming.values('payment_purpose').annotate(
            count=Count('id'),
            revenue=Sum('amount')
        ).order_by('-revenue')[:5]
        
        extra_context = extra_context or {}
        extra_context.update({
            'incoming_revenue': float(incoming_revenue),
            'incoming_count': incoming_count,
            'outgoing_total': float(outgoing_total),
            'outgoing_count': outgoing_count,
            'net_revenue': float(net_revenue),
            'total_transactions': total_transactions,
            'success_rate': round(success_rate, 2),
            'top_purposes': list(top_purposes),
            'date_range': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
        })
        
        return super().changelist_view(request, extra_context=extra_context)


# Register the analytics view (commented out to avoid duplicate registration)
# admin.site.register(PaymentTransaction, PaymentAnalyticsAdmin)
