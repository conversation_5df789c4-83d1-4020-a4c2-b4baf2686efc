# Generated by Django 5.0.4 on 2025-08-03 20:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("voice_features", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="texttospeechrequest",
            name="voice_type",
            field=models.CharField(
                choices=[
                    ("female", "Female"),
                    ("male", "Male"),
                    ("auto", "Auto-detect"),
                ],
                default="female",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="voicecomment",
            name="voice_settings",
            field=models.JSONField(
                default=dict,
                help_text="Voice settings used when recording (rate, pitch, etc.)",
            ),
        ),
        migrations.AddField(
            model_name="voicecomment",
            name="voice_type",
            field=models.Char<PERSON>ield(
                choices=[
                    ("female", "Female"),
                    ("male", "Male"),
                    ("auto", "Auto-detect"),
                ],
                default="female",
                help_text="Voice type used for TTS playback",
                max_length=10,
            ),
        ),
        migrations.CreateModel(
            name="UserVoicePreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "preferred_voice_type",
                    models.CharField(
                        choices=[
                            ("female", "Female"),
                            ("male", "Male"),
                            ("auto", "Auto-detect"),
                        ],
                        default="female",
                        max_length=10,
                    ),
                ),
                (
                    "speech_rate",
                    models.FloatField(
                        default=0.5, help_text="Speech rate (0.1 to 1.0)"
                    ),
                ),
                (
                    "pitch",
                    models.FloatField(
                        default=1.0, help_text="Voice pitch (0.5 to 2.0)"
                    ),
                ),
                (
                    "volume",
                    models.FloatField(
                        default=1.0, help_text="Voice volume (0.0 to 1.0)"
                    ),
                ),
                (
                    "preferred_language",
                    models.CharField(default="en-US", max_length=10),
                ),
                (
                    "auto_transcribe",
                    models.BooleanField(
                        default=True,
                        help_text="Automatically transcribe voice comments",
                    ),
                ),
                (
                    "voice_comment_quality",
                    models.CharField(
                        choices=[
                            ("low", "Low (smaller file)"),
                            ("medium", "Medium"),
                            ("high", "High (better quality)"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                (
                    "voice_settings",
                    models.JSONField(
                        default=dict,
                        help_text="Additional voice settings (voice name, accent, etc.)",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="voice_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Voice Preferences",
                "verbose_name_plural": "User Voice Preferences",
                "indexes": [
                    models.Index(
                        fields=["user"], name="voice_featu_user_id_e2cc41_idx"
                    ),
                    models.Index(
                        fields=["preferred_voice_type"],
                        name="voice_featu_preferr_d4adff_idx",
                    ),
                ],
            },
        ),
    ]
