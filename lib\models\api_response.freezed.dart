// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GenericApiResponse _$GenericApiResponseFromJson(Map<String, dynamic> json) {
  return _GenericApiResponse.fromJson(json);
}

/// @nodoc
mixin _$GenericApiResponse {
  @JsonKey(name: 'success')
  bool get isSuccess => throw _privateConstructorUsedError;
  dynamic get data => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GenericApiResponseCopyWith<GenericApiResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GenericApiResponseCopyWith<$Res> {
  factory $GenericApiResponseCopyWith(
          GenericApiResponse value, $Res Function(GenericApiResponse) then) =
      _$GenericApiResponseCopyWithImpl<$Res, GenericApiResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'success') bool isSuccess, dynamic data, String? error});
}

/// @nodoc
class _$GenericApiResponseCopyWithImpl<$Res, $Val extends GenericApiResponse>
    implements $GenericApiResponseCopyWith<$Res> {
  _$GenericApiResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isSuccess = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GenericApiResponseImplCopyWith<$Res>
    implements $GenericApiResponseCopyWith<$Res> {
  factory _$$GenericApiResponseImplCopyWith(_$GenericApiResponseImpl value,
          $Res Function(_$GenericApiResponseImpl) then) =
      __$$GenericApiResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'success') bool isSuccess, dynamic data, String? error});
}

/// @nodoc
class __$$GenericApiResponseImplCopyWithImpl<$Res>
    extends _$GenericApiResponseCopyWithImpl<$Res, _$GenericApiResponseImpl>
    implements _$$GenericApiResponseImplCopyWith<$Res> {
  __$$GenericApiResponseImplCopyWithImpl(_$GenericApiResponseImpl _value,
      $Res Function(_$GenericApiResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isSuccess = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_$GenericApiResponseImpl(
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GenericApiResponseImpl implements _GenericApiResponse {
  const _$GenericApiResponseImpl(
      {@JsonKey(name: 'success') required this.isSuccess,
      this.data,
      this.error});

  factory _$GenericApiResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$GenericApiResponseImplFromJson(json);

  @override
  @JsonKey(name: 'success')
  final bool isSuccess;
  @override
  final dynamic data;
  @override
  final String? error;

  @override
  String toString() {
    return 'GenericApiResponse(isSuccess: $isSuccess, data: $data, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GenericApiResponseImpl &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, isSuccess, const DeepCollectionEquality().hash(data), error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GenericApiResponseImplCopyWith<_$GenericApiResponseImpl> get copyWith =>
      __$$GenericApiResponseImplCopyWithImpl<_$GenericApiResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GenericApiResponseImplToJson(
      this,
    );
  }
}

abstract class _GenericApiResponse implements GenericApiResponse {
  const factory _GenericApiResponse(
      {@JsonKey(name: 'success') required final bool isSuccess,
      final dynamic data,
      final String? error}) = _$GenericApiResponseImpl;

  factory _GenericApiResponse.fromJson(Map<String, dynamic> json) =
      _$GenericApiResponseImpl.fromJson;

  @override
  @JsonKey(name: 'success')
  bool get isSuccess;
  @override
  dynamic get data;
  @override
  String? get error;
  @override
  @JsonKey(ignore: true)
  _$$GenericApiResponseImplCopyWith<_$GenericApiResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
