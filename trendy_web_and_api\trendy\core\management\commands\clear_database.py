"""
Django management command for safely clearing all database data
Usage: python manage.py clear_database [--force] [--preserve-users] [--preserve-groups]
"""
from django.core.management.base import BaseCommand, CommandError
from django.core.management import call_command
from django.db import connection, transaction
from django.apps import apps
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.sessions.models import Session
import sys


class Command(BaseCommand):
    help = 'Safely clear all database data with confirmation prompts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Skip confirmation prompts'
        )
        parser.add_argument(
            '--preserve-users',
            action='store_true',
            help='Preserve superuser accounts'
        )
        parser.add_argument(
            '--preserve-groups',
            action='store_true',
            help='Preserve user groups and permissions'
        )
        parser.add_argument(
            '--preserve-content-types',
            action='store_true',
            help='Preserve Django content types'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually doing it'
        )
        parser.add_argument(
            '--create-backup',
            action='store_true',
            help='Create backup before clearing'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.WARNING('🗑️  Database Clear Process'))
        self.stdout.write('=' * 50)

        if options['dry_run']:
            self.stdout.write(self.style.WARNING('🧪 DRY RUN MODE - No changes will be made'))
            self._show_clear_plan(options)
            return

        # Create backup if requested
        if options['create_backup']:
            self.stdout.write('\n📦 Creating backup before clearing...')
            self._create_backup()

        # Show what will be cleared
        self._show_clear_plan(options)

        # Confirmation
        if not options['force']:
            self.stdout.write(
                self.style.ERROR('\n⚠️  DANGER: This will permanently delete ALL database data!')
            )
            self.stdout.write(
                self.style.ERROR('⚠️  This action cannot be undone!')
            )
            
            confirm1 = input('\nType "DELETE ALL DATA" to confirm: ')
            if confirm1 != 'DELETE ALL DATA':
                self.stdout.write('❌ Clear operation cancelled')
                return
            
            confirm2 = input('Are you absolutely sure? (yes/no): ')
            if confirm2.lower() != 'yes':
                self.stdout.write('❌ Clear operation cancelled')
                return

        try:
            with transaction.atomic():
                self._clear_database(options)
            
            self.stdout.write(f'\n✅ Database cleared successfully!')
            
            # Run migrations to recreate tables
            self.stdout.write('\n🔄 Running migrations to recreate tables...')
            call_command('migrate', verbosity=0)
            
            self.stdout.write('✅ Database structure recreated')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Clear operation failed: {str(e)}')
            )
            raise CommandError(f'Database clear failed: {str(e)}')

    def _show_clear_plan(self, options):
        """Show what will be cleared"""
        self.stdout.write('\n📋 CLEAR PLAN:')
        self.stdout.write('-' * 30)
        
        # Get all models and their counts
        models_to_clear = []
        models_to_preserve = []
        
        User = get_user_model()
        
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                model_name = f'{app_config.label}.{model._meta.model_name}'
                
                # Check if model should be preserved
                should_preserve = False
                
                if options['preserve_users'] and model == User:
                    should_preserve = True
                elif options['preserve_groups'] and model in [Group, Permission]:
                    should_preserve = True
                elif options['preserve_content_types'] and model == ContentType:
                    should_preserve = True
                
                try:
                    count = model.objects.count()
                    if should_preserve:
                        models_to_preserve.append((model_name, count))
                    else:
                        models_to_clear.append((model_name, count))
                except Exception:
                    # Skip models that can't be counted
                    pass
        
        # Show models to be cleared
        if models_to_clear:
            self.stdout.write('\n🗑️  WILL BE CLEARED:')
            total_records = 0
            for model_name, count in models_to_clear:
                if count > 0:
                    self.stdout.write(f'  • {model_name}: {count:,} records')
                    total_records += count
            self.stdout.write(f'\n  📊 Total records to delete: {total_records:,}')
        
        # Show models to be preserved
        if models_to_preserve:
            self.stdout.write('\n💾 WILL BE PRESERVED:')
            for model_name, count in models_to_preserve:
                if count > 0:
                    self.stdout.write(f'  • {model_name}: {count:,} records')

    def _create_backup(self):
        """Create backup before clearing"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'pre_clear_{timestamp}'
        
        try:
            call_command(
                'backup_database',
                '--output-dir', 'backups/pre_clear',
                '--name', backup_name,
                '--compress',
                verbosity=0
            )
            self.stdout.write(f'  ✅ Backup created: {backup_name}')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  Backup failed: {e}')
            )
            if not input('Continue without backup? (yes/no): ').lower() == 'yes':
                sys.exit(1)

    def _clear_database(self, options):
        """Clear database data"""
        User = get_user_model()
        
        # Get models in dependency order (reverse of creation order)
        models_to_clear = []
        preserved_users = []
        preserved_groups = []
        
        # Preserve superusers if requested
        if options['preserve_users']:
            preserved_users = list(User.objects.filter(is_superuser=True))
            self.stdout.write(f'  💾 Preserving {len(preserved_users)} superuser(s)')
        
        # Preserve groups if requested
        if options['preserve_groups']:
            preserved_groups = list(Group.objects.all())
            self.stdout.write(f'  💾 Preserving {len(preserved_groups)} group(s)')
        
        # Get all models in reverse dependency order
        for app_config in reversed(apps.get_app_configs()):
            for model in reversed(app_config.get_models()):
                # Skip models that should be preserved
                if options['preserve_users'] and model == User:
                    continue
                elif options['preserve_groups'] and model in [Group, Permission]:
                    continue
                elif options['preserve_content_types'] and model == ContentType:
                    continue
                
                models_to_clear.append(model)
        
        # Clear models
        self.stdout.write('\n🗑️  Clearing database...')
        total_deleted = 0
        
        for model in models_to_clear:
            try:
                count = model.objects.count()
                if count > 0:
                    deleted_count, _ = model.objects.all().delete()
                    total_deleted += deleted_count
                    self.stdout.write(f'  ✅ {model._meta.label}: {deleted_count:,} records')
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'  ⚠️  {model._meta.label}: {str(e)}')
                )
        
        # Clear sessions
        try:
            session_count = Session.objects.count()
            if session_count > 0:
                Session.objects.all().delete()
                total_deleted += session_count
                self.stdout.write(f'  ✅ Sessions: {session_count:,} records')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  Sessions: {str(e)}')
            )
        
        # Reset sequences (for PostgreSQL)
        if 'postgresql' in connection.settings_dict['ENGINE']:
            self._reset_sequences()
        
        # Restore preserved data
        if preserved_users:
            self.stdout.write('\n💾 Restoring preserved users...')
            for user in preserved_users:
                user.pk = None  # Create new instance
                user.save()
            self.stdout.write(f'  ✅ Restored {len(preserved_users)} superuser(s)')
        
        if preserved_groups:
            self.stdout.write('\n💾 Restoring preserved groups...')
            for group in preserved_groups:
                group.pk = None  # Create new instance
                group.save()
            self.stdout.write(f'  ✅ Restored {len(preserved_groups)} group(s)')
        
        self.stdout.write(f'\n📊 Total records deleted: {total_deleted:,}')

    def _reset_sequences(self):
        """Reset database sequences (PostgreSQL)"""
        try:
            with connection.cursor() as cursor:
                # Get all sequences
                cursor.execute("""
                    SELECT sequence_name FROM information_schema.sequences 
                    WHERE sequence_schema = 'public'
                """)
                sequences = cursor.fetchall()
                
                # Reset each sequence
                for (sequence_name,) in sequences:
                    cursor.execute(f'ALTER SEQUENCE {sequence_name} RESTART WITH 1')
                
                self.stdout.write(f'  ✅ Reset {len(sequences)} sequences')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠️  Sequence reset failed: {e}')
            )

    def _get_model_dependencies(self):
        """Get models in dependency order"""
        # This is a simplified approach - Django's migration system
        # handles this more comprehensively
        dependency_order = []
        
        # Add models in reverse dependency order
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                dependency_order.append(model)
        
        return reversed(dependency_order)
