#!/usr/bin/env python3
"""
Test script for automated transaction system
"""

import os
import sys
import django
import requests
import json
from decimal import Decimal

# Add the Django project to the Python path
import pathlib
script_dir = pathlib.Path(__file__).parent.absolute()
django_dir = script_dir / 'trendy_web_and_api' / 'trendy'
sys.path.append(str(django_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from wallet.services.automated_transactions import AutomatedTransactionService
from wallet.models import TransactionVerificationCode, UserWallet

User = get_user_model()

class TransactionTester:
    def __init__(self):
        self.base_url = 'http://127.0.0.1:8000'
        self.test_user = None
        self.auth_token = None
        
    def setup_test_user(self):
        """Create or get test user"""
        try:
            self.test_user = User.objects.get(username='testuser')
        except User.DoesNotExist:
            self.test_user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123',
                first_name='Test',
                last_name='User',
                is_email_verified=True
            )
        
        # Ensure user has a wallet
        from wallet.services import WalletService
        wallet = WalletService.get_or_create_wallet(self.test_user)
        print(f"✅ Test user created: {self.test_user.username}")
        print(f"💰 Initial wallet balance: ${wallet.balance}")
        
    def get_auth_token(self):
        """Get authentication token for API calls"""
        # For testing, we'll use the Django service directly
        # In a real app, you'd authenticate via API
        print("🔑 Using direct service calls (bypassing API authentication)")
        
    def test_automated_deposit(self):
        """Test automated deposit with email verification"""
        print("\n🏦 Testing Automated Deposit...")
        
        amount = Decimal('25.00')
        result = AutomatedTransactionService.process_automated_deposit(
            user=self.test_user,
            amount=amount,
            payment_method='paypal'
        )
        
        print(f"📤 Deposit Result: {result}")
        
        if result['success'] and result.get('requires_verification'):
            # Get the verification code
            verification = TransactionVerificationCode.objects.filter(
                user=self.test_user,
                transaction_type='deposit',
                is_used=False
            ).first()
            
            if verification:
                print(f"📧 Verification code generated: {verification.verification_code}")
                
                # Test verification
                verify_result = AutomatedTransactionService.verify_transaction_code(
                    user=self.test_user,
                    transaction_id=verification.transaction_id,
                    verification_code=verification.verification_code,
                    transaction_type='deposit'
                )
                
                print(f"✅ Verification Result: {verify_result}")
                
                # Check updated balance
                wallet = UserWallet.objects.get(user=self.test_user)
                print(f"💰 New wallet balance: ${wallet.balance}")
            else:
                print("❌ No verification code found")
        
        return result
    
    def test_automated_withdrawal(self):
        """Test automated withdrawal with email verification"""
        print("\n💸 Testing Automated Withdrawal...")
        
        amount = Decimal('10.00')
        paypal_email = '<EMAIL>'
        
        result = AutomatedTransactionService.process_automated_withdrawal(
            user=self.test_user,
            amount=amount,
            paypal_email=paypal_email
        )
        
        print(f"📤 Withdrawal Result: {result}")
        
        if result['success'] and result.get('requires_verification'):
            # Get the verification code
            verification = TransactionVerificationCode.objects.filter(
                user=self.test_user,
                transaction_type='withdrawal',
                is_used=False
            ).first()
            
            if verification:
                print(f"📧 Verification code generated: {verification.verification_code}")
                
                # Test verification
                verify_result = AutomatedTransactionService.verify_transaction_code(
                    user=self.test_user,
                    transaction_id=verification.transaction_id,
                    verification_code=verification.verification_code,
                    transaction_type='withdrawal'
                )
                
                print(f"✅ Verification Result: {verify_result}")
                
                # Check updated balance
                wallet = UserWallet.objects.get(user=self.test_user)
                print(f"💰 New wallet balance: ${wallet.balance}")
            else:
                print("❌ No verification code found")
        
        return result
    
    def test_automated_purchase(self):
        """Test automated purchase (small and large amounts)"""
        print("\n🛒 Testing Automated Purchase...")
        
        # Test small purchase (no verification required)
        print("Testing small purchase ($5.00)...")
        small_result = AutomatedTransactionService.process_automated_purchase(
            user=self.test_user,
            amount=Decimal('5.00'),
            item_name='Small Badge',
            description='A small test badge'
        )
        print(f"📤 Small Purchase Result: {small_result}")
        
        # Test large purchase (verification required)
        print("\nTesting large purchase ($15.00)...")
        large_result = AutomatedTransactionService.process_automated_purchase(
            user=self.test_user,
            amount=Decimal('15.00'),
            item_name='Premium Badge',
            description='A premium test badge'
        )
        print(f"📤 Large Purchase Result: {large_result}")
        
        if large_result['success'] and large_result.get('requires_verification'):
            # Get the verification code
            verification = TransactionVerificationCode.objects.filter(
                user=self.test_user,
                transaction_type='purchase',
                is_used=False
            ).first()
            
            if verification:
                print(f"📧 Verification code generated: {verification.verification_code}")
                
                # For large purchases, we'd need to implement the verification completion
                print("💡 Large purchase verification would be completed by user in app")
        
        # Check final balance
        wallet = UserWallet.objects.get(user=self.test_user)
        print(f"💰 Final wallet balance: ${wallet.balance}")
        
        return small_result, large_result
    
    def test_verification_code_expiry(self):
        """Test verification code expiry and security"""
        print("\n🔒 Testing Verification Code Security...")
        
        # Test invalid code
        invalid_result = AutomatedTransactionService.verify_transaction_code(
            user=self.test_user,
            transaction_id='invalid-id',
            verification_code='000000',
            transaction_type='deposit'
        )
        print(f"❌ Invalid Code Result: {invalid_result}")
        
        # Check pending verifications
        pending_codes = TransactionVerificationCode.objects.filter(
            user=self.test_user,
            is_used=False
        )
        print(f"📋 Pending verification codes: {pending_codes.count()}")
        
        for code in pending_codes:
            print(f"   - {code.transaction_type}: {code.verification_code} (expires: {code.expires_at})")
    
    def display_transaction_history(self):
        """Display user's transaction history"""
        print("\n📊 Transaction History...")
        
        wallet = UserWallet.objects.get(user=self.test_user)
        transactions = wallet.transactions.all().order_by('-created_at')[:10]
        
        print(f"💰 Current Balance: ${wallet.balance}")
        print(f"📈 Total Transactions: {transactions.count()}")
        print("\nRecent Transactions:")
        
        for txn in transactions:
            symbol = "+" if txn.transaction_type == 'credit' else "-"
            print(f"   {symbol}${txn.amount} - {txn.purpose} - {txn.status} - {txn.created_at.strftime('%Y-%m-%d %H:%M')}")
    
    def run_all_tests(self):
        """Run all automated transaction tests"""
        print("🚀 Starting Automated Transaction Tests")
        print("=" * 50)
        
        try:
            # Setup
            self.setup_test_user()
            self.get_auth_token()
            
            # Run tests
            self.test_automated_deposit()
            self.test_automated_withdrawal()
            self.test_automated_purchase()
            self.test_verification_code_expiry()
            self.display_transaction_history()
            
            print("\n" + "=" * 50)
            print("✅ All tests completed successfully!")
            print("\n💡 Next steps:")
            print("   1. Start the Django server: python manage.py runserver")
            print("   2. Test the Flutter app with automated transactions")
            print("   3. Check email templates in the console output")
            print("   4. Monitor transaction logs for any issues")
            
        except Exception as e:
            print(f"\n❌ Test failed with error: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    tester = TransactionTester()
    tester.run_all_tests()
