import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:trendy/config/api_config.dart';
import 'package:trendy/models/user.dart';
import 'package:trendy/services/auth_service.dart';

class RoleInfo {
  final int userId;
  final String username;
  final String email;
  final String role;
  final bool isStaff;
  final bool isSuperuser;
  final bool isContentCreator;
  final bool isRegularUser;
  final bool canCreateContent;
  final bool canModerateContent;
  final List<String> groups;
  final Map<String, bool> permissions;

  RoleInfo({
    required this.userId,
    required this.username,
    required this.email,
    required this.role,
    required this.isStaff,
    required this.isSuperuser,
    required this.isContentCreator,
    required this.isRegularUser,
    required this.canCreateContent,
    required this.canModerateContent,
    required this.groups,
    required this.permissions,
  });

  factory RoleInfo.fromJson(Map<String, dynamic> json) {
    return RoleInfo(
      userId: json['user_id'],
      username: json['username'],
      email: json['email'],
      role: json['role'],
      isStaff: json['is_staff'],
      isSuperuser: json['is_superuser'],
      isContentCreator: json['is_content_creator'],
      isRegularUser: json['is_regular_user'],
      canCreateContent: json['can_create_content'],
      canModerateContent: json['can_moderate_content'],
      groups: List<String>.from(json['groups']),
      permissions: Map<String, bool>.from(json['permissions']),
    );
  }

  String get roleDisplayName {
    switch (role) {
      case 'superuser':
        return 'Super Admin';
      case 'admin':
        return 'Admin';
      case 'content_creator':
        return 'Content Creator';
      case 'regular_user':
        return 'User';
      default:
        return 'User';
    }
  }

  String get roleDescription {
    switch (role) {
      case 'superuser':
        return 'Full system access';
      case 'admin':
        return 'Administrative privileges';
      case 'content_creator':
        return 'Can create and publish content';
      case 'regular_user':
        return 'Can engage with content';
      default:
        return 'Basic user';
    }
  }
}

class RoleService {
  static const String _baseUrl = '${ApiConfig.baseUrl}/api/v1/accounts';

  /// Get current user's role information and permissions
  static Future<RoleInfo?> getUserPermissions() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) return null;

      final response = await http.get(
        Uri.parse('$_baseUrl/permissions/'),
        headers: {
          'Authorization': 'Token $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return RoleInfo.fromJson(data['role_info']);
        }
      }
      return null;
    } catch (e) {
      print('Error getting user permissions: $e');
      return null;
    }
  }

  /// Check if current user can create content
  static Future<bool> canCreateContent() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) return false;

      final response = await http.get(
        Uri.parse('$_baseUrl/check-content-permission/'),
        headers: {
          'Authorization': 'Token $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] && data['can_create_content'];
      }
      return false;
    } catch (e) {
      print('Error checking content creation permission: $e');
      return false;
    }
  }

  /// Get all users grouped by role (Admin only)
  static Future<Map<String, List<dynamic>>?> getUsersByRole() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) return null;

      final response = await http.get(
        Uri.parse('$_baseUrl/admin/users/by-role/'),
        headers: {
          'Authorization': 'Token $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          return Map<String, List<dynamic>>.from(data['users_by_role']);
        }
      }
      return null;
    } catch (e) {
      print('Error getting users by role: $e');
      return null;
    }
  }

  /// Promote user to content creator (Admin only)
  static Future<bool> promoteUserToContentCreator(int userId) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) return false;

      final response = await http.post(
        Uri.parse('$_baseUrl/admin/users/$userId/promote/'),
        headers: {
          'Authorization': 'Token $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'];
      }
      return false;
    } catch (e) {
      print('Error promoting user: $e');
      return false;
    }
  }

  /// Demote user to regular user (Admin only)
  static Future<bool> demoteUserToRegular(int userId) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) return false;

      final response = await http.post(
        Uri.parse('$_baseUrl/admin/users/$userId/demote/'),
        headers: {
          'Authorization': 'Token $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'];
      }
      return false;
    } catch (e) {
      print('Error demoting user: $e');
      return false;
    }
  }

  /// Check if user has specific permission
  static bool hasPermission(RoleInfo? roleInfo, String permission) {
    if (roleInfo == null) return false;
    return roleInfo.permissions[permission] ?? false;
  }

  /// Check if user can moderate content
  static bool canModerate(RoleInfo? roleInfo) {
    if (roleInfo == null) return false;
    return roleInfo.canModerateContent || roleInfo.isStaff || roleInfo.isSuperuser;
  }

  /// Check if user is admin
  static bool isAdmin(RoleInfo? roleInfo) {
    if (roleInfo == null) return false;
    return roleInfo.isStaff || roleInfo.isSuperuser;
  }

  /// Get role badge color
  static String getRoleBadgeColor(String role) {
    switch (role) {
      case 'superuser':
        return '#FF6B6B'; // Red
      case 'admin':
        return '#4ECDC4'; // Teal
      case 'content_creator':
        return '#45B7D1'; // Blue
      case 'regular_user':
        return '#96CEB4'; // Green
      default:
        return '#95A5A6'; // Gray
    }
  }

  /// Get role icon
  static String getRoleIcon(String role) {
    switch (role) {
      case 'superuser':
        return '👑';
      case 'admin':
        return '🛡️';
      case 'content_creator':
        return '✍️';
      case 'regular_user':
        return '👤';
      default:
        return '👤';
    }
  }
}
