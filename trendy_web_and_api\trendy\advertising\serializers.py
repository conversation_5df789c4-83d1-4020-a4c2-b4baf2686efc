from rest_framework import serializers
from .models import (
    AdNetwork, AdPlacement, AdImpression, RewardedAd, 
    SponsoredPost, AdSettings, AdNetworkPlacement
)


class AdNetworkSerializer(serializers.ModelSerializer):
    """Serializer for AdNetwork model"""
    
    class Meta:
        model = AdNetwork
        fields = [
            'id', 'name', 'network_type', 'is_active', 'priority',
            'revenue_share_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AdPlacementSerializer(serializers.ModelSerializer):
    """Serializer for AdPlacement model"""
    
    class Meta:
        model = AdPlacement
        fields = [
            'id', 'name', 'location', 'placement_type', 'is_active',
            'frequency', 'max_per_session', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AdNetworkPlacementSerializer(serializers.ModelSerializer):
    """Serializer for AdNetworkPlacement model"""
    ad_network = AdNetworkSerializer(read_only=True)
    ad_placement = AdPlacementSerializer(read_only=True)
    
    class Meta:
        model = AdNetworkPlacement
        fields = [
            'id', 'ad_network', 'ad_placement', 'ad_unit_id', 'weight',
            'total_requests', 'total_impressions', 'total_clicks', 
            'total_revenue', 'is_active', 'fill_rate', 'ctr'
        ]
        read_only_fields = [
            'total_requests', 'total_impressions', 'total_clicks', 
            'total_revenue', 'fill_rate', 'ctr'
        ]


class RewardedAdSerializer(serializers.ModelSerializer):
    """Serializer for RewardedAd model"""
    
    class Meta:
        model = RewardedAd
        fields = [
            'id', 'name', 'description', 'reward_type', 'points_reward',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class AdImpressionSerializer(serializers.ModelSerializer):
    """Serializer for AdImpression model"""
    ad_placement = AdPlacementSerializer(read_only=True)
    user = serializers.StringRelatedField(read_only=True)
    
    class Meta:
        model = AdImpression
        fields = [
            'id', 'user', 'ad_placement', 'impression_type', 'session_id',
            'points_awarded', 'created_at'
        ]
        read_only_fields = ['created_at']


class SponsoredPostSerializer(serializers.ModelSerializer):
    """Serializer for SponsoredPost model"""
    
    class Meta:
        model = SponsoredPost
        fields = [
            'id', 'title', 'sponsor_name', 'sponsor_type', 'status',
            'budget', 'total_spent', 'total_impressions', 'total_clicks',
            'start_date', 'end_date', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'total_spent', 'total_impressions', 'total_clicks',
            'created_at', 'updated_at'
        ]


class AdSettingsSerializer(serializers.ModelSerializer):
    """Serializer for AdSettings model"""
    
    class Meta:
        model = AdSettings
        fields = [
            'id', 'ads_enabled', 'show_ads_to_premium', 'max_ads_per_session',
            'min_time_between_ads', 'rewarded_ads_enabled', 'max_rewarded_ads_per_day',
            'rewarded_ad_cooldown', 'revenue_share_with_users', 'minimum_payout_threshold',
            'sponsored_posts_enabled', 'sponsored_post_frequency', 'enable_user_targeting',
            'enable_behavioral_targeting', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


# Simplified serializers for Flutter app
class SimpleAdPlacementSerializer(serializers.ModelSerializer):
    """Simplified serializer for Flutter app"""
    
    class Meta:
        model = AdPlacement
        fields = ['id', 'name', 'location', 'placement_type', 'points_reward']


class SimpleRewardedAdSerializer(serializers.ModelSerializer):
    """Simplified serializer for Flutter app"""
    
    class Meta:
        model = RewardedAd
        fields = ['id', 'name', 'points_reward', 'reward_type']


class SimpleSponsoredContentSerializer(serializers.ModelSerializer):
    """Simplified serializer for Flutter app"""
    
    class Meta:
        model = SponsoredPost
        fields = [
            'id', 'title', 'sponsor_name', 'sponsor_type', 'status'
        ]


class AdAnalyticsSerializer(serializers.Serializer):
    """Serializer for ad analytics data"""
    total_impressions = serializers.IntegerField()
    total_clicks = serializers.IntegerField()
    total_points_earned = serializers.IntegerField()
    daily_points_earned = serializers.IntegerField()
    weekly_points_earned = serializers.IntegerField()
    total_ads_watched = serializers.IntegerField()
    click_through_rate = serializers.FloatField()


class AdAvailabilitySerializer(serializers.Serializer):
    """Serializer for ad availability data"""
    can_watch_ads = serializers.BooleanField()
    daily_points_earned = serializers.IntegerField()
    daily_limit = serializers.IntegerField()
    remaining_points = serializers.IntegerField()
    ads_enabled = serializers.BooleanField()
    rewarded_ads_enabled = serializers.BooleanField()


class RewardedAdSessionSerializer(serializers.Serializer):
    """Serializer for rewarded ad session data"""
    session_id = serializers.CharField()
    placement = SimpleAdPlacementSerializer()
    points_offered = serializers.IntegerField()
    points_awarded = serializers.IntegerField()
    reward_claimed = serializers.BooleanField()
    status = serializers.CharField()
    started_at = serializers.DateTimeField()
    completed_at = serializers.DateTimeField(allow_null=True)
