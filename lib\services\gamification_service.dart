import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'platform_storage_service.dart';
import '../config/api_config.dart';
import '../models/gamification.dart';
import 'performance_service.dart';

class GamificationService {
  late final Dio _dio;
  // Using SharedPreferences instead of secure storage

  GamificationService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      sendTimeout: ApiConfig.sendTimeout,
      validateStatus: (status) {
        return status != null && status < 500;
      },
    ));

    // Add interceptor to automatically include authentication headers
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Token $token';
        }
        handler.next(options);
      },
    ));
  }

  Future<String?> _getToken() async {
    return await PlatformStorageService.getSecureData('token');
  }

  // Badges
  Future<List<Badge>> getBadges() async {
    try {
      final response = await _dio.get('/api/v1/gamification/badges/');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => Badge.fromJson(json)).toList();
      }

      throw Exception('Failed to load badges');
    } catch (e) {
      print('Error fetching badges: $e');
      rethrow;
    }
  }

  Future<List<UserBadge>> getUserBadges() async {
    try {
      final response = await _dio.get('/api/v1/gamification/user/badges/');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => UserBadge.fromJson(json)).toList();
      }

      throw Exception('Failed to load user badges');
    } catch (e) {
      print('Error fetching user badges: $e');
      rethrow;
    }
  }

  // Challenges
  Future<List<Challenge>> getChallenges({
    String? type,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;

      final response = await _dio.get(
        '/api/v1/gamification/challenges/',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => Challenge.fromJson(json)).toList();
      }

      throw Exception('Failed to load challenges');
    } catch (e) {
      print('Error fetching challenges: $e');
      rethrow;
    }
  }

  Future<ChallengeParticipation> joinChallenge(int challengeId) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/challenges/$challengeId/join/',
      );

      if (response.statusCode == 201) {
        return ChallengeParticipation.fromJson(response.data['participation']);
      }

      throw Exception('Failed to join challenge');
    } catch (e) {
      print('Error joining challenge: $e');
      rethrow;
    }
  }

  Future<ChallengeParticipation> updateChallengeProgress(
    int challengeId,
    Map<String, dynamic> progressData,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/gamification/challenges/$challengeId/progress/',
        data: {
          'progress_data': progressData,
        },
      );

      if (response.statusCode == 200) {
        return ChallengeParticipation.fromJson(response.data['participation']);
      }

      throw Exception('Failed to update challenge progress');
    } catch (e) {
      print('Error updating challenge progress: $e');
      rethrow;
    }
  }

  // User Profile and Level
  Future<GamificationUserProfile> getUserProfile() async {
    try {
      final response = await _dio.get('/api/v1/gamification/user/profile/');

      if (response.statusCode == 200) {
        return GamificationUserProfile.fromJson(response.data);
      }

      throw Exception('Failed to load user profile');
    } catch (e) {
      print('Error fetching user profile: $e');
      rethrow;
    }
  }

  // Unified Points Data (Performance Optimized)
  Future<Map<String, dynamic>> getUnifiedUserPoints() async {
    try {
      final response = await _dio.get('/api/v1/gamification/points/unified/');

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      }

      throw Exception('Failed to load unified points data');
    } catch (e) {
      print('Error fetching unified points: $e');
      rethrow;
    }
  }

  Future<UserLevel> getUserLevel() async {
    try {
      final response = await _dio.get('/api/v1/gamification/user/level/');

      if (response.statusCode == 200) {
        return UserLevel.fromJson(response.data);
      }

      throw Exception('Failed to load user level');
    } catch (e) {
      print('Error fetching user level: $e');
      rethrow;
    }
  }

  Future<List<PointTransaction>> getUserTransactions({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/user/transactions/',
        queryParameters: {
          'limit': limit,
          'offset': offset,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => PointTransaction.fromJson(json)).toList();
      }

      throw Exception('Failed to load transactions');
    } catch (e) {
      print('Error fetching transactions: $e');
      rethrow;
    }
  }

  // Leaderboard
  Future<List<LeaderboardEntry>> getLeaderboard({int limit = 50}) async {
    try {
      final response = await _dio.get(
        '/api/v1/gamification/leaderboard/',
        queryParameters: {
          'limit': limit,
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => LeaderboardEntry.fromJson(json)).toList();
      }

      throw Exception('Failed to load leaderboard');
    } catch (e) {
      print('Error fetching leaderboard: $e');
      rethrow;
    }
  }

  // Helper methods
  String getBadgeRarityColor(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'common':
        return '#9E9E9E'; // Grey
      case 'uncommon':
        return '#4CAF50'; // Green
      case 'rare':
        return '#2196F3'; // Blue
      case 'epic':
        return '#9C27B0'; // Purple
      case 'legendary':
        return '#FF9800'; // Orange/Gold
      default:
        return '#9E9E9E';
    }
  }

  String getChallengeDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return '#4CAF50'; // Green
      case 'medium':
        return '#FF9800'; // Orange
      case 'hard':
        return '#F44336'; // Red
      case 'expert':
        return '#9C27B0'; // Purple
      default:
        return '#9E9E9E';
    }
  }

  String formatPoints(int points) {
    if (points >= 1000000) {
      return '${(points / 1000000).toStringAsFixed(1)}M';
    } else if (points >= 1000) {
      return '${(points / 1000).toStringAsFixed(1)}K';
    }
    return points.toString();
  }

  String getStreakEmoji(int streak) {
    if (streak >= 30) return '🔥🔥🔥';
    if (streak >= 14) return '🔥🔥';
    if (streak >= 7) return '🔥';
    if (streak >= 3) return '⚡';
    return '✨';
  }

  String getLevelTitle(int level) {
    if (level >= 50) return 'Grandmaster';
    if (level >= 40) return 'Master';
    if (level >= 30) return 'Expert';
    if (level >= 20) return 'Advanced';
    if (level >= 10) return 'Intermediate';
    if (level >= 5) return 'Novice';
    return 'Beginner';
  }

  double calculateChallengeProgress(Challenge challenge) {
    if (challenge.userParticipation != null) {
      return challenge.userParticipation!.completionPercentage;
    }
    return 0.0;
  }

  bool isChallengeCompleted(Challenge challenge) {
    return challenge.userParticipation?.isCompleted ?? false;
  }

  bool isChallengeJoined(Challenge challenge) {
    return challenge.userParticipation != null;
  }

  String formatDuration(DateTime startDate, DateTime endDate) {
    final duration = endDate.difference(startDate);

    if (duration.inDays > 0) {
      return '${duration.inDays} day${duration.inDays == 1 ? '' : 's'}';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} hour${duration.inHours == 1 ? '' : 's'}';
    } else {
      return '${duration.inMinutes} minute${duration.inMinutes == 1 ? '' : 's'}';
    }
  }

  String getTimeRemaining(DateTime endDate) {
    final now = DateTime.now();
    final remaining = endDate.difference(now);

    if (remaining.isNegative) {
      return 'Expired';
    }

    if (remaining.inDays > 0) {
      return '${remaining.inDays}d ${remaining.inHours % 24}h left';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours}h ${remaining.inMinutes % 60}m left';
    } else {
      return '${remaining.inMinutes}m left';
    }
  }

  String getBadgeDescription(Badge badge) {
    final requirements = badge.requirements;

    if (requirements.containsKey('min_posts_read')) {
      return 'Read ${requirements['min_posts_read']} posts';
    } else if (requirements.containsKey('min_posts_written')) {
      return 'Write ${requirements['min_posts_written']} posts';
    } else if (requirements.containsKey('min_comments')) {
      return 'Make ${requirements['min_comments']} comments';
    } else if (requirements.containsKey('min_level')) {
      return 'Reach level ${requirements['min_level']}';
    } else if (requirements.containsKey('min_reading_streak')) {
      return 'Maintain ${requirements['min_reading_streak']}-day reading streak';
    }

    return badge.description;
  }
}
