import 'package:flutter_test/flutter_test.dart';
import 'package:trendy/main.dart' as app;
import 'package:trendy/services/api_service.dart';
import 'package:trendy/services/gamification_service.dart';
import 'package:trendy/models/post.dart';
import 'package:trendy/models/gamification.dart';
import 'package:trendy/models/paginated_response.dart';

void main() {
  group('Trendy App Integration Tests', () {
    late ApiService apiService;
    late GamificationService gamificationService;

    setUpAll(() {
      apiService = ApiService();
      gamificationService = GamificationService();
    });

    testWidgets('App launches successfully', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Verify the app launches and shows the home screen
      expect(find.text('Trendy'), findsOneWidget);
    });

    test('API Service - Fetch Posts', () async {
      try {
        final response = await apiService.getPosts();
        expect(response.results, isNotEmpty);

        final firstPost = response.results.first;
        expect(firstPost.title, isNotEmpty);
        expect(firstPost.content, isNotEmpty);
        expect(firstPost.author, isNotNull);

        print('✅ Posts API test passed - Found ${response.results.length} posts');
      } catch (e) {
        fail('Posts API test failed: $e');
      }
    });

    test('API Service - Fetch Categories', () async {
      try {
        final response = await apiService.getCategories();
        expect(response.results, isNotEmpty);

        final firstCategory = response.results.first;
        expect(firstCategory.name, isNotEmpty);
        expect(firstCategory.slug, isNotEmpty);

        print('✅ Categories API test passed - Found ${response.results.length} categories');
      } catch (e) {
        fail('Categories API test failed: $e');
      }
    });

    test('Gamification Service - Fetch Badges', () async {
      try {
        final badges = await gamificationService.getBadges();
        expect(badges, isNotEmpty);
        
        final firstBadge = badges.first;
        expect(firstBadge.name, isNotEmpty);
        expect(firstBadge.description, isNotEmpty);
        expect(firstBadge.badgeType, isNotEmpty);
        expect(firstBadge.rarity, isNotEmpty);
        expect(firstBadge.pointsReward, greaterThan(0));
        
        print('✅ Badges API test passed - Found ${badges.length} badges');
      } catch (e) {
        fail('Badges API test failed: $e');
      }
    });

    test('Gamification Service - Fetch Challenges', () async {
      try {
        final challenges = await gamificationService.getChallenges();
        expect(challenges, isNotEmpty);
        
        final firstChallenge = challenges.first;
        expect(firstChallenge.title, isNotEmpty);
        expect(firstChallenge.description, isNotEmpty);
        expect(firstChallenge.challengeType, isNotEmpty);
        expect(firstChallenge.difficulty, isNotEmpty);
        expect(firstChallenge.pointsReward, greaterThan(0));
        
        print('✅ Challenges API test passed - Found ${challenges.length} challenges');
      } catch (e) {
        fail('Challenges API test failed: $e');
      }
    });

    test('Gamification Service - Fetch Leaderboard', () async {
      try {
        final leaderboard = await gamificationService.getLeaderboard();
        expect(leaderboard, isNotNull);
        
        print('✅ Leaderboard API test passed');
      } catch (e) {
        fail('Leaderboard API test failed: $e');
      }
    });

    test('API Service - Search Functionality', () async {
      try {
        final searchResults = await apiService.search('test');
        expect(searchResults, isNotNull);
        expect(searchResults.posts, isNotNull);
        expect(searchResults.users, isNotNull);

        print('✅ Search API test passed');
      } catch (e) {
        // Search might not be implemented yet, so just log the attempt
        print('⚠️ Search API test skipped - Feature may not be implemented: $e');
      }
    });

    test('API Service - Recommendations', () async {
      try {
        final recommendations = await apiService.getRecommendations();
        expect(recommendations, isNotNull);

        print('✅ Recommendations API test passed - Found ${recommendations.length} recommendations');
      } catch (e) {
        // Recommendations might not be implemented yet, so just log the attempt
        print('⚠️ Recommendations API test skipped - Feature may not be implemented: $e');
      }
    });

    test('Error Handling - Invalid Endpoint', () async {
      try {
        // Test error handling with invalid endpoint
        final response = await apiService.get('/invalid-endpoint/');
        expect(response.isSuccess, false);
        expect(response.error, isNotNull);

        print('✅ Error handling test passed');
      } catch (e) {
        // This is expected for invalid endpoints
        print('✅ Error handling test passed - Exception caught as expected');
      }
    });

    test('Performance Test - Multiple API Calls', () async {
      final stopwatch = Stopwatch()..start();

      try {
        // Make multiple concurrent API calls
        final futures = [
          apiService.getPosts(),
          apiService.getCategories(),
          gamificationService.getBadges(),
          gamificationService.getChallenges(),
        ];

        final results = await Future.wait(futures);
        stopwatch.stop();

        // Verify all calls succeeded
        expect((results[0] as PaginatedResponse).results, isNotEmpty); // Posts
        expect((results[1] as PaginatedResponse).results, isNotEmpty); // Categories
        expect(results[2], isNotEmpty); // Badges
        expect(results[3], isNotEmpty); // Challenges

        // Performance check - should complete within 10 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));

        print('✅ Performance test passed - Completed in ${stopwatch.elapsedMilliseconds}ms');
      } catch (e) {
        fail('Performance test failed: $e');
      }
    });

    test('Data Integrity - Post Model Validation', () async {
      try {
        final response = await apiService.getPosts();

        final posts = response.results;
        for (final post in posts) {
          // Validate required fields
          expect(post.id, greaterThan(0));
          expect(post.title, isNotEmpty);
          expect(post.content, isNotEmpty);
          expect(post.author, isNotNull);
          expect(post.author.username, isNotEmpty);
          expect(post.createdAt, isNotNull);
          expect(post.status, equals('published'));

          // Validate optional fields don't break the model
          expect(post.category, isNotNull);
          expect(post.tags, isNotNull);
          expect(post.mediaItems, isNotNull);
        }

        print('✅ Data integrity test passed - All post models are valid');
      } catch (e) {
        fail('Data integrity test failed: $e');
      }
    });

    test('Gamification Data Integrity', () async {
      try {
        final badges = await gamificationService.getBadges();
        final challenges = await gamificationService.getChallenges();
        
        // Validate badges
        for (final badge in badges) {
          expect(badge.id, greaterThan(0));
          expect(badge.name, isNotEmpty);
          expect(badge.description, isNotEmpty);
          expect(['reading', 'writing', 'engagement', 'milestone'], contains(badge.badgeType));
          expect(['common', 'uncommon', 'rare', 'epic', 'legendary'], contains(badge.rarity));
          expect(badge.pointsReward, greaterThan(0));
        }
        
        // Validate challenges
        for (final challenge in challenges) {
          expect(challenge.id, greaterThan(0));
          expect(challenge.title, isNotEmpty);
          expect(challenge.description, isNotEmpty);
          expect(['reading', 'writing', 'engagement'], contains(challenge.challengeType));
          expect(['easy', 'medium', 'hard', 'expert'], contains(challenge.difficulty));
          expect(challenge.pointsReward, greaterThan(0));
          expect(challenge.startDate, isNotNull);
          expect(challenge.endDate, isNotNull);
        }
        
        print('✅ Gamification data integrity test passed');
      } catch (e) {
        fail('Gamification data integrity test failed: $e');
      }
    });
  });
}
