// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'code_playground.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CodePlaygroundImpl _$$CodePlaygroundImplFromJson(Map<String, dynamic> json) =>
    _$CodePlaygroundImpl(
      id: (json['id'] as num).toInt(),
      language: json['language'] as String? ?? 'python',
      initialCode: json['initial_code'] as String? ?? '',
      expectedOutput: json['expected_output'] as String? ?? '',
      instructions: json['instructions'] as String? ?? '',
      isEditable: json['is_editable'] as bool? ?? true,
      showLineNumbers: json['show_line_numbers'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
    );

Map<String, dynamic> _$$CodePlaygroundImplToJson(
        _$CodePlaygroundImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'language': instance.language,
      'initial_code': instance.initialCode,
      'expected_output': instance.expectedOutput,
      'instructions': instance.instructions,
      'is_editable': instance.isEditable,
      'show_line_numbers': instance.showLineNumbers,
      'created_at': instance.createdAt.toIso8601String(),
    };
