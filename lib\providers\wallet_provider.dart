import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/wallet_models.dart';
import '../services/api_service.dart';
import 'auth_provider.dart';

final walletProvider =
    StateNotifierProvider<WalletNotifier, WalletState>((ref) {
  return WalletNotifier(ref.read(apiServiceProvider));
});

class WalletNotifier extends StateNotifier<WalletState> {
  final ApiService _apiService;

  WalletNotifier(this._apiService) : super(const WalletState());

  Future<void> loadWalletOverview() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.getWalletOverview();
      print('🔍 Wallet overview response: $response');

      if (response['success'] == true) {
        final data = response['data'];
        print('🔍 Wallet overview data: $data');

        if (data != null) {
          try {
            final overview = WalletOverview.fromJson(data);
            print('✅ Wallet overview parsed successfully');
            state = state.copyWith(
              overview: overview,
              isLoading: false,
            );
          } catch (parseError) {
            print('❌ Error parsing wallet overview: $parseError');
            print('📋 Data structure: ${data.runtimeType}');
            print(
                '📋 Data keys: ${data is Map ? data.keys.toList() : 'Not a map'}');

            state = state.copyWith(
              isLoading: false,
              error: 'Error parsing wallet data: $parseError',
            );
          }
        } else {
          state = state.copyWith(
            isLoading: false,
            error: 'No wallet data received from server',
          );
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? 'Failed to load wallet overview',
        );
      }
    } catch (e) {
      print('❌ Wallet overview error: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Error loading wallet overview: $e',
      );
    }
  }

  Future<void> loadWalletSettings() async {
    try {
      final response = await _apiService.getWalletSettings();
      print('🔍 Wallet settings response: $response');

      if (response['success'] == true) {
        print('🔍 Settings data: ${response['settings']}');
        final settings = WalletSettings.fromBackendJson(response['settings']);
        print('✅ Wallet settings parsed successfully: $settings');
        state = state.copyWith(settings: settings);
      } else {
        print('❌ API response success was false');
      }
    } catch (e, stackTrace) {
      print('Error loading wallet settings: $e');
      print('Stack trace: $stackTrace');
    }
  }

  Future<Map<String, dynamic>> createDeposit(
    double amount,
    String paymentMethod, {
    String? paypalEmail,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.createWalletDeposit(
        amount,
        paymentMethod,
        paypalEmail: paypalEmail,
      );

      state = state.copyWith(isLoading: false);

      if (response['success'] == true) {
        return {
          'success': true,
          'depositRequest': WalletDepositRequest.fromJson(response),
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to create deposit',
        };
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return {
        'success': false,
        'message': 'Error creating deposit: $e',
      };
    }
  }

  Future<Map<String, dynamic>> confirmDeposit(
      String depositRequestId, String transactionId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _apiService.confirmWalletDeposit(
          depositRequestId, transactionId);

      if (response['success'] == true) {
        // Reload wallet overview to get updated balance
        await loadWalletOverview();

        return {
          'success': true,
          'message': response['message'],
          'newBalance': response['new_balance'],
        };
      } else {
        state = state.copyWith(isLoading: false);
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to confirm deposit',
        };
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return {
        'success': false,
        'message': 'Error confirming deposit: $e',
      };
    }
  }

  Future<Map<String, dynamic>> createWithdrawal(
      double amount, String paypalEmail) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response =
          await _apiService.createWalletWithdrawal(amount, paypalEmail);

      state = state.copyWith(isLoading: false);

      if (response['success'] == true) {
        // Reload wallet overview to reflect any changes
        await loadWalletOverview();

        return {
          'success': true,
          'withdrawalRequest': WalletWithdrawalRequest.fromJson(response),
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to create withdrawal',
        };
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return {
        'success': false,
        'message': 'Error creating withdrawal: $e',
      };
    }
  }

  Future<Map<String, dynamic>> spendFromWallet(
      double amount, String purpose, String description) async {
    try {
      final response =
          await _apiService.spendFromWallet(amount, purpose, description);

      if (response['success'] == true) {
        // Reload wallet overview to get updated balance
        await loadWalletOverview();

        return {
          'success': true,
          'result': WalletSpendResult.fromJson(response),
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to spend from wallet',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Error spending from wallet: $e',
      };
    }
  }

  Future<void> loadTransactionHistory({int limit = 20, int offset = 0}) async {
    try {
      if (offset == 0) {
        state = state.copyWith(isLoading: true, error: null);
      }

      final response =
          await _apiService.getWalletTransactions(limit: limit, offset: offset);

      if (response['success'] == true) {
        final data = response['data'];
        final transactionsList = data['transactions'] as List?;
        final newTransactions = (transactionsList ?? [])
            .map((json) => WalletTransaction.fromJson(json))
            .toList();

        List<WalletTransaction> allTransactions;
        if (offset == 0) {
          allTransactions = newTransactions;
        } else {
          allTransactions = [...state.transactions, ...newTransactions];
        }

        state = state.copyWith(
          transactions: allTransactions,
          hasMoreTransactions: data['has_more'] ?? false,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? 'Failed to load transaction history',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error loading transaction history: $e',
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const WalletState();
  }
}
