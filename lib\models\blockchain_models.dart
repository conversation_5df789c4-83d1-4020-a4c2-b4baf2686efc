import 'package:freezed_annotation/freezed_annotation.dart';

part 'blockchain_models.freezed.dart';
part 'blockchain_models.g.dart';

@freezed
class BlockchainWallet with _$BlockchainWallet {
  const factory BlockchainWallet({
    required String address,
    required String network,
    required bool isPrimary,
    required DateTime createdAt,
    required List<TokenBalance> balances,
    @Default(false) bool isActive,
    DateTime? activatedAt,
  }) = _BlockchainWallet;

  factory BlockchainWallet.fromJson(Map<String, dynamic> json) =>
      _$BlockchainWalletFromJson(json);
}

@freezed
class TokenBalance with _$TokenBalance {
  const factory TokenBalance({
    required String contractName,
    required String contractAddress,
    required String balance,
    required String stakedBalance,
    required String totalBalance,
  }) = _TokenBalance;

  factory TokenBalance.fromJson(Map<String, dynamic> json) =>
      _$TokenBalanceFromJson(json);
}

@freezed
class NFTAsset with _$NFTAsset {
  const factory NFTAsset({
    required int id,
    required int tokenId,
    required String name,
    required String description,
    required String imageUrl,
    required int rarity,
    required String rarityDisplay,
    required String contractAddress,
    required String network,
    required DateTime mintedAt,
    required Map<String, dynamic> attributes,
  }) = _NFTAsset;

  factory NFTAsset.fromJson(Map<String, dynamic> json) =>
      _$NFTAssetFromJson(json);
}

@freezed
class StakingPool with _$StakingPool {
  const factory StakingPool({
    required int id,
    required String name,
    required String description,
    required String apyPercentage,
    required String minimumStake,
    String? maximumStake,
    required String totalStaked,
    required int activeStakers,
    required DateTime startDate,
    DateTime? endDate,
  }) = _StakingPool;

  factory StakingPool.fromJson(Map<String, dynamic> json) =>
      _$StakingPoolFromJson(json);
}

@freezed
class UserStake with _$UserStake {
  const factory UserStake({
    required int id,
    required String poolName,
    required String amountStaked,
    required String rewardsEarned,
    required String pendingRewards,
    required String apyPercentage,
    required DateTime stakedAt,
  }) = _UserStake;

  factory UserStake.fromJson(Map<String, dynamic> json) =>
      _$UserStakeFromJson(json);
}

@freezed
class BlockchainTransaction with _$BlockchainTransaction {
  const factory BlockchainTransaction({
    required String id,
    required String type,
    required String status,
    required String? amount,
    required String? txHash,
    required DateTime createdAt,
    String? description,
  }) = _BlockchainTransaction;

  factory BlockchainTransaction.fromJson(Map<String, dynamic> json) =>
      _$BlockchainTransactionFromJson(json);
}

@freezed
class AchievementNotification with _$AchievementNotification {
  const factory AchievementNotification({
    required String id,
    required String name,
    required String description,
    required int rarity,
    required String imageUrl,
    required Map<String, dynamic> rewards,
    required DateTime unlockedAt,
    @Default(false) bool isRead,
  }) = _AchievementNotification;

  factory AchievementNotification.fromJson(Map<String, dynamic> json) =>
      _$AchievementNotificationFromJson(json);
}

@freezed
class BlockchainApiResponse with _$BlockchainApiResponse {
  const factory BlockchainApiResponse({
    required bool success,
    required String message,
    Map<String, dynamic>? data,
  }) = _BlockchainApiResponse;

  factory BlockchainApiResponse.fromJson(Map<String, dynamic> json) =>
      _$BlockchainApiResponseFromJson(json);
}

// Enums for blockchain operations
enum NFTRarity {
  common(1, 'Common', 0xFF6C757D),
  uncommon(2, 'Uncommon', 0xFF28A745),
  rare(3, 'Rare', 0xFF007BFF),
  epic(4, 'Epic', 0xFF6F42C1),
  legendary(5, 'Legendary', 0xFFFFD700);

  const NFTRarity(this.value, this.displayName, this.color);

  final int value;
  final String displayName;
  final int color;

  static NFTRarity fromValue(int value) {
    return NFTRarity.values.firstWhere(
      (rarity) => rarity.value == value,
      orElse: () => NFTRarity.common,
    );
  }
}

enum TransactionType {
  tokenReward('token_reward', 'Token Reward', '🪙'),
  nftMint('nft_mint', 'NFT Mint', '🖼️'),
  tokenTransfer('token_transfer', 'Token Transfer', '💸'),
  stakingDeposit('staking_deposit', 'Staking Deposit', '🏊'),
  stakingWithdraw('staking_withdraw', 'Staking Withdraw', '💰'),
  marketplaceBuy('marketplace_buy', 'Marketplace Purchase', '🛒'),
  marketplaceSell('marketplace_sell', 'Marketplace Sale', '💵');

  const TransactionType(this.value, this.displayName, this.icon);

  final String value;
  final String displayName;
  final String icon;

  static TransactionType fromValue(String value) {
    return TransactionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TransactionType.tokenReward,
    );
  }
}

enum TransactionStatus {
  pending('pending', 'Pending', 0xFFFFC107),
  confirmed('confirmed', 'Confirmed', 0xFF28A745),
  failed('failed', 'Failed', 0xFFDC3545),
  cancelled('cancelled', 'Cancelled', 0xFF6C757D);

  const TransactionStatus(this.value, this.displayName, this.color);

  final String value;
  final String displayName;
  final int color;

  static TransactionStatus fromValue(String value) {
    return TransactionStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TransactionStatus.pending,
    );
  }
}

// Helper extensions
extension NFTAssetExtension on NFTAsset {
  NFTRarity get rarityEnum => NFTRarity.fromValue(rarity);
  
  String get rarityIcon {
    switch (rarityEnum) {
      case NFTRarity.common:
        return '⚪';
      case NFTRarity.uncommon:
        return '🟢';
      case NFTRarity.rare:
        return '🔵';
      case NFTRarity.epic:
        return '🟣';
      case NFTRarity.legendary:
        return '🟡';
    }
  }
}

extension BlockchainTransactionExtension on BlockchainTransaction {
  TransactionType get typeEnum => TransactionType.fromValue(type);
  TransactionStatus get statusEnum => TransactionStatus.fromValue(status);
  
  String get displayAmount {
    if (amount == null) return '';
    final value = double.tryParse(amount!) ?? 0.0;
    return value.toStringAsFixed(2);
  }
  
  String get shortTxHash {
    if (txHash == null || txHash!.length < 10) return txHash ?? '';
    return '${txHash!.substring(0, 6)}...${txHash!.substring(txHash!.length - 4)}';
  }
}
