"""
Enhanced Email Service for Trendy App
Handles all email verification and notification sending
"""

import random
import string
from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class EmailService:
    """Centralized email service for all email operations"""
    
    @staticmethod
    def generate_verification_code(length=6):
        """Generate a random verification code"""
        return ''.join(random.choices(string.digits, k=length))
    
    @staticmethod
    def send_account_verification_email(user):
        """Send account email verification email"""
        try:
            # Don't send to admin users
            if user.is_staff or user.is_superuser:
                return True
                
            subject = '🎉 Welcome to Trendy! Verify Your Email'
            
            # Create HTML email content
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                    .button {{ display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Welcome to Trendy!</h1>
                        <p>You're one step away from joining our amazing community</p>
                    </div>
                    <div class="content">
                        <h2>Hi {user.get_full_name() or user.username}!</h2>
                        <p>Thank you for joining Trendy! We're excited to have you as part of our community.</p>
                        
                        <p>To complete your registration and start earning points, please verify your email address:</p>
                        
                        <div style="text-align: center;">
                            <a href="{settings.FRONTEND_URL}/verify-email/{user.email_verification_token}" class="button">
                                ✅ Verify My Email
                            </a>
                        </div>
                        
                        <p><strong>What happens after verification?</strong></p>
                        <ul>
                            <li>🎯 Start earning points for activities</li>
                            <li>💰 Access PayPal rewards</li>
                            <li>🏆 Unlock achievements and badges</li>
                            <li>👥 Connect with the community</li>
                        </ul>
                        
                        <p><em>This verification link will expire in 24 hours.</em></p>
                        
                        <p>If you didn't create this account, please ignore this email.</p>
                    </div>
                    <div class="footer">
                        <p>Best regards,<br>The Trendy Team</p>
                        <p><small>This email was sent to {user.email}</small></p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Plain text version
            text_content = f"""
            Hi {user.get_full_name() or user.username}!
            
            Welcome to Trendy! Please verify your email address by clicking the link below:
            
            {settings.FRONTEND_URL}/verify-email/{user.email_verification_token}
            
            This link will expire in 24 hours.
            
            Best regards,
            The Trendy Team
            """
            
            # Send email
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            # Update sent timestamp
            user.email_verification_sent_at = timezone.now()
            user.save(update_fields=['email_verification_sent_at'])
            
            logger.info(f"Account verification email sent to {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send account verification email to {user.email}: {str(e)}")
            return False
    
    @staticmethod
    def send_paypal_verification_email(user, paypal_email, verification_code):
        """Send PayPal email verification email"""
        try:
            subject = '🔐 Verify Your PayPal Email for Trendy Rewards'
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #0070ba 0%, #003087 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                    .code-box {{ background: #fff; border: 2px dashed #0070ba; padding: 20px; text-align: center; margin: 20px 0; border-radius: 10px; }}
                    .code {{ font-size: 32px; font-weight: bold; color: #0070ba; letter-spacing: 5px; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>💰 PayPal Verification</h1>
                        <p>Secure your reward payments</p>
                    </div>
                    <div class="content">
                        <h2>Hi {user.get_full_name() or user.username}!</h2>
                        <p>You've added <strong>{paypal_email}</strong> as your PayPal email for receiving rewards.</p>
                        
                        <p>To verify this email address and enable reward payments, please use this verification code:</p>
                        
                        <div class="code-box">
                            <div class="code">{verification_code}</div>
                        </div>
                        
                        <p><strong>Why verify your PayPal email?</strong></p>
                        <ul>
                            <li>🔒 Secure your reward payments</li>
                            <li>💸 Enable automatic PayPal payouts</li>
                            <li>✅ Confirm you own this email address</li>
                            <li>🚀 Faster reward processing</li>
                        </ul>
                        
                        <p><em>This verification code will expire in 15 minutes.</em></p>
                        
                        <p>If you didn't request this verification, please ignore this email.</p>
                    </div>
                    <div class="footer">
                        <p>Best regards,<br>The Trendy Team</p>
                        <p><small>This email was sent to {paypal_email}</small></p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            text_content = f"""
            Hi {user.get_full_name() or user.username}!
            
            You've added {paypal_email} as your PayPal email for receiving rewards.
            
            Verification Code: {verification_code}
            
            Enter this code in the app to verify your PayPal email.
            This code will expire in 15 minutes.
            
            Best regards,
            The Trendy Team
            """
            
            # Send to PayPal email address
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[paypal_email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            logger.info(f"PayPal verification email sent to {paypal_email} for user {user.username}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send PayPal verification email to {paypal_email}: {str(e)}")
            return False
    
    @staticmethod
    def send_reward_notification(user, reward_type, amount, status):
        """Send reward-related notification emails"""
        try:
            if status == 'claimed':
                subject = f'🎉 Reward Claimed - ${amount}'
                message_type = 'claimed'
            elif status == 'approved':
                subject = f'✅ Reward Approved - ${amount}'
                message_type = 'approved'
            elif status == 'paid':
                subject = f'💰 Payment Sent - ${amount}'
                message_type = 'paid'
            else:
                return False
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                    .amount {{ font-size: 36px; font-weight: bold; color: #28a745; text-align: center; margin: 20px 0; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Reward Update</h1>
                        <p>Your {reward_type} reward</p>
                    </div>
                    <div class="content">
                        <h2>Hi {user.get_full_name() or user.username}!</h2>
                        
                        <div class="amount">${amount}</div>
                        
                        {EmailService._get_reward_message_content(message_type, amount)}
                        
                        <p>Keep up the great work in the Trendy community!</p>
                    </div>
                    <div class="footer">
                        <p>Best regards,<br>The Trendy Team</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            text_content = f"""
            Hi {user.get_full_name() or user.username}!
            
            {EmailService._get_reward_message_text(message_type, amount)}
            
            Keep up the great work!
            
            Best regards,
            The Trendy Team
            """
            
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            logger.info(f"Reward notification sent to {user.email}: {status} - ${amount}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send reward notification to {user.email}: {str(e)}")
            return False
    
    @staticmethod
    def _get_reward_message_content(message_type, amount):
        """Get HTML content for reward messages"""
        if message_type == 'claimed':
            return f"""
            <p>Your reward claim has been submitted successfully!</p>
            <p><strong>What happens next?</strong></p>
            <ul>
                <li>📋 Our team will review your claim</li>
                <li>✅ You'll get approval within 24-48 hours</li>
                <li>💸 Payment will be sent to your PayPal</li>
            </ul>
            """
        elif message_type == 'approved':
            return f"""
            <p>Great news! Your reward claim has been approved!</p>
            <p><strong>What happens next?</strong></p>
            <ul>
                <li>💰 Payment will be processed within 24 hours</li>
                <li>📧 You'll receive a payment confirmation email</li>
                <li>💸 Funds will appear in your PayPal account</li>
            </ul>
            """
        elif message_type == 'paid':
            return f"""
            <p>🎉 Your payment has been sent successfully!</p>
            <p><strong>Payment Details:</strong></p>
            <ul>
                <li>💰 Amount: ${amount}</li>
                <li>📅 Sent: {timezone.now().strftime('%Y-%m-%d %H:%M')}</li>
                <li>💸 Check your PayPal account</li>
            </ul>
            """
        return ""
    
    @staticmethod
    def _get_reward_message_text(message_type, amount):
        """Get plain text content for reward messages"""
        if message_type == 'claimed':
            return f"Your ${amount} reward claim has been submitted and is under review."
        elif message_type == 'approved':
            return f"Your ${amount} reward claim has been approved! Payment will be processed within 24 hours."
        elif message_type == 'paid':
            return f"Your ${amount} payment has been sent to your PayPal account!"
        return ""

    @staticmethod
    def send_password_reset_email(user, reset_token):
        """Send password reset email"""
        try:
            subject = '🔑 Reset Your Trendy Password'

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                    .button {{ display: inline-block; background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🔑 Password Reset</h1>
                        <p>Reset your Trendy account password</p>
                    </div>
                    <div class="content">
                        <h2>Hi {user.get_full_name() or user.username}!</h2>
                        <p>You requested to reset your password for your Trendy account.</p>

                        <p>Click the button below to reset your password:</p>

                        <div style="text-align: center;">
                            <a href="{settings.FRONTEND_URL}/reset-password/{reset_token}" class="button">
                                🔑 Reset My Password
                            </a>
                        </div>

                        <p><strong>Security Notice:</strong></p>
                        <ul>
                            <li>🔒 This link will expire in 1 hour</li>
                            <li>🔐 Only use this link if you requested it</li>
                            <li>🚫 Don't share this link with anyone</li>
                        </ul>

                        <p>If you didn't request this password reset, please ignore this email and your password will remain unchanged.</p>
                    </div>
                    <div class="footer">
                        <p>Best regards,<br>The Trendy Team</p>
                        <p><small>This email was sent to {user.email}</small></p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            Hi {user.get_full_name() or user.username}!

            You requested to reset your password for your Trendy account.

            Reset your password by clicking this link:
            {settings.FRONTEND_URL}/reset-password/{reset_token}

            This link will expire in 1 hour.

            If you didn't request this, please ignore this email.

            Best regards,
            The Trendy Team
            """

            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()

            logger.info(f"Password reset email sent to {user.email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send password reset email to {user.email}: {str(e)}")
            return False

    @staticmethod
    def send_welcome_email(user):
        """Send welcome email after email verification"""
        try:
            subject = '🎉 Welcome to Trendy - Start Earning Today!'

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                    .feature {{ background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #28a745; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Welcome to Trendy!</h1>
                        <p>Your journey to earning money starts now</p>
                    </div>
                    <div class="content">
                        <h2>Hi {user.get_full_name() or user.username}!</h2>
                        <p>Congratulations! Your email has been verified and you're now a full member of the Trendy community.</p>

                        <h3>🚀 Here's how to start earning:</h3>

                        <div class="feature">
                            <h4>📖 Read Posts (+10 points)</h4>
                            <p>Browse our content and earn points for every post you read</p>
                        </div>

                        <div class="feature">
                            <h4>💬 Engage (+5 points)</h4>
                            <p>Comment on posts and interact with the community</p>
                        </div>

                        <div class="feature">
                            <h4>👥 Refer Friends (+$5)</h4>
                            <p>Invite friends and earn real money when they join and upgrade</p>
                        </div>

                        <div class="feature">
                            <h4>⭐ Go Premium (2x points)</h4>
                            <p>Upgrade for just $9.99/month and double your earning potential</p>
                        </div>

                        <p><strong>Your Referral Code:</strong> TRENDY_{user.username.upper()}_{user.id}</p>
                        <p>Share this code with friends to start earning referral bonuses!</p>

                        <p>Ready to start earning? Open the Trendy app and begin your journey!</p>
                    </div>
                    <div class="footer">
                        <p>Best regards,<br>The Trendy Team</p>
                        <p><small>This email was sent to {user.email}</small></p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            Hi {user.get_full_name() or user.username}!

            Welcome to Trendy! Your email has been verified and you can now start earning.

            Here's how to earn:
            • Read posts: +10 points each
            • Comment and engage: +5 points each
            • Refer friends: +$5 when they upgrade
            • Go Premium: 2x points for $9.99/month

            Your Referral Code: TRENDY_{user.username.upper()}_{user.id}

            Start earning today!

            Best regards,
            The Trendy Team
            """

            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()

            logger.info(f"Welcome email sent to {user.email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")
            return False

    @staticmethod
    def send_referral_reward_email(referrer, referee, reward_amount, milestone):
        """Send referral reward notification email"""
        try:
            subject = f'🎉 Referral Reward Earned - ${reward_amount}'

            milestone_text = {
                'join': 'joined Trendy',
                'level5': 'reached Level 5',
                'premium': 'upgraded to Premium'
            }.get(milestone, 'completed a milestone')

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                    .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                    .amount {{ font-size: 36px; font-weight: bold; color: #ffc107; text-align: center; margin: 20px 0; }}
                    .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🎉 Referral Reward!</h1>
                        <p>You've earned money from your referral</p>
                    </div>
                    <div class="content">
                        <h2>Hi {referrer.get_full_name() or referrer.username}!</h2>

                        <p>Great news! Your friend <strong>{referee.get_full_name() or referee.username}</strong> has {milestone_text}.</p>

                        <div class="amount">${reward_amount}</div>

                        <p>This reward has been added to your account and will be included in your next payout.</p>

                        <p><strong>Keep referring friends to earn more:</strong></p>
                        <ul>
                            <li>💰 $2 when they join</li>
                            <li>💰 $2 when they reach Level 5</li>
                            <li>💰 $5 when they go Premium</li>
                        </ul>

                        <p>Your Referral Code: <strong>TRENDY_{referrer.username.upper()}_{referrer.id}</strong></p>

                        <p>Share your code and keep earning!</p>
                    </div>
                    <div class="footer">
                        <p>Best regards,<br>The Trendy Team</p>
                    </div>
                </div>
            </body>
            </html>
            """

            text_content = f"""
            Hi {referrer.get_full_name() or referrer.username}!

            Great news! Your friend {referee.get_full_name() or referee.username} has {milestone_text}.

            You've earned: ${reward_amount}

            This reward has been added to your account.

            Your Referral Code: TRENDY_{referrer.username.upper()}_{referrer.id}

            Keep sharing to earn more!

            Best regards,
            The Trendy Team
            """

            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[referrer.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()

            logger.info(f"Referral reward email sent to {referrer.email}: ${reward_amount} for {milestone}")
            return True

        except Exception as e:
            logger.error(f"Failed to send referral reward email to {referrer.email}: {str(e)}")
            return False
