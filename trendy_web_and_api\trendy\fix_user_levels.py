#!/usr/bin/env python
"""
Quick script to fix UserLevel records for existing users
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from django.contrib.auth import get_user_model
from gamification.models import UserLevel
from gamification.services import GamificationService

User = get_user_model()

def main():
    print("🔧 Fixing UserLevel records...")
    
    # Get all users without UserLevel records
    users_without_levels = User.objects.filter(level__isnull=True)
    
    if not users_without_levels.exists():
        print("✅ All users already have UserLevel records!")
        return
    
    print(f"📊 Found {users_without_levels.count()} users without UserLevel records")
    
    created_count = 0
    for user in users_without_levels:
        try:
            user_level = GamificationService.get_or_create_user_level(user)
            if user_level:
                created_count += 1
                print(f"✅ Created UserLevel for: {user.username}")
        except Exception as e:
            print(f"❌ Error creating UserLevel for {user.username}: {e}")
    
    print(f"🎉 Successfully created {created_count} UserLevel records!")
    
    # Also check if any users exist at all
    total_users = User.objects.count()
    total_levels = UserLevel.objects.count()
    print(f"📈 Summary: {total_levels}/{total_users} users have UserLevel records")

if __name__ == "__main__":
    main()
