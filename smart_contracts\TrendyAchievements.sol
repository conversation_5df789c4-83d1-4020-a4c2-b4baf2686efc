// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * @title TrendyAchievements
 * @dev NFT contract for Trendy platform achievements and badges
 * Features:
 * - Achievement badges as NFTs
 * - Rarity system (Common to Legendary)
 * - Soul-bound tokens (non-transferable achievements)
 * - Metadata and attributes storage
 * - Achievement verification system
 */
contract TrendyAchievements is ERC721, ERC721Enumerable, ERC721URIStorage, Pausable, Ownable {
    using Counters for Counters.Counter;
    
    Counters.Counter private _tokenIdCounter;
    
    // Achievement structure
    struct Achievement {
        string name;
        string description;
        uint256 rarity; // 1=Common, 2=Uncommon, 3=Rare, 4=Epic, 5=Legendary
        uint256 earnedDate;
        bool isSoulBound; // Cannot be transferred
        string category; // e.g., "social", "content", "milestone"
        mapping(string => string) attributes;
        string[] attributeKeys;
    }
    
    // Mappings
    mapping(uint256 => Achievement) public achievements;
    mapping(address => bool) public achievementMinters;
    mapping(string => bool) public achievementTypes;
    mapping(uint256 => uint256) public rarityCount; // Count of each rarity level
    
    // Achievement categories
    string[] public categories = ["social", "content", "milestone", "special", "community"];
    
    // Events
    event AchievementMinted(
        address indexed user,
        uint256 indexed tokenId,
        string name,
        uint256 rarity,
        string category
    );
    event AchievementTypeAdded(string achievementType);
    event MinterAdded(address indexed minter);
    event MinterRemoved(address indexed minter);
    
    constructor() ERC721("Trendy Achievements", "TACH") {
        achievementMinters[msg.sender] = true;
        
        // Initialize achievement types
        achievementTypes["first_post"] = true;
        achievementTypes["social_butterfly"] = true;
        achievementTypes["content_creator"] = true;
        achievementTypes["community_leader"] = true;
        achievementTypes["trendsetter"] = true;
    }
    
    /**
     * @dev Mint achievement NFT to user
     * @param to User address
     * @param name Achievement name
     * @param description Achievement description
     * @param rarity Rarity level (1-5)
     * @param category Achievement category
     * @param isSoulBound Whether the achievement is soul-bound
     * @param tokenURI Metadata URI
     */
    function mintAchievement(
        address to,
        string memory name,
        string memory description,
        uint256 rarity,
        string memory category,
        bool isSoulBound,
        string memory tokenURI
    ) external {
        require(achievementMinters[msg.sender], "Not authorized to mint achievements");
        require(rarity >= 1 && rarity <= 5, "Invalid rarity level");
        require(isValidCategory(category), "Invalid category");
        
        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();
        
        // Mint NFT
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, tokenURI);
        
        // Set achievement data
        Achievement storage achievement = achievements[tokenId];
        achievement.name = name;
        achievement.description = description;
        achievement.rarity = rarity;
        achievement.earnedDate = block.timestamp;
        achievement.isSoulBound = isSoulBound;
        achievement.category = category;
        
        // Update rarity count
        rarityCount[rarity]++;
        
        emit AchievementMinted(to, tokenId, name, rarity, category);
    }
    
    /**
     * @dev Batch mint achievements to multiple users
     * @param recipients Array of user addresses
     * @param names Array of achievement names
     * @param rarities Array of rarity levels
     * @param categories Array of categories
     * @param tokenURIs Array of metadata URIs
     */
    function batchMintAchievements(
        address[] calldata recipients,
        string[] calldata names,
        uint256[] calldata rarities,
        string[] calldata categories,
        string[] calldata tokenURIs
    ) external {
        require(achievementMinters[msg.sender], "Not authorized to mint achievements");
        require(
            recipients.length == names.length &&
            names.length == rarities.length &&
            rarities.length == categories.length &&
            categories.length == tokenURIs.length,
            "Array length mismatch"
        );
        
        for (uint256 i = 0; i < recipients.length; i++) {
            mintAchievement(
                recipients[i],
                names[i],
                "",
                rarities[i],
                categories[i],
                true, // Batch minted achievements are soul-bound by default
                tokenURIs[i]
            );
        }
    }
    
    /**
     * @dev Add attribute to achievement
     * @param tokenId Token ID
     * @param key Attribute key
     * @param value Attribute value
     */
    function addAchievementAttribute(
        uint256 tokenId,
        string memory key,
        string memory value
    ) external {
        require(achievementMinters[msg.sender], "Not authorized");
        require(_exists(tokenId), "Token does not exist");
        
        Achievement storage achievement = achievements[tokenId];
        
        // Check if attribute already exists
        if (bytes(achievement.attributes[key]).length == 0) {
            achievement.attributeKeys.push(key);
        }
        
        achievement.attributes[key] = value;
    }
    
    /**
     * @dev Get achievement attribute
     * @param tokenId Token ID
     * @param key Attribute key
     * @return Attribute value
     */
    function getAchievementAttribute(uint256 tokenId, string memory key)
        external
        view
        returns (string memory)
    {
        require(_exists(tokenId), "Token does not exist");
        return achievements[tokenId].attributes[key];
    }
    
    /**
     * @dev Get all attributes for an achievement
     * @param tokenId Token ID
     * @return keys Array of attribute keys
     * @return values Array of attribute values
     */
    function getAchievementAttributes(uint256 tokenId)
        external
        view
        returns (string[] memory keys, string[] memory values)
    {
        require(_exists(tokenId), "Token does not exist");
        
        Achievement storage achievement = achievements[tokenId];
        keys = achievement.attributeKeys;
        values = new string[](keys.length);
        
        for (uint256 i = 0; i < keys.length; i++) {
            values[i] = achievement.attributes[keys[i]];
        }
    }
    
    /**
     * @dev Get user's achievements by rarity
     * @param user User address
     * @param rarity Rarity level
     * @return Array of token IDs
     */
    function getUserAchievementsByRarity(address user, uint256 rarity)
        external
        view
        returns (uint256[] memory)
    {
        uint256 balance = balanceOf(user);
        uint256[] memory result = new uint256[](balance);
        uint256 resultIndex = 0;
        
        for (uint256 i = 0; i < balance; i++) {
            uint256 tokenId = tokenOfOwnerByIndex(user, i);
            if (achievements[tokenId].rarity == rarity) {
                result[resultIndex] = tokenId;
                resultIndex++;
            }
        }
        
        // Resize array to actual size
        uint256[] memory finalResult = new uint256[](resultIndex);
        for (uint256 i = 0; i < resultIndex; i++) {
            finalResult[i] = result[i];
        }
        
        return finalResult;
    }
    
    /**
     * @dev Get user's achievements by category
     * @param user User address
     * @param category Achievement category
     * @return Array of token IDs
     */
    function getUserAchievementsByCategory(address user, string memory category)
        external
        view
        returns (uint256[] memory)
    {
        uint256 balance = balanceOf(user);
        uint256[] memory result = new uint256[](balance);
        uint256 resultIndex = 0;
        
        for (uint256 i = 0; i < balance; i++) {
            uint256 tokenId = tokenOfOwnerByIndex(user, i);
            if (keccak256(bytes(achievements[tokenId].category)) == keccak256(bytes(category))) {
                result[resultIndex] = tokenId;
                resultIndex++;
            }
        }
        
        // Resize array to actual size
        uint256[] memory finalResult = new uint256[](resultIndex);
        for (uint256 i = 0; i < resultIndex; i++) {
            finalResult[i] = result[i];
        }
        
        return finalResult;
    }
    
    /**
     * @dev Get achievement statistics
     * @return totalAchievements Total number of achievements minted
     * @return rarityCounts Array of counts for each rarity level
     */
    function getAchievementStats()
        external
        view
        returns (uint256 totalAchievements, uint256[] memory rarityCounts)
    {
        totalAchievements = totalSupply();
        rarityCounts = new uint256[](5);
        
        for (uint256 i = 1; i <= 5; i++) {
            rarityCounts[i - 1] = rarityCount[i];
        }
    }
    
    /**
     * @dev Check if category is valid
     * @param category Category to check
     * @return True if valid
     */
    function isValidCategory(string memory category) public view returns (bool) {
        for (uint256 i = 0; i < categories.length; i++) {
            if (keccak256(bytes(categories[i])) == keccak256(bytes(category))) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * @dev Override transfer to respect soul-bound tokens
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId,
        uint256 batchSize
    ) internal override(ERC721, ERC721Enumerable) {
        // Allow minting (from == address(0))
        if (from != address(0)) {
            require(!achievements[tokenId].isSoulBound, "Soul-bound token cannot be transferred");
        }
        
        super._beforeTokenTransfer(from, to, tokenId, batchSize);
    }
    
    /**
     * @dev Add achievement minter (only owner)
     * @param minter Address to add as minter
     */
    function addMinter(address minter) external onlyOwner {
        achievementMinters[minter] = true;
        emit MinterAdded(minter);
    }
    
    /**
     * @dev Remove achievement minter (only owner)
     * @param minter Address to remove as minter
     */
    function removeMinter(address minter) external onlyOwner {
        achievementMinters[minter] = false;
        emit MinterRemoved(minter);
    }
    
    /**
     * @dev Add new achievement type (only owner)
     * @param achievementType New achievement type
     */
    function addAchievementType(string memory achievementType) external onlyOwner {
        achievementTypes[achievementType] = true;
        emit AchievementTypeAdded(achievementType);
    }
    
    /**
     * @dev Pause contract (only owner)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract (only owner)
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    // Required overrides
    function _burn(uint256 tokenId) internal override(ERC721, ERC721URIStorage) {
        super._burn(tokenId);
    }
    
    function tokenURI(uint256 tokenId)
        public
        view
        override(ERC721, ERC721URIStorage)
        returns (string memory)
    {
        return super.tokenURI(tokenId);
    }
    
    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721Enumerable, ERC721URIStorage)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
}
