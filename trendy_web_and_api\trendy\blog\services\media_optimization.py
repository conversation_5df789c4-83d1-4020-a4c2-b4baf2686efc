"""
Media Optimization Service for Trendy Blog Platform
Handles image compression, resizing, format conversion, and progressive loading
"""

import os
import io
from PIL import Image, ImageOps
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.conf import settings
import hashlib
import json
from typing import Dict, List, Tuple, Optional

class MediaOptimizationService:
    """Advanced media optimization for better performance"""
    
    # Image quality settings
    QUALITY_SETTINGS = {
        'thumbnail': {'quality': 70, 'max_size': (150, 150)},
        'small': {'quality': 75, 'max_size': (400, 400)},
        'medium': {'quality': 80, 'max_size': (800, 600)},
        'large': {'quality': 85, 'max_size': (1200, 900)},
        'original': {'quality': 90, 'max_size': (1920, 1080)},
    }
    
    # Supported formats
    SUPPORTED_FORMATS = ['JPEG', 'PNG', 'WEBP', 'GIF']
    OUTPUT_FORMAT = 'WEBP'  # Modern format for better compression
    
    @classmethod
    def optimize_image(cls, image_file, sizes: List[str] = None) -> Dict[str, str]:
        """
        Optimize image for multiple sizes and formats
        
        Args:
            image_file: Django UploadedFile
            sizes: List of size variants to generate
            
        Returns:
            Dict with URLs for different sizes
        """
        if sizes is None:
            sizes = ['thumbnail', 'small', 'medium', 'large']
        
        try:
            # Open and process image
            image = Image.open(image_file)
            image = ImageOps.exif_transpose(image)  # Fix orientation
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # Generate file hash for caching
            image_file.seek(0)
            file_hash = hashlib.md5(image_file.read()).hexdigest()
            image_file.seek(0)
            
            # Get original filename without extension
            original_name = os.path.splitext(image_file.name)[0]
            
            optimized_urls = {}
            
            for size_name in sizes:
                if size_name not in cls.QUALITY_SETTINGS:
                    continue
                
                settings_config = cls.QUALITY_SETTINGS[size_name]
                optimized_image = cls._resize_image(image, settings_config)
                
                # Generate filename
                filename = f"{original_name}_{size_name}_{file_hash}.webp"
                
                # Save optimized image
                buffer = io.BytesIO()
                optimized_image.save(
                    buffer, 
                    format='WEBP', 
                    quality=settings_config['quality'],
                    optimize=True
                )
                
                # Save to storage
                file_path = f"optimized_images/{filename}"
                saved_path = default_storage.save(
                    file_path,
                    ContentFile(buffer.getvalue())
                )
                
                optimized_urls[size_name] = default_storage.url(saved_path)
            
            # Generate responsive image metadata
            metadata = cls._generate_image_metadata(image, optimized_urls)
            
            return {
                'urls': optimized_urls,
                'metadata': metadata,
                'hash': file_hash
            }
            
        except Exception as e:
            # Fallback to original image
            return {
                'urls': {'original': default_storage.url(image_file.name)},
                'metadata': {'error': str(e)},
                'hash': None
            }
    
    @classmethod
    def _resize_image(cls, image: Image.Image, settings_config: Dict) -> Image.Image:
        """Resize image while maintaining aspect ratio"""
        max_size = settings_config['max_size']
        
        # Calculate new size maintaining aspect ratio
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        return image
    
    @classmethod
    def _generate_image_metadata(cls, image: Image.Image, urls: Dict[str, str]) -> Dict:
        """Generate metadata for responsive images"""
        width, height = image.size
        
        return {
            'original_width': width,
            'original_height': height,
            'aspect_ratio': round(width / height, 2),
            'sizes_available': list(urls.keys()),
            'format': 'webp',
            'optimized': True
        }
    
    @classmethod
    def generate_progressive_loading_data(cls, image_urls: Dict[str, str]) -> Dict:
        """Generate data for progressive image loading"""
        return {
            'placeholder': image_urls.get('thumbnail'),
            'low_quality': image_urls.get('small'),
            'medium_quality': image_urls.get('medium'),
            'high_quality': image_urls.get('large'),
            'loading_strategy': 'progressive'
        }
    
    @classmethod
    def create_image_srcset(cls, image_urls: Dict[str, str]) -> str:
        """Create srcset string for responsive images"""
        srcset_parts = []
        
        size_widths = {
            'small': '400w',
            'medium': '800w',
            'large': '1200w'
        }
        
        for size_name, width in size_widths.items():
            if size_name in image_urls:
                srcset_parts.append(f"{image_urls[size_name]} {width}")
        
        return ', '.join(srcset_parts)
    
    @classmethod
    def optimize_video_thumbnail(cls, video_file, timestamp: float = 1.0) -> Optional[str]:
        """Generate optimized thumbnail from video"""
        try:
            import cv2
            import numpy as np
            
            # Read video
            cap = cv2.VideoCapture(video_file.temporary_file_path())
            
            # Set timestamp
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # Read frame
            ret, frame = cap.read()
            cap.release()
            
            if not ret:
                return None
            
            # Convert to PIL Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(frame_rgb)
            
            # Optimize thumbnail
            thumbnail = cls._resize_image(image, cls.QUALITY_SETTINGS['medium'])
            
            # Save thumbnail
            buffer = io.BytesIO()
            thumbnail.save(buffer, format='WEBP', quality=80, optimize=True)
            
            filename = f"video_thumbnail_{hashlib.md5(video_file.read()).hexdigest()}.webp"
            file_path = f"video_thumbnails/{filename}"
            
            saved_path = default_storage.save(
                file_path,
                ContentFile(buffer.getvalue())
            )
            
            return default_storage.url(saved_path)
            
        except ImportError:
            # OpenCV not available
            return None
        except Exception:
            return None

class LazyLoadingService:
    """Service for implementing lazy loading strategies"""
    
    @classmethod
    def generate_lazy_loading_config(cls, media_items: List[Dict]) -> Dict:
        """Generate configuration for lazy loading"""
        return {
            'strategy': 'intersection_observer',
            'root_margin': '50px',
            'threshold': 0.1,
            'placeholder_strategy': 'blur',
            'fade_in_duration': 300,
            'items': cls._prepare_lazy_items(media_items)
        }
    
    @classmethod
    def _prepare_lazy_items(cls, media_items: List[Dict]) -> List[Dict]:
        """Prepare media items for lazy loading"""
        prepared_items = []
        
        for item in media_items:
            prepared_item = {
                'id': item.get('id'),
                'type': item.get('type', 'image'),
                'placeholder': item.get('placeholder_url'),
                'src': item.get('url'),
                'srcset': item.get('srcset'),
                'sizes': '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
                'alt': item.get('alt_text', ''),
                'loading': 'lazy'
            }
            prepared_items.append(prepared_item)
        
        return prepared_items

class MediaCacheService:
    """Service for media caching and CDN integration"""
    
    @classmethod
    def generate_cache_headers(cls, media_type: str) -> Dict[str, str]:
        """Generate appropriate cache headers for media"""
        cache_durations = {
            'image': 31536000,  # 1 year
            'video': 2592000,   # 30 days
            'audio': 2592000,   # 30 days
        }
        
        duration = cache_durations.get(media_type, 86400)  # Default 1 day
        
        return {
            'Cache-Control': f'public, max-age={duration}, immutable',
            'Expires': f'max-age={duration}',
            'ETag': f'"{media_type}-{duration}"'
        }
    
    @classmethod
    def generate_cdn_urls(cls, urls: Dict[str, str], cdn_domain: str = None) -> Dict[str, str]:
        """Convert storage URLs to CDN URLs"""
        if not cdn_domain:
            return urls
        
        cdn_urls = {}
        for size, url in urls.items():
            # Replace domain with CDN domain
            if url.startswith('http'):
                path = url.split('/', 3)[-1]  # Get path after domain
                cdn_urls[size] = f"https://{cdn_domain}/{path}"
            else:
                cdn_urls[size] = f"https://{cdn_domain}{url}"
        
        return cdn_urls
