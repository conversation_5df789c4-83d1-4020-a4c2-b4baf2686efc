import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'api_response.freezed.dart';
part 'api_response.g.dart';

@freezed
class GenericApiResponse with _$GenericApiResponse {
  const factory GenericApiResponse({
    @JsonKey(name: 'success') required bool isSuccess,
    dynamic data,
    String? error,
  }) = _GenericApiResponse;

  factory GenericApiResponse.fromJson(Map<String, dynamic> json) =>
      _$GenericApiResponseFromJson(json);
}
