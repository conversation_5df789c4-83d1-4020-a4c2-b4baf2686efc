import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/app_theme.dart';
import '../providers/store_provider.dart';
import '../services/api_service.dart';
import '../services/compliant_payment_service.dart';

class PremiumUpgradeScreen extends ConsumerStatefulWidget {
  const PremiumUpgradeScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<PremiumUpgradeScreen> createState() => _PremiumUpgradeScreenState();
}

class _PremiumUpgradeScreenState extends ConsumerState<PremiumUpgradeScreen> {
  String selectedPlan = 'monthly';
  Map<String, dynamic>? monetizationSettings;
  bool isLoading = true;
  Map<String, dynamic>? paymentIntent;

  @override
  void initState() {
    super.initState();
    _loadMonetizationSettings();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Get payment intent from route arguments
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      paymentIntent = args;
    }
  }

  Future<void> _loadMonetizationSettings() async {
    try {
      final apiService = ApiService();
      final settings = await apiService.getMonetizationSettings();
      if (mounted) {
        setState(() {
          monetizationSettings = settings;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if this is a point boost payment
    if (paymentIntent != null && paymentIntent!['payment_intent'] == 'point_boost') {
      return _buildPointBoostPaymentScreen();
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppTheme.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '👑 Upgrade to Premium',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
      ),
      body: isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Premium hero section
                  _buildHeroSection(),

                  // Benefits comparison
                  _buildBenefitsComparison(),

                  // Pricing plans
                  _buildPricingPlans(),

                  // Testimonials
                  _buildTestimonials(),

                  // Upgrade button
                  _buildUpgradeButton(),

                  // Terms and conditions
                  _buildTerms(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple[400]!, Colors.purple[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.diamond,
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          const Text(
            'Unlock Your Earning Potential',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Earn ${_getPremiumMultiplier()}x points and access exclusive rewards',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              '🎯 Premium users earn \$15-50/month',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsComparison() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                  flex: 2,
                  child: Text(
                    'Features',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                const Expanded(
                  child: Text(
                    'Free',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'Premium',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildComparisonRow('Point Multiplier', '1x', '${_getPremiumMultiplier()}x', true),
          _buildComparisonRow('Daily Streak Bonus', '+5 points', '+${_getPremiumDailyBonus()} points', true),
          _buildComparisonRow('Voice Comments', '3/day', 'Unlimited', true),
          _buildComparisonRow('Reward Access', 'Basic only', 'All + Exclusive', true),
          _buildComparisonRow('Processing Time', '3-5 days', '24 hours', true),
          _buildComparisonRow('Premium Badge', '❌', '✅', true),
          _buildComparisonRow('Ad Experience', 'All ads', '50% fewer ads', true),
        ],
      ),
    );
  }

  Widget _buildComparisonRow(String feature, String free, String premium, bool premiumBetter) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              feature,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Expanded(
            child: Text(
              free,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              premium,
              style: TextStyle(
                fontSize: 12,
                color: premiumBetter ? AppTheme.primaryColor : Colors.grey[600],
                fontWeight: premiumBetter ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricingPlans() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '💳 Choose Your Plan',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildPricingCard(
            'monthly',
            'Monthly',
            _getMonthlyPrice(),
            'month',
            'Most Flexible',
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildPricingCard(
            'quarterly',
            'Quarterly',
            _getQuarterlyPrice(),
            '3 months',
            '15% Off - Popular',
            Colors.orange,
          ),
          const SizedBox(height: 12),
          _buildPricingCard(
            'yearly',
            'Yearly',
            _getYearlyPrice(),
            'year',
            '30% Off - Best Value',
            Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildPricingCard(String planId, String title, double price, String period, String badge, Color color) {
    final isSelected = selectedPlan == planId;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPlan = planId;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: color.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : [],
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? color : Colors.grey[400]!,
                  width: 2,
                ),
                color: isSelected ? color : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, color: Colors.white, size: 12)
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          badge,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${price.toStringAsFixed(2)} / $period',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  if (planId != 'monthly') ...[
                    const SizedBox(height: 4),
                    Text(
                      'Save \$${_calculateSavings(planId).toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestimonials() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '💬 What Premium Users Say',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ..._buildDynamicTestimonials(),
        ],
      ),
    );
  }

  List<Widget> _buildDynamicTestimonials() {
    final multiplier = _getPremiumMultiplier();
    final dailyBonus = _getPremiumDailyBonus();
    final monthlyPrice = _getMonthlyPrice();

    return [
      _buildTestimonial(
        'Sarah M.',
        'I\'ve earned \$127 in 2 months with Premium! The ${multiplier}x multiplier makes such a difference.',
        '⭐⭐⭐⭐⭐',
      ),
      const SizedBox(height: 12),
      _buildTestimonial(
        'Mike C.',
        'Premium is worth every penny. I earn back the \$${monthlyPrice.toStringAsFixed(2)} subscription cost in the first week!',
        '⭐⭐⭐⭐⭐',
      ),
      const SizedBox(height: 12),
      _buildTestimonial(
        'Jessica L.',
        'The +$dailyBonus daily bonus and ${multiplier}x points helped me reach my first PayPal reward in just 3 weeks!',
        '⭐⭐⭐⭐⭐',
      ),
    ];
  }

  Widget _buildTestimonial(String name, String text, String rating) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppTheme.primaryColor,
                radius: 16,
                child: Text(
                  name[0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      rating,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '"$text"',
            style: TextStyle(
              color: Colors.grey[700],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradeButton() {
    final planDetails = _getPlanDetails(selectedPlan);
    
    return Container(
      margin: const EdgeInsets.all(16),
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ElevatedButton(
        key: const ValueKey('premium_upgrade_button'),
        onPressed: () => _processUpgrade(),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Upgrade to Premium - \$${planDetails['price'].toStringAsFixed(2)}',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildTerms() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'By upgrading, you agree to our Terms of Service and Privacy Policy. '
            'Subscription automatically renews unless cancelled. '
            'Cancel anytime in your account settings.',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.security, size: 16, color: Colors.green),
              const SizedBox(width: 4),
              Text(
                'Secure PayPal Payment',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods for dynamic pricing and settings
  double _getMonthlyPrice() {
    if (monetizationSettings != null && monetizationSettings!['settings'] != null) {
      final settings = monetizationSettings!['settings'];
      return double.tryParse(settings['premium_monthly_price'] ?? '9.99') ?? 9.99;
    }
    return 9.99;
  }

  double _getQuarterlyPrice() {
    final monthlyPrice = _getMonthlyPrice();
    return (monthlyPrice * 3) * 0.85; // 15% off
  }

  double _getYearlyPrice() {
    final monthlyPrice = _getMonthlyPrice();
    return (monthlyPrice * 12) * 0.70; // 30% off
  }

  double _getPremiumMultiplier() {
    if (monetizationSettings != null && monetizationSettings!['settings'] != null) {
      final settings = monetizationSettings!['settings'];
      return double.tryParse(settings['premium_point_multiplier'] ?? '2.0') ?? 2.0;
    }
    return 2.0;
  }

  int _getPremiumDailyBonus() {
    if (monetizationSettings != null && monetizationSettings!['settings'] != null) {
      final settings = monetizationSettings!['settings'];
      return int.tryParse(settings['premium_daily_bonus']?.toString() ?? '15') ?? 15;
    }
    return 15;
  }

  Map<String, dynamic> _getPlanDetails(String planId) {
    switch (planId) {
      case 'monthly':
        return {'price': _getMonthlyPrice(), 'period': 'month'};
      case 'quarterly':
        return {'price': _getQuarterlyPrice(), 'period': '3 months'};
      case 'yearly':
        return {'price': _getYearlyPrice(), 'period': 'year'};
      default:
        return {'price': _getMonthlyPrice(), 'period': 'month'};
    }
  }

  double _calculateSavings(String planId) {
    final monthlyPrice = _getMonthlyPrice();
    switch (planId) {
      case 'quarterly':
        return (monthlyPrice * 3) - _getQuarterlyPrice(); // 15% off
      case 'yearly':
        return (monthlyPrice * 12) - _getYearlyPrice(); // 30% off
      default:
        return 0.0;
    }
  }

  void _processUpgrade() async {
    final planDetails = _getPlanDetails(selectedPlan);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Upgrade to Premium'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'You\'re about to upgrade to Premium for \$${planDetails['price'].toStringAsFixed(2)}.',
            ),
            const SizedBox(height: 16),
            const Text(
              'You\'ll immediately get:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('• ${_getPremiumMultiplier()}x point multiplier'),
            Text('• +${_getPremiumDailyBonus()} daily streak bonus'),
            const Text('• Unlimited voice comments'),
            const Text('• Exclusive rewards access'),
            const Text('• Priority processing'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            key: const ValueKey('premium_continue_paypal_button'),
            onPressed: () {
              Navigator.pop(context);
              _createSubscriptionAndPay();
            },
            child: const Text('Continue to PayPal'),
          ),
        ],
      ),
    );
  }

  void _createSubscriptionAndPay() async {
    // Use compliant payment service for premium subscriptions (digital goods)
    final success = await CompliantPaymentService.purchasePremiumSubscription(
      context,
      plan: selectedPlan,
    );

    if (success) {
      _showUpgradeSuccess();
    }
    // Error handling is done within the CompliantPaymentService
  }

  void _showPayPalCheckout(Map<String, dynamic> subscriptionData) async {
    try {
      // Create actual payment order through the payment system
      final apiService = ApiService();
      final paymentResponse = await apiService.createPremiumPayment(
        selectedPlan,
        subscriptionData['amount']?.toDouble() ?? 0.0,
      );

      if (paymentResponse['success'] == true) {
        // Show PayPal checkout with real payment order
        _showRealPayPalCheckout(paymentResponse, subscriptionData);
      } else {
        _showError(paymentResponse['message'] ?? 'Failed to create payment order');
      }
    } catch (e) {
      _showError('Error creating payment: $e');
    }
  }

  void _showRealPayPalCheckout(Map<String, dynamic> paymentData, Map<String, dynamic> subscriptionData) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('💳 PayPal Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.payment, size: 64, color: Colors.blue),
            const SizedBox(height: 16),
            const Text(
              'Complete your payment with PayPal',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Amount: \$${subscriptionData['amount']?.toStringAsFixed(2) ?? '0.00'}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            Text(
              'Order ID: ${paymentData['order_id']}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'In a real app, this would redirect to PayPal. For demo purposes, click "Pay Now" to simulate payment.',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _cancelSubscription(subscriptionData['subscription_id']);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            key: const ValueKey('premium_pay_now_demo_button'),
            onPressed: () {
              Navigator.pop(context);
              _processRealPaymentResult(paymentData, subscriptionData);
            },
            child: const Text('Pay Now (Demo)'),
          ),
        ],
      ),
    );
  }

  void _processRealPaymentResult(Map<String, dynamic> paymentData, Map<String, dynamic> subscriptionData) async {
    // Show processing dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        title: Text('⏳ Processing Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Confirming your payment...'),
          ],
        ),
      ),
    );

    try {
      // First capture the payment order
      final apiService = ApiService();
      final captureResponse = await apiService.capturePaymentOrder(paymentData['order_id']);

      if (captureResponse['success'] == true) {
        // Then confirm the subscription payment
        final confirmResponse = await apiService.confirmSubscriptionPayment(
          captureResponse['transaction_id'].toString(),
          'COMPLETED', // PayPal payment status
        );

        if (!mounted) return;
        Navigator.pop(context); // Close processing dialog

        if (confirmResponse['success'] == true) {
          _showUpgradeSuccess();
        } else {
          _showError(confirmResponse['message'] ?? 'Subscription confirmation failed');
        }
      } else {
        if (!mounted) return;
        Navigator.pop(context); // Close processing dialog
        _showError(captureResponse['message'] ?? 'Payment capture failed');
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.pop(context); // Close processing dialog
      _showError('Error confirming payment: $e');
    }
  }

  void _cancelSubscription(dynamic subscriptionId) async {
    if (subscriptionId == null) return;

    try {
      final apiService = ApiService();
      await apiService.cancelPendingSubscription(subscriptionId.toString());
    } catch (e) {
      print('Error cancelling subscription: $e');
    }
  }

  void _showError(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('❌ Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showUpgradeSuccess() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Welcome to Premium!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.diamond,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Your Premium subscription is now active!',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'You\'re now earning ${_getPremiumMultiplier()}x points and have access to all Premium features.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ElevatedButton(
              onPressed: () {
                Navigator.pop(context); // Close success dialog
                Navigator.pop(context); // Go back to previous screen
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Start Earning ${_getPremiumMultiplier()}x Points!',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointBoostPaymentScreen() {
    final itemName = paymentIntent!['item_name'] ?? 'Point Boost';
    final price = paymentIntent!['price'] ?? 0.0;
    final metadata = paymentIntent!['metadata'] ?? {};
    final packageId = metadata['package_id']?.toString() ?? '';
    final points = metadata['points'] ?? 0;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppTheme.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '💰 Complete Purchase',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Purchase summary
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange[400]!, Colors.orange[600]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.stars,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    itemName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '$points Points',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '\$${price.toStringAsFixed(2)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Payment button
            Container(
              margin: const EdgeInsets.all(16),
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _processPointBoostPayment(packageId, price),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '💳 Pay with PayPal',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

            // Terms
            Container(
              margin: const EdgeInsets.all(16),
              child: const Text(
                'By completing this purchase, you agree to our Terms of Service and Privacy Policy.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _processPointBoostPayment(String packageId, double price) async {
    // Use compliant payment service for point boosts (digital goods)
    final success = await CompliantPaymentService.purchasePointBoost(
      context,
      packageId: packageId,
    );

    if (success && mounted) {
      // Show success dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('🎉 Purchase Successful!'),
          content: const Text('Your points have been added to your account!'),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog
                Navigator.pop(context); // Go back to previous screen
              },
              child: const Text('Great!'),
            ),
          ],
        ),
      );
    }
    // Error handling is done within the CompliantPaymentService
  }

  void _showPointBoostPaymentDialog(Map<String, dynamic> paymentData, String packageId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('💳 Complete Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.payment, size: 64, color: Colors.orange),
            const SizedBox(height: 16),
            Text(
              'Complete your purchase of ${paymentData['package_name']}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Amount: \$${paymentData['price']?.toStringAsFixed(2) ?? '0.00'}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'Points: ${paymentData['points']}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 16),
            Text(
              'Order ID: ${paymentData['order_id']}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'In a real app, this would redirect to PayPal. For demo purposes, click "Pay Now" to simulate payment.',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processPointBoostPaymentConfirmation(paymentData, packageId);
            },
            child: const Text('Pay Now (Demo)'),
          ),
        ],
      ),
    );
  }

  void _processPointBoostPaymentConfirmation(Map<String, dynamic> paymentData, String packageId) async {
    // Show processing dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        title: Text('⏳ Processing Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Confirming your payment...'),
          ],
        ),
      ),
    );

    try {
      // First capture the payment order
      final apiService = ApiService();
      final captureResponse = await apiService.capturePaymentOrder(paymentData['order_id']);

      if (captureResponse['success'] == true) {
        // Then confirm the point boost payment
        final storeNotifier = ref.read(storeProvider.notifier);
        final result = await storeNotifier.confirmPointBoostPayment(
          captureResponse['transaction_id'].toString(),
          packageId,
          'COMPLETED',
        );

        if (!mounted) return;
        Navigator.pop(context); // Close processing dialog

        if (result['success'] == true) {
          _showPointBoostSuccess(result);
        } else {
          _showError(result['message'] ?? 'Payment confirmation failed');
        }
      } else {
        if (!mounted) return;
        Navigator.pop(context); // Close processing dialog
        _showError(captureResponse['message'] ?? 'Payment capture failed');
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.pop(context); // Close processing dialog
      _showError('Error processing payment: $e');
    }
  }

  void _showPointBoostSuccess(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Purchase Successful!'),
        content: Text(
          'You have received ${result['points_awarded']} points!',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close success dialog
              Navigator.pop(context); // Return to store
            },
            child: const Text('Awesome!'),
          ),
        ],
      ),
    );
  }
}
