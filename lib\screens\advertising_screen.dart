import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/advertising_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/rewarded_ad_widget.dart';
import '../widgets/sponsored_content_widget.dart';

class AdvertisingScreen extends ConsumerStatefulWidget {
  const AdvertisingScreen({super.key});

  @override
  ConsumerState<AdvertisingScreen> createState() => _AdvertisingScreenState();
}

class _AdvertisingScreenState extends ConsumerState<AdvertisingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // Load advertising data when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(advertisingProvider.notifier).loadAdvertisingData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final advertisingState = ref.watch(advertisingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Earn with Ads'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Watch Ads', icon: Icon(Icons.play_circle_filled)),
            Tab(text: 'History', icon: Icon(Icons.history)),
            Tab(text: 'Analytics', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: advertisingState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildWatchAdsTab(),
                _buildHistoryTab(),
                _buildAnalyticsTab(),
              ],
            ),
    );
  }

  Widget _buildWatchAdsTab() {
    final advertisingState = ref.watch(advertisingProvider);
    final canWatchAds = advertisingState.canWatchAds;

    return RefreshIndicator(
      onRefresh: () => ref.read(advertisingProvider.notifier).loadAdvertisingData(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Daily points summary
            _buildDailyPointsSummary(),
            const SizedBox(height: 24),

            // Rewarded ads section
            Text(
              'Rewarded Ads',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Watch short videos to earn points instantly',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 16),

            if (canWatchAds) ...[
              const RewardedAdWidget(placementLocation: 'main_screen'),
              const SizedBox(height: 16),
              const RewardedAdWidget(placementLocation: 'bonus_screen'),
            ] else
              _buildNoAdsAvailable(),

            const SizedBox(height: 32),

            // Sponsored content section
            Text(
              'Sponsored Content',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Discover interesting content from our partners',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 16),

            const SponsoredContentList(),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab() {
    final advertisingState = ref.watch(advertisingProvider);
    final adHistory = advertisingState.adHistory;

    if (adHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Ad History',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Your watched ads will appear here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(advertisingProvider.notifier).loadAdHistory(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: adHistory.length,
        itemBuilder: (context, index) {
          final session = adHistory[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: session.rewardClaimed
                    ? AppTheme.successColor.withOpacity(0.1)
                    : AppTheme.warningColor.withOpacity(0.1),
                child: Icon(
                  session.rewardClaimed ? Icons.check : Icons.pending,
                  color: session.rewardClaimed
                      ? AppTheme.successColor
                      : AppTheme.warningColor,
                ),
              ),
              title: Text(session.placement.name),
              subtitle: Text(
                'Watched on ${_formatDate(session.startedAt)}',
                style: const TextStyle(color: AppTheme.textSecondary),
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '+${session.pointsAwarded} pts',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.successColor,
                    ),
                  ),
                  Text(
                    session.status,
                    style: TextStyle(
                      fontSize: 12,
                      color: _getStatusColor(session.status),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    final advertisingState = ref.watch(advertisingProvider);
    final analytics = advertisingState.analytics;

    if (analytics == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(advertisingProvider.notifier).refreshAnalytics(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Ad Statistics',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            // Stats cards
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Points',
                    '${analytics['total_points_earned'] ?? 0}',
                    Icons.stars,
                    AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Today',
                    '${analytics['daily_points_earned'] ?? 0}',
                    Icons.today,
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Ads Watched',
                    '${analytics['total_ads_watched'] ?? 0}',
                    Icons.play_circle,
                    AppTheme.secondaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'This Week',
                    '${analytics['weekly_points_earned'] ?? 0}',
                    Icons.calendar_today,
                    AppTheme.accentColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyPointsSummary() {
    final advertisingState = ref.watch(advertisingProvider);
    final settings = advertisingState.settings;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.1),
            AppTheme.secondaryColor.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Today\'s Earnings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${advertisingState.dailyAdPoints} / ${settings?.maxDailyAdPoints ?? 100} points',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          CircleAvatar(
            radius: 24,
            backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
            child: Text(
              '${advertisingState.dailyAdPoints}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoAdsAvailable() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.warningColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.warningColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: AppTheme.warningColor,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'No Ads Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.warningColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'You\'ve reached your daily ad limit or no ads are currently available.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successColor;
      case 'failed':
        return AppTheme.errorColor;
      case 'pending':
        return AppTheme.warningColor;
      default:
        return AppTheme.textSecondary;
    }
  }
}
