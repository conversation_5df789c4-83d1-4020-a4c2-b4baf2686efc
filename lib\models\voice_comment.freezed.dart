// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_comment.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VoiceComment _$VoiceCommentFromJson(Map<String, dynamic> json) {
  return _VoiceComment.fromJson(json);
}

/// @nodoc
mixin _$VoiceComment {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'post_id')
  int get postId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  int get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_name')
  String get userName => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_avatar')
  String? get userAvatar => throw _privateConstructorUsedError;
  @JsonKey(name: 'audio_url')
  String get audioUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'transcription')
  String? get transcription => throw _privateConstructorUsedError;
  @JsonKey(name: 'duration_seconds')
  int get durationSeconds => throw _privateConstructorUsedError;
  @JsonKey(name: 'file_size')
  int get fileSize => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_transcribed')
  bool get isTranscribed => throw _privateConstructorUsedError;
  @JsonKey(name: 'like_count')
  int get likeCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_liked')
  bool get isLiked => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VoiceCommentCopyWith<VoiceComment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCommentCopyWith<$Res> {
  factory $VoiceCommentCopyWith(
          VoiceComment value, $Res Function(VoiceComment) then) =
      _$VoiceCommentCopyWithImpl<$Res, VoiceComment>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'post_id') int postId,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'user_name') String userName,
      @JsonKey(name: 'user_avatar') String? userAvatar,
      @JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'transcription') String? transcription,
      @JsonKey(name: 'duration_seconds') int durationSeconds,
      @JsonKey(name: 'file_size') int fileSize,
      @JsonKey(name: 'is_transcribed') bool isTranscribed,
      @JsonKey(name: 'like_count') int likeCount,
      @JsonKey(name: 'is_liked') bool isLiked,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$VoiceCommentCopyWithImpl<$Res, $Val extends VoiceComment>
    implements $VoiceCommentCopyWith<$Res> {
  _$VoiceCommentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postId = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatar = freezed,
    Object? audioUrl = null,
    Object? transcription = freezed,
    Object? durationSeconds = null,
    Object? fileSize = null,
    Object? isTranscribed = null,
    Object? likeCount = null,
    Object? isLiked = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      postId: null == postId
          ? _value.postId
          : postId // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      userAvatar: freezed == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      transcription: freezed == transcription
          ? _value.transcription
          : transcription // ignore: cast_nullable_to_non_nullable
              as String?,
      durationSeconds: null == durationSeconds
          ? _value.durationSeconds
          : durationSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      fileSize: null == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int,
      isTranscribed: null == isTranscribed
          ? _value.isTranscribed
          : isTranscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      likeCount: null == likeCount
          ? _value.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int,
      isLiked: null == isLiked
          ? _value.isLiked
          : isLiked // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoiceCommentImplCopyWith<$Res>
    implements $VoiceCommentCopyWith<$Res> {
  factory _$$VoiceCommentImplCopyWith(
          _$VoiceCommentImpl value, $Res Function(_$VoiceCommentImpl) then) =
      __$$VoiceCommentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'post_id') int postId,
      @JsonKey(name: 'user_id') int userId,
      @JsonKey(name: 'user_name') String userName,
      @JsonKey(name: 'user_avatar') String? userAvatar,
      @JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'transcription') String? transcription,
      @JsonKey(name: 'duration_seconds') int durationSeconds,
      @JsonKey(name: 'file_size') int fileSize,
      @JsonKey(name: 'is_transcribed') bool isTranscribed,
      @JsonKey(name: 'like_count') int likeCount,
      @JsonKey(name: 'is_liked') bool isLiked,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$$VoiceCommentImplCopyWithImpl<$Res>
    extends _$VoiceCommentCopyWithImpl<$Res, _$VoiceCommentImpl>
    implements _$$VoiceCommentImplCopyWith<$Res> {
  __$$VoiceCommentImplCopyWithImpl(
      _$VoiceCommentImpl _value, $Res Function(_$VoiceCommentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? postId = null,
    Object? userId = null,
    Object? userName = null,
    Object? userAvatar = freezed,
    Object? audioUrl = null,
    Object? transcription = freezed,
    Object? durationSeconds = null,
    Object? fileSize = null,
    Object? isTranscribed = null,
    Object? likeCount = null,
    Object? isLiked = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$VoiceCommentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      postId: null == postId
          ? _value.postId
          : postId // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      userAvatar: freezed == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: null == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      transcription: freezed == transcription
          ? _value.transcription
          : transcription // ignore: cast_nullable_to_non_nullable
              as String?,
      durationSeconds: null == durationSeconds
          ? _value.durationSeconds
          : durationSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      fileSize: null == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int,
      isTranscribed: null == isTranscribed
          ? _value.isTranscribed
          : isTranscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      likeCount: null == likeCount
          ? _value.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int,
      isLiked: null == isLiked
          ? _value.isLiked
          : isLiked // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceCommentImpl implements _VoiceComment {
  const _$VoiceCommentImpl(
      {required this.id,
      @JsonKey(name: 'post_id') required this.postId,
      @JsonKey(name: 'user_id') required this.userId,
      @JsonKey(name: 'user_name') required this.userName,
      @JsonKey(name: 'user_avatar') this.userAvatar,
      @JsonKey(name: 'audio_url') required this.audioUrl,
      @JsonKey(name: 'transcription') this.transcription,
      @JsonKey(name: 'duration_seconds') this.durationSeconds = 0,
      @JsonKey(name: 'file_size') this.fileSize = 0,
      @JsonKey(name: 'is_transcribed') this.isTranscribed = false,
      @JsonKey(name: 'like_count') this.likeCount = 0,
      @JsonKey(name: 'is_liked') this.isLiked = false,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});

  factory _$VoiceCommentImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceCommentImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'post_id')
  final int postId;
  @override
  @JsonKey(name: 'user_id')
  final int userId;
  @override
  @JsonKey(name: 'user_name')
  final String userName;
  @override
  @JsonKey(name: 'user_avatar')
  final String? userAvatar;
  @override
  @JsonKey(name: 'audio_url')
  final String audioUrl;
  @override
  @JsonKey(name: 'transcription')
  final String? transcription;
  @override
  @JsonKey(name: 'duration_seconds')
  final int durationSeconds;
  @override
  @JsonKey(name: 'file_size')
  final int fileSize;
  @override
  @JsonKey(name: 'is_transcribed')
  final bool isTranscribed;
  @override
  @JsonKey(name: 'like_count')
  final int likeCount;
  @override
  @JsonKey(name: 'is_liked')
  final bool isLiked;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @override
  String toString() {
    return 'VoiceComment(id: $id, postId: $postId, userId: $userId, userName: $userName, userAvatar: $userAvatar, audioUrl: $audioUrl, transcription: $transcription, durationSeconds: $durationSeconds, fileSize: $fileSize, isTranscribed: $isTranscribed, likeCount: $likeCount, isLiked: $isLiked, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceCommentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.transcription, transcription) ||
                other.transcription == transcription) &&
            (identical(other.durationSeconds, durationSeconds) ||
                other.durationSeconds == durationSeconds) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.isTranscribed, isTranscribed) ||
                other.isTranscribed == isTranscribed) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            (identical(other.isLiked, isLiked) || other.isLiked == isLiked) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      postId,
      userId,
      userName,
      userAvatar,
      audioUrl,
      transcription,
      durationSeconds,
      fileSize,
      isTranscribed,
      likeCount,
      isLiked,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceCommentImplCopyWith<_$VoiceCommentImpl> get copyWith =>
      __$$VoiceCommentImplCopyWithImpl<_$VoiceCommentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceCommentImplToJson(
      this,
    );
  }
}

abstract class _VoiceComment implements VoiceComment {
  const factory _VoiceComment(
          {required final int id,
          @JsonKey(name: 'post_id') required final int postId,
          @JsonKey(name: 'user_id') required final int userId,
          @JsonKey(name: 'user_name') required final String userName,
          @JsonKey(name: 'user_avatar') final String? userAvatar,
          @JsonKey(name: 'audio_url') required final String audioUrl,
          @JsonKey(name: 'transcription') final String? transcription,
          @JsonKey(name: 'duration_seconds') final int durationSeconds,
          @JsonKey(name: 'file_size') final int fileSize,
          @JsonKey(name: 'is_transcribed') final bool isTranscribed,
          @JsonKey(name: 'like_count') final int likeCount,
          @JsonKey(name: 'is_liked') final bool isLiked,
          @JsonKey(name: 'created_at') required final DateTime createdAt,
          @JsonKey(name: 'updated_at') required final DateTime updatedAt}) =
      _$VoiceCommentImpl;

  factory _VoiceComment.fromJson(Map<String, dynamic> json) =
      _$VoiceCommentImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'post_id')
  int get postId;
  @override
  @JsonKey(name: 'user_id')
  int get userId;
  @override
  @JsonKey(name: 'user_name')
  String get userName;
  @override
  @JsonKey(name: 'user_avatar')
  String? get userAvatar;
  @override
  @JsonKey(name: 'audio_url')
  String get audioUrl;
  @override
  @JsonKey(name: 'transcription')
  String? get transcription;
  @override
  @JsonKey(name: 'duration_seconds')
  int get durationSeconds;
  @override
  @JsonKey(name: 'file_size')
  int get fileSize;
  @override
  @JsonKey(name: 'is_transcribed')
  bool get isTranscribed;
  @override
  @JsonKey(name: 'like_count')
  int get likeCount;
  @override
  @JsonKey(name: 'is_liked')
  bool get isLiked;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$VoiceCommentImplCopyWith<_$VoiceCommentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateVoiceComment _$CreateVoiceCommentFromJson(Map<String, dynamic> json) {
  return _CreateVoiceComment.fromJson(json);
}

/// @nodoc
mixin _$CreateVoiceComment {
  @JsonKey(name: 'post_id')
  int get postId => throw _privateConstructorUsedError;
  @JsonKey(name: 'audio_file')
  String get audioFile =>
      throw _privateConstructorUsedError; // Base64 encoded or file path
  @JsonKey(name: 'duration_seconds')
  int get durationSeconds => throw _privateConstructorUsedError;
  @JsonKey(name: 'transcription')
  String? get transcription => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CreateVoiceCommentCopyWith<CreateVoiceComment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateVoiceCommentCopyWith<$Res> {
  factory $CreateVoiceCommentCopyWith(
          CreateVoiceComment value, $Res Function(CreateVoiceComment) then) =
      _$CreateVoiceCommentCopyWithImpl<$Res, CreateVoiceComment>;
  @useResult
  $Res call(
      {@JsonKey(name: 'post_id') int postId,
      @JsonKey(name: 'audio_file') String audioFile,
      @JsonKey(name: 'duration_seconds') int durationSeconds,
      @JsonKey(name: 'transcription') String? transcription});
}

/// @nodoc
class _$CreateVoiceCommentCopyWithImpl<$Res, $Val extends CreateVoiceComment>
    implements $CreateVoiceCommentCopyWith<$Res> {
  _$CreateVoiceCommentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? audioFile = null,
    Object? durationSeconds = null,
    Object? transcription = freezed,
  }) {
    return _then(_value.copyWith(
      postId: null == postId
          ? _value.postId
          : postId // ignore: cast_nullable_to_non_nullable
              as int,
      audioFile: null == audioFile
          ? _value.audioFile
          : audioFile // ignore: cast_nullable_to_non_nullable
              as String,
      durationSeconds: null == durationSeconds
          ? _value.durationSeconds
          : durationSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      transcription: freezed == transcription
          ? _value.transcription
          : transcription // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateVoiceCommentImplCopyWith<$Res>
    implements $CreateVoiceCommentCopyWith<$Res> {
  factory _$$CreateVoiceCommentImplCopyWith(_$CreateVoiceCommentImpl value,
          $Res Function(_$CreateVoiceCommentImpl) then) =
      __$$CreateVoiceCommentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'post_id') int postId,
      @JsonKey(name: 'audio_file') String audioFile,
      @JsonKey(name: 'duration_seconds') int durationSeconds,
      @JsonKey(name: 'transcription') String? transcription});
}

/// @nodoc
class __$$CreateVoiceCommentImplCopyWithImpl<$Res>
    extends _$CreateVoiceCommentCopyWithImpl<$Res, _$CreateVoiceCommentImpl>
    implements _$$CreateVoiceCommentImplCopyWith<$Res> {
  __$$CreateVoiceCommentImplCopyWithImpl(_$CreateVoiceCommentImpl _value,
      $Res Function(_$CreateVoiceCommentImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? postId = null,
    Object? audioFile = null,
    Object? durationSeconds = null,
    Object? transcription = freezed,
  }) {
    return _then(_$CreateVoiceCommentImpl(
      postId: null == postId
          ? _value.postId
          : postId // ignore: cast_nullable_to_non_nullable
              as int,
      audioFile: null == audioFile
          ? _value.audioFile
          : audioFile // ignore: cast_nullable_to_non_nullable
              as String,
      durationSeconds: null == durationSeconds
          ? _value.durationSeconds
          : durationSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      transcription: freezed == transcription
          ? _value.transcription
          : transcription // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateVoiceCommentImpl implements _CreateVoiceComment {
  const _$CreateVoiceCommentImpl(
      {@JsonKey(name: 'post_id') required this.postId,
      @JsonKey(name: 'audio_file') required this.audioFile,
      @JsonKey(name: 'duration_seconds') required this.durationSeconds,
      @JsonKey(name: 'transcription') this.transcription});

  factory _$CreateVoiceCommentImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateVoiceCommentImplFromJson(json);

  @override
  @JsonKey(name: 'post_id')
  final int postId;
  @override
  @JsonKey(name: 'audio_file')
  final String audioFile;
// Base64 encoded or file path
  @override
  @JsonKey(name: 'duration_seconds')
  final int durationSeconds;
  @override
  @JsonKey(name: 'transcription')
  final String? transcription;

  @override
  String toString() {
    return 'CreateVoiceComment(postId: $postId, audioFile: $audioFile, durationSeconds: $durationSeconds, transcription: $transcription)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateVoiceCommentImpl &&
            (identical(other.postId, postId) || other.postId == postId) &&
            (identical(other.audioFile, audioFile) ||
                other.audioFile == audioFile) &&
            (identical(other.durationSeconds, durationSeconds) ||
                other.durationSeconds == durationSeconds) &&
            (identical(other.transcription, transcription) ||
                other.transcription == transcription));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, postId, audioFile, durationSeconds, transcription);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateVoiceCommentImplCopyWith<_$CreateVoiceCommentImpl> get copyWith =>
      __$$CreateVoiceCommentImplCopyWithImpl<_$CreateVoiceCommentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateVoiceCommentImplToJson(
      this,
    );
  }
}

abstract class _CreateVoiceComment implements CreateVoiceComment {
  const factory _CreateVoiceComment(
          {@JsonKey(name: 'post_id') required final int postId,
          @JsonKey(name: 'audio_file') required final String audioFile,
          @JsonKey(name: 'duration_seconds') required final int durationSeconds,
          @JsonKey(name: 'transcription') final String? transcription}) =
      _$CreateVoiceCommentImpl;

  factory _CreateVoiceComment.fromJson(Map<String, dynamic> json) =
      _$CreateVoiceCommentImpl.fromJson;

  @override
  @JsonKey(name: 'post_id')
  int get postId;
  @override
  @JsonKey(name: 'audio_file')
  String get audioFile;
  @override // Base64 encoded or file path
  @JsonKey(name: 'duration_seconds')
  int get durationSeconds;
  @override
  @JsonKey(name: 'transcription')
  String? get transcription;
  @override
  @JsonKey(ignore: true)
  _$$CreateVoiceCommentImplCopyWith<_$CreateVoiceCommentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VoiceCommentResponse _$VoiceCommentResponseFromJson(Map<String, dynamic> json) {
  return _VoiceCommentResponse.fromJson(json);
}

/// @nodoc
mixin _$VoiceCommentResponse {
  List<VoiceComment> get results => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;
  String? get next => throw _privateConstructorUsedError;
  String? get previous => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VoiceCommentResponseCopyWith<VoiceCommentResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCommentResponseCopyWith<$Res> {
  factory $VoiceCommentResponseCopyWith(VoiceCommentResponse value,
          $Res Function(VoiceCommentResponse) then) =
      _$VoiceCommentResponseCopyWithImpl<$Res, VoiceCommentResponse>;
  @useResult
  $Res call(
      {List<VoiceComment> results, int count, String? next, String? previous});
}

/// @nodoc
class _$VoiceCommentResponseCopyWithImpl<$Res,
        $Val extends VoiceCommentResponse>
    implements $VoiceCommentResponseCopyWith<$Res> {
  _$VoiceCommentResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = null,
    Object? count = null,
    Object? next = freezed,
    Object? previous = freezed,
  }) {
    return _then(_value.copyWith(
      results: null == results
          ? _value.results
          : results // ignore: cast_nullable_to_non_nullable
              as List<VoiceComment>,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      next: freezed == next
          ? _value.next
          : next // ignore: cast_nullable_to_non_nullable
              as String?,
      previous: freezed == previous
          ? _value.previous
          : previous // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VoiceCommentResponseImplCopyWith<$Res>
    implements $VoiceCommentResponseCopyWith<$Res> {
  factory _$$VoiceCommentResponseImplCopyWith(_$VoiceCommentResponseImpl value,
          $Res Function(_$VoiceCommentResponseImpl) then) =
      __$$VoiceCommentResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<VoiceComment> results, int count, String? next, String? previous});
}

/// @nodoc
class __$$VoiceCommentResponseImplCopyWithImpl<$Res>
    extends _$VoiceCommentResponseCopyWithImpl<$Res, _$VoiceCommentResponseImpl>
    implements _$$VoiceCommentResponseImplCopyWith<$Res> {
  __$$VoiceCommentResponseImplCopyWithImpl(_$VoiceCommentResponseImpl _value,
      $Res Function(_$VoiceCommentResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? results = null,
    Object? count = null,
    Object? next = freezed,
    Object? previous = freezed,
  }) {
    return _then(_$VoiceCommentResponseImpl(
      results: null == results
          ? _value._results
          : results // ignore: cast_nullable_to_non_nullable
              as List<VoiceComment>,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      next: freezed == next
          ? _value.next
          : next // ignore: cast_nullable_to_non_nullable
              as String?,
      previous: freezed == previous
          ? _value.previous
          : previous // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceCommentResponseImpl implements _VoiceCommentResponse {
  const _$VoiceCommentResponseImpl(
      {final List<VoiceComment> results = const [],
      this.count = 0,
      this.next,
      this.previous})
      : _results = results;

  factory _$VoiceCommentResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceCommentResponseImplFromJson(json);

  final List<VoiceComment> _results;
  @override
  @JsonKey()
  List<VoiceComment> get results {
    if (_results is EqualUnmodifiableListView) return _results;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_results);
  }

  @override
  @JsonKey()
  final int count;
  @override
  final String? next;
  @override
  final String? previous;

  @override
  String toString() {
    return 'VoiceCommentResponse(results: $results, count: $count, next: $next, previous: $previous)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceCommentResponseImpl &&
            const DeepCollectionEquality().equals(other._results, _results) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.next, next) || other.next == next) &&
            (identical(other.previous, previous) ||
                other.previous == previous));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_results), count, next, previous);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceCommentResponseImplCopyWith<_$VoiceCommentResponseImpl>
      get copyWith =>
          __$$VoiceCommentResponseImplCopyWithImpl<_$VoiceCommentResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceCommentResponseImplToJson(
      this,
    );
  }
}

abstract class _VoiceCommentResponse implements VoiceCommentResponse {
  const factory _VoiceCommentResponse(
      {final List<VoiceComment> results,
      final int count,
      final String? next,
      final String? previous}) = _$VoiceCommentResponseImpl;

  factory _VoiceCommentResponse.fromJson(Map<String, dynamic> json) =
      _$VoiceCommentResponseImpl.fromJson;

  @override
  List<VoiceComment> get results;
  @override
  int get count;
  @override
  String? get next;
  @override
  String? get previous;
  @override
  @JsonKey(ignore: true)
  _$$VoiceCommentResponseImplCopyWith<_$VoiceCommentResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
