/// Full-screen media gallery with swipe navigation and zoom support
/// Supports images and videos with smooth transitions and optimized loading

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import '../models/media_models.dart';
import '../widgets/progressive_image.dart';
import '../widgets/zoomable_image.dart';

class MediaGalleryScreen extends StatefulWidget {
  final MediaGalleryData galleryData;
  final int initialIndex;

  const MediaGalleryScreen({
    Key? key,
    required this.galleryData,
    this.initialIndex = 0,
  }) : super(key: key);

  @override
  State<MediaGalleryScreen> createState() => _MediaGalleryScreenState();
}

class _MediaGalleryScreenState extends State<MediaGalleryScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _overlayController;
  late Animation<double> _overlayAnimation;
  
  int _currentIndex = 0;
  bool _showOverlay = true;
  bool _isFullscreen = false;
  Map<int, VideoPlayerController> _videoControllers = {};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex.clamp(0, widget.galleryData.totalItems - 1);
    _pageController = PageController(initialPage: _currentIndex);
    
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.easeInOut,
    ));

    _overlayController.forward();
    _enterFullscreen();
    _preloadAdjacentItems();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _overlayController.dispose();
    _disposeVideoControllers();
    _exitFullscreen();
    super.dispose();
  }

  void _enterFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    setState(() {
      _isFullscreen = true;
    });
  }

  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    setState(() {
      _isFullscreen = false;
    });
  }

  void _toggleOverlay() {
    setState(() {
      _showOverlay = !_showOverlay;
    });
    
    if (_showOverlay) {
      _overlayController.forward();
    } else {
      _overlayController.reverse();
    }
  }

  void _preloadAdjacentItems() {
    // Preload previous and next items for smooth navigation
    final prevIndex = widget.galleryData.getPreviousIndex(_currentIndex);
    final nextIndex = widget.galleryData.getNextIndex(_currentIndex);
    
    _preloadItem(prevIndex);
    _preloadItem(nextIndex);
  }

  void _preloadItem(int index) {
    final item = widget.galleryData.getItemAt(index);
    if (item != null && item.isVideo) {
      _initializeVideoController(index, item);
    }
  }

  void _initializeVideoController(int index, MediaGalleryItem item) {
    if (_videoControllers.containsKey(index) || item.videoSource == null) {
      return;
    }

    final controller = VideoPlayerController.network(item.videoSource!);
    _videoControllers[index] = controller;
    
    controller.initialize().then((_) {
      if (mounted && index == _currentIndex) {
        setState(() {});
      }
    });
  }

  void _disposeVideoControllers() {
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    _videoControllers.clear();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
    
    // Pause all videos except current
    for (final entry in _videoControllers.entries) {
      if (entry.key != index) {
        entry.value.pause();
      }
    }
    
    _preloadAdjacentItems();
  }

  void _navigateToIndex(int index) {
    if (index >= 0 && index < widget.galleryData.totalItems) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _closeGallery() {
    Navigator.of(context).pop();
  }

  Widget _buildImageItem(MediaGalleryItem item) {
    return ZoomableImage(
      imageUrl: item.getBestUrl(quality: 'large'),
      heroTag: 'media_${item.id}',
      onTap: _toggleOverlay,
      minScale: 0.5,
      maxScale: 3.0,
    );
  }

  Widget _buildVideoItem(MediaGalleryItem item, int index) {
    final controller = _videoControllers[index];
    
    if (controller == null || !controller.value.isInitialized) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (item.videoPoster != null)
                Image.network(
                  item.videoPoster!,
                  fit: BoxFit.contain,
                ),
              const SizedBox(height: 16),
              const CircularProgressIndicator(color: Colors.white),
              const SizedBox(height: 8),
              const Text(
                'Loading video...',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: _toggleOverlay,
      child: Container(
        color: Colors.black,
        child: Center(
          child: AspectRatio(
            aspectRatio: controller.value.aspectRatio,
            child: VideoPlayer(controller),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaItem(MediaGalleryItem item, int index) {
    if (item.isImage) {
      return _buildImageItem(item);
    } else if (item.isVideo) {
      return _buildVideoItem(item, index);
    } else {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Text(
            'Unsupported media type',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }
  }

  Widget _buildThumbnailStrip() {
    if (widget.galleryData.totalItems <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.galleryData.totalItems,
        itemBuilder: (context, index) {
          final item = widget.galleryData.galleryItems[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: GestureDetector(
              onTap: () => _navigateToIndex(index),
              child: Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: index == _currentIndex
                      ? Border.all(color: Colors.white, width: 2)
                      : null,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: item.isVideo
                      ? Stack(
                          children: [
                            Image.network(
                              item.videoPoster ?? item.thumbnail ?? '',
                              fit: BoxFit.cover,
                              width: 64,
                              height: 64,
                            ),
                            const Center(
                              child: Icon(
                                Icons.play_circle_outline,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ],
                        )
                      : Image.network(
                          item.thumbnail ?? item.getBestUrl(quality: 'thumbnail'),
                          fit: BoxFit.cover,
                          width: 64,
                          height: 64,
                        ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverlay() {
    return FadeTransition(
      opacity: _overlayAnimation,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withOpacity(0.7),
              Colors.transparent,
              Colors.transparent,
              Colors.black.withOpacity(0.7),
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Top bar
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: _closeGallery,
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.galleryData.galleryItems[_currentIndex].title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '${_currentIndex + 1} of ${widget.galleryData.totalItems}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (widget.galleryData.galleryItems[_currentIndex].isVideo)
                      IconButton(
                        onPressed: () {
                          final controller = _videoControllers[_currentIndex];
                          if (controller != null) {
                            if (controller.value.isPlaying) {
                              controller.pause();
                            } else {
                              controller.play();
                            }
                            setState(() {});
                          }
                        },
                        icon: Icon(
                          _videoControllers[_currentIndex]?.value.isPlaying == true
                              ? Icons.pause
                              : Icons.play_arrow,
                          color: Colors.white,
                        ),
                      ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Bottom thumbnail strip
              _buildThumbnailStrip(),
              
              // Caption
              if (widget.galleryData.galleryItems[_currentIndex].caption != null)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    widget.galleryData.galleryItems[_currentIndex].caption!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Main gallery
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: widget.galleryData.totalItems,
            itemBuilder: (context, index) {
              final item = widget.galleryData.galleryItems[index];
              
              // Initialize video controller if needed
              if (item.isVideo && !_videoControllers.containsKey(index)) {
                _initializeVideoController(index, item);
              }
              
              return _buildMediaItem(item, index);
            },
          ),
          
          // Overlay
          if (_showOverlay) _buildOverlay(),
        ],
      ),
    );
  }
}
