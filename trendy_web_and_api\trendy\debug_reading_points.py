#!/usr/bin/env python3
"""
Debug script for reading points system
Run this to check why reading points aren't being awarded
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')
django.setup()

from gamification.models import EngagementSettings, PostReadingHistory
from gamification.services import GamificationService
from accounts.models import CustomUser
from blog.models import Post

def debug_reading_points():
    print("🔍 DEBUGGING READING POINTS SYSTEM")
    print("=" * 50)
    
    # Check engagement settings
    settings = EngagementSettings.get_settings()
    print(f"\n📋 Engagement Settings:")
    print(f"   - Fraud detection: {settings.enable_fraud_detection}")
    print(f"   - Min reading time: {settings.min_reading_time_seconds}s")
    print(f"   - Min scroll percentage: {settings.min_scroll_percentage}%")
    print(f"   - Reading points: {settings.reading_points}")
    
    # Get users
    users = CustomUser.objects.all()[:5]
    print(f"\n👥 Available Users ({CustomUser.objects.count()} total):")
    for user in users:
        print(f"   - {user.username} (ID: {user.id})")
    
    # Get posts
    posts = Post.objects.all()[:5]
    print(f"\n📝 Available Posts ({Post.objects.count()} total):")
    for post in posts:
        print(f"   - Post {post.id}: {post.title[:50]}...")
    
    # Check reading history
    print(f"\n📚 Recent Reading History:")
    recent_readings = PostReadingHistory.objects.all().order_by('-first_read_at')[:10]
    if recent_readings:
        for reading in recent_readings:
            print(f"   - {reading.user.username} read Post {reading.post_id}: "
                  f"Points={reading.points_awarded}, Reward={reading.reward_given}, "
                  f"Time={reading.time_spent_seconds}s, Scroll={reading.scroll_percentage}%")
    else:
        print("   - No reading history found")
    
    # Test with a specific user
    print(f"\n🧪 TESTING READING POINTS")
    print("-" * 30)
    
    # Get test user (you can change this to your username)
    test_username = input("Enter your username (or press Enter for 'testuser'): ").strip()
    if not test_username:
        test_username = 'testuser'
    
    try:
        user = CustomUser.objects.get(username=test_username)
        print(f"✅ Found user: {user.username}")
    except CustomUser.DoesNotExist:
        print(f"❌ User '{test_username}' not found")
        return
    
    # Get a post to test with
    post_id = input("Enter post ID to test (or press Enter for first available): ").strip()
    if not post_id:
        post = Post.objects.first()
        post_id = post.id
    else:
        try:
            post_id = int(post_id)
            post = Post.objects.get(id=post_id)
        except (ValueError, Post.DoesNotExist):
            print(f"❌ Invalid post ID")
            return
    
    print(f"📖 Testing with Post {post_id}: {post.title[:50]}...")
    
    # Check if user has already read this post
    existing = PostReadingHistory.objects.filter(user=user, post_id=post_id).first()
    if existing:
        print(f"⚠️  User has already read this post:")
        print(f"   - Points awarded: {existing.points_awarded}")
        print(f"   - Reward given: {existing.reward_given}")
        print(f"   - Time spent: {existing.time_spent_seconds}s")
        print(f"   - Scroll percentage: {existing.scroll_percentage}%")
        print(f"   - Read count: {existing.read_count}")
        
        # Ask if they want to test with a different post
        print(f"\n💡 Try with a post you haven't read yet!")
        unread_posts = Post.objects.exclude(
            id__in=PostReadingHistory.objects.filter(user=user).values_list('post_id', flat=True)
        )[:5]
        if unread_posts:
            print(f"   Unread posts for {user.username}:")
            for unread_post in unread_posts:
                print(f"   - Post {unread_post.id}: {unread_post.title[:50]}...")
        return
    
    # Test reading points validation
    time_spent = 15  # seconds
    scroll_percentage = 50.0  # percent
    
    print(f"\n🔍 Testing with: Time={time_spent}s, Scroll={scroll_percentage}%")
    
    can_earn, reason = GamificationService.can_earn_reading_points(
        user, post_id, time_spent, scroll_percentage
    )
    print(f"Can earn points: {can_earn}")
    print(f"Reason: {reason}")
    
    if can_earn:
        print(f"\n✅ User should be able to earn points!")
        print(f"💡 Make sure you:")
        print(f"   1. Are logged in as '{user.username}'")
        print(f"   2. Spend at least {settings.min_reading_time_seconds} seconds reading")
        print(f"   3. Scroll at least {settings.min_scroll_percentage}% of the post")
        print(f"   4. Haven't read this post before")
    else:
        print(f"\n❌ User cannot earn points: {reason}")

if __name__ == "__main__":
    debug_reading_points()
