import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:url_launcher/url_launcher.dart';

class ComplianceService {
  static const String privacyPolicyUrl = 'https://trendy.app/privacy';
  static const String termsOfServiceUrl = 'https://trendy.app/terms';
  static const String communityGuidelinesUrl = 'https://trendy.app/guidelines';

  // Blockchain compliance disclaimers
  static const String blockchainDisclaimer = '''
BLOCKCHAIN FEATURES DISCLOSURE:

• TRD tokens are utility tokens for platform rewards only
• Not intended as investment or financial instruments
• Subject to local cryptocurrency regulations
• Platform reserves right to modify token mechanics
• Users responsible for tax compliance in their jurisdiction
• Tokens have no guaranteed monetary value
• Platform may discontinue blockchain features at any time

By using blockchain features, you acknowledge understanding these terms.
''';

  static const String paymentDisclaimer = '''
PAYMENT INFORMATION:

• Digital content purchases use platform payment systems
• External payments only for physical goods and services
• All transactions subject to platform terms
• Refunds handled according to platform policies
• Currency exchange rates may apply
• Additional fees may apply for external payments

Please review our Terms of Service for complete payment terms.
''';

  // Show compliance dialogs
  static Future<bool> showBlockchainDisclaimer(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Blockchain Features'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Important Information',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Text(
                  blockchainDisclaimer,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.info_outline, size: 16),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'This disclosure is required for app store compliance.',
                        style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Decline'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('I Understand'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  static Future<bool> showPaymentDisclaimer(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Payment Information'),
          content: SingleChildScrollView(
            child: Text(
              paymentDisclaimer,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Continue'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  // Age verification
  static Future<bool> verifyAge(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Age Verification'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'This app is intended for users 13 years and older.',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 16),
              Text(
                'By continuing, you confirm that you are at least 13 years old.',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 12),
              Text(
                'If you are under 18, please ensure you have parental permission to use this app.',
                style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('I am under 13'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('I am 13 or older'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  // Open legal documents
  static Future<void> openPrivacyPolicy() async {
    final Uri url = Uri.parse(privacyPolicyUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  static Future<void> openTermsOfService() async {
    final Uri url = Uri.parse(termsOfServiceUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  static Future<void> openCommunityGuidelines() async {
    final Uri url = Uri.parse(communityGuidelinesUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    }
  }

  // Content reporting
  static Future<void> reportContent(BuildContext context, {
    required String contentId,
    required String contentType,
    String? reason,
  }) async {
    final reasons = [
      'Inappropriate content',
      'Spam or misleading',
      'Harassment or bullying',
      'Hate speech',
      'Violence or dangerous content',
      'Copyright infringement',
      'Other',
    ];

    String? selectedReason = reason;
    
    if (selectedReason == null) {
      selectedReason = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            title: const Text('Report Content'),
            children: reasons.map((reason) {
              return SimpleDialogOption(
                onPressed: () => Navigator.of(context).pop(reason),
                child: Text(reason),
              );
            }).toList(),
          );
        },
      );
    }

    if (selectedReason != null) {
      // Send report to backend
      try {
        // TODO: Implement actual reporting API call
        await Future.delayed(const Duration(seconds: 1)); // Simulate API call
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Content reported. Thank you for helping keep our community safe.'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to report content. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Block user
  static Future<void> blockUser(BuildContext context, {
    required String userId,
    required String username,
  }) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Block User'),
          content: Text('Are you sure you want to block @$username? You will no longer see their content.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Block'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        // TODO: Implement actual blocking API call
        await Future.delayed(const Duration(seconds: 1)); // Simulate API call
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('@$username has been blocked.'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to block user. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Store-compliant payment handling
  static Future<bool> purchaseDigitalContent({
    required String productId,
    required BuildContext context,
  }) async {
    try {
      // Show payment disclaimer first
      final accepted = await showPaymentDisclaimer(context);
      if (!accepted) return false;

      // Use in-app purchase for digital content (store compliance)
      final InAppPurchase inAppPurchase = InAppPurchase.instance;
      
      if (!await inAppPurchase.isAvailable()) {
        throw Exception('In-app purchases not available');
      }

      // Get product details
      final ProductDetailsResponse response = await inAppPurchase.queryProductDetails({productId});
      
      if (response.productDetails.isEmpty) {
        throw Exception('Product not found');
      }

      final ProductDetails productDetails = response.productDetails.first;
      
      // Make purchase
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: productDetails);
      await inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
      
      return true;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  // External payment for physical goods only (compliance)
  static Future<bool> purchasePhysicalGood({
    required double amount,
    required String description,
    required BuildContext context,
  }) async {
    try {
      // Show disclaimer
      final accepted = await showPaymentDisclaimer(context);
      if (!accepted) return false;

      // External payment is allowed for physical goods
      // TODO: Implement PayPal or other external payment
      await Future.delayed(const Duration(seconds: 2)); // Simulate payment
      
      return true;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }

  // Check if user has accepted all required agreements
  static Future<bool> checkComplianceStatus() async {
    // TODO: Check user's agreement status from local storage or backend
    return true; // Placeholder
  }

  // Initialize compliance on app start
  static Future<void> initializeCompliance(BuildContext context) async {
    // Check age verification
    final ageVerified = await verifyAge(context);
    if (!ageVerified) {
      SystemNavigator.pop(); // Exit app if age not verified
      return;
    }

    // Show blockchain disclaimer if user will use blockchain features
    // This can be shown later when user first accesses blockchain features
  }
}
