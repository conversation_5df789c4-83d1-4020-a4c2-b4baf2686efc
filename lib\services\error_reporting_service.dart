import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/app_error.dart';
import '../services/connectivity_service.dart';

class ErrorReportingService {
  static final ErrorReportingService _instance =
      ErrorReportingService._internal();
  factory ErrorReportingService() => _instance;
  ErrorReportingService._internal();

  final List<AppError> _errorHistory = [];
  final StreamController<AppError> _errorController =
      StreamController<AppError>.broadcast();

  ConnectivityService? _connectivityService;
  bool _isInitialized = false;

  // Getters
  Stream<AppError> get errorStream => _errorController.stream;
  List<AppError> get errorHistory => List.unmodifiable(_errorHistory);

  /// Initialize the error reporting service
  void initialize(ConnectivityService connectivityService) {
    if (_isInitialized) return;

    _connectivityService = connectivityService;
    _setupGlobalErrorHandling();
    _isInitialized = true;

    if (kDebugMode) {
      print('🚨 Error reporting service initialized');
    }
  }

  /// Setup global error handling
  void _setupGlobalErrorHandling() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      final error = AppError(
        type: AppErrorType.unknown,
        message: 'Flutter framework error: ${details.exception}',
        userMessage: 'An unexpected error occurred in the app.',
        suggestion:
            'Please restart the app. If the problem persists, contact support.',
        details: details.stack?.toString(),
        isRetryable: true,
        isTemporary: true,
      );

      reportError(error);

      // Also report to Flutter's default error handler in debug mode
      if (kDebugMode) {
        FlutterError.presentError(details);
      }
    };

    // Handle async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      final appError = AppError(
        type: AppErrorType.unknown,
        message: 'Unhandled async error: $error',
        userMessage: 'An unexpected error occurred.',
        suggestion:
            'Please try again. If the problem persists, restart the app.',
        details: stack.toString(),
        isRetryable: true,
        isTemporary: true,
      );

      reportError(appError);
      return true;
    };
  }

  /// Report an error
  void reportError(AppError error) {
    // Add to history (keep last 50 errors)
    _errorHistory.add(error);
    if (_errorHistory.length > 50) {
      _errorHistory.removeAt(0);
    }

    // Emit to stream
    _errorController.add(error);

    // Log error based on type and environment
    _logError(error);

    // Send to remote service if online (in production)
    if (!kDebugMode && _connectivityService?.isOnline == true) {
      _sendToRemoteService(error);
    }
  }

  /// Log error locally
  void _logError(AppError error) {
    final timestamp = DateTime.now().toIso8601String();
    final logLevel = _getLogLevel(error.type);

    if (kDebugMode) {
      print('[$timestamp] $logLevel: ${error.message}');
      if (error.details != null) {
        print('Details: ${error.details}');
      }
    }
  }

  /// Get log level for error type
  String _getLogLevel(AppErrorType type) {
    switch (type) {
      case AppErrorType.network:
        return 'WARN';
      case AppErrorType.server:
        return 'ERROR';
      case AppErrorType.authentication:
        return 'WARN';
      case AppErrorType.authorization:
        return 'WARN';
      case AppErrorType.validation:
        return 'INFO';
      case AppErrorType.notFound:
        return 'INFO';
      case AppErrorType.maintenance:
        return 'INFO';
      case AppErrorType.featureDisabled:
        return 'INFO';
      case AppErrorType.storage:
        return 'ERROR';
      case AppErrorType.unknown:
        return 'ERROR';
    }
  }

  /// Send error to remote service (placeholder)
  Future<void> _sendToRemoteService(AppError error) async {
    try {
      // In a real app, you would send this to your error reporting service
      // like Sentry, Crashlytics, or your own backend

      final errorData = {
        'timestamp': DateTime.now().toIso8601String(),
        'type': error.type.name,
        'message': error.message,
        'user_message': error.userMessage,
        'details': error.details,
        'status_code': error.statusCode,
        'is_retryable': error.isRetryable,
        'is_temporary': error.isTemporary,
        'platform': Platform.operatingSystem,
        'app_version': '1.0.0', // You would get this from package_info
      };

      if (kDebugMode) {
        print('📤 Would send error to remote service: $errorData');
      }

      // TODO: Implement actual remote reporting
      // await _httpClient.post('/api/errors', data: errorData);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to send error to remote service: $e');
      }
    }
  }

  /// Get user-friendly error message for common scenarios
  static String getUserFriendlyMessage(dynamic error) {
    if (error is AppError) {
      return error.userMessage;
    }

    if (error is SocketException) {
      return 'Unable to connect to the internet. Please check your connection and try again.';
    }

    if (error is TimeoutException) {
      return 'The request is taking too long. Please try again.';
    }

    if (error is FormatException) {
      return 'The data received is in an unexpected format. Please try again.';
    }

    if (error is ArgumentError) {
      return 'Invalid input provided. Please check your data and try again.';
    }

    // Generic fallback
    return 'Something went wrong. Please try again.';
  }

  /// Get user-friendly error message specifically for payment and shop errors
  static String getPaymentErrorMessage(dynamic error, {String? context}) {
    final errorString = error.toString().toLowerCase();

    // PayPal specific errors
    if (errorString.contains('paypal')) {
      if (errorString.contains('not verified') ||
          errorString.contains('verification')) {
        return 'Please verify your PayPal account before making payments.';
      }
      if (errorString.contains('insufficient funds') ||
          errorString.contains('balance')) {
        return 'Insufficient funds in your PayPal account. Please add funds and try again.';
      }
      if (errorString.contains('declined') ||
          errorString.contains('rejected')) {
        return 'Payment was declined by PayPal. Please check your payment method and try again.';
      }
      if (errorString.contains('demo') || errorString.contains('sandbox')) {
        return 'Payment system is in demo mode. Contact support for assistance.';
      }
      return 'PayPal payment failed. Please try again or use a different payment method.';
    }

    // Store points errors
    if (errorString.contains('insufficient points') ||
        errorString.contains('not enough points')) {
      return 'You don\'t have enough store points for this purchase. Earn more points or buy a point boost.';
    }

    // Premium subscription errors
    if (errorString.contains('subscription') ||
        errorString.contains('premium')) {
      if (errorString.contains('already active') ||
          errorString.contains('already subscribed')) {
        return 'You already have an active premium subscription.';
      }
      return 'Failed to process premium subscription. Please try again.';
    }

    // Purchase errors
    if (errorString.contains('purchase') || errorString.contains('buy')) {
      if (errorString.contains('already owned') ||
          errorString.contains('already purchased')) {
        return 'You already own this item.';
      }
      if (errorString.contains('out of stock') ||
          errorString.contains('unavailable')) {
        return 'This item is currently unavailable. Please try again later.';
      }
      return 'Purchase failed. Please check your payment method and try again.';
    }

    // Network and server errors
    if (error is SocketException) {
      return 'Unable to connect to the payment server. Please check your internet connection.';
    }

    if (errorString.contains('server error') || errorString.contains('500')) {
      return 'Payment server is temporarily unavailable. Please try again in a few minutes.';
    }

    if (errorString.contains('timeout')) {
      return 'Payment request timed out. Please try again.';
    }

    // Authentication errors
    if (errorString.contains('unauthorized') || errorString.contains('401')) {
      return 'Please log in again to complete your purchase.';
    }

    // Validation errors
    if (errorString.contains('invalid') && errorString.contains('email')) {
      return 'Please enter a valid email address.';
    }

    if (errorString.contains('invalid') && errorString.contains('amount')) {
      return 'Please enter a valid amount.';
    }

    // Context-specific fallbacks
    if (context != null) {
      switch (context.toLowerCase()) {
        case 'paypal_setup':
          return 'Failed to set up PayPal account. Please check your email and try again.';
        case 'point_boost':
          return 'Failed to purchase point boost. Please try again or contact support.';
        case 'virtual_item':
          return 'Failed to purchase item. Please try again or contact support.';
        case 'premium_subscription':
          return 'Failed to activate premium subscription. Please try again or contact support.';
        case 'payout_request':
          return 'Failed to request payout. Please verify your PayPal account and try again.';
        default:
          break;
      }
    }

    // Generic payment error fallback
    return 'Payment failed. Please try again or contact support if the problem persists.';
  }

  /// Show user-friendly error dialog for payment errors
  static void showPaymentErrorDialog(BuildContext context, dynamic error,
      {String? title, String? errorContext}) {
    final message = getPaymentErrorMessage(error, context: errorContext);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 24),
              const SizedBox(width: 8),
              Text(title ?? 'Payment Error'),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show user-friendly error snackbar for payment errors
  static void showPaymentErrorSnackbar(BuildContext context, dynamic error,
      {String? errorContext}) {
    final message = getPaymentErrorMessage(error, context: errorContext);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Get error statistics
  Map<String, dynamic> getErrorStats() {
    final stats = <AppErrorType, int>{};
    for (final error in _errorHistory) {
      stats[error.type] = (stats[error.type] ?? 0) + 1;
    }

    return {
      'total_errors': _errorHistory.length,
      'error_types': stats.map((key, value) => MapEntry(key.name, value)),
      'recent_errors': _errorHistory
          .take(5)
          .map((e) => {
                'type': e.type.name,
                'message': e.userMessage,
                'timestamp': DateTime.now().toIso8601String(),
              })
          .toList(),
    };
  }

  /// Clear error history
  void clearHistory() {
    _errorHistory.clear();
    if (kDebugMode) {
      print('🧹 Error history cleared');
    }
  }

  /// Check if there are recent critical errors
  bool hasRecentCriticalErrors({Duration? within}) {
    final timeLimit = within ?? const Duration(minutes: 5);
    final cutoff = DateTime.now().subtract(timeLimit);

    // This is a simplified check - in a real app you'd track timestamps
    return _errorHistory.any((error) =>
        error.type == AppErrorType.server ||
        error.type == AppErrorType.storage ||
        error.type == AppErrorType.unknown);
  }

  /// Get suggested actions for current error state
  List<String> getSuggestedActions() {
    final suggestions = <String>[];

    if (_connectivityService?.isOffline == true) {
      suggestions.add('Check your internet connection');
    }

    if (hasRecentCriticalErrors()) {
      suggestions.add('Restart the app');
      suggestions.add('Clear app cache');
    }

    final networkErrors =
        _errorHistory.where((e) => e.type == AppErrorType.network).length;
    if (networkErrors > 3) {
      suggestions.add('Try switching between WiFi and mobile data');
    }

    if (suggestions.isEmpty) {
      suggestions.add('Try again in a few moments');
    }

    return suggestions;
  }

  /// Dispose resources
  void dispose() {
    _errorController.close();
    _isInitialized = false;
  }
}
