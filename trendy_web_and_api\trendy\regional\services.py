"""
Regional content filtering services
"""

import requests
import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from .models import Country, Region, UserLocationHistory
from typing import Optional, Dict, Any

User = get_user_model()
logger = logging.getLogger(__name__)


class LocationDetectionService:
    """Service for detecting user location and managing regional preferences"""
    
    # Free IP geolocation services (with fallbacks)
    GEOLOCATION_SERVICES = [
        {
            'name': 'ipapi',
            'url': 'http://ip-api.com/json/{ip}',
            'country_field': 'countryCode',
            'confidence_field': None,
        },
        {
            'name': 'ipinfo',
            'url': 'https://ipinfo.io/{ip}/json',
            'country_field': 'country',
            'confidence_field': None,
        },
        {
            'name': 'geojs',
            'url': 'https://get.geojs.io/v1/ip/geo/{ip}.json',
            'country_field': 'country_code',
            'confidence_field': None,
        }
    ]
    
    @staticmethod
    def get_client_ip(request):
        """Extract client IP address from request"""
        # Handle both Django HttpRequest and DRF Request objects
        if hasattr(request, '_request'):
            # DRF Request object - get the underlying Django request
            django_request = request._request
            meta = django_request.META
        else:
            # Django HttpRequest object
            meta = request.META

        # Try multiple headers to get the real IP
        ip_headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR',
        ]

        ip = None
        for header in ip_headers:
            value = meta.get(header)
            if value:
                # Handle comma-separated IPs (take the first one)
                ip = value.split(',')[0].strip()
                if ip and ip not in ['unknown', '']:
                    break

        # Debug logging
        logger.info(f"🔍 IP Detection Debug:")
        logger.info(f"  - Raw REMOTE_ADDR: {meta.get('REMOTE_ADDR')}")
        logger.info(f"  - HTTP_X_FORWARDED_FOR: {meta.get('HTTP_X_FORWARDED_FOR')}")
        logger.info(f"  - HTTP_X_REAL_IP: {meta.get('HTTP_X_REAL_IP')}")
        logger.info(f"  - Final detected IP: {ip}")

        # Handle localhost/development IPs
        if ip in ['127.0.0.1', '::1', 'localhost']:
            logger.info(f"  - Localhost IP detected, skipping geolocation")
            return None

        # Handle private network IPs (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
        if ip and (ip.startswith('192.168.') or ip.startswith('10.') or
                   any(ip.startswith(f'172.{i}.') for i in range(16, 32))):
            logger.info(f"  - Private network IP detected: {ip}")
            # For development/testing, we can try to get the public IP
            # or return None to let user manually select country
            return None

        return ip

    @staticmethod
    def get_public_ip():
        """Get the server's public IP address as a fallback"""
        try:
            # Try multiple services to get public IP
            services = [
                'https://api.ipify.org?format=json',
                'https://httpbin.org/ip',
                'https://api.myip.com',
            ]

            for service in services:
                try:
                    response = requests.get(service, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        # Different services return IP in different formats
                        ip = data.get('ip') or data.get('origin') or data.get('ip_address')
                        if ip:
                            logger.info(f"Got public IP from {service}: {ip}")
                            return ip
                except Exception as e:
                    logger.warning(f"Failed to get IP from {service}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error getting public IP: {e}")

        return None
    
    @staticmethod
    def detect_country_from_ip(ip_address: str) -> Optional[Dict[str, Any]]:
        """Detect country from IP address using multiple services"""
        if not ip_address:
            return None
        
        # Check cache first
        cache_key = f"ip_location_{ip_address}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
        
        for service in LocationDetectionService.GEOLOCATION_SERVICES:
            try:
                url = service['url'].format(ip=ip_address)
                response = requests.get(url, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    country_code = data.get(service['country_field'])
                    
                    if country_code:
                        result = {
                            'country_code': country_code.upper(),
                            'service': service['name'],
                            'confidence': 0.8,  # Default confidence
                            'raw_data': data
                        }
                        
                        # Cache for 1 hour
                        cache.set(cache_key, result, 3600)
                        return result
                        
            except Exception as e:
                logger.warning(f"Failed to get location from {service['name']}: {e}")
                continue
        
        return None
    
    @staticmethod
    def get_or_detect_user_country(user, request=None) -> Optional[Country]:
        """Get user's country with automatic detection if needed"""
        # Return preferred country if set
        if user.preferred_country:
            return user.preferred_country
        
        # Return detected country if available and recent
        if user.detected_country:
            # Check if detection is recent (within 7 days)
            recent_detection = UserLocationHistory.objects.filter(
                user=user,
                detected_country=user.detected_country,
                created_at__gte=timezone.now() - timezone.timedelta(days=7)
            ).first()
            
            if recent_detection:
                return user.detected_country
        
        # Attempt new detection if auto-detection is enabled
        if user.auto_detect_location and request:
            detected_country = LocationDetectionService.detect_and_save_location(user, request)
            if detected_country:
                return detected_country
        
        return None
    
    @staticmethod
    def detect_and_save_location(user, request) -> Optional[Country]:
        """Detect user location and save to database"""
        ip_address = LocationDetectionService.get_client_ip(request)
        if not ip_address:
            return None
        
        # Detect country from IP
        location_data = LocationDetectionService.detect_country_from_ip(ip_address)
        if not location_data:
            return None
        
        try:
            # Find country in database
            country = Country.objects.get(
                code=location_data['country_code'],
                is_active=True
            )
            
            # Update user's detected country
            user.detected_country = country
            user.save(update_fields=['detected_country'])
            
            # Save location history
            UserLocationHistory.objects.create(
                user=user,
                detected_country=country,
                ip_address=ip_address,
                detection_method='ip_geolocation',
                confidence_score=location_data.get('confidence', 0.8)
            )
            
            logger.info(f"Detected country {country.name} for user {user.username}")
            return country
            
        except Country.DoesNotExist:
            logger.warning(f"Country {location_data['country_code']} not found in database")
            
            # Save failed detection
            UserLocationHistory.objects.create(
                user=user,
                detected_country=None,
                ip_address=ip_address,
                detection_method='ip_geolocation',
                confidence_score=0.0
            )
            
        except Exception as e:
            logger.error(f"Error saving location detection: {e}")
        
        return None
    
    @staticmethod
    def set_user_preferred_country(user, country_code: Optional[str]) -> bool:
        """Set user's preferred country or clear it if country_code is None"""
        try:
            if country_code is None:
                # Clear the preferred country
                user.preferred_country = None
                user.save(update_fields=['preferred_country'])

                # Log clearing of preference
                UserLocationHistory.objects.create(
                    user=user,
                    detected_country=None,
                    ip_address='0.0.0.0',  # Manual selection
                    detection_method='manual_clear',
                    confidence_score=1.0
                )

                return True
            else:
                # Set the preferred country
                country = Country.objects.get(code=country_code.upper(), is_active=True)
                user.preferred_country = country
                user.save(update_fields=['preferred_country'])

                # Log manual selection
                UserLocationHistory.objects.create(
                    user=user,
                    detected_country=country,
                    ip_address='0.0.0.0',  # Manual selection
                    detection_method='manual_selection',
                    confidence_score=1.0
                )

                return True
        except Country.DoesNotExist:
            return False
    
    @staticmethod
    def get_available_countries():
        """Get list of available countries for selection"""
        return Country.objects.filter(is_active=True).select_related('region')


class RegionalContentService:
    """Service for filtering content based on user's region"""
    
    @staticmethod
    def get_user_content_countries(user, request=None):
        """Get countries for which user should see content"""
        countries = []
        
        # Get user's effective country
        user_country = LocationDetectionService.get_or_detect_user_country(user, request)
        if user_country:
            countries.append(user_country)
        
        return countries
    
    @staticmethod
    def filter_posts_by_region(queryset, user, request=None):
        """Filter posts based on user's regional preferences"""
        from blog.models import Post
        from django.db.models import Q
        
        # Get user's countries
        user_countries = RegionalContentService.get_user_content_countries(user, request)
        
        # Build filter query
        filters = Q()
        
        # Always include global posts if user allows it
        if user.show_global_content:
            filters |= Q(is_global=True)
        
        # Include posts targeted to user's countries
        if user_countries:
            filters |= Q(target_countries__in=user_countries)
        
        # If no specific filters, show global content
        if not filters:
            filters = Q(is_global=True)
        
        return queryset.filter(filters).distinct()
    
    @staticmethod
    def get_regional_stats(user):
        """Get statistics about regional content for user"""
        from blog.models import Post
        
        user_countries = RegionalContentService.get_user_content_countries(user)
        
        stats = {
            'user_country': user.effective_country.name if user.effective_country else None,
            'total_posts': Post.objects.filter(status='published').count(),
            'global_posts': Post.objects.filter(status='published', is_global=True).count(),
            'regional_posts': 0,
            'available_countries': Country.objects.filter(is_active=True).count(),
        }
        
        if user_countries:
            stats['regional_posts'] = Post.objects.filter(
                status='published',
                target_countries__in=user_countries
            ).distinct().count()
        
        return stats
