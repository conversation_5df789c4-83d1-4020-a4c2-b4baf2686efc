import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../providers/notifications_provider.dart';
import '../providers/auth_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/notification_card.dart';
import '../widgets/empty_state_widget.dart';

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _scrollController.addListener(_onScroll);
    
    // Load notifications when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationsProvider.notifier).loadNotifications(refresh: true);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      ref.read(notificationsProvider.notifier).loadMoreNotifications();
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final notificationsState = ref.watch(notificationsProvider);
    final unreadCount = ref.watch(unreadCountProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notifications',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
            if (unreadCount > 0)
              Text(
                '$unreadCount unread',
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondary,
                  fontWeight: FontWeight.normal,
                ),
              ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: AppTheme.textPrimary,
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: () => _markAllAsRead(),
              child: Text(
                'Mark all read',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshNotifications(),
          ),
        ],
        bottom: authState.user != null ? TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondary,
          indicatorColor: AppTheme.primaryColor,
          labelStyle: const TextStyle(fontWeight: FontWeight.w600),
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Likes'),
            Tab(text: 'Comments'),
            Tab(text: 'Follows'),
            Tab(text: 'System'),
          ],
        ) : null,
      ),
      body: authState.user == null
          ? _buildNotLoggedIn()
          : _buildNotificationsContent(notificationsState),
    );
  }

  Widget _buildNotLoggedIn() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Please log in to view notifications',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/auth');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text('Login'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsContent(notificationsState) {
    if (notificationsState.isLoading && notificationsState.notifications.isEmpty) {
      return _buildLoadingState();
    }

    if (notificationsState.notifications.isEmpty && !notificationsState.isLoading) {
      return _buildEmptyState();
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildNotificationsList(notificationsState.notifications),
        _buildNotificationsList(
          ref.read(notificationsProvider.notifier).getNotificationsByType('like'),
        ),
        _buildNotificationsList(
          ref.read(notificationsProvider.notifier).getNotificationsByType('comment'),
        ),
        _buildNotificationsList(
          ref.read(notificationsProvider.notifier).getNotificationsByType('follow'),
        ),
        _buildNotificationsList(
          ref.read(notificationsProvider.notifier).getNotificationsByType('system'),
        ),
      ],
    );
  }

  Widget _buildNotificationsList(notifications) {
    return RefreshIndicator(
      onRefresh: _refreshNotifications,
      color: AppTheme.primaryColor,
      child: AnimationLimiter(
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: notifications.length + (ref.watch(notificationsProvider).hasMorePages ? 1 : 0),
          itemBuilder: (context, index) {
            // Show loading indicator at the bottom
            if (index == notifications.length) {
              return Container(
                padding: const EdgeInsets.all(16),
                child: Center(
                  child: ref.watch(notificationsProvider).isLoadingMore
                      ? const CircularProgressIndicator()
                      : ElevatedButton(
                          onPressed: () => ref.read(notificationsProvider.notifier).loadMoreNotifications(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          child: const Text('Load More'),
                        ),
                ),
              );
            }

            final notification = notifications[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 375),
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: NotificationCard(
                    notification: notification,
                    onTap: () => _handleNotificationTap(notification),
                    onMarkAsRead: () => _markAsRead(notification.id),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Loading notifications...',
            style: TextStyle(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return EmptyStateWidget(
      icon: Icons.notifications_none_outlined,
      title: 'No notifications yet',
      subtitle: 'When you get notifications, they\'ll appear here',
      actionText: 'Refresh',
      onAction: _refreshNotifications,
    );
  }

  Future<void> _refreshNotifications() async {
    await ref.read(notificationsProvider.notifier).loadNotifications(refresh: true);
  }

  Future<void> _markAllAsRead() async {
    try {
      await ref.read(notificationsProvider.notifier).markAllAsRead();
      _showSuccessSnackBar('All notifications marked as read');
    } catch (e) {
      _showErrorSnackBar('Failed to mark notifications as read');
    }
  }

  Future<void> _markAsRead(int notificationId) async {
    try {
      await ref.read(notificationsProvider.notifier).markAsRead(notificationId);
    } catch (e) {
      _showErrorSnackBar('Failed to mark notification as read');
    }
  }

  void _handleNotificationTap(notification) {
    // Mark as read if not already read
    if (!notification.isRead) {
      _markAsRead(notification.id);
    }

    // Navigate based on notification type
    switch (notification.notificationType) {
      case 'like':
      case 'comment':
        if (notification.postId != null) {
          Navigator.pushNamed(
            context,
            '/post',
            arguments: notification.postId.toString(),
          );
        }
        break;
      case 'follow':
        // Navigate to user profile
        break;
      case 'mention':
        if (notification.postId != null) {
          Navigator.pushNamed(
            context,
            '/post',
            arguments: notification.postId.toString(),
          );
        }
        break;
      default:
        // Handle other notification types
        break;
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
