from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import FileExtensionValidator
from blog.models import Post
import os

User = get_user_model()

def voice_comment_upload_path(instance, filename):
    """Generate upload path for voice comments"""
    return f'voice_comments/{instance.post.id}/{filename}'

class VoiceComment(models.Model):
    """Voice comment model for audio comments on posts"""
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='voice_comments')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='voice_comments')

    # Audio file
    audio_file = models.FileField(
        upload_to=voice_comment_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['mp3', 'm4a', 'wav', 'ogg'])],
        help_text="Supported formats: MP3, M4A, WAV, OGG"
    )

    # Metadata
    duration_seconds = models.PositiveIntegerField(default=0)
    file_size = models.PositiveIntegerField(default=0)  # in bytes

    # Transcription
    transcription = models.TextField(blank=True, null=True)
    is_transcribed = models.BooleanField(default=False)
    transcription_confidence = models.FloatField(default=0.0)  # 0.0 to 1.0

    # Engagement
    like_count = models.PositiveIntegerField(default=0)

    # Status
    is_active = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=True)  # For moderation

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', '-created_at']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['is_active', 'is_approved']),
        ]

    def __str__(self):
        return f"Voice comment by {self.user.username} on {self.post.title}"

    @property
    def audio_url(self):
        """Get the full URL for the audio file"""
        if self.audio_file:
            return self.audio_file.url
        return None

    def delete(self, *args, **kwargs):
        """Override delete to remove audio file"""
        if self.audio_file:
            if os.path.isfile(self.audio_file.path):
                os.remove(self.audio_file.path)
        super().delete(*args, **kwargs)

class VoiceCommentLike(models.Model):
    """Like system for voice comments"""
    voice_comment = models.ForeignKey(VoiceComment, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['voice_comment', 'user']
        indexes = [
            models.Index(fields=['voice_comment', 'user']),
        ]

    def __str__(self):
        return f"{self.user.username} likes voice comment {self.voice_comment.id}"

class AIWritingSession(models.Model):
    """Track AI writing assistance sessions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ai_writing_sessions')
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='ai_sessions', null=True, blank=True)

    # Session data
    session_id = models.CharField(max_length=100, unique=True)
    content_before = models.TextField()
    content_after = models.TextField()

    # AI assistance metrics
    suggestions_count = models.PositiveIntegerField(default=0)
    suggestions_accepted = models.PositiveIntegerField(default=0)
    completion_requests = models.PositiveIntegerField(default=0)
    improvement_requests = models.PositiveIntegerField(default=0)

    # Session metadata
    session_duration = models.PositiveIntegerField(default=0)  # in seconds
    words_added = models.IntegerField(default=0)
    readability_improvement = models.FloatField(default=0.0)

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['user', '-started_at']),
            models.Index(fields=['session_id']),
        ]

    def __str__(self):
        return f"AI session {self.session_id} by {self.user.username}"

class TextToSpeechRequest(models.Model):
    """Track text-to-speech usage"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tts_requests', null=True, blank=True)
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='tts_requests')

    # Request details
    text_length = models.PositiveIntegerField()
    language = models.CharField(max_length=10, default='en-US')
    voice_settings = models.JSONField(default=dict)  # speed, pitch, etc.

    # Usage tracking
    duration_seconds = models.PositiveIntegerField(default=0)
    completed = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', '-created_at']),
            models.Index(fields=['user', '-created_at']),
        ]

    def __str__(self):
        user_str = self.user.username if self.user else 'Anonymous'
        return f"TTS request by {user_str} for {self.post.title}"

class SpeechToTextSession(models.Model):
    """Track speech-to-text usage for voice comments"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='stt_sessions')
    voice_comment = models.OneToOneField(VoiceComment, on_delete=models.CASCADE, related_name='stt_session', null=True, blank=True)

    # Session details
    audio_duration = models.PositiveIntegerField()  # in seconds
    transcription_accuracy = models.FloatField(default=0.0)  # 0.0 to 1.0
    language_detected = models.CharField(max_length=10, default='en-US')

    # Processing details
    processing_time = models.FloatField(default=0.0)  # in seconds
    success = models.BooleanField(default=False)
    error_message = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['success']),
        ]

    def __str__(self):
        return f"STT session by {self.user.username} - {'Success' if self.success else 'Failed'}"
