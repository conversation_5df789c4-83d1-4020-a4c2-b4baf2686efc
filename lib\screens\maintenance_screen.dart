import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/maintenance_provider.dart';
import '../theme/app_theme.dart';

class MaintenanceScreen extends ConsumerWidget {
  const MaintenanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final systemStatus = ref.watch(systemStatusProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: systemStatus.when(
        data: (status) => _buildMaintenanceContent(context, status),
        loading: () => _buildLoadingContent(context),
        error: (error, _) => _buildErrorContent(context, error),
      ),
    );
  }

  Widget _buildMaintenanceContent(BuildContext context, status) {
    final theme = Theme.of(context);
    final maintenance = status.activeMaintenance;

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Maintenance Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.build_rounded,
                size: 60,
                color: Colors.orange,
              ),
            ),

            const SizedBox(height: 32),

            // Title
            Text(
              maintenance?.title ?? 'System Maintenance',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Message
            Text(
              maintenance?.message ??
                  'We\'re currently performing system maintenance to improve your experience. Please check back soon.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Maintenance Type Badge
            if (maintenance?.maintenanceType != null)
              _buildMaintenanceTypeBadge(context, maintenance.maintenanceType),

            const SizedBox(height: 24),

            // Estimated Completion Time
            if (maintenance?.scheduledEnd != null)
              _buildEstimatedCompletion(context, maintenance.scheduledEnd),

            const SizedBox(height: 48),

            // Progress Indicator
            const SizedBox(
              width: 200,
              child: LinearProgressIndicator(
                backgroundColor: Colors.grey,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            ),

            const SizedBox(height: 24),

            Text(
              'Please wait while we complete the maintenance...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 48),

            // Retry Button
            Consumer(
              builder: (context, ref, child) => ElevatedButton.icon(
                onPressed: () {
                  // Refresh system status
                  ref.read(systemStatusProvider.notifier).refresh();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Check Status'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaintenanceTypeBadge(BuildContext context, String type) {
    final theme = Theme.of(context);

    Color badgeColor;
    String displayText;
    IconData icon;

    switch (type.toLowerCase()) {
      case 'emergency':
        badgeColor = Colors.red;
        displayText = 'Emergency Maintenance';
        icon = Icons.warning_rounded;
        break;
      case 'upgrade':
        badgeColor = Colors.blue;
        displayText = 'System Upgrade';
        icon = Icons.upgrade_rounded;
        break;
      case 'database':
        badgeColor = Colors.purple;
        displayText = 'Database Maintenance';
        icon = Icons.storage_rounded;
        break;
      case 'partial':
        badgeColor = Colors.amber;
        displayText = 'Partial Maintenance';
        icon = Icons.build_circle_rounded;
        break;
      default:
        badgeColor = Colors.orange;
        displayText = 'Scheduled Maintenance';
        icon = Icons.schedule_rounded;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: badgeColor, size: 18),
          const SizedBox(width: 8),
          Text(
            displayText,
            style: theme.textTheme.labelMedium?.copyWith(
              color: badgeColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEstimatedCompletion(BuildContext context, DateTime endTime) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final duration = endTime.difference(now);

    String timeText;
    if (duration.isNegative) {
      timeText = 'Maintenance should be completing soon';
    } else if (duration.inDays > 0) {
      timeText =
          'Estimated completion: ${duration.inDays}d ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      timeText =
          'Estimated completion: ${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      timeText = 'Estimated completion: ${duration.inMinutes} minutes';
    } else {
      timeText = 'Completing very soon...';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.access_time_rounded, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Text(
            timeText,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.blue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.orange),
          SizedBox(height: 16),
          Text('Checking system status...'),
        ],
      ),
    );
  }

  Widget _buildErrorContent(BuildContext context, Object error) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 80,
              color: Colors.red.withValues(alpha: 0.7),
            ),
            const SizedBox(height: 24),
            Text(
              'Unable to Check System Status',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'There was an error checking the maintenance status. Please try again.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Consumer(
              builder: (context, ref, child) => ElevatedButton.icon(
                onPressed: () {
                  // Retry checking status
                  ref.read(systemStatusProvider.notifier).refresh();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
