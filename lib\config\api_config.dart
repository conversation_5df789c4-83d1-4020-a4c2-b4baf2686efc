class ApiConfig {
  // Base URL for the API server
  // Update this to match your Django server's address and port
  // Examples:
  // - Local development: 'http://127.0.0.1:8000'
  // - Network access: 'http://**************:8000'
  // - Production: 'https://yourdomain.com'
  static const String baseUrl = 'https://trendyweb-0azf.onrender.com';

  
  // API endpoints
  static const String apiVersion = '/api/v1';
  static const String apiBaseUrl = '$baseUrl$apiVersion';
  
  // Specific endpoints
  static const String postsEndpoint = '$apiBaseUrl/posts/';
  static const String categoriesEndpoint = '$apiBaseUrl/categories/';
  static const String authEndpoint = '$baseUrl/api/auth/';
  static const String loginEndpoint = '${authEndpoint}login/';
  static const String registerEndpoint = '${authEndpoint}register/';
  static const String logoutEndpoint = '${authEndpoint}logout/';
  static const String userEndpoint = '${authEndpoint}user/';
  
  // Media URL helper
  static String getMediaUrl(String? relativePath) {
    if (relativePath == null || relativePath.isEmpty) {
      return '';
    }
    
    // If it's already a full URL, return as is
    if (relativePath.startsWith('http')) {
      return relativePath;
    }
    
    // If it starts with /, remove it to avoid double slashes
    String cleanPath = relativePath.startsWith('/') 
        ? relativePath.substring(1) 
        : relativePath;
    
    return '$baseUrl/$cleanPath';
  }
  
  // Helper to build full API URL
  static String buildApiUrl(String endpoint) {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    
    String cleanEndpoint = endpoint.startsWith('/') 
        ? endpoint.substring(1) 
        : endpoint;
    
    return '$apiBaseUrl/$cleanEndpoint';
  }
  
  // Development/Production environment detection
  static bool get isDevelopment => baseUrl.contains('127.0.0.1') || baseUrl.contains('localhost');
  
  // Timeout configurations
  static const Duration connectTimeout = Duration(seconds: 60);
  static const Duration receiveTimeout = Duration(seconds: 60);
  static const Duration sendTimeout = Duration(seconds: 60);
}
