import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:video_player/video_player.dart';
import 'package:trendy/widgets/video_player_widget.dart';

void main() {
  group('VideoPlayerWidget Tests', () {
    testWidgets('should display loading state initially',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoPlayerWidget(
              videoUrl: 'https://example.com/test-video.mp4',
            ),
          ),
        ),
      );

      // Verify loading state is displayed
      expect(find.text('Loading video...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display error state for invalid URL',
        (WidgetTester tester) async {
      // Build the widget with invalid URL
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoPlayerWidget(
              videoUrl: 'invalid-url',
            ),
          ),
        ),
      );

      // Wait for the error state to appear
      await tester.pumpAndSettle();

      // Verify error state is displayed (the widget shows "Invalid video URL" for invalid URLs)
      expect(find.textContaining('Failed to load video'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline_rounded), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should have proper widget configuration',
        (WidgetTester tester) async {
      // Build the widget with custom parameters
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoPlayerWidget(
              videoUrl: 'https://example.com/test-video.mp4',
              autoPlay: false,
              showControls: true,
              aspectRatio: 16 / 9,
            ),
          ),
        ),
      );

      // Verify widget is created
      expect(find.byType(VideoPlayerWidget), findsOneWidget);
    });

    testWidgets('should handle retry functionality',
        (WidgetTester tester) async {
      // Build the widget with invalid URL to trigger error
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoPlayerWidget(
              videoUrl: 'invalid-url',
            ),
          ),
        ),
      );

      // Wait for error state
      await tester.pumpAndSettle();

      // Find and tap retry button
      final retryButton = find.text('Retry');
      expect(retryButton, findsOneWidget);

      await tester.tap(retryButton);
      await tester.pump();

      // Verify loading state appears again after retry
      expect(find.text('Loading video...'), findsOneWidget);
    });
  });

  group('FullscreenVideoPlayer Tests', () {
    testWidgets('should create fullscreen player', (WidgetTester tester) async {
      // Create a mock video controller
      final controller = VideoPlayerController.networkUrl(
          Uri.parse('https://example.com/test-video.mp4'));

      // Build the fullscreen player
      await tester.pumpWidget(
        MaterialApp(
          home: FullscreenVideoPlayer(
            controller: controller,
            videoUrl: 'https://example.com/test-video.mp4',
          ),
        ),
      );

      // Verify fullscreen player is created
      expect(find.byType(FullscreenVideoPlayer), findsOneWidget);
      expect(find.text('Video Player'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      expect(find.byIcon(Icons.fullscreen_exit_rounded), findsOneWidget);

      // Clean up
      controller.dispose();
    });
  });
}
