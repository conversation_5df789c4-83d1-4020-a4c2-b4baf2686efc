from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from gamification.services import GamificationService
from gamification.models import EngagementSettings, PostReadingHistory, EngagementHistory
from blog.models import Post

User = get_user_model()


class Command(BaseCommand):
    help = 'Test engagement rules and unfair practice prevention'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username to test with')
        parser.add_argument('--post-id', type=int, help='Post ID to test with')

    def handle(self, *args, **options):
        username = options.get('username', 'testuser')
        post_id = options.get('post_id', 1)

        # Get or create test user
        user, created = User.objects.get_or_create(
            username=username,
            defaults={'email': f'{username}@test.com'}
        )
        if created:
            self.stdout.write(f"Created test user: {username}")
        else:
            self.stdout.write(f"Using existing user: {username}")

        # Ensure engagement settings exist
        settings = EngagementSettings.get_settings()
        self.stdout.write(f"Engagement settings loaded: fraud detection = {settings.enable_fraud_detection}")

        self.stdout.write("\n" + "="*50)
        self.stdout.write("TESTING READING ACTIVITY RULES")
        self.stdout.write("="*50)

        # Test 1: First time reading a post (should succeed)
        self.stdout.write("\n1. Testing first-time reading...")
        success, message = GamificationService.update_reading_activity(
            user=user,
            post_id=post_id,
            time_spent=15,  # Above minimum
            scroll_percentage=50.0  # Above minimum
        )
        self.stdout.write(f"Result: {success} - {message}")

        # Test 2: Reading the same post again (should fail)
        self.stdout.write("\n2. Testing duplicate reading...")
        success, message = GamificationService.update_reading_activity(
            user=user,
            post_id=post_id,
            time_spent=20,
            scroll_percentage=60.0
        )
        self.stdout.write(f"Result: {success} - {message}")

        # Test 3: Reading too quickly (should fail)
        self.stdout.write("\n3. Testing rapid reading...")
        success, message = GamificationService.update_reading_activity(
            user=user,
            post_id=post_id + 1,
            time_spent=5,  # Below minimum
            scroll_percentage=50.0
        )
        self.stdout.write(f"Result: {success} - {message}")

        # Test 4: Insufficient scroll (should fail)
        self.stdout.write("\n4. Testing insufficient scroll...")
        success, message = GamificationService.update_reading_activity(
            user=user,
            post_id=post_id + 2,
            time_spent=15,
            scroll_percentage=20.0  # Below minimum
        )
        self.stdout.write(f"Result: {success} - {message}")

        self.stdout.write("\n" + "="*50)
        self.stdout.write("TESTING ENGAGEMENT ACTIVITY RULES")
        self.stdout.write("="*50)

        # Test 5: First like on a post (should succeed)
        self.stdout.write("\n5. Testing first-time like...")
        success, message = GamificationService.update_engagement_activity(
            user=user,
            activity_type='like',
            object_id=post_id,
            target_type='post'
        )
        self.stdout.write(f"Result: {success} - {message}")

        # Test 6: Liking the same post again (should fail)
        self.stdout.write("\n6. Testing duplicate like...")
        success, message = GamificationService.update_engagement_activity(
            user=user,
            activity_type='like',
            object_id=post_id,
            target_type='post'
        )
        self.stdout.write(f"Result: {success} - {message}")

        # Test 7: First comment on a post (should succeed)
        self.stdout.write("\n7. Testing first-time comment...")
        success, message = GamificationService.update_engagement_activity(
            user=user,
            activity_type='comment',
            object_id=post_id,
            target_type='post'
        )
        self.stdout.write(f"Result: {success} - {message}")

        # Test 8: Commenting on the same post again (should fail)
        self.stdout.write("\n8. Testing duplicate comment...")
        success, message = GamificationService.update_engagement_activity(
            user=user,
            activity_type='comment',
            object_id=post_id,
            target_type='post'
        )
        self.stdout.write(f"Result: {success} - {message}")

        self.stdout.write("\n" + "="*50)
        self.stdout.write("TESTING RATE LIMITING")
        self.stdout.write("="*50)

        # Test 9: Rapid successive actions
        self.stdout.write("\n9. Testing rate limiting...")
        for i in range(5):
            success, message = GamificationService.update_engagement_activity(
                user=user,
                activity_type='like',
                object_id=post_id + 10 + i,
                target_type='post'
            )
            self.stdout.write(f"Action {i+1}: {success} - {message}")

        self.stdout.write("\n" + "="*50)
        self.stdout.write("SUMMARY")
        self.stdout.write("="*50)

        # Show user's reading history
        reading_history = PostReadingHistory.objects.filter(user=user)
        self.stdout.write(f"\nReading History ({reading_history.count()} records):")
        for record in reading_history:
            self.stdout.write(f"  Post {record.post_id}: {record.points_awarded} points, "
                            f"read {record.read_count} times, reward given: {record.reward_given}")

        # Show user's engagement history
        engagement_history = EngagementHistory.objects.filter(user=user)
        self.stdout.write(f"\nEngagement History ({engagement_history.count()} records):")
        for record in engagement_history:
            self.stdout.write(f"  {record.engagement_type} on {record.target_type} {record.target_id}: "
                            f"{record.points_awarded} points, valid: {record.is_valid}")

        # Show user's current level and points
        user_level = GamificationService.get_or_create_user_level(user)
        self.stdout.write(f"\nUser Level: {user_level.current_level}")
        self.stdout.write(f"Total Points: {user_level.total_points}")
        self.stdout.write(f"Posts Read: {user_level.total_posts_read}")
        self.stdout.write(f"Comments Made: {user_level.total_comments_made}")
        self.stdout.write(f"Likes Given: {user_level.total_likes_given}")

        # Show engagement tracker
        tracker = GamificationService.get_or_create_engagement_tracker(user)
        self.stdout.write(f"\nEngagement Tracker:")
        self.stdout.write(f"  Posts read today: {tracker.posts_read_today}")
        self.stdout.write(f"  Comments today: {tracker.comments_today}")
        self.stdout.write(f"  Likes today: {tracker.likes_today}")
        self.stdout.write(f"  Is flagged: {tracker.is_flagged}")
        if tracker.is_flagged:
            self.stdout.write(f"  Flag reason: {tracker.flag_reason}")

        self.stdout.write("\n" + "="*50)
        self.stdout.write("TEST COMPLETED")
        self.stdout.write("="*50)
