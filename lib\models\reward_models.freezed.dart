// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reward_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PayPalReward _$PayPalRewardFromJson(Map<String, dynamic> json) {
  return _PayPalReward.fromJson(json);
}

/// @nodoc
mixin _$PayPalReward {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  int get pointsRequired => throw _privateConstructorUsedError;
  String get tier => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isLimitedTime => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  int get maxClaims => throw _privateConstructorUsedError;
  int get currentClaims => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PayPalRewardCopyWith<PayPalReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PayPalRewardCopyWith<$Res> {
  factory $PayPalRewardCopyWith(
          PayPalReward value, $Res Function(PayPalReward) then) =
      _$PayPalRewardCopyWithImpl<$Res, PayPalReward>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      double amount,
      int pointsRequired,
      String tier,
      bool isActive,
      bool isLimitedTime,
      DateTime? expiresAt,
      int maxClaims,
      int currentClaims});
}

/// @nodoc
class _$PayPalRewardCopyWithImpl<$Res, $Val extends PayPalReward>
    implements $PayPalRewardCopyWith<$Res> {
  _$PayPalRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? amount = null,
    Object? pointsRequired = null,
    Object? tier = null,
    Object? isActive = null,
    Object? isLimitedTime = null,
    Object? expiresAt = freezed,
    Object? maxClaims = null,
    Object? currentClaims = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      pointsRequired: null == pointsRequired
          ? _value.pointsRequired
          : pointsRequired // ignore: cast_nullable_to_non_nullable
              as int,
      tier: null == tier
          ? _value.tier
          : tier // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isLimitedTime: null == isLimitedTime
          ? _value.isLimitedTime
          : isLimitedTime // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxClaims: null == maxClaims
          ? _value.maxClaims
          : maxClaims // ignore: cast_nullable_to_non_nullable
              as int,
      currentClaims: null == currentClaims
          ? _value.currentClaims
          : currentClaims // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PayPalRewardImplCopyWith<$Res>
    implements $PayPalRewardCopyWith<$Res> {
  factory _$$PayPalRewardImplCopyWith(
          _$PayPalRewardImpl value, $Res Function(_$PayPalRewardImpl) then) =
      __$$PayPalRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      double amount,
      int pointsRequired,
      String tier,
      bool isActive,
      bool isLimitedTime,
      DateTime? expiresAt,
      int maxClaims,
      int currentClaims});
}

/// @nodoc
class __$$PayPalRewardImplCopyWithImpl<$Res>
    extends _$PayPalRewardCopyWithImpl<$Res, _$PayPalRewardImpl>
    implements _$$PayPalRewardImplCopyWith<$Res> {
  __$$PayPalRewardImplCopyWithImpl(
      _$PayPalRewardImpl _value, $Res Function(_$PayPalRewardImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? amount = null,
    Object? pointsRequired = null,
    Object? tier = null,
    Object? isActive = null,
    Object? isLimitedTime = null,
    Object? expiresAt = freezed,
    Object? maxClaims = null,
    Object? currentClaims = null,
  }) {
    return _then(_$PayPalRewardImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      pointsRequired: null == pointsRequired
          ? _value.pointsRequired
          : pointsRequired // ignore: cast_nullable_to_non_nullable
              as int,
      tier: null == tier
          ? _value.tier
          : tier // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isLimitedTime: null == isLimitedTime
          ? _value.isLimitedTime
          : isLimitedTime // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxClaims: null == maxClaims
          ? _value.maxClaims
          : maxClaims // ignore: cast_nullable_to_non_nullable
              as int,
      currentClaims: null == currentClaims
          ? _value.currentClaims
          : currentClaims // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PayPalRewardImpl implements _PayPalReward {
  const _$PayPalRewardImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.amount,
      required this.pointsRequired,
      required this.tier,
      required this.isActive,
      this.isLimitedTime = false,
      this.expiresAt,
      this.maxClaims = 0,
      this.currentClaims = 0});

  factory _$PayPalRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$PayPalRewardImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final double amount;
  @override
  final int pointsRequired;
  @override
  final String tier;
  @override
  final bool isActive;
  @override
  @JsonKey()
  final bool isLimitedTime;
  @override
  final DateTime? expiresAt;
  @override
  @JsonKey()
  final int maxClaims;
  @override
  @JsonKey()
  final int currentClaims;

  @override
  String toString() {
    return 'PayPalReward(id: $id, name: $name, description: $description, amount: $amount, pointsRequired: $pointsRequired, tier: $tier, isActive: $isActive, isLimitedTime: $isLimitedTime, expiresAt: $expiresAt, maxClaims: $maxClaims, currentClaims: $currentClaims)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayPalRewardImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.pointsRequired, pointsRequired) ||
                other.pointsRequired == pointsRequired) &&
            (identical(other.tier, tier) || other.tier == tier) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isLimitedTime, isLimitedTime) ||
                other.isLimitedTime == isLimitedTime) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.maxClaims, maxClaims) ||
                other.maxClaims == maxClaims) &&
            (identical(other.currentClaims, currentClaims) ||
                other.currentClaims == currentClaims));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      amount,
      pointsRequired,
      tier,
      isActive,
      isLimitedTime,
      expiresAt,
      maxClaims,
      currentClaims);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PayPalRewardImplCopyWith<_$PayPalRewardImpl> get copyWith =>
      __$$PayPalRewardImplCopyWithImpl<_$PayPalRewardImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PayPalRewardImplToJson(
      this,
    );
  }
}

abstract class _PayPalReward implements PayPalReward {
  const factory _PayPalReward(
      {required final String id,
      required final String name,
      required final String description,
      required final double amount,
      required final int pointsRequired,
      required final String tier,
      required final bool isActive,
      final bool isLimitedTime,
      final DateTime? expiresAt,
      final int maxClaims,
      final int currentClaims}) = _$PayPalRewardImpl;

  factory _PayPalReward.fromJson(Map<String, dynamic> json) =
      _$PayPalRewardImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  double get amount;
  @override
  int get pointsRequired;
  @override
  String get tier;
  @override
  bool get isActive;
  @override
  bool get isLimitedTime;
  @override
  DateTime? get expiresAt;
  @override
  int get maxClaims;
  @override
  int get currentClaims;
  @override
  @JsonKey(ignore: true)
  _$$PayPalRewardImplCopyWith<_$PayPalRewardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserPayPalReward _$UserPayPalRewardFromJson(Map<String, dynamic> json) {
  return _UserPayPalReward.fromJson(json);
}

/// @nodoc
mixin _$UserPayPalReward {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get rewardId => throw _privateConstructorUsedError;
  PayPalReward get reward => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get status =>
      throw _privateConstructorUsedError; // 'pending', 'approved', 'completed', 'failed'
  String get paypalEmail => throw _privateConstructorUsedError;
  DateTime? get claimedAt => throw _privateConstructorUsedError;
  DateTime? get approvedAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  String? get paypalTransactionId => throw _privateConstructorUsedError;
  String? get adminNotes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserPayPalRewardCopyWith<UserPayPalReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserPayPalRewardCopyWith<$Res> {
  factory $UserPayPalRewardCopyWith(
          UserPayPalReward value, $Res Function(UserPayPalReward) then) =
      _$UserPayPalRewardCopyWithImpl<$Res, UserPayPalReward>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String rewardId,
      PayPalReward reward,
      double amount,
      String status,
      String paypalEmail,
      DateTime? claimedAt,
      DateTime? approvedAt,
      DateTime? completedAt,
      String? paypalTransactionId,
      String? adminNotes});

  $PayPalRewardCopyWith<$Res> get reward;
}

/// @nodoc
class _$UserPayPalRewardCopyWithImpl<$Res, $Val extends UserPayPalReward>
    implements $UserPayPalRewardCopyWith<$Res> {
  _$UserPayPalRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? rewardId = null,
    Object? reward = null,
    Object? amount = null,
    Object? status = null,
    Object? paypalEmail = null,
    Object? claimedAt = freezed,
    Object? approvedAt = freezed,
    Object? completedAt = freezed,
    Object? paypalTransactionId = freezed,
    Object? adminNotes = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      rewardId: null == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as PayPalReward,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      paypalEmail: null == paypalEmail
          ? _value.paypalEmail
          : paypalEmail // ignore: cast_nullable_to_non_nullable
              as String,
      claimedAt: freezed == claimedAt
          ? _value.claimedAt
          : claimedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      approvedAt: freezed == approvedAt
          ? _value.approvedAt
          : approvedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paypalTransactionId: freezed == paypalTransactionId
          ? _value.paypalTransactionId
          : paypalTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminNotes: freezed == adminNotes
          ? _value.adminNotes
          : adminNotes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PayPalRewardCopyWith<$Res> get reward {
    return $PayPalRewardCopyWith<$Res>(_value.reward, (value) {
      return _then(_value.copyWith(reward: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserPayPalRewardImplCopyWith<$Res>
    implements $UserPayPalRewardCopyWith<$Res> {
  factory _$$UserPayPalRewardImplCopyWith(_$UserPayPalRewardImpl value,
          $Res Function(_$UserPayPalRewardImpl) then) =
      __$$UserPayPalRewardImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String rewardId,
      PayPalReward reward,
      double amount,
      String status,
      String paypalEmail,
      DateTime? claimedAt,
      DateTime? approvedAt,
      DateTime? completedAt,
      String? paypalTransactionId,
      String? adminNotes});

  @override
  $PayPalRewardCopyWith<$Res> get reward;
}

/// @nodoc
class __$$UserPayPalRewardImplCopyWithImpl<$Res>
    extends _$UserPayPalRewardCopyWithImpl<$Res, _$UserPayPalRewardImpl>
    implements _$$UserPayPalRewardImplCopyWith<$Res> {
  __$$UserPayPalRewardImplCopyWithImpl(_$UserPayPalRewardImpl _value,
      $Res Function(_$UserPayPalRewardImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? rewardId = null,
    Object? reward = null,
    Object? amount = null,
    Object? status = null,
    Object? paypalEmail = null,
    Object? claimedAt = freezed,
    Object? approvedAt = freezed,
    Object? completedAt = freezed,
    Object? paypalTransactionId = freezed,
    Object? adminNotes = freezed,
  }) {
    return _then(_$UserPayPalRewardImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      rewardId: null == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as String,
      reward: null == reward
          ? _value.reward
          : reward // ignore: cast_nullable_to_non_nullable
              as PayPalReward,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      paypalEmail: null == paypalEmail
          ? _value.paypalEmail
          : paypalEmail // ignore: cast_nullable_to_non_nullable
              as String,
      claimedAt: freezed == claimedAt
          ? _value.claimedAt
          : claimedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      approvedAt: freezed == approvedAt
          ? _value.approvedAt
          : approvedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paypalTransactionId: freezed == paypalTransactionId
          ? _value.paypalTransactionId
          : paypalTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminNotes: freezed == adminNotes
          ? _value.adminNotes
          : adminNotes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserPayPalRewardImpl implements _UserPayPalReward {
  const _$UserPayPalRewardImpl(
      {required this.id,
      required this.userId,
      required this.rewardId,
      required this.reward,
      required this.amount,
      required this.status,
      required this.paypalEmail,
      this.claimedAt,
      this.approvedAt,
      this.completedAt,
      this.paypalTransactionId,
      this.adminNotes});

  factory _$UserPayPalRewardImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserPayPalRewardImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String rewardId;
  @override
  final PayPalReward reward;
  @override
  final double amount;
  @override
  final String status;
// 'pending', 'approved', 'completed', 'failed'
  @override
  final String paypalEmail;
  @override
  final DateTime? claimedAt;
  @override
  final DateTime? approvedAt;
  @override
  final DateTime? completedAt;
  @override
  final String? paypalTransactionId;
  @override
  final String? adminNotes;

  @override
  String toString() {
    return 'UserPayPalReward(id: $id, userId: $userId, rewardId: $rewardId, reward: $reward, amount: $amount, status: $status, paypalEmail: $paypalEmail, claimedAt: $claimedAt, approvedAt: $approvedAt, completedAt: $completedAt, paypalTransactionId: $paypalTransactionId, adminNotes: $adminNotes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserPayPalRewardImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.rewardId, rewardId) ||
                other.rewardId == rewardId) &&
            (identical(other.reward, reward) || other.reward == reward) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.paypalEmail, paypalEmail) ||
                other.paypalEmail == paypalEmail) &&
            (identical(other.claimedAt, claimedAt) ||
                other.claimedAt == claimedAt) &&
            (identical(other.approvedAt, approvedAt) ||
                other.approvedAt == approvedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.paypalTransactionId, paypalTransactionId) ||
                other.paypalTransactionId == paypalTransactionId) &&
            (identical(other.adminNotes, adminNotes) ||
                other.adminNotes == adminNotes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      rewardId,
      reward,
      amount,
      status,
      paypalEmail,
      claimedAt,
      approvedAt,
      completedAt,
      paypalTransactionId,
      adminNotes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserPayPalRewardImplCopyWith<_$UserPayPalRewardImpl> get copyWith =>
      __$$UserPayPalRewardImplCopyWithImpl<_$UserPayPalRewardImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserPayPalRewardImplToJson(
      this,
    );
  }
}

abstract class _UserPayPalReward implements UserPayPalReward {
  const factory _UserPayPalReward(
      {required final String id,
      required final String userId,
      required final String rewardId,
      required final PayPalReward reward,
      required final double amount,
      required final String status,
      required final String paypalEmail,
      final DateTime? claimedAt,
      final DateTime? approvedAt,
      final DateTime? completedAt,
      final String? paypalTransactionId,
      final String? adminNotes}) = _$UserPayPalRewardImpl;

  factory _UserPayPalReward.fromJson(Map<String, dynamic> json) =
      _$UserPayPalRewardImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get rewardId;
  @override
  PayPalReward get reward;
  @override
  double get amount;
  @override
  String get status;
  @override // 'pending', 'approved', 'completed', 'failed'
  String get paypalEmail;
  @override
  DateTime? get claimedAt;
  @override
  DateTime? get approvedAt;
  @override
  DateTime? get completedAt;
  @override
  String? get paypalTransactionId;
  @override
  String? get adminNotes;
  @override
  @JsonKey(ignore: true)
  _$$UserPayPalRewardImplCopyWith<_$UserPayPalRewardImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserPayPalProfile _$UserPayPalProfileFromJson(Map<String, dynamic> json) {
  return _UserPayPalProfile.fromJson(json);
}

/// @nodoc
mixin _$UserPayPalProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get paypalEmail => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime? get verifiedAt => throw _privateConstructorUsedError;
  double get totalEarnings => throw _privateConstructorUsedError;
  int get totalRewardsClaimed => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserPayPalProfileCopyWith<UserPayPalProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserPayPalProfileCopyWith<$Res> {
  factory $UserPayPalProfileCopyWith(
          UserPayPalProfile value, $Res Function(UserPayPalProfile) then) =
      _$UserPayPalProfileCopyWithImpl<$Res, UserPayPalProfile>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String paypalEmail,
      bool isVerified,
      DateTime? verifiedAt,
      double totalEarnings,
      int totalRewardsClaimed,
      bool isActive});
}

/// @nodoc
class _$UserPayPalProfileCopyWithImpl<$Res, $Val extends UserPayPalProfile>
    implements $UserPayPalProfileCopyWith<$Res> {
  _$UserPayPalProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? paypalEmail = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
    Object? totalEarnings = null,
    Object? totalRewardsClaimed = null,
    Object? isActive = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      paypalEmail: null == paypalEmail
          ? _value.paypalEmail
          : paypalEmail // ignore: cast_nullable_to_non_nullable
              as String,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      verifiedAt: freezed == verifiedAt
          ? _value.verifiedAt
          : verifiedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalEarnings: null == totalEarnings
          ? _value.totalEarnings
          : totalEarnings // ignore: cast_nullable_to_non_nullable
              as double,
      totalRewardsClaimed: null == totalRewardsClaimed
          ? _value.totalRewardsClaimed
          : totalRewardsClaimed // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserPayPalProfileImplCopyWith<$Res>
    implements $UserPayPalProfileCopyWith<$Res> {
  factory _$$UserPayPalProfileImplCopyWith(_$UserPayPalProfileImpl value,
          $Res Function(_$UserPayPalProfileImpl) then) =
      __$$UserPayPalProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String paypalEmail,
      bool isVerified,
      DateTime? verifiedAt,
      double totalEarnings,
      int totalRewardsClaimed,
      bool isActive});
}

/// @nodoc
class __$$UserPayPalProfileImplCopyWithImpl<$Res>
    extends _$UserPayPalProfileCopyWithImpl<$Res, _$UserPayPalProfileImpl>
    implements _$$UserPayPalProfileImplCopyWith<$Res> {
  __$$UserPayPalProfileImplCopyWithImpl(_$UserPayPalProfileImpl _value,
      $Res Function(_$UserPayPalProfileImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? paypalEmail = null,
    Object? isVerified = null,
    Object? verifiedAt = freezed,
    Object? totalEarnings = null,
    Object? totalRewardsClaimed = null,
    Object? isActive = null,
  }) {
    return _then(_$UserPayPalProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      paypalEmail: null == paypalEmail
          ? _value.paypalEmail
          : paypalEmail // ignore: cast_nullable_to_non_nullable
              as String,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      verifiedAt: freezed == verifiedAt
          ? _value.verifiedAt
          : verifiedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalEarnings: null == totalEarnings
          ? _value.totalEarnings
          : totalEarnings // ignore: cast_nullable_to_non_nullable
              as double,
      totalRewardsClaimed: null == totalRewardsClaimed
          ? _value.totalRewardsClaimed
          : totalRewardsClaimed // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserPayPalProfileImpl implements _UserPayPalProfile {
  const _$UserPayPalProfileImpl(
      {required this.id,
      required this.userId,
      required this.paypalEmail,
      required this.isVerified,
      this.verifiedAt,
      this.totalEarnings = 0.0,
      this.totalRewardsClaimed = 0,
      this.isActive = true});

  factory _$UserPayPalProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserPayPalProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String paypalEmail;
  @override
  final bool isVerified;
  @override
  final DateTime? verifiedAt;
  @override
  @JsonKey()
  final double totalEarnings;
  @override
  @JsonKey()
  final int totalRewardsClaimed;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'UserPayPalProfile(id: $id, userId: $userId, paypalEmail: $paypalEmail, isVerified: $isVerified, verifiedAt: $verifiedAt, totalEarnings: $totalEarnings, totalRewardsClaimed: $totalRewardsClaimed, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserPayPalProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.paypalEmail, paypalEmail) ||
                other.paypalEmail == paypalEmail) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.verifiedAt, verifiedAt) ||
                other.verifiedAt == verifiedAt) &&
            (identical(other.totalEarnings, totalEarnings) ||
                other.totalEarnings == totalEarnings) &&
            (identical(other.totalRewardsClaimed, totalRewardsClaimed) ||
                other.totalRewardsClaimed == totalRewardsClaimed) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, paypalEmail,
      isVerified, verifiedAt, totalEarnings, totalRewardsClaimed, isActive);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserPayPalProfileImplCopyWith<_$UserPayPalProfileImpl> get copyWith =>
      __$$UserPayPalProfileImplCopyWithImpl<_$UserPayPalProfileImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserPayPalProfileImplToJson(
      this,
    );
  }
}

abstract class _UserPayPalProfile implements UserPayPalProfile {
  const factory _UserPayPalProfile(
      {required final String id,
      required final String userId,
      required final String paypalEmail,
      required final bool isVerified,
      final DateTime? verifiedAt,
      final double totalEarnings,
      final int totalRewardsClaimed,
      final bool isActive}) = _$UserPayPalProfileImpl;

  factory _UserPayPalProfile.fromJson(Map<String, dynamic> json) =
      _$UserPayPalProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get paypalEmail;
  @override
  bool get isVerified;
  @override
  DateTime? get verifiedAt;
  @override
  double get totalEarnings;
  @override
  int get totalRewardsClaimed;
  @override
  bool get isActive;
  @override
  @JsonKey(ignore: true)
  _$$UserPayPalProfileImplCopyWith<_$UserPayPalProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReferralData _$ReferralDataFromJson(Map<String, dynamic> json) {
  return _ReferralData.fromJson(json);
}

/// @nodoc
mixin _$ReferralData {
  String get id => throw _privateConstructorUsedError;
  String get referrerId => throw _privateConstructorUsedError;
  String get refereeId => throw _privateConstructorUsedError;
  String get referralCode => throw _privateConstructorUsedError;
  String get friendName => throw _privateConstructorUsedError;
  int get friendLevel => throw _privateConstructorUsedError;
  DateTime get joinedAt => throw _privateConstructorUsedError;
  double get earnedAmount => throw _privateConstructorUsedError;
  bool get wentPremium => throw _privateConstructorUsedError;
  bool get reachedLevel5 => throw _privateConstructorUsedError;
  bool get madePurchase => throw _privateConstructorUsedError;
  double get totalRevenue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReferralDataCopyWith<ReferralData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReferralDataCopyWith<$Res> {
  factory $ReferralDataCopyWith(
          ReferralData value, $Res Function(ReferralData) then) =
      _$ReferralDataCopyWithImpl<$Res, ReferralData>;
  @useResult
  $Res call(
      {String id,
      String referrerId,
      String refereeId,
      String referralCode,
      String friendName,
      int friendLevel,
      DateTime joinedAt,
      double earnedAmount,
      bool wentPremium,
      bool reachedLevel5,
      bool madePurchase,
      double totalRevenue});
}

/// @nodoc
class _$ReferralDataCopyWithImpl<$Res, $Val extends ReferralData>
    implements $ReferralDataCopyWith<$Res> {
  _$ReferralDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? referrerId = null,
    Object? refereeId = null,
    Object? referralCode = null,
    Object? friendName = null,
    Object? friendLevel = null,
    Object? joinedAt = null,
    Object? earnedAmount = null,
    Object? wentPremium = null,
    Object? reachedLevel5 = null,
    Object? madePurchase = null,
    Object? totalRevenue = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      referrerId: null == referrerId
          ? _value.referrerId
          : referrerId // ignore: cast_nullable_to_non_nullable
              as String,
      refereeId: null == refereeId
          ? _value.refereeId
          : refereeId // ignore: cast_nullable_to_non_nullable
              as String,
      referralCode: null == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String,
      friendName: null == friendName
          ? _value.friendName
          : friendName // ignore: cast_nullable_to_non_nullable
              as String,
      friendLevel: null == friendLevel
          ? _value.friendLevel
          : friendLevel // ignore: cast_nullable_to_non_nullable
              as int,
      joinedAt: null == joinedAt
          ? _value.joinedAt
          : joinedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      earnedAmount: null == earnedAmount
          ? _value.earnedAmount
          : earnedAmount // ignore: cast_nullable_to_non_nullable
              as double,
      wentPremium: null == wentPremium
          ? _value.wentPremium
          : wentPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      reachedLevel5: null == reachedLevel5
          ? _value.reachedLevel5
          : reachedLevel5 // ignore: cast_nullable_to_non_nullable
              as bool,
      madePurchase: null == madePurchase
          ? _value.madePurchase
          : madePurchase // ignore: cast_nullable_to_non_nullable
              as bool,
      totalRevenue: null == totalRevenue
          ? _value.totalRevenue
          : totalRevenue // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReferralDataImplCopyWith<$Res>
    implements $ReferralDataCopyWith<$Res> {
  factory _$$ReferralDataImplCopyWith(
          _$ReferralDataImpl value, $Res Function(_$ReferralDataImpl) then) =
      __$$ReferralDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String referrerId,
      String refereeId,
      String referralCode,
      String friendName,
      int friendLevel,
      DateTime joinedAt,
      double earnedAmount,
      bool wentPremium,
      bool reachedLevel5,
      bool madePurchase,
      double totalRevenue});
}

/// @nodoc
class __$$ReferralDataImplCopyWithImpl<$Res>
    extends _$ReferralDataCopyWithImpl<$Res, _$ReferralDataImpl>
    implements _$$ReferralDataImplCopyWith<$Res> {
  __$$ReferralDataImplCopyWithImpl(
      _$ReferralDataImpl _value, $Res Function(_$ReferralDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? referrerId = null,
    Object? refereeId = null,
    Object? referralCode = null,
    Object? friendName = null,
    Object? friendLevel = null,
    Object? joinedAt = null,
    Object? earnedAmount = null,
    Object? wentPremium = null,
    Object? reachedLevel5 = null,
    Object? madePurchase = null,
    Object? totalRevenue = null,
  }) {
    return _then(_$ReferralDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      referrerId: null == referrerId
          ? _value.referrerId
          : referrerId // ignore: cast_nullable_to_non_nullable
              as String,
      refereeId: null == refereeId
          ? _value.refereeId
          : refereeId // ignore: cast_nullable_to_non_nullable
              as String,
      referralCode: null == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String,
      friendName: null == friendName
          ? _value.friendName
          : friendName // ignore: cast_nullable_to_non_nullable
              as String,
      friendLevel: null == friendLevel
          ? _value.friendLevel
          : friendLevel // ignore: cast_nullable_to_non_nullable
              as int,
      joinedAt: null == joinedAt
          ? _value.joinedAt
          : joinedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      earnedAmount: null == earnedAmount
          ? _value.earnedAmount
          : earnedAmount // ignore: cast_nullable_to_non_nullable
              as double,
      wentPremium: null == wentPremium
          ? _value.wentPremium
          : wentPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      reachedLevel5: null == reachedLevel5
          ? _value.reachedLevel5
          : reachedLevel5 // ignore: cast_nullable_to_non_nullable
              as bool,
      madePurchase: null == madePurchase
          ? _value.madePurchase
          : madePurchase // ignore: cast_nullable_to_non_nullable
              as bool,
      totalRevenue: null == totalRevenue
          ? _value.totalRevenue
          : totalRevenue // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReferralDataImpl implements _ReferralData {
  const _$ReferralDataImpl(
      {required this.id,
      required this.referrerId,
      required this.refereeId,
      required this.referralCode,
      required this.friendName,
      required this.friendLevel,
      required this.joinedAt,
      required this.earnedAmount,
      required this.wentPremium,
      this.reachedLevel5 = false,
      this.madePurchase = false,
      this.totalRevenue = 0.0});

  factory _$ReferralDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReferralDataImplFromJson(json);

  @override
  final String id;
  @override
  final String referrerId;
  @override
  final String refereeId;
  @override
  final String referralCode;
  @override
  final String friendName;
  @override
  final int friendLevel;
  @override
  final DateTime joinedAt;
  @override
  final double earnedAmount;
  @override
  final bool wentPremium;
  @override
  @JsonKey()
  final bool reachedLevel5;
  @override
  @JsonKey()
  final bool madePurchase;
  @override
  @JsonKey()
  final double totalRevenue;

  @override
  String toString() {
    return 'ReferralData(id: $id, referrerId: $referrerId, refereeId: $refereeId, referralCode: $referralCode, friendName: $friendName, friendLevel: $friendLevel, joinedAt: $joinedAt, earnedAmount: $earnedAmount, wentPremium: $wentPremium, reachedLevel5: $reachedLevel5, madePurchase: $madePurchase, totalRevenue: $totalRevenue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferralDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.referrerId, referrerId) ||
                other.referrerId == referrerId) &&
            (identical(other.refereeId, refereeId) ||
                other.refereeId == refereeId) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.friendName, friendName) ||
                other.friendName == friendName) &&
            (identical(other.friendLevel, friendLevel) ||
                other.friendLevel == friendLevel) &&
            (identical(other.joinedAt, joinedAt) ||
                other.joinedAt == joinedAt) &&
            (identical(other.earnedAmount, earnedAmount) ||
                other.earnedAmount == earnedAmount) &&
            (identical(other.wentPremium, wentPremium) ||
                other.wentPremium == wentPremium) &&
            (identical(other.reachedLevel5, reachedLevel5) ||
                other.reachedLevel5 == reachedLevel5) &&
            (identical(other.madePurchase, madePurchase) ||
                other.madePurchase == madePurchase) &&
            (identical(other.totalRevenue, totalRevenue) ||
                other.totalRevenue == totalRevenue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      referrerId,
      refereeId,
      referralCode,
      friendName,
      friendLevel,
      joinedAt,
      earnedAmount,
      wentPremium,
      reachedLevel5,
      madePurchase,
      totalRevenue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferralDataImplCopyWith<_$ReferralDataImpl> get copyWith =>
      __$$ReferralDataImplCopyWithImpl<_$ReferralDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReferralDataImplToJson(
      this,
    );
  }
}

abstract class _ReferralData implements ReferralData {
  const factory _ReferralData(
      {required final String id,
      required final String referrerId,
      required final String refereeId,
      required final String referralCode,
      required final String friendName,
      required final int friendLevel,
      required final DateTime joinedAt,
      required final double earnedAmount,
      required final bool wentPremium,
      final bool reachedLevel5,
      final bool madePurchase,
      final double totalRevenue}) = _$ReferralDataImpl;

  factory _ReferralData.fromJson(Map<String, dynamic> json) =
      _$ReferralDataImpl.fromJson;

  @override
  String get id;
  @override
  String get referrerId;
  @override
  String get refereeId;
  @override
  String get referralCode;
  @override
  String get friendName;
  @override
  int get friendLevel;
  @override
  DateTime get joinedAt;
  @override
  double get earnedAmount;
  @override
  bool get wentPremium;
  @override
  bool get reachedLevel5;
  @override
  bool get madePurchase;
  @override
  double get totalRevenue;
  @override
  @JsonKey(ignore: true)
  _$$ReferralDataImplCopyWith<_$ReferralDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReferralStats _$ReferralStatsFromJson(Map<String, dynamic> json) {
  return _ReferralStats.fromJson(json);
}

/// @nodoc
mixin _$ReferralStats {
  int get totalReferrals => throw _privateConstructorUsedError;
  double get totalEarned => throw _privateConstructorUsedError;
  int get premiumReferrals => throw _privateConstructorUsedError;
  int get activeReferrals => throw _privateConstructorUsedError;
  int get level5Referrals => throw _privateConstructorUsedError;
  String? get referralCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReferralStatsCopyWith<ReferralStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReferralStatsCopyWith<$Res> {
  factory $ReferralStatsCopyWith(
          ReferralStats value, $Res Function(ReferralStats) then) =
      _$ReferralStatsCopyWithImpl<$Res, ReferralStats>;
  @useResult
  $Res call(
      {int totalReferrals,
      double totalEarned,
      int premiumReferrals,
      int activeReferrals,
      int level5Referrals,
      String? referralCode});
}

/// @nodoc
class _$ReferralStatsCopyWithImpl<$Res, $Val extends ReferralStats>
    implements $ReferralStatsCopyWith<$Res> {
  _$ReferralStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReferrals = null,
    Object? totalEarned = null,
    Object? premiumReferrals = null,
    Object? activeReferrals = null,
    Object? level5Referrals = null,
    Object? referralCode = freezed,
  }) {
    return _then(_value.copyWith(
      totalReferrals: null == totalReferrals
          ? _value.totalReferrals
          : totalReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      totalEarned: null == totalEarned
          ? _value.totalEarned
          : totalEarned // ignore: cast_nullable_to_non_nullable
              as double,
      premiumReferrals: null == premiumReferrals
          ? _value.premiumReferrals
          : premiumReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      activeReferrals: null == activeReferrals
          ? _value.activeReferrals
          : activeReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      level5Referrals: null == level5Referrals
          ? _value.level5Referrals
          : level5Referrals // ignore: cast_nullable_to_non_nullable
              as int,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReferralStatsImplCopyWith<$Res>
    implements $ReferralStatsCopyWith<$Res> {
  factory _$$ReferralStatsImplCopyWith(
          _$ReferralStatsImpl value, $Res Function(_$ReferralStatsImpl) then) =
      __$$ReferralStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalReferrals,
      double totalEarned,
      int premiumReferrals,
      int activeReferrals,
      int level5Referrals,
      String? referralCode});
}

/// @nodoc
class __$$ReferralStatsImplCopyWithImpl<$Res>
    extends _$ReferralStatsCopyWithImpl<$Res, _$ReferralStatsImpl>
    implements _$$ReferralStatsImplCopyWith<$Res> {
  __$$ReferralStatsImplCopyWithImpl(
      _$ReferralStatsImpl _value, $Res Function(_$ReferralStatsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalReferrals = null,
    Object? totalEarned = null,
    Object? premiumReferrals = null,
    Object? activeReferrals = null,
    Object? level5Referrals = null,
    Object? referralCode = freezed,
  }) {
    return _then(_$ReferralStatsImpl(
      totalReferrals: null == totalReferrals
          ? _value.totalReferrals
          : totalReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      totalEarned: null == totalEarned
          ? _value.totalEarned
          : totalEarned // ignore: cast_nullable_to_non_nullable
              as double,
      premiumReferrals: null == premiumReferrals
          ? _value.premiumReferrals
          : premiumReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      activeReferrals: null == activeReferrals
          ? _value.activeReferrals
          : activeReferrals // ignore: cast_nullable_to_non_nullable
              as int,
      level5Referrals: null == level5Referrals
          ? _value.level5Referrals
          : level5Referrals // ignore: cast_nullable_to_non_nullable
              as int,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReferralStatsImpl implements _ReferralStats {
  const _$ReferralStatsImpl(
      {this.totalReferrals = 0,
      this.totalEarned = 0.0,
      this.premiumReferrals = 0,
      this.activeReferrals = 0,
      this.level5Referrals = 0,
      this.referralCode});

  factory _$ReferralStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReferralStatsImplFromJson(json);

  @override
  @JsonKey()
  final int totalReferrals;
  @override
  @JsonKey()
  final double totalEarned;
  @override
  @JsonKey()
  final int premiumReferrals;
  @override
  @JsonKey()
  final int activeReferrals;
  @override
  @JsonKey()
  final int level5Referrals;
  @override
  final String? referralCode;

  @override
  String toString() {
    return 'ReferralStats(totalReferrals: $totalReferrals, totalEarned: $totalEarned, premiumReferrals: $premiumReferrals, activeReferrals: $activeReferrals, level5Referrals: $level5Referrals, referralCode: $referralCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferralStatsImpl &&
            (identical(other.totalReferrals, totalReferrals) ||
                other.totalReferrals == totalReferrals) &&
            (identical(other.totalEarned, totalEarned) ||
                other.totalEarned == totalEarned) &&
            (identical(other.premiumReferrals, premiumReferrals) ||
                other.premiumReferrals == premiumReferrals) &&
            (identical(other.activeReferrals, activeReferrals) ||
                other.activeReferrals == activeReferrals) &&
            (identical(other.level5Referrals, level5Referrals) ||
                other.level5Referrals == level5Referrals) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, totalReferrals, totalEarned,
      premiumReferrals, activeReferrals, level5Referrals, referralCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferralStatsImplCopyWith<_$ReferralStatsImpl> get copyWith =>
      __$$ReferralStatsImplCopyWithImpl<_$ReferralStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReferralStatsImplToJson(
      this,
    );
  }
}

abstract class _ReferralStats implements ReferralStats {
  const factory _ReferralStats(
      {final int totalReferrals,
      final double totalEarned,
      final int premiumReferrals,
      final int activeReferrals,
      final int level5Referrals,
      final String? referralCode}) = _$ReferralStatsImpl;

  factory _ReferralStats.fromJson(Map<String, dynamic> json) =
      _$ReferralStatsImpl.fromJson;

  @override
  int get totalReferrals;
  @override
  double get totalEarned;
  @override
  int get premiumReferrals;
  @override
  int get activeReferrals;
  @override
  int get level5Referrals;
  @override
  String? get referralCode;
  @override
  @JsonKey(ignore: true)
  _$$ReferralStatsImplCopyWith<_$ReferralStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VirtualItem _$VirtualItemFromJson(Map<String, dynamic> json) {
  return _VirtualItem.fromJson(json);
}

/// @nodoc
mixin _$VirtualItem {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get category =>
      throw _privateConstructorUsedError; // 'cosmetic', 'functional', 'boost'
  double get price => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isLimitedTime => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  int get maxPurchases => throw _privateConstructorUsedError;
  Map<String, dynamic>? get effects => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VirtualItemCopyWith<VirtualItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VirtualItemCopyWith<$Res> {
  factory $VirtualItemCopyWith(
          VirtualItem value, $Res Function(VirtualItem) then) =
      _$VirtualItemCopyWithImpl<$Res, VirtualItem>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      String category,
      double price,
      String currency,
      bool isActive,
      bool isLimitedTime,
      DateTime? expiresAt,
      int maxPurchases,
      Map<String, dynamic>? effects,
      String? imageUrl});
}

/// @nodoc
class _$VirtualItemCopyWithImpl<$Res, $Val extends VirtualItem>
    implements $VirtualItemCopyWith<$Res> {
  _$VirtualItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? category = null,
    Object? price = null,
    Object? currency = null,
    Object? isActive = null,
    Object? isLimitedTime = null,
    Object? expiresAt = freezed,
    Object? maxPurchases = null,
    Object? effects = freezed,
    Object? imageUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isLimitedTime: null == isLimitedTime
          ? _value.isLimitedTime
          : isLimitedTime // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxPurchases: null == maxPurchases
          ? _value.maxPurchases
          : maxPurchases // ignore: cast_nullable_to_non_nullable
              as int,
      effects: freezed == effects
          ? _value.effects
          : effects // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VirtualItemImplCopyWith<$Res>
    implements $VirtualItemCopyWith<$Res> {
  factory _$$VirtualItemImplCopyWith(
          _$VirtualItemImpl value, $Res Function(_$VirtualItemImpl) then) =
      __$$VirtualItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      String category,
      double price,
      String currency,
      bool isActive,
      bool isLimitedTime,
      DateTime? expiresAt,
      int maxPurchases,
      Map<String, dynamic>? effects,
      String? imageUrl});
}

/// @nodoc
class __$$VirtualItemImplCopyWithImpl<$Res>
    extends _$VirtualItemCopyWithImpl<$Res, _$VirtualItemImpl>
    implements _$$VirtualItemImplCopyWith<$Res> {
  __$$VirtualItemImplCopyWithImpl(
      _$VirtualItemImpl _value, $Res Function(_$VirtualItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? category = null,
    Object? price = null,
    Object? currency = null,
    Object? isActive = null,
    Object? isLimitedTime = null,
    Object? expiresAt = freezed,
    Object? maxPurchases = null,
    Object? effects = freezed,
    Object? imageUrl = freezed,
  }) {
    return _then(_$VirtualItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isLimitedTime: null == isLimitedTime
          ? _value.isLimitedTime
          : isLimitedTime // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxPurchases: null == maxPurchases
          ? _value.maxPurchases
          : maxPurchases // ignore: cast_nullable_to_non_nullable
              as int,
      effects: freezed == effects
          ? _value._effects
          : effects // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VirtualItemImpl implements _VirtualItem {
  const _$VirtualItemImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.category,
      required this.price,
      required this.currency,
      required this.isActive,
      this.isLimitedTime = false,
      this.expiresAt,
      this.maxPurchases = 0,
      final Map<String, dynamic>? effects,
      this.imageUrl})
      : _effects = effects;

  factory _$VirtualItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$VirtualItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String category;
// 'cosmetic', 'functional', 'boost'
  @override
  final double price;
  @override
  final String currency;
  @override
  final bool isActive;
  @override
  @JsonKey()
  final bool isLimitedTime;
  @override
  final DateTime? expiresAt;
  @override
  @JsonKey()
  final int maxPurchases;
  final Map<String, dynamic>? _effects;
  @override
  Map<String, dynamic>? get effects {
    final value = _effects;
    if (value == null) return null;
    if (_effects is EqualUnmodifiableMapView) return _effects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? imageUrl;

  @override
  String toString() {
    return 'VirtualItem(id: $id, name: $name, description: $description, category: $category, price: $price, currency: $currency, isActive: $isActive, isLimitedTime: $isLimitedTime, expiresAt: $expiresAt, maxPurchases: $maxPurchases, effects: $effects, imageUrl: $imageUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VirtualItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isLimitedTime, isLimitedTime) ||
                other.isLimitedTime == isLimitedTime) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.maxPurchases, maxPurchases) ||
                other.maxPurchases == maxPurchases) &&
            const DeepCollectionEquality().equals(other._effects, _effects) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      category,
      price,
      currency,
      isActive,
      isLimitedTime,
      expiresAt,
      maxPurchases,
      const DeepCollectionEquality().hash(_effects),
      imageUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VirtualItemImplCopyWith<_$VirtualItemImpl> get copyWith =>
      __$$VirtualItemImplCopyWithImpl<_$VirtualItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VirtualItemImplToJson(
      this,
    );
  }
}

abstract class _VirtualItem implements VirtualItem {
  const factory _VirtualItem(
      {required final String id,
      required final String name,
      required final String description,
      required final String category,
      required final double price,
      required final String currency,
      required final bool isActive,
      final bool isLimitedTime,
      final DateTime? expiresAt,
      final int maxPurchases,
      final Map<String, dynamic>? effects,
      final String? imageUrl}) = _$VirtualItemImpl;

  factory _VirtualItem.fromJson(Map<String, dynamic> json) =
      _$VirtualItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String get category;
  @override // 'cosmetic', 'functional', 'boost'
  double get price;
  @override
  String get currency;
  @override
  bool get isActive;
  @override
  bool get isLimitedTime;
  @override
  DateTime? get expiresAt;
  @override
  int get maxPurchases;
  @override
  Map<String, dynamic>? get effects;
  @override
  String? get imageUrl;
  @override
  @JsonKey(ignore: true)
  _$$VirtualItemImplCopyWith<_$VirtualItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserVirtualItem _$UserVirtualItemFromJson(Map<String, dynamic> json) {
  return _UserVirtualItem.fromJson(json);
}

/// @nodoc
mixin _$UserVirtualItem {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get itemId => throw _privateConstructorUsedError;
  VirtualItem get item => throw _privateConstructorUsedError;
  DateTime get purchasedAt => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;
  int get usesRemaining => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserVirtualItemCopyWith<UserVirtualItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserVirtualItemCopyWith<$Res> {
  factory $UserVirtualItemCopyWith(
          UserVirtualItem value, $Res Function(UserVirtualItem) then) =
      _$UserVirtualItemCopyWithImpl<$Res, UserVirtualItem>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      VirtualItem item,
      DateTime purchasedAt,
      bool isActive,
      DateTime? expiresAt,
      int usesRemaining});

  $VirtualItemCopyWith<$Res> get item;
}

/// @nodoc
class _$UserVirtualItemCopyWithImpl<$Res, $Val extends UserVirtualItem>
    implements $UserVirtualItemCopyWith<$Res> {
  _$UserVirtualItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? item = null,
    Object? purchasedAt = null,
    Object? isActive = null,
    Object? expiresAt = freezed,
    Object? usesRemaining = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _value.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      item: null == item
          ? _value.item
          : item // ignore: cast_nullable_to_non_nullable
              as VirtualItem,
      purchasedAt: null == purchasedAt
          ? _value.purchasedAt
          : purchasedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      usesRemaining: null == usesRemaining
          ? _value.usesRemaining
          : usesRemaining // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $VirtualItemCopyWith<$Res> get item {
    return $VirtualItemCopyWith<$Res>(_value.item, (value) {
      return _then(_value.copyWith(item: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserVirtualItemImplCopyWith<$Res>
    implements $UserVirtualItemCopyWith<$Res> {
  factory _$$UserVirtualItemImplCopyWith(_$UserVirtualItemImpl value,
          $Res Function(_$UserVirtualItemImpl) then) =
      __$$UserVirtualItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      VirtualItem item,
      DateTime purchasedAt,
      bool isActive,
      DateTime? expiresAt,
      int usesRemaining});

  @override
  $VirtualItemCopyWith<$Res> get item;
}

/// @nodoc
class __$$UserVirtualItemImplCopyWithImpl<$Res>
    extends _$UserVirtualItemCopyWithImpl<$Res, _$UserVirtualItemImpl>
    implements _$$UserVirtualItemImplCopyWith<$Res> {
  __$$UserVirtualItemImplCopyWithImpl(
      _$UserVirtualItemImpl _value, $Res Function(_$UserVirtualItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? item = null,
    Object? purchasedAt = null,
    Object? isActive = null,
    Object? expiresAt = freezed,
    Object? usesRemaining = null,
  }) {
    return _then(_$UserVirtualItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _value.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      item: null == item
          ? _value.item
          : item // ignore: cast_nullable_to_non_nullable
              as VirtualItem,
      purchasedAt: null == purchasedAt
          ? _value.purchasedAt
          : purchasedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      usesRemaining: null == usesRemaining
          ? _value.usesRemaining
          : usesRemaining // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserVirtualItemImpl implements _UserVirtualItem {
  const _$UserVirtualItemImpl(
      {required this.id,
      required this.userId,
      required this.itemId,
      required this.item,
      required this.purchasedAt,
      required this.isActive,
      this.expiresAt,
      this.usesRemaining = 0});

  factory _$UserVirtualItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserVirtualItemImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String itemId;
  @override
  final VirtualItem item;
  @override
  final DateTime purchasedAt;
  @override
  final bool isActive;
  @override
  final DateTime? expiresAt;
  @override
  @JsonKey()
  final int usesRemaining;

  @override
  String toString() {
    return 'UserVirtualItem(id: $id, userId: $userId, itemId: $itemId, item: $item, purchasedAt: $purchasedAt, isActive: $isActive, expiresAt: $expiresAt, usesRemaining: $usesRemaining)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserVirtualItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.item, item) || other.item == item) &&
            (identical(other.purchasedAt, purchasedAt) ||
                other.purchasedAt == purchasedAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.usesRemaining, usesRemaining) ||
                other.usesRemaining == usesRemaining));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, itemId, item,
      purchasedAt, isActive, expiresAt, usesRemaining);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserVirtualItemImplCopyWith<_$UserVirtualItemImpl> get copyWith =>
      __$$UserVirtualItemImplCopyWithImpl<_$UserVirtualItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserVirtualItemImplToJson(
      this,
    );
  }
}

abstract class _UserVirtualItem implements UserVirtualItem {
  const factory _UserVirtualItem(
      {required final String id,
      required final String userId,
      required final String itemId,
      required final VirtualItem item,
      required final DateTime purchasedAt,
      required final bool isActive,
      final DateTime? expiresAt,
      final int usesRemaining}) = _$UserVirtualItemImpl;

  factory _UserVirtualItem.fromJson(Map<String, dynamic> json) =
      _$UserVirtualItemImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get itemId;
  @override
  VirtualItem get item;
  @override
  DateTime get purchasedAt;
  @override
  bool get isActive;
  @override
  DateTime? get expiresAt;
  @override
  int get usesRemaining;
  @override
  @JsonKey(ignore: true)
  _$$UserVirtualItemImplCopyWith<_$UserVirtualItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PointBoostPackage _$PointBoostPackageFromJson(Map<String, dynamic> json) {
  return _PointBoostPackage.fromJson(json);
}

/// @nodoc
mixin _$PointBoostPackage {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  int get basePoints => throw _privateConstructorUsedError;
  int get bonusPoints => throw _privateConstructorUsedError;
  int get totalPoints => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isPopular => throw _privateConstructorUsedError;
  bool get isLimitedTime => throw _privateConstructorUsedError;
  DateTime? get expiresAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PointBoostPackageCopyWith<PointBoostPackage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PointBoostPackageCopyWith<$Res> {
  factory $PointBoostPackageCopyWith(
          PointBoostPackage value, $Res Function(PointBoostPackage) then) =
      _$PointBoostPackageCopyWithImpl<$Res, PointBoostPackage>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      int basePoints,
      int bonusPoints,
      int totalPoints,
      double price,
      String currency,
      bool isActive,
      bool isPopular,
      bool isLimitedTime,
      DateTime? expiresAt});
}

/// @nodoc
class _$PointBoostPackageCopyWithImpl<$Res, $Val extends PointBoostPackage>
    implements $PointBoostPackageCopyWith<$Res> {
  _$PointBoostPackageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? basePoints = null,
    Object? bonusPoints = null,
    Object? totalPoints = null,
    Object? price = null,
    Object? currency = null,
    Object? isActive = null,
    Object? isPopular = null,
    Object? isLimitedTime = null,
    Object? expiresAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      basePoints: null == basePoints
          ? _value.basePoints
          : basePoints // ignore: cast_nullable_to_non_nullable
              as int,
      bonusPoints: null == bonusPoints
          ? _value.bonusPoints
          : bonusPoints // ignore: cast_nullable_to_non_nullable
              as int,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isPopular: null == isPopular
          ? _value.isPopular
          : isPopular // ignore: cast_nullable_to_non_nullable
              as bool,
      isLimitedTime: null == isLimitedTime
          ? _value.isLimitedTime
          : isLimitedTime // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PointBoostPackageImplCopyWith<$Res>
    implements $PointBoostPackageCopyWith<$Res> {
  factory _$$PointBoostPackageImplCopyWith(_$PointBoostPackageImpl value,
          $Res Function(_$PointBoostPackageImpl) then) =
      __$$PointBoostPackageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      int basePoints,
      int bonusPoints,
      int totalPoints,
      double price,
      String currency,
      bool isActive,
      bool isPopular,
      bool isLimitedTime,
      DateTime? expiresAt});
}

/// @nodoc
class __$$PointBoostPackageImplCopyWithImpl<$Res>
    extends _$PointBoostPackageCopyWithImpl<$Res, _$PointBoostPackageImpl>
    implements _$$PointBoostPackageImplCopyWith<$Res> {
  __$$PointBoostPackageImplCopyWithImpl(_$PointBoostPackageImpl _value,
      $Res Function(_$PointBoostPackageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? basePoints = null,
    Object? bonusPoints = null,
    Object? totalPoints = null,
    Object? price = null,
    Object? currency = null,
    Object? isActive = null,
    Object? isPopular = null,
    Object? isLimitedTime = null,
    Object? expiresAt = freezed,
  }) {
    return _then(_$PointBoostPackageImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      basePoints: null == basePoints
          ? _value.basePoints
          : basePoints // ignore: cast_nullable_to_non_nullable
              as int,
      bonusPoints: null == bonusPoints
          ? _value.bonusPoints
          : bonusPoints // ignore: cast_nullable_to_non_nullable
              as int,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isPopular: null == isPopular
          ? _value.isPopular
          : isPopular // ignore: cast_nullable_to_non_nullable
              as bool,
      isLimitedTime: null == isLimitedTime
          ? _value.isLimitedTime
          : isLimitedTime // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PointBoostPackageImpl implements _PointBoostPackage {
  const _$PointBoostPackageImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.basePoints,
      required this.bonusPoints,
      required this.totalPoints,
      required this.price,
      required this.currency,
      required this.isActive,
      this.isPopular = false,
      this.isLimitedTime = false,
      this.expiresAt});

  factory _$PointBoostPackageImpl.fromJson(Map<String, dynamic> json) =>
      _$$PointBoostPackageImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final int basePoints;
  @override
  final int bonusPoints;
  @override
  final int totalPoints;
  @override
  final double price;
  @override
  final String currency;
  @override
  final bool isActive;
  @override
  @JsonKey()
  final bool isPopular;
  @override
  @JsonKey()
  final bool isLimitedTime;
  @override
  final DateTime? expiresAt;

  @override
  String toString() {
    return 'PointBoostPackage(id: $id, name: $name, description: $description, basePoints: $basePoints, bonusPoints: $bonusPoints, totalPoints: $totalPoints, price: $price, currency: $currency, isActive: $isActive, isPopular: $isPopular, isLimitedTime: $isLimitedTime, expiresAt: $expiresAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PointBoostPackageImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.basePoints, basePoints) ||
                other.basePoints == basePoints) &&
            (identical(other.bonusPoints, bonusPoints) ||
                other.bonusPoints == bonusPoints) &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isPopular, isPopular) ||
                other.isPopular == isPopular) &&
            (identical(other.isLimitedTime, isLimitedTime) ||
                other.isLimitedTime == isLimitedTime) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      basePoints,
      bonusPoints,
      totalPoints,
      price,
      currency,
      isActive,
      isPopular,
      isLimitedTime,
      expiresAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PointBoostPackageImplCopyWith<_$PointBoostPackageImpl> get copyWith =>
      __$$PointBoostPackageImplCopyWithImpl<_$PointBoostPackageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PointBoostPackageImplToJson(
      this,
    );
  }
}

abstract class _PointBoostPackage implements PointBoostPackage {
  const factory _PointBoostPackage(
      {required final String id,
      required final String name,
      required final String description,
      required final int basePoints,
      required final int bonusPoints,
      required final int totalPoints,
      required final double price,
      required final String currency,
      required final bool isActive,
      final bool isPopular,
      final bool isLimitedTime,
      final DateTime? expiresAt}) = _$PointBoostPackageImpl;

  factory _PointBoostPackage.fromJson(Map<String, dynamic> json) =
      _$PointBoostPackageImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  int get basePoints;
  @override
  int get bonusPoints;
  @override
  int get totalPoints;
  @override
  double get price;
  @override
  String get currency;
  @override
  bool get isActive;
  @override
  bool get isPopular;
  @override
  bool get isLimitedTime;
  @override
  DateTime? get expiresAt;
  @override
  @JsonKey(ignore: true)
  _$$PointBoostPackageImplCopyWith<_$PointBoostPackageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PremiumSubscription _$PremiumSubscriptionFromJson(Map<String, dynamic> json) {
  return _PremiumSubscription.fromJson(json);
}

/// @nodoc
mixin _$PremiumSubscription {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get plan =>
      throw _privateConstructorUsedError; // 'monthly', 'quarterly', 'yearly'
  String get status =>
      throw _privateConstructorUsedError; // 'active', 'cancelled', 'expired'
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;
  double get monthlyPrice => throw _privateConstructorUsedError;
  double get totalPaid => throw _privateConstructorUsedError;
  DateTime? get lastPaymentDate => throw _privateConstructorUsedError;
  DateTime? get nextPaymentDate => throw _privateConstructorUsedError;
  double get pointMultiplier => throw _privateConstructorUsedError;
  int get dailyStreakBonus => throw _privateConstructorUsedError;
  int get voiceCommentsLimit => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PremiumSubscriptionCopyWith<PremiumSubscription> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PremiumSubscriptionCopyWith<$Res> {
  factory $PremiumSubscriptionCopyWith(
          PremiumSubscription value, $Res Function(PremiumSubscription) then) =
      _$PremiumSubscriptionCopyWithImpl<$Res, PremiumSubscription>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String plan,
      String status,
      DateTime startDate,
      DateTime endDate,
      double monthlyPrice,
      double totalPaid,
      DateTime? lastPaymentDate,
      DateTime? nextPaymentDate,
      double pointMultiplier,
      int dailyStreakBonus,
      int voiceCommentsLimit});
}

/// @nodoc
class _$PremiumSubscriptionCopyWithImpl<$Res, $Val extends PremiumSubscription>
    implements $PremiumSubscriptionCopyWith<$Res> {
  _$PremiumSubscriptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? plan = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? monthlyPrice = null,
    Object? totalPaid = null,
    Object? lastPaymentDate = freezed,
    Object? nextPaymentDate = freezed,
    Object? pointMultiplier = null,
    Object? dailyStreakBonus = null,
    Object? voiceCommentsLimit = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      plan: null == plan
          ? _value.plan
          : plan // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      monthlyPrice: null == monthlyPrice
          ? _value.monthlyPrice
          : monthlyPrice // ignore: cast_nullable_to_non_nullable
              as double,
      totalPaid: null == totalPaid
          ? _value.totalPaid
          : totalPaid // ignore: cast_nullable_to_non_nullable
              as double,
      lastPaymentDate: freezed == lastPaymentDate
          ? _value.lastPaymentDate
          : lastPaymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      nextPaymentDate: freezed == nextPaymentDate
          ? _value.nextPaymentDate
          : nextPaymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      pointMultiplier: null == pointMultiplier
          ? _value.pointMultiplier
          : pointMultiplier // ignore: cast_nullable_to_non_nullable
              as double,
      dailyStreakBonus: null == dailyStreakBonus
          ? _value.dailyStreakBonus
          : dailyStreakBonus // ignore: cast_nullable_to_non_nullable
              as int,
      voiceCommentsLimit: null == voiceCommentsLimit
          ? _value.voiceCommentsLimit
          : voiceCommentsLimit // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PremiumSubscriptionImplCopyWith<$Res>
    implements $PremiumSubscriptionCopyWith<$Res> {
  factory _$$PremiumSubscriptionImplCopyWith(_$PremiumSubscriptionImpl value,
          $Res Function(_$PremiumSubscriptionImpl) then) =
      __$$PremiumSubscriptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String plan,
      String status,
      DateTime startDate,
      DateTime endDate,
      double monthlyPrice,
      double totalPaid,
      DateTime? lastPaymentDate,
      DateTime? nextPaymentDate,
      double pointMultiplier,
      int dailyStreakBonus,
      int voiceCommentsLimit});
}

/// @nodoc
class __$$PremiumSubscriptionImplCopyWithImpl<$Res>
    extends _$PremiumSubscriptionCopyWithImpl<$Res, _$PremiumSubscriptionImpl>
    implements _$$PremiumSubscriptionImplCopyWith<$Res> {
  __$$PremiumSubscriptionImplCopyWithImpl(_$PremiumSubscriptionImpl _value,
      $Res Function(_$PremiumSubscriptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? plan = null,
    Object? status = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? monthlyPrice = null,
    Object? totalPaid = null,
    Object? lastPaymentDate = freezed,
    Object? nextPaymentDate = freezed,
    Object? pointMultiplier = null,
    Object? dailyStreakBonus = null,
    Object? voiceCommentsLimit = null,
  }) {
    return _then(_$PremiumSubscriptionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      plan: null == plan
          ? _value.plan
          : plan // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      monthlyPrice: null == monthlyPrice
          ? _value.monthlyPrice
          : monthlyPrice // ignore: cast_nullable_to_non_nullable
              as double,
      totalPaid: null == totalPaid
          ? _value.totalPaid
          : totalPaid // ignore: cast_nullable_to_non_nullable
              as double,
      lastPaymentDate: freezed == lastPaymentDate
          ? _value.lastPaymentDate
          : lastPaymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      nextPaymentDate: freezed == nextPaymentDate
          ? _value.nextPaymentDate
          : nextPaymentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      pointMultiplier: null == pointMultiplier
          ? _value.pointMultiplier
          : pointMultiplier // ignore: cast_nullable_to_non_nullable
              as double,
      dailyStreakBonus: null == dailyStreakBonus
          ? _value.dailyStreakBonus
          : dailyStreakBonus // ignore: cast_nullable_to_non_nullable
              as int,
      voiceCommentsLimit: null == voiceCommentsLimit
          ? _value.voiceCommentsLimit
          : voiceCommentsLimit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PremiumSubscriptionImpl implements _PremiumSubscription {
  const _$PremiumSubscriptionImpl(
      {required this.id,
      required this.userId,
      required this.plan,
      required this.status,
      required this.startDate,
      required this.endDate,
      required this.monthlyPrice,
      required this.totalPaid,
      this.lastPaymentDate,
      this.nextPaymentDate,
      this.pointMultiplier = 2.0,
      this.dailyStreakBonus = 15,
      this.voiceCommentsLimit = -1});

  factory _$PremiumSubscriptionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PremiumSubscriptionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String plan;
// 'monthly', 'quarterly', 'yearly'
  @override
  final String status;
// 'active', 'cancelled', 'expired'
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  final double monthlyPrice;
  @override
  final double totalPaid;
  @override
  final DateTime? lastPaymentDate;
  @override
  final DateTime? nextPaymentDate;
  @override
  @JsonKey()
  final double pointMultiplier;
  @override
  @JsonKey()
  final int dailyStreakBonus;
  @override
  @JsonKey()
  final int voiceCommentsLimit;

  @override
  String toString() {
    return 'PremiumSubscription(id: $id, userId: $userId, plan: $plan, status: $status, startDate: $startDate, endDate: $endDate, monthlyPrice: $monthlyPrice, totalPaid: $totalPaid, lastPaymentDate: $lastPaymentDate, nextPaymentDate: $nextPaymentDate, pointMultiplier: $pointMultiplier, dailyStreakBonus: $dailyStreakBonus, voiceCommentsLimit: $voiceCommentsLimit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PremiumSubscriptionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.plan, plan) || other.plan == plan) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.monthlyPrice, monthlyPrice) ||
                other.monthlyPrice == monthlyPrice) &&
            (identical(other.totalPaid, totalPaid) ||
                other.totalPaid == totalPaid) &&
            (identical(other.lastPaymentDate, lastPaymentDate) ||
                other.lastPaymentDate == lastPaymentDate) &&
            (identical(other.nextPaymentDate, nextPaymentDate) ||
                other.nextPaymentDate == nextPaymentDate) &&
            (identical(other.pointMultiplier, pointMultiplier) ||
                other.pointMultiplier == pointMultiplier) &&
            (identical(other.dailyStreakBonus, dailyStreakBonus) ||
                other.dailyStreakBonus == dailyStreakBonus) &&
            (identical(other.voiceCommentsLimit, voiceCommentsLimit) ||
                other.voiceCommentsLimit == voiceCommentsLimit));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      plan,
      status,
      startDate,
      endDate,
      monthlyPrice,
      totalPaid,
      lastPaymentDate,
      nextPaymentDate,
      pointMultiplier,
      dailyStreakBonus,
      voiceCommentsLimit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PremiumSubscriptionImplCopyWith<_$PremiumSubscriptionImpl> get copyWith =>
      __$$PremiumSubscriptionImplCopyWithImpl<_$PremiumSubscriptionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PremiumSubscriptionImplToJson(
      this,
    );
  }
}

abstract class _PremiumSubscription implements PremiumSubscription {
  const factory _PremiumSubscription(
      {required final String id,
      required final String userId,
      required final String plan,
      required final String status,
      required final DateTime startDate,
      required final DateTime endDate,
      required final double monthlyPrice,
      required final double totalPaid,
      final DateTime? lastPaymentDate,
      final DateTime? nextPaymentDate,
      final double pointMultiplier,
      final int dailyStreakBonus,
      final int voiceCommentsLimit}) = _$PremiumSubscriptionImpl;

  factory _PremiumSubscription.fromJson(Map<String, dynamic> json) =
      _$PremiumSubscriptionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get plan;
  @override // 'monthly', 'quarterly', 'yearly'
  String get status;
  @override // 'active', 'cancelled', 'expired'
  DateTime get startDate;
  @override
  DateTime get endDate;
  @override
  double get monthlyPrice;
  @override
  double get totalPaid;
  @override
  DateTime? get lastPaymentDate;
  @override
  DateTime? get nextPaymentDate;
  @override
  double get pointMultiplier;
  @override
  int get dailyStreakBonus;
  @override
  int get voiceCommentsLimit;
  @override
  @JsonKey(ignore: true)
  _$$PremiumSubscriptionImplCopyWith<_$PremiumSubscriptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentTransaction _$PaymentTransactionFromJson(Map<String, dynamic> json) {
  return _PaymentTransaction.fromJson(json);
}

/// @nodoc
mixin _$PaymentTransaction {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get transactionType =>
      throw _privateConstructorUsedError; // 'incoming', 'outgoing'
  String get paymentPurpose => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  String get status =>
      throw _privateConstructorUsedError; // 'pending', 'processing', 'completed', 'failed'
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get processedAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  String? get paypalPaymentId => throw _privateConstructorUsedError;
  String? get paypalPayerId => throw _privateConstructorUsedError;
  String? get referenceId => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PaymentTransactionCopyWith<PaymentTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentTransactionCopyWith<$Res> {
  factory $PaymentTransactionCopyWith(
          PaymentTransaction value, $Res Function(PaymentTransaction) then) =
      _$PaymentTransactionCopyWithImpl<$Res, PaymentTransaction>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String transactionType,
      String paymentPurpose,
      double amount,
      String currency,
      String status,
      DateTime createdAt,
      DateTime? processedAt,
      DateTime? completedAt,
      String? paypalPaymentId,
      String? paypalPayerId,
      String? referenceId,
      String? description,
      String? errorMessage});
}

/// @nodoc
class _$PaymentTransactionCopyWithImpl<$Res, $Val extends PaymentTransaction>
    implements $PaymentTransactionCopyWith<$Res> {
  _$PaymentTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? transactionType = null,
    Object? paymentPurpose = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? createdAt = null,
    Object? processedAt = freezed,
    Object? completedAt = freezed,
    Object? paypalPaymentId = freezed,
    Object? paypalPayerId = freezed,
    Object? referenceId = freezed,
    Object? description = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String,
      paymentPurpose: null == paymentPurpose
          ? _value.paymentPurpose
          : paymentPurpose // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      processedAt: freezed == processedAt
          ? _value.processedAt
          : processedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paypalPaymentId: freezed == paypalPaymentId
          ? _value.paypalPaymentId
          : paypalPaymentId // ignore: cast_nullable_to_non_nullable
              as String?,
      paypalPayerId: freezed == paypalPayerId
          ? _value.paypalPayerId
          : paypalPayerId // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceId: freezed == referenceId
          ? _value.referenceId
          : referenceId // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentTransactionImplCopyWith<$Res>
    implements $PaymentTransactionCopyWith<$Res> {
  factory _$$PaymentTransactionImplCopyWith(_$PaymentTransactionImpl value,
          $Res Function(_$PaymentTransactionImpl) then) =
      __$$PaymentTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String transactionType,
      String paymentPurpose,
      double amount,
      String currency,
      String status,
      DateTime createdAt,
      DateTime? processedAt,
      DateTime? completedAt,
      String? paypalPaymentId,
      String? paypalPayerId,
      String? referenceId,
      String? description,
      String? errorMessage});
}

/// @nodoc
class __$$PaymentTransactionImplCopyWithImpl<$Res>
    extends _$PaymentTransactionCopyWithImpl<$Res, _$PaymentTransactionImpl>
    implements _$$PaymentTransactionImplCopyWith<$Res> {
  __$$PaymentTransactionImplCopyWithImpl(_$PaymentTransactionImpl _value,
      $Res Function(_$PaymentTransactionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? transactionType = null,
    Object? paymentPurpose = null,
    Object? amount = null,
    Object? currency = null,
    Object? status = null,
    Object? createdAt = null,
    Object? processedAt = freezed,
    Object? completedAt = freezed,
    Object? paypalPaymentId = freezed,
    Object? paypalPayerId = freezed,
    Object? referenceId = freezed,
    Object? description = freezed,
    Object? errorMessage = freezed,
  }) {
    return _then(_$PaymentTransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String,
      paymentPurpose: null == paymentPurpose
          ? _value.paymentPurpose
          : paymentPurpose // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      processedAt: freezed == processedAt
          ? _value.processedAt
          : processedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paypalPaymentId: freezed == paypalPaymentId
          ? _value.paypalPaymentId
          : paypalPaymentId // ignore: cast_nullable_to_non_nullable
              as String?,
      paypalPayerId: freezed == paypalPayerId
          ? _value.paypalPayerId
          : paypalPayerId // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceId: freezed == referenceId
          ? _value.referenceId
          : referenceId // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentTransactionImpl implements _PaymentTransaction {
  const _$PaymentTransactionImpl(
      {required this.id,
      required this.userId,
      required this.transactionType,
      required this.paymentPurpose,
      required this.amount,
      required this.currency,
      required this.status,
      required this.createdAt,
      this.processedAt,
      this.completedAt,
      this.paypalPaymentId,
      this.paypalPayerId,
      this.referenceId,
      this.description,
      this.errorMessage});

  factory _$PaymentTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentTransactionImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String transactionType;
// 'incoming', 'outgoing'
  @override
  final String paymentPurpose;
  @override
  final double amount;
  @override
  final String currency;
  @override
  final String status;
// 'pending', 'processing', 'completed', 'failed'
  @override
  final DateTime createdAt;
  @override
  final DateTime? processedAt;
  @override
  final DateTime? completedAt;
  @override
  final String? paypalPaymentId;
  @override
  final String? paypalPayerId;
  @override
  final String? referenceId;
  @override
  final String? description;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'PaymentTransaction(id: $id, userId: $userId, transactionType: $transactionType, paymentPurpose: $paymentPurpose, amount: $amount, currency: $currency, status: $status, createdAt: $createdAt, processedAt: $processedAt, completedAt: $completedAt, paypalPaymentId: $paypalPaymentId, paypalPayerId: $paypalPayerId, referenceId: $referenceId, description: $description, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.paymentPurpose, paymentPurpose) ||
                other.paymentPurpose == paymentPurpose) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.processedAt, processedAt) ||
                other.processedAt == processedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.paypalPaymentId, paypalPaymentId) ||
                other.paypalPaymentId == paypalPaymentId) &&
            (identical(other.paypalPayerId, paypalPayerId) ||
                other.paypalPayerId == paypalPayerId) &&
            (identical(other.referenceId, referenceId) ||
                other.referenceId == referenceId) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      transactionType,
      paymentPurpose,
      amount,
      currency,
      status,
      createdAt,
      processedAt,
      completedAt,
      paypalPaymentId,
      paypalPayerId,
      referenceId,
      description,
      errorMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentTransactionImplCopyWith<_$PaymentTransactionImpl> get copyWith =>
      __$$PaymentTransactionImplCopyWithImpl<_$PaymentTransactionImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentTransactionImplToJson(
      this,
    );
  }
}

abstract class _PaymentTransaction implements PaymentTransaction {
  const factory _PaymentTransaction(
      {required final String id,
      required final String userId,
      required final String transactionType,
      required final String paymentPurpose,
      required final double amount,
      required final String currency,
      required final String status,
      required final DateTime createdAt,
      final DateTime? processedAt,
      final DateTime? completedAt,
      final String? paypalPaymentId,
      final String? paypalPayerId,
      final String? referenceId,
      final String? description,
      final String? errorMessage}) = _$PaymentTransactionImpl;

  factory _PaymentTransaction.fromJson(Map<String, dynamic> json) =
      _$PaymentTransactionImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String get transactionType;
  @override // 'incoming', 'outgoing'
  String get paymentPurpose;
  @override
  double get amount;
  @override
  String get currency;
  @override
  String get status;
  @override // 'pending', 'processing', 'completed', 'failed'
  DateTime get createdAt;
  @override
  DateTime? get processedAt;
  @override
  DateTime? get completedAt;
  @override
  String? get paypalPaymentId;
  @override
  String? get paypalPayerId;
  @override
  String? get referenceId;
  @override
  String? get description;
  @override
  String? get errorMessage;
  @override
  @JsonKey(ignore: true)
  _$$PaymentTransactionImplCopyWith<_$PaymentTransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AdStats _$AdStatsFromJson(Map<String, dynamic> json) {
  return _AdStats.fromJson(json);
}

/// @nodoc
mixin _$AdStats {
  int get todayImpressions => throw _privateConstructorUsedError;
  int get todayPoints => throw _privateConstructorUsedError;
  int get dailyLimitRemaining => throw _privateConstructorUsedError;
  int get totalImpressions => throw _privateConstructorUsedError;
  int get totalClicks => throw _privateConstructorUsedError;
  int get totalPointsEarned => throw _privateConstructorUsedError;
  int get completedRewardedAds => throw _privateConstructorUsedError;
  double get clickThroughRate => throw _privateConstructorUsedError;
  int get maxDailyAdPoints => throw _privateConstructorUsedError;
  int get maxAdsPerSession => throw _privateConstructorUsedError;
  int get minTimeBetweenAds => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AdStatsCopyWith<AdStats> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdStatsCopyWith<$Res> {
  factory $AdStatsCopyWith(AdStats value, $Res Function(AdStats) then) =
      _$AdStatsCopyWithImpl<$Res, AdStats>;
  @useResult
  $Res call(
      {int todayImpressions,
      int todayPoints,
      int dailyLimitRemaining,
      int totalImpressions,
      int totalClicks,
      int totalPointsEarned,
      int completedRewardedAds,
      double clickThroughRate,
      int maxDailyAdPoints,
      int maxAdsPerSession,
      int minTimeBetweenAds});
}

/// @nodoc
class _$AdStatsCopyWithImpl<$Res, $Val extends AdStats>
    implements $AdStatsCopyWith<$Res> {
  _$AdStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayImpressions = null,
    Object? todayPoints = null,
    Object? dailyLimitRemaining = null,
    Object? totalImpressions = null,
    Object? totalClicks = null,
    Object? totalPointsEarned = null,
    Object? completedRewardedAds = null,
    Object? clickThroughRate = null,
    Object? maxDailyAdPoints = null,
    Object? maxAdsPerSession = null,
    Object? minTimeBetweenAds = null,
  }) {
    return _then(_value.copyWith(
      todayImpressions: null == todayImpressions
          ? _value.todayImpressions
          : todayImpressions // ignore: cast_nullable_to_non_nullable
              as int,
      todayPoints: null == todayPoints
          ? _value.todayPoints
          : todayPoints // ignore: cast_nullable_to_non_nullable
              as int,
      dailyLimitRemaining: null == dailyLimitRemaining
          ? _value.dailyLimitRemaining
          : dailyLimitRemaining // ignore: cast_nullable_to_non_nullable
              as int,
      totalImpressions: null == totalImpressions
          ? _value.totalImpressions
          : totalImpressions // ignore: cast_nullable_to_non_nullable
              as int,
      totalClicks: null == totalClicks
          ? _value.totalClicks
          : totalClicks // ignore: cast_nullable_to_non_nullable
              as int,
      totalPointsEarned: null == totalPointsEarned
          ? _value.totalPointsEarned
          : totalPointsEarned // ignore: cast_nullable_to_non_nullable
              as int,
      completedRewardedAds: null == completedRewardedAds
          ? _value.completedRewardedAds
          : completedRewardedAds // ignore: cast_nullable_to_non_nullable
              as int,
      clickThroughRate: null == clickThroughRate
          ? _value.clickThroughRate
          : clickThroughRate // ignore: cast_nullable_to_non_nullable
              as double,
      maxDailyAdPoints: null == maxDailyAdPoints
          ? _value.maxDailyAdPoints
          : maxDailyAdPoints // ignore: cast_nullable_to_non_nullable
              as int,
      maxAdsPerSession: null == maxAdsPerSession
          ? _value.maxAdsPerSession
          : maxAdsPerSession // ignore: cast_nullable_to_non_nullable
              as int,
      minTimeBetweenAds: null == minTimeBetweenAds
          ? _value.minTimeBetweenAds
          : minTimeBetweenAds // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdStatsImplCopyWith<$Res> implements $AdStatsCopyWith<$Res> {
  factory _$$AdStatsImplCopyWith(
          _$AdStatsImpl value, $Res Function(_$AdStatsImpl) then) =
      __$$AdStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int todayImpressions,
      int todayPoints,
      int dailyLimitRemaining,
      int totalImpressions,
      int totalClicks,
      int totalPointsEarned,
      int completedRewardedAds,
      double clickThroughRate,
      int maxDailyAdPoints,
      int maxAdsPerSession,
      int minTimeBetweenAds});
}

/// @nodoc
class __$$AdStatsImplCopyWithImpl<$Res>
    extends _$AdStatsCopyWithImpl<$Res, _$AdStatsImpl>
    implements _$$AdStatsImplCopyWith<$Res> {
  __$$AdStatsImplCopyWithImpl(
      _$AdStatsImpl _value, $Res Function(_$AdStatsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayImpressions = null,
    Object? todayPoints = null,
    Object? dailyLimitRemaining = null,
    Object? totalImpressions = null,
    Object? totalClicks = null,
    Object? totalPointsEarned = null,
    Object? completedRewardedAds = null,
    Object? clickThroughRate = null,
    Object? maxDailyAdPoints = null,
    Object? maxAdsPerSession = null,
    Object? minTimeBetweenAds = null,
  }) {
    return _then(_$AdStatsImpl(
      todayImpressions: null == todayImpressions
          ? _value.todayImpressions
          : todayImpressions // ignore: cast_nullable_to_non_nullable
              as int,
      todayPoints: null == todayPoints
          ? _value.todayPoints
          : todayPoints // ignore: cast_nullable_to_non_nullable
              as int,
      dailyLimitRemaining: null == dailyLimitRemaining
          ? _value.dailyLimitRemaining
          : dailyLimitRemaining // ignore: cast_nullable_to_non_nullable
              as int,
      totalImpressions: null == totalImpressions
          ? _value.totalImpressions
          : totalImpressions // ignore: cast_nullable_to_non_nullable
              as int,
      totalClicks: null == totalClicks
          ? _value.totalClicks
          : totalClicks // ignore: cast_nullable_to_non_nullable
              as int,
      totalPointsEarned: null == totalPointsEarned
          ? _value.totalPointsEarned
          : totalPointsEarned // ignore: cast_nullable_to_non_nullable
              as int,
      completedRewardedAds: null == completedRewardedAds
          ? _value.completedRewardedAds
          : completedRewardedAds // ignore: cast_nullable_to_non_nullable
              as int,
      clickThroughRate: null == clickThroughRate
          ? _value.clickThroughRate
          : clickThroughRate // ignore: cast_nullable_to_non_nullable
              as double,
      maxDailyAdPoints: null == maxDailyAdPoints
          ? _value.maxDailyAdPoints
          : maxDailyAdPoints // ignore: cast_nullable_to_non_nullable
              as int,
      maxAdsPerSession: null == maxAdsPerSession
          ? _value.maxAdsPerSession
          : maxAdsPerSession // ignore: cast_nullable_to_non_nullable
              as int,
      minTimeBetweenAds: null == minTimeBetweenAds
          ? _value.minTimeBetweenAds
          : minTimeBetweenAds // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AdStatsImpl implements _AdStats {
  const _$AdStatsImpl(
      {this.todayImpressions = 0,
      this.todayPoints = 0,
      this.dailyLimitRemaining = 0,
      this.totalImpressions = 0,
      this.totalClicks = 0,
      this.totalPointsEarned = 0,
      this.completedRewardedAds = 0,
      this.clickThroughRate = 0.0,
      this.maxDailyAdPoints = 0,
      this.maxAdsPerSession = 0,
      this.minTimeBetweenAds = 0});

  factory _$AdStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AdStatsImplFromJson(json);

  @override
  @JsonKey()
  final int todayImpressions;
  @override
  @JsonKey()
  final int todayPoints;
  @override
  @JsonKey()
  final int dailyLimitRemaining;
  @override
  @JsonKey()
  final int totalImpressions;
  @override
  @JsonKey()
  final int totalClicks;
  @override
  @JsonKey()
  final int totalPointsEarned;
  @override
  @JsonKey()
  final int completedRewardedAds;
  @override
  @JsonKey()
  final double clickThroughRate;
  @override
  @JsonKey()
  final int maxDailyAdPoints;
  @override
  @JsonKey()
  final int maxAdsPerSession;
  @override
  @JsonKey()
  final int minTimeBetweenAds;

  @override
  String toString() {
    return 'AdStats(todayImpressions: $todayImpressions, todayPoints: $todayPoints, dailyLimitRemaining: $dailyLimitRemaining, totalImpressions: $totalImpressions, totalClicks: $totalClicks, totalPointsEarned: $totalPointsEarned, completedRewardedAds: $completedRewardedAds, clickThroughRate: $clickThroughRate, maxDailyAdPoints: $maxDailyAdPoints, maxAdsPerSession: $maxAdsPerSession, minTimeBetweenAds: $minTimeBetweenAds)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdStatsImpl &&
            (identical(other.todayImpressions, todayImpressions) ||
                other.todayImpressions == todayImpressions) &&
            (identical(other.todayPoints, todayPoints) ||
                other.todayPoints == todayPoints) &&
            (identical(other.dailyLimitRemaining, dailyLimitRemaining) ||
                other.dailyLimitRemaining == dailyLimitRemaining) &&
            (identical(other.totalImpressions, totalImpressions) ||
                other.totalImpressions == totalImpressions) &&
            (identical(other.totalClicks, totalClicks) ||
                other.totalClicks == totalClicks) &&
            (identical(other.totalPointsEarned, totalPointsEarned) ||
                other.totalPointsEarned == totalPointsEarned) &&
            (identical(other.completedRewardedAds, completedRewardedAds) ||
                other.completedRewardedAds == completedRewardedAds) &&
            (identical(other.clickThroughRate, clickThroughRate) ||
                other.clickThroughRate == clickThroughRate) &&
            (identical(other.maxDailyAdPoints, maxDailyAdPoints) ||
                other.maxDailyAdPoints == maxDailyAdPoints) &&
            (identical(other.maxAdsPerSession, maxAdsPerSession) ||
                other.maxAdsPerSession == maxAdsPerSession) &&
            (identical(other.minTimeBetweenAds, minTimeBetweenAds) ||
                other.minTimeBetweenAds == minTimeBetweenAds));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      todayImpressions,
      todayPoints,
      dailyLimitRemaining,
      totalImpressions,
      totalClicks,
      totalPointsEarned,
      completedRewardedAds,
      clickThroughRate,
      maxDailyAdPoints,
      maxAdsPerSession,
      minTimeBetweenAds);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AdStatsImplCopyWith<_$AdStatsImpl> get copyWith =>
      __$$AdStatsImplCopyWithImpl<_$AdStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AdStatsImplToJson(
      this,
    );
  }
}

abstract class _AdStats implements AdStats {
  const factory _AdStats(
      {final int todayImpressions,
      final int todayPoints,
      final int dailyLimitRemaining,
      final int totalImpressions,
      final int totalClicks,
      final int totalPointsEarned,
      final int completedRewardedAds,
      final double clickThroughRate,
      final int maxDailyAdPoints,
      final int maxAdsPerSession,
      final int minTimeBetweenAds}) = _$AdStatsImpl;

  factory _AdStats.fromJson(Map<String, dynamic> json) = _$AdStatsImpl.fromJson;

  @override
  int get todayImpressions;
  @override
  int get todayPoints;
  @override
  int get dailyLimitRemaining;
  @override
  int get totalImpressions;
  @override
  int get totalClicks;
  @override
  int get totalPointsEarned;
  @override
  int get completedRewardedAds;
  @override
  double get clickThroughRate;
  @override
  int get maxDailyAdPoints;
  @override
  int get maxAdsPerSession;
  @override
  int get minTimeBetweenAds;
  @override
  @JsonKey(ignore: true)
  _$$AdStatsImplCopyWith<_$AdStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
