from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create a router for API endpoints
router = DefaultRouter()
router.register(r'placements', views.AdPlacementViewSet)
router.register(r'networks', views.AdNetworkViewSet)
router.register(r'impressions', views.AdImpressionViewSet)
router.register(r'rewarded-ads', views.RewardedAdViewSet)
router.register(r'sponsored-content', views.SponsoredContentViewSet)

app_name = 'advertising'

urlpatterns = [
    # API endpoints
    path('', include(router.urls)),

    # Specific advertising endpoints
    path('settings/', views.AdSettingsView.as_view(), name='ad-settings'),
    path('availability/', views.AdAvailabilityView.as_view(), name='ad-availability'),
    path('user-analytics/', views.UserAdAnalyticsView.as_view(), name='user-ad-analytics'),
    path('user-history/', views.UserAdHistoryView.as_view(), name='user-ad-history'),

    # Rewarded ad specific endpoints
    path('rewarded-ads/start/', views.StartRewardedAdView.as_view(), name='start-rewarded-ad'),
    path('rewarded-ads/<str:session_id>/complete/', views.CompleteRewardedAdView.as_view(), name='complete-rewarded-ad'),

    # Sponsored content endpoints
    path('sponsored-content/', views.SponsoredContentListView.as_view(), name='sponsored-content-list'),
    path('sponsored-content/<str:content_id>/click/', views.SponsoredContentClickView.as_view(), name='sponsored-content-click'),
    path('sponsored-content/<str:content_id>/impression/', views.SponsoredContentImpressionView.as_view(), name='sponsored-content-impression'),

    # Impression tracking
    path('impressions/', views.RecordImpressionView.as_view(), name='record-impression'),
]
