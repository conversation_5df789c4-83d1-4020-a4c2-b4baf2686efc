import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import '../config/api_config.dart';

enum ConnectionStatus {
  online,
  offline,
  limited, // Connected but server unreachable
  slow,    // Connected but slow response
}

enum ConnectionType {
  wifi,
  mobile,
  ethernet,
  none,
}

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final Dio _dio = Dio();
  
  ConnectionStatus _currentStatus = ConnectionStatus.offline;
  ConnectionType _currentType = ConnectionType.none;
  
  StreamController<ConnectionStatus>? _statusController;
  StreamController<ConnectionType>? _typeController;
  Timer? _healthCheckTimer;
  Timer? _retryTimer;
  
  bool _isInitialized = false;
  int _consecutiveFailures = 0;
  DateTime? _lastSuccessfulCheck;
  
  // Getters
  ConnectionStatus get currentStatus => _currentStatus;
  ConnectionType get currentType => _currentType;
  bool get isOnline => _currentStatus == ConnectionStatus.online;
  bool get isOffline => _currentStatus == ConnectionStatus.offline;
  bool get hasLimitedConnection => _currentStatus == ConnectionStatus.limited;
  bool get hasSlowConnection => _currentStatus == ConnectionStatus.slow;
  
  // Streams
  Stream<ConnectionStatus> get statusStream => _statusController?.stream ?? const Stream.empty();
  Stream<ConnectionType> get typeStream => _typeController?.stream ?? const Stream.empty();

  /// Initialize the connectivity service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _statusController = StreamController<ConnectionStatus>.broadcast();
    _typeController = StreamController<ConnectionType>.broadcast();
    
    // Configure Dio for health checks
    _dio.options = BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: const Duration(seconds: 5),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 5),
    );
    
    // Listen to connectivity changes
    _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
    
    // Initial connectivity check
    await _checkInitialConnectivity();
    
    // Start periodic health checks
    _startHealthChecks();
    
    _isInitialized = true;
  }

  /// Dispose resources
  void dispose() {
    _statusController?.close();
    _typeController?.close();
    _healthCheckTimer?.cancel();
    _retryTimer?.cancel();
    _isInitialized = false;
  }

  /// Check initial connectivity state
  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      await _onConnectivityChanged(connectivityResult);
    } catch (e) {
      print('Error checking initial connectivity: $e');
      _updateStatus(ConnectionStatus.offline);
      _updateType(ConnectionType.none);
    }
  }

  /// Handle connectivity changes
  Future<void> _onConnectivityChanged(List<ConnectivityResult> results) async {
    if (results.isEmpty) {
      _updateType(ConnectionType.none);
      _updateStatus(ConnectionStatus.offline);
      return;
    }

    final result = results.first;
    
    // Update connection type
    switch (result) {
      case ConnectivityResult.wifi:
        _updateType(ConnectionType.wifi);
        break;
      case ConnectivityResult.mobile:
        _updateType(ConnectionType.mobile);
        break;
      case ConnectivityResult.ethernet:
        _updateType(ConnectionType.ethernet);
        break;
      case ConnectivityResult.none:
        _updateType(ConnectionType.none);
        _updateStatus(ConnectionStatus.offline);
        return;
      default:
        _updateType(ConnectionType.none);
        _updateStatus(ConnectionStatus.offline);
        return;
    }

    // Check server reachability
    await _checkServerHealth();
  }

  /// Start periodic health checks
  void _startHealthChecks() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (_currentType != ConnectionType.none) {
        _checkServerHealth();
      }
    });
  }

  /// Check server health and reachability
  Future<void> _checkServerHealth() async {
    if (_currentType == ConnectionType.none) {
      _updateStatus(ConnectionStatus.offline);
      return;
    }

    try {
      final stopwatch = Stopwatch()..start();
      
      // Try to reach the health endpoint
      final response = await _dio.get(
        '/api/v1/system/health/',
        options: Options(
          validateStatus: (status) => status != null && status < 500,
        ),
      );
      
      stopwatch.stop();
      final responseTime = stopwatch.elapsedMilliseconds;
      
      if (response.statusCode == 200 || response.statusCode == 503) {
        // Server is reachable (even if in maintenance)
        _consecutiveFailures = 0;
        _lastSuccessfulCheck = DateTime.now();
        
        if (responseTime > 3000) {
          _updateStatus(ConnectionStatus.slow);
        } else {
          _updateStatus(ConnectionStatus.online);
        }
      } else {
        _handleHealthCheckFailure();
      }
    } catch (e) {
      _handleHealthCheckFailure();
    }
  }

  /// Handle health check failures
  void _handleHealthCheckFailure() {
    _consecutiveFailures++;
    
    if (_consecutiveFailures >= 3) {
      _updateStatus(ConnectionStatus.limited);
    } else if (_currentStatus == ConnectionStatus.online) {
      // Keep online status for first few failures
      return;
    }
    
    // Start retry mechanism for limited connections
    if (_currentStatus == ConnectionStatus.limited) {
      _startRetryMechanism();
    }
  }

  /// Start retry mechanism for limited connections
  void _startRetryMechanism() {
    _retryTimer?.cancel();
    
    // Exponential backoff: 5s, 10s, 20s, 40s, then 60s max
    final retryDelay = Duration(
      seconds: (5 * (1 << (_consecutiveFailures - 3).clamp(0, 4))).clamp(5, 60),
    );
    
    _retryTimer = Timer(retryDelay, () {
      _checkServerHealth();
    });
  }

  /// Force a connectivity check
  Future<void> forceCheck() async {
    await _checkServerHealth();
  }

  /// Check if we can make API calls
  bool canMakeApiCalls() {
    return _currentStatus == ConnectionStatus.online || 
           _currentStatus == ConnectionStatus.slow;
  }

  /// Get user-friendly status message
  String getStatusMessage() {
    switch (_currentStatus) {
      case ConnectionStatus.online:
        return 'Connected';
      case ConnectionStatus.offline:
        return 'No internet connection';
      case ConnectionStatus.limited:
        return 'Limited connectivity - server unreachable';
      case ConnectionStatus.slow:
        return 'Slow connection detected';
    }
  }

  /// Get detailed status information
  Map<String, dynamic> getStatusInfo() {
    return {
      'status': _currentStatus.name,
      'type': _currentType.name,
      'canMakeApiCalls': canMakeApiCalls(),
      'consecutiveFailures': _consecutiveFailures,
      'lastSuccessfulCheck': _lastSuccessfulCheck?.toIso8601String(),
      'message': getStatusMessage(),
    };
  }

  /// Update connection status
  void _updateStatus(ConnectionStatus status) {
    if (_currentStatus != status) {
      _currentStatus = status;
      _statusController?.add(status);
      print('🌐 Connection status changed: ${status.name}');
    }
  }

  /// Update connection type
  void _updateType(ConnectionType type) {
    if (_currentType != type) {
      _currentType = type;
      _typeController?.add(type);
      print('📡 Connection type changed: ${type.name}');
    }
  }

  /// Test internet connectivity (ping Google DNS)
  Future<bool> hasInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Get connection quality score (0-100)
  int getConnectionQuality() {
    switch (_currentStatus) {
      case ConnectionStatus.online:
        return _currentType == ConnectionType.wifi ? 100 : 80;
      case ConnectionStatus.slow:
        return 40;
      case ConnectionStatus.limited:
        return 20;
      case ConnectionStatus.offline:
        return 0;
    }
  }
}
