import 'package:json_annotation/json_annotation.dart';

part 'tag.g.dart';

@JsonSerializable()
class Tag {
  final int id;
  final String name;
  final String slug;
  final String? description;

  Tag({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
  });

  factory Tag.fromJson(Map<String, dynamic> json) => _$TagFromJson(json);
  Map<String, dynamic> toJson() => _$TagToJson(this);

  Tag copyWith({
    int? id,
    String? name,
    String? slug,
    String? description,
  }) {
    return Tag(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
    );
  }
}