# Generated by Django 5.1.7 on 2025-06-24 07:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blockchain', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='stakingpool',
            name='maximum_stake',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='stakingpool',
            name='minimum_stake',
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='stakingpool',
            name='total_rewards_distributed',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15),
        ),
        migrations.AlterField(
            model_name='stakingpool',
            name='total_staked',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15),
        ),
        migrations.AlterField(
            model_name='userstake',
            name='amount_staked',
            field=models.DecimalField(decimal_places=2, max_digits=15),
        ),
        migrations.Alter<PERSON>ield(
            model_name='userstake',
            name='rewards_earned',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15),
        ),
    ]
