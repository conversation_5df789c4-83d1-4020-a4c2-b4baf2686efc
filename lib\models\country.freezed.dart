// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'country.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Region _$RegionFromJson(Map<String, dynamic> json) {
  return _Region.fromJson(json);
}

/// @nodoc
mixin _$Region {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get code => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int get countryCount => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RegionCopyWith<Region> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegionCopyWith<$Res> {
  factory $RegionCopyWith(Region value, $Res Function(Region) then) =
      _$RegionCopyWithImpl<$Res, Region>;
  @useResult
  $Res call(
      {int id,
      String name,
      String code,
      String? description,
      int countryCount,
      bool isActive});
}

/// @nodoc
class _$RegionCopyWithImpl<$Res, $Val extends Region>
    implements $RegionCopyWith<$Res> {
  _$RegionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? code = null,
    Object? description = freezed,
    Object? countryCount = null,
    Object? isActive = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCount: null == countryCount
          ? _value.countryCount
          : countryCount // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegionImplCopyWith<$Res> implements $RegionCopyWith<$Res> {
  factory _$$RegionImplCopyWith(
          _$RegionImpl value, $Res Function(_$RegionImpl) then) =
      __$$RegionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      String code,
      String? description,
      int countryCount,
      bool isActive});
}

/// @nodoc
class __$$RegionImplCopyWithImpl<$Res>
    extends _$RegionCopyWithImpl<$Res, _$RegionImpl>
    implements _$$RegionImplCopyWith<$Res> {
  __$$RegionImplCopyWithImpl(
      _$RegionImpl _value, $Res Function(_$RegionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? code = null,
    Object? description = freezed,
    Object? countryCount = null,
    Object? isActive = null,
  }) {
    return _then(_$RegionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCount: null == countryCount
          ? _value.countryCount
          : countryCount // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegionImpl implements _Region {
  const _$RegionImpl(
      {required this.id,
      required this.name,
      required this.code,
      this.description,
      this.countryCount = 0,
      this.isActive = true});

  factory _$RegionImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegionImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final String code;
  @override
  final String? description;
  @override
  @JsonKey()
  final int countryCount;
  @override
  @JsonKey()
  final bool isActive;

  @override
  String toString() {
    return 'Region(id: $id, name: $name, code: $code, description: $description, countryCount: $countryCount, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.countryCount, countryCount) ||
                other.countryCount == countryCount) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, name, code, description, countryCount, isActive);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegionImplCopyWith<_$RegionImpl> get copyWith =>
      __$$RegionImplCopyWithImpl<_$RegionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegionImplToJson(
      this,
    );
  }
}

abstract class _Region implements Region {
  const factory _Region(
      {required final int id,
      required final String name,
      required final String code,
      final String? description,
      final int countryCount,
      final bool isActive}) = _$RegionImpl;

  factory _Region.fromJson(Map<String, dynamic> json) = _$RegionImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  String get code;
  @override
  String? get description;
  @override
  int get countryCount;
  @override
  bool get isActive;
  @override
  @JsonKey(ignore: true)
  _$$RegionImplCopyWith<_$RegionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Country _$CountryFromJson(Map<String, dynamic> json) {
  return _Country.fromJson(json);
}

/// @nodoc
mixin _$Country {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get code => throw _privateConstructorUsedError;
  @JsonKey(name: 'code_3')
  String? get code3 => throw _privateConstructorUsedError;
  Region? get region => throw _privateConstructorUsedError;
  String? get currencyCode => throw _privateConstructorUsedError;
  String? get currencyName => throw _privateConstructorUsedError;
  String? get phoneCode => throw _privateConstructorUsedError;
  String? get flagEmoji => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get allowGlobalContent => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CountryCopyWith<Country> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CountryCopyWith<$Res> {
  factory $CountryCopyWith(Country value, $Res Function(Country) then) =
      _$CountryCopyWithImpl<$Res, Country>;
  @useResult
  $Res call(
      {int id,
      String name,
      String code,
      @JsonKey(name: 'code_3') String? code3,
      Region? region,
      String? currencyCode,
      String? currencyName,
      String? phoneCode,
      String? flagEmoji,
      String? displayName,
      bool isActive,
      bool allowGlobalContent,
      int priority});

  $RegionCopyWith<$Res>? get region;
}

/// @nodoc
class _$CountryCopyWithImpl<$Res, $Val extends Country>
    implements $CountryCopyWith<$Res> {
  _$CountryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? code = null,
    Object? code3 = freezed,
    Object? region = freezed,
    Object? currencyCode = freezed,
    Object? currencyName = freezed,
    Object? phoneCode = freezed,
    Object? flagEmoji = freezed,
    Object? displayName = freezed,
    Object? isActive = null,
    Object? allowGlobalContent = null,
    Object? priority = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      code3: freezed == code3
          ? _value.code3
          : code3 // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as Region?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyName: freezed == currencyName
          ? _value.currencyName
          : currencyName // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneCode: freezed == phoneCode
          ? _value.phoneCode
          : phoneCode // ignore: cast_nullable_to_non_nullable
              as String?,
      flagEmoji: freezed == flagEmoji
          ? _value.flagEmoji
          : flagEmoji // ignore: cast_nullable_to_non_nullable
              as String?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      allowGlobalContent: null == allowGlobalContent
          ? _value.allowGlobalContent
          : allowGlobalContent // ignore: cast_nullable_to_non_nullable
              as bool,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RegionCopyWith<$Res>? get region {
    if (_value.region == null) {
      return null;
    }

    return $RegionCopyWith<$Res>(_value.region!, (value) {
      return _then(_value.copyWith(region: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CountryImplCopyWith<$Res> implements $CountryCopyWith<$Res> {
  factory _$$CountryImplCopyWith(
          _$CountryImpl value, $Res Function(_$CountryImpl) then) =
      __$$CountryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      String code,
      @JsonKey(name: 'code_3') String? code3,
      Region? region,
      String? currencyCode,
      String? currencyName,
      String? phoneCode,
      String? flagEmoji,
      String? displayName,
      bool isActive,
      bool allowGlobalContent,
      int priority});

  @override
  $RegionCopyWith<$Res>? get region;
}

/// @nodoc
class __$$CountryImplCopyWithImpl<$Res>
    extends _$CountryCopyWithImpl<$Res, _$CountryImpl>
    implements _$$CountryImplCopyWith<$Res> {
  __$$CountryImplCopyWithImpl(
      _$CountryImpl _value, $Res Function(_$CountryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? code = null,
    Object? code3 = freezed,
    Object? region = freezed,
    Object? currencyCode = freezed,
    Object? currencyName = freezed,
    Object? phoneCode = freezed,
    Object? flagEmoji = freezed,
    Object? displayName = freezed,
    Object? isActive = null,
    Object? allowGlobalContent = null,
    Object? priority = null,
  }) {
    return _then(_$CountryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      code3: freezed == code3
          ? _value.code3
          : code3 // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _value.region
          : region // ignore: cast_nullable_to_non_nullable
              as Region?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyName: freezed == currencyName
          ? _value.currencyName
          : currencyName // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneCode: freezed == phoneCode
          ? _value.phoneCode
          : phoneCode // ignore: cast_nullable_to_non_nullable
              as String?,
      flagEmoji: freezed == flagEmoji
          ? _value.flagEmoji
          : flagEmoji // ignore: cast_nullable_to_non_nullable
              as String?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      allowGlobalContent: null == allowGlobalContent
          ? _value.allowGlobalContent
          : allowGlobalContent // ignore: cast_nullable_to_non_nullable
              as bool,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CountryImpl implements _Country {
  const _$CountryImpl(
      {required this.id,
      required this.name,
      required this.code,
      @JsonKey(name: 'code_3') this.code3,
      this.region,
      this.currencyCode,
      this.currencyName,
      this.phoneCode,
      this.flagEmoji,
      this.displayName,
      this.isActive = true,
      this.allowGlobalContent = true,
      this.priority = 0});

  factory _$CountryImpl.fromJson(Map<String, dynamic> json) =>
      _$$CountryImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final String code;
  @override
  @JsonKey(name: 'code_3')
  final String? code3;
  @override
  final Region? region;
  @override
  final String? currencyCode;
  @override
  final String? currencyName;
  @override
  final String? phoneCode;
  @override
  final String? flagEmoji;
  @override
  final String? displayName;
  @override
  @JsonKey()
  final bool isActive;
  @override
  @JsonKey()
  final bool allowGlobalContent;
  @override
  @JsonKey()
  final int priority;

  @override
  String toString() {
    return 'Country(id: $id, name: $name, code: $code, code3: $code3, region: $region, currencyCode: $currencyCode, currencyName: $currencyName, phoneCode: $phoneCode, flagEmoji: $flagEmoji, displayName: $displayName, isActive: $isActive, allowGlobalContent: $allowGlobalContent, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CountryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.code3, code3) || other.code3 == code3) &&
            (identical(other.region, region) || other.region == region) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.currencyName, currencyName) ||
                other.currencyName == currencyName) &&
            (identical(other.phoneCode, phoneCode) ||
                other.phoneCode == phoneCode) &&
            (identical(other.flagEmoji, flagEmoji) ||
                other.flagEmoji == flagEmoji) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.allowGlobalContent, allowGlobalContent) ||
                other.allowGlobalContent == allowGlobalContent) &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      code,
      code3,
      region,
      currencyCode,
      currencyName,
      phoneCode,
      flagEmoji,
      displayName,
      isActive,
      allowGlobalContent,
      priority);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CountryImplCopyWith<_$CountryImpl> get copyWith =>
      __$$CountryImplCopyWithImpl<_$CountryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CountryImplToJson(
      this,
    );
  }
}

abstract class _Country implements Country {
  const factory _Country(
      {required final int id,
      required final String name,
      required final String code,
      @JsonKey(name: 'code_3') final String? code3,
      final Region? region,
      final String? currencyCode,
      final String? currencyName,
      final String? phoneCode,
      final String? flagEmoji,
      final String? displayName,
      final bool isActive,
      final bool allowGlobalContent,
      final int priority}) = _$CountryImpl;

  factory _Country.fromJson(Map<String, dynamic> json) = _$CountryImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  String get code;
  @override
  @JsonKey(name: 'code_3')
  String? get code3;
  @override
  Region? get region;
  @override
  String? get currencyCode;
  @override
  String? get currencyName;
  @override
  String? get phoneCode;
  @override
  String? get flagEmoji;
  @override
  String? get displayName;
  @override
  bool get isActive;
  @override
  bool get allowGlobalContent;
  @override
  int get priority;
  @override
  @JsonKey(ignore: true)
  _$$CountryImplCopyWith<_$CountryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RegionalPreferences _$RegionalPreferencesFromJson(Map<String, dynamic> json) {
  return _RegionalPreferences.fromJson(json);
}

/// @nodoc
mixin _$RegionalPreferences {
  @JsonKey(name: 'preferred_country')
  Country? get preferredCountry => throw _privateConstructorUsedError;
  @JsonKey(name: 'detected_country')
  Country? get detectedCountry => throw _privateConstructorUsedError;
  @JsonKey(name: 'effective_country')
  Country? get effectiveCountry => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_global_content')
  bool get showGlobalContent => throw _privateConstructorUsedError;
  @JsonKey(name: 'auto_detect_location')
  bool get autoDetectLocation => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RegionalPreferencesCopyWith<RegionalPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegionalPreferencesCopyWith<$Res> {
  factory $RegionalPreferencesCopyWith(
          RegionalPreferences value, $Res Function(RegionalPreferences) then) =
      _$RegionalPreferencesCopyWithImpl<$Res, RegionalPreferences>;
  @useResult
  $Res call(
      {@JsonKey(name: 'preferred_country') Country? preferredCountry,
      @JsonKey(name: 'detected_country') Country? detectedCountry,
      @JsonKey(name: 'effective_country') Country? effectiveCountry,
      @JsonKey(name: 'show_global_content') bool showGlobalContent,
      @JsonKey(name: 'auto_detect_location') bool autoDetectLocation});

  $CountryCopyWith<$Res>? get preferredCountry;
  $CountryCopyWith<$Res>? get detectedCountry;
  $CountryCopyWith<$Res>? get effectiveCountry;
}

/// @nodoc
class _$RegionalPreferencesCopyWithImpl<$Res, $Val extends RegionalPreferences>
    implements $RegionalPreferencesCopyWith<$Res> {
  _$RegionalPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? preferredCountry = freezed,
    Object? detectedCountry = freezed,
    Object? effectiveCountry = freezed,
    Object? showGlobalContent = null,
    Object? autoDetectLocation = null,
  }) {
    return _then(_value.copyWith(
      preferredCountry: freezed == preferredCountry
          ? _value.preferredCountry
          : preferredCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      detectedCountry: freezed == detectedCountry
          ? _value.detectedCountry
          : detectedCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      effectiveCountry: freezed == effectiveCountry
          ? _value.effectiveCountry
          : effectiveCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      showGlobalContent: null == showGlobalContent
          ? _value.showGlobalContent
          : showGlobalContent // ignore: cast_nullable_to_non_nullable
              as bool,
      autoDetectLocation: null == autoDetectLocation
          ? _value.autoDetectLocation
          : autoDetectLocation // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CountryCopyWith<$Res>? get preferredCountry {
    if (_value.preferredCountry == null) {
      return null;
    }

    return $CountryCopyWith<$Res>(_value.preferredCountry!, (value) {
      return _then(_value.copyWith(preferredCountry: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CountryCopyWith<$Res>? get detectedCountry {
    if (_value.detectedCountry == null) {
      return null;
    }

    return $CountryCopyWith<$Res>(_value.detectedCountry!, (value) {
      return _then(_value.copyWith(detectedCountry: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CountryCopyWith<$Res>? get effectiveCountry {
    if (_value.effectiveCountry == null) {
      return null;
    }

    return $CountryCopyWith<$Res>(_value.effectiveCountry!, (value) {
      return _then(_value.copyWith(effectiveCountry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RegionalPreferencesImplCopyWith<$Res>
    implements $RegionalPreferencesCopyWith<$Res> {
  factory _$$RegionalPreferencesImplCopyWith(_$RegionalPreferencesImpl value,
          $Res Function(_$RegionalPreferencesImpl) then) =
      __$$RegionalPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'preferred_country') Country? preferredCountry,
      @JsonKey(name: 'detected_country') Country? detectedCountry,
      @JsonKey(name: 'effective_country') Country? effectiveCountry,
      @JsonKey(name: 'show_global_content') bool showGlobalContent,
      @JsonKey(name: 'auto_detect_location') bool autoDetectLocation});

  @override
  $CountryCopyWith<$Res>? get preferredCountry;
  @override
  $CountryCopyWith<$Res>? get detectedCountry;
  @override
  $CountryCopyWith<$Res>? get effectiveCountry;
}

/// @nodoc
class __$$RegionalPreferencesImplCopyWithImpl<$Res>
    extends _$RegionalPreferencesCopyWithImpl<$Res, _$RegionalPreferencesImpl>
    implements _$$RegionalPreferencesImplCopyWith<$Res> {
  __$$RegionalPreferencesImplCopyWithImpl(_$RegionalPreferencesImpl _value,
      $Res Function(_$RegionalPreferencesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? preferredCountry = freezed,
    Object? detectedCountry = freezed,
    Object? effectiveCountry = freezed,
    Object? showGlobalContent = null,
    Object? autoDetectLocation = null,
  }) {
    return _then(_$RegionalPreferencesImpl(
      preferredCountry: freezed == preferredCountry
          ? _value.preferredCountry
          : preferredCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      detectedCountry: freezed == detectedCountry
          ? _value.detectedCountry
          : detectedCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      effectiveCountry: freezed == effectiveCountry
          ? _value.effectiveCountry
          : effectiveCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      showGlobalContent: null == showGlobalContent
          ? _value.showGlobalContent
          : showGlobalContent // ignore: cast_nullable_to_non_nullable
              as bool,
      autoDetectLocation: null == autoDetectLocation
          ? _value.autoDetectLocation
          : autoDetectLocation // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegionalPreferencesImpl implements _RegionalPreferences {
  const _$RegionalPreferencesImpl(
      {@JsonKey(name: 'preferred_country') this.preferredCountry,
      @JsonKey(name: 'detected_country') this.detectedCountry,
      @JsonKey(name: 'effective_country') this.effectiveCountry,
      @JsonKey(name: 'show_global_content') this.showGlobalContent = true,
      @JsonKey(name: 'auto_detect_location') this.autoDetectLocation = true});

  factory _$RegionalPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegionalPreferencesImplFromJson(json);

  @override
  @JsonKey(name: 'preferred_country')
  final Country? preferredCountry;
  @override
  @JsonKey(name: 'detected_country')
  final Country? detectedCountry;
  @override
  @JsonKey(name: 'effective_country')
  final Country? effectiveCountry;
  @override
  @JsonKey(name: 'show_global_content')
  final bool showGlobalContent;
  @override
  @JsonKey(name: 'auto_detect_location')
  final bool autoDetectLocation;

  @override
  String toString() {
    return 'RegionalPreferences(preferredCountry: $preferredCountry, detectedCountry: $detectedCountry, effectiveCountry: $effectiveCountry, showGlobalContent: $showGlobalContent, autoDetectLocation: $autoDetectLocation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegionalPreferencesImpl &&
            (identical(other.preferredCountry, preferredCountry) ||
                other.preferredCountry == preferredCountry) &&
            (identical(other.detectedCountry, detectedCountry) ||
                other.detectedCountry == detectedCountry) &&
            (identical(other.effectiveCountry, effectiveCountry) ||
                other.effectiveCountry == effectiveCountry) &&
            (identical(other.showGlobalContent, showGlobalContent) ||
                other.showGlobalContent == showGlobalContent) &&
            (identical(other.autoDetectLocation, autoDetectLocation) ||
                other.autoDetectLocation == autoDetectLocation));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, preferredCountry,
      detectedCountry, effectiveCountry, showGlobalContent, autoDetectLocation);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegionalPreferencesImplCopyWith<_$RegionalPreferencesImpl> get copyWith =>
      __$$RegionalPreferencesImplCopyWithImpl<_$RegionalPreferencesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegionalPreferencesImplToJson(
      this,
    );
  }
}

abstract class _RegionalPreferences implements RegionalPreferences {
  const factory _RegionalPreferences(
      {@JsonKey(name: 'preferred_country') final Country? preferredCountry,
      @JsonKey(name: 'detected_country') final Country? detectedCountry,
      @JsonKey(name: 'effective_country') final Country? effectiveCountry,
      @JsonKey(name: 'show_global_content') final bool showGlobalContent,
      @JsonKey(name: 'auto_detect_location')
      final bool autoDetectLocation}) = _$RegionalPreferencesImpl;

  factory _RegionalPreferences.fromJson(Map<String, dynamic> json) =
      _$RegionalPreferencesImpl.fromJson;

  @override
  @JsonKey(name: 'preferred_country')
  Country? get preferredCountry;
  @override
  @JsonKey(name: 'detected_country')
  Country? get detectedCountry;
  @override
  @JsonKey(name: 'effective_country')
  Country? get effectiveCountry;
  @override
  @JsonKey(name: 'show_global_content')
  bool get showGlobalContent;
  @override
  @JsonKey(name: 'auto_detect_location')
  bool get autoDetectLocation;
  @override
  @JsonKey(ignore: true)
  _$$RegionalPreferencesImplCopyWith<_$RegionalPreferencesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RegionalStats _$RegionalStatsFromJson(Map<String, dynamic> json) {
  return _RegionalStats.fromJson(json);
}

/// @nodoc
mixin _$RegionalStats {
  String? get userCountry => throw _privateConstructorUsedError;
  int get totalPosts => throw _privateConstructorUsedError;
  int get globalPosts => throw _privateConstructorUsedError;
  int get regionalPosts => throw _privateConstructorUsedError;
  int get availableCountries => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RegionalStatsCopyWith<RegionalStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegionalStatsCopyWith<$Res> {
  factory $RegionalStatsCopyWith(
          RegionalStats value, $Res Function(RegionalStats) then) =
      _$RegionalStatsCopyWithImpl<$Res, RegionalStats>;
  @useResult
  $Res call(
      {String? userCountry,
      int totalPosts,
      int globalPosts,
      int regionalPosts,
      int availableCountries});
}

/// @nodoc
class _$RegionalStatsCopyWithImpl<$Res, $Val extends RegionalStats>
    implements $RegionalStatsCopyWith<$Res> {
  _$RegionalStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userCountry = freezed,
    Object? totalPosts = null,
    Object? globalPosts = null,
    Object? regionalPosts = null,
    Object? availableCountries = null,
  }) {
    return _then(_value.copyWith(
      userCountry: freezed == userCountry
          ? _value.userCountry
          : userCountry // ignore: cast_nullable_to_non_nullable
              as String?,
      totalPosts: null == totalPosts
          ? _value.totalPosts
          : totalPosts // ignore: cast_nullable_to_non_nullable
              as int,
      globalPosts: null == globalPosts
          ? _value.globalPosts
          : globalPosts // ignore: cast_nullable_to_non_nullable
              as int,
      regionalPosts: null == regionalPosts
          ? _value.regionalPosts
          : regionalPosts // ignore: cast_nullable_to_non_nullable
              as int,
      availableCountries: null == availableCountries
          ? _value.availableCountries
          : availableCountries // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegionalStatsImplCopyWith<$Res>
    implements $RegionalStatsCopyWith<$Res> {
  factory _$$RegionalStatsImplCopyWith(
          _$RegionalStatsImpl value, $Res Function(_$RegionalStatsImpl) then) =
      __$$RegionalStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? userCountry,
      int totalPosts,
      int globalPosts,
      int regionalPosts,
      int availableCountries});
}

/// @nodoc
class __$$RegionalStatsImplCopyWithImpl<$Res>
    extends _$RegionalStatsCopyWithImpl<$Res, _$RegionalStatsImpl>
    implements _$$RegionalStatsImplCopyWith<$Res> {
  __$$RegionalStatsImplCopyWithImpl(
      _$RegionalStatsImpl _value, $Res Function(_$RegionalStatsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userCountry = freezed,
    Object? totalPosts = null,
    Object? globalPosts = null,
    Object? regionalPosts = null,
    Object? availableCountries = null,
  }) {
    return _then(_$RegionalStatsImpl(
      userCountry: freezed == userCountry
          ? _value.userCountry
          : userCountry // ignore: cast_nullable_to_non_nullable
              as String?,
      totalPosts: null == totalPosts
          ? _value.totalPosts
          : totalPosts // ignore: cast_nullable_to_non_nullable
              as int,
      globalPosts: null == globalPosts
          ? _value.globalPosts
          : globalPosts // ignore: cast_nullable_to_non_nullable
              as int,
      regionalPosts: null == regionalPosts
          ? _value.regionalPosts
          : regionalPosts // ignore: cast_nullable_to_non_nullable
              as int,
      availableCountries: null == availableCountries
          ? _value.availableCountries
          : availableCountries // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RegionalStatsImpl implements _RegionalStats {
  const _$RegionalStatsImpl(
      {this.userCountry,
      this.totalPosts = 0,
      this.globalPosts = 0,
      this.regionalPosts = 0,
      this.availableCountries = 0});

  factory _$RegionalStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$RegionalStatsImplFromJson(json);

  @override
  final String? userCountry;
  @override
  @JsonKey()
  final int totalPosts;
  @override
  @JsonKey()
  final int globalPosts;
  @override
  @JsonKey()
  final int regionalPosts;
  @override
  @JsonKey()
  final int availableCountries;

  @override
  String toString() {
    return 'RegionalStats(userCountry: $userCountry, totalPosts: $totalPosts, globalPosts: $globalPosts, regionalPosts: $regionalPosts, availableCountries: $availableCountries)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegionalStatsImpl &&
            (identical(other.userCountry, userCountry) ||
                other.userCountry == userCountry) &&
            (identical(other.totalPosts, totalPosts) ||
                other.totalPosts == totalPosts) &&
            (identical(other.globalPosts, globalPosts) ||
                other.globalPosts == globalPosts) &&
            (identical(other.regionalPosts, regionalPosts) ||
                other.regionalPosts == regionalPosts) &&
            (identical(other.availableCountries, availableCountries) ||
                other.availableCountries == availableCountries));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userCountry, totalPosts,
      globalPosts, regionalPosts, availableCountries);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RegionalStatsImplCopyWith<_$RegionalStatsImpl> get copyWith =>
      __$$RegionalStatsImplCopyWithImpl<_$RegionalStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RegionalStatsImplToJson(
      this,
    );
  }
}

abstract class _RegionalStats implements RegionalStats {
  const factory _RegionalStats(
      {final String? userCountry,
      final int totalPosts,
      final int globalPosts,
      final int regionalPosts,
      final int availableCountries}) = _$RegionalStatsImpl;

  factory _RegionalStats.fromJson(Map<String, dynamic> json) =
      _$RegionalStatsImpl.fromJson;

  @override
  String? get userCountry;
  @override
  int get totalPosts;
  @override
  int get globalPosts;
  @override
  int get regionalPosts;
  @override
  int get availableCountries;
  @override
  @JsonKey(ignore: true)
  _$$RegionalStatsImplCopyWith<_$RegionalStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LocationHistory _$LocationHistoryFromJson(Map<String, dynamic> json) {
  return _LocationHistory.fromJson(json);
}

/// @nodoc
mixin _$LocationHistory {
  int get id => throw _privateConstructorUsedError;
  Country? get country => throw _privateConstructorUsedError;
  String get ipAddress => throw _privateConstructorUsedError;
  String get detectionMethod => throw _privateConstructorUsedError;
  double get confidenceScore => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LocationHistoryCopyWith<LocationHistory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationHistoryCopyWith<$Res> {
  factory $LocationHistoryCopyWith(
          LocationHistory value, $Res Function(LocationHistory) then) =
      _$LocationHistoryCopyWithImpl<$Res, LocationHistory>;
  @useResult
  $Res call(
      {int id,
      Country? country,
      String ipAddress,
      String detectionMethod,
      double confidenceScore,
      DateTime createdAt});

  $CountryCopyWith<$Res>? get country;
}

/// @nodoc
class _$LocationHistoryCopyWithImpl<$Res, $Val extends LocationHistory>
    implements $LocationHistoryCopyWith<$Res> {
  _$LocationHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? country = freezed,
    Object? ipAddress = null,
    Object? detectionMethod = null,
    Object? confidenceScore = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      ipAddress: null == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String,
      detectionMethod: null == detectionMethod
          ? _value.detectionMethod
          : detectionMethod // ignore: cast_nullable_to_non_nullable
              as String,
      confidenceScore: null == confidenceScore
          ? _value.confidenceScore
          : confidenceScore // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CountryCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LocationHistoryImplCopyWith<$Res>
    implements $LocationHistoryCopyWith<$Res> {
  factory _$$LocationHistoryImplCopyWith(_$LocationHistoryImpl value,
          $Res Function(_$LocationHistoryImpl) then) =
      __$$LocationHistoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      Country? country,
      String ipAddress,
      String detectionMethod,
      double confidenceScore,
      DateTime createdAt});

  @override
  $CountryCopyWith<$Res>? get country;
}

/// @nodoc
class __$$LocationHistoryImplCopyWithImpl<$Res>
    extends _$LocationHistoryCopyWithImpl<$Res, _$LocationHistoryImpl>
    implements _$$LocationHistoryImplCopyWith<$Res> {
  __$$LocationHistoryImplCopyWithImpl(
      _$LocationHistoryImpl _value, $Res Function(_$LocationHistoryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? country = freezed,
    Object? ipAddress = null,
    Object? detectionMethod = null,
    Object? confidenceScore = null,
    Object? createdAt = null,
  }) {
    return _then(_$LocationHistoryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as Country?,
      ipAddress: null == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String,
      detectionMethod: null == detectionMethod
          ? _value.detectionMethod
          : detectionMethod // ignore: cast_nullable_to_non_nullable
              as String,
      confidenceScore: null == confidenceScore
          ? _value.confidenceScore
          : confidenceScore // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationHistoryImpl implements _LocationHistory {
  const _$LocationHistoryImpl(
      {required this.id,
      this.country,
      required this.ipAddress,
      required this.detectionMethod,
      this.confidenceScore = 0.0,
      required this.createdAt});

  factory _$LocationHistoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationHistoryImplFromJson(json);

  @override
  final int id;
  @override
  final Country? country;
  @override
  final String ipAddress;
  @override
  final String detectionMethod;
  @override
  @JsonKey()
  final double confidenceScore;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'LocationHistory(id: $id, country: $country, ipAddress: $ipAddress, detectionMethod: $detectionMethod, confidenceScore: $confidenceScore, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationHistoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.detectionMethod, detectionMethod) ||
                other.detectionMethod == detectionMethod) &&
            (identical(other.confidenceScore, confidenceScore) ||
                other.confidenceScore == confidenceScore) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, country, ipAddress,
      detectionMethod, confidenceScore, createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationHistoryImplCopyWith<_$LocationHistoryImpl> get copyWith =>
      __$$LocationHistoryImplCopyWithImpl<_$LocationHistoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationHistoryImplToJson(
      this,
    );
  }
}

abstract class _LocationHistory implements LocationHistory {
  const factory _LocationHistory(
      {required final int id,
      final Country? country,
      required final String ipAddress,
      required final String detectionMethod,
      final double confidenceScore,
      required final DateTime createdAt}) = _$LocationHistoryImpl;

  factory _LocationHistory.fromJson(Map<String, dynamic> json) =
      _$LocationHistoryImpl.fromJson;

  @override
  int get id;
  @override
  Country? get country;
  @override
  String get ipAddress;
  @override
  String get detectionMethod;
  @override
  double get confidenceScore;
  @override
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$LocationHistoryImplCopyWith<_$LocationHistoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
