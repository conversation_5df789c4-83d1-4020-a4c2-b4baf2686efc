import os
import random
from datetime import datetime, timedelta
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.core.files.base import ContentFile

# Import all models
from blog.models import Category, Post, Comment, PostMedia
from accounts.models import CustomUser
from regional.models import Country, Region
from gamification.models import Badge, UserBadge, PointTransaction, PayPalReward, UserPayPalReward
from monetization.models import VirtualItem, PremiumSubscription, UserReferralCode, ReferralProgram
from social.models import Follow, UserProfile
from advertising.models import AdSettings, AdNetwork, AdPlacement, SponsoredPost, RewardedAd
from wallet.models import UserWallet, WalletTransaction, WalletSettings
from blockchain.models import BlockchainNetwork, SmartContract, UserWalletAddress, BlockchainTransaction, TokenBalance, NFTAsset
from analytics.models import ReadingSession, ContentAnalytics
from interactive.models import Interactive<PERSON>lock, <PERSON>, PollOption, PollVote, Quiz, QuizQuestion, QuizAnswer, QuizAttempt  # , CodePlayground
from voice_features.models import VoiceComment, VoiceCommentLike, AIWritingSession, TextToSpeechRequest, SpeechToTextSession
from payments.models import PayPalAccount, PaymentTransaction, PaymentSettings, UserPayPalProfile

User = get_user_model()

class Command(BaseCommand):
    help = 'Reset database and populate with comprehensive test data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to reset all data',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This will DELETE ALL existing data and create new test data.\n'
                    'Use --confirm to proceed.'
                )
            )
            return

        self.stdout.write(self.style.SUCCESS('🚀 Starting comprehensive test data population...'))
        
        with transaction.atomic():
            # Step 1: Clear existing data
            self.clear_existing_data()

            # Step 2: Create test data in proper order
            self.create_countries_and_regions()
            self.create_categories()
            self.create_users()
            self.create_user_profiles()
            self.create_gamification_data()
            self.create_monetization_data()
            self.create_referral_system()
            self.create_blockchain_data()
            self.create_advertising_data()
            self.create_payment_system()
            self.create_posts()
            self.create_analytics_data()
            self.create_interactive_content()
            self.create_comments()
            self.create_voice_features()
            self.create_social_interactions()
            self.create_wallet_data()
            
        self.stdout.write(self.style.SUCCESS('✅ Test data population completed successfully!'))

    def clear_existing_data(self):
        """Clear all existing data safely"""
        self.stdout.write('🗑️  Clearing existing data...')
        
        # Clear in reverse dependency order
        models_to_clear = [
            # Voice and AI features
            SpeechToTextSession, TextToSpeechRequest, AIWritingSession, VoiceCommentLike, VoiceComment,
            # Interactive content
            QuizAttempt, QuizAnswer, QuizQuestion, Quiz, PollVote, PollOption, Poll, InteractiveBlock,  # CodePlayground,
            # Analytics
            ReadingSession, ContentAnalytics,
            # Payments
            UserPayPalProfile, PaymentTransaction, PayPalAccount, PaymentSettings,
            # Blockchain
            NFTAsset, TokenBalance, BlockchainTransaction, UserWalletAddress, SmartContract, BlockchainNetwork,
            # Wallet and transactions
            WalletTransaction, UserWallet, WalletSettings,
            # Social features
            UserProfile, Follow,
            # Blog content
            Comment, PostMedia, Post,
            # Monetization and referrals
            ReferralProgram, UserReferralCode, UserPayPalReward, PayPalReward, PremiumSubscription, VirtualItem,
            # Gamification
            PointTransaction, UserBadge, Badge,
            # Advertising
            SponsoredPost, RewardedAd, AdPlacement, AdNetwork, AdSettings,
            # Users and regional
            User,
            Category, Region, Country,
        ]
        
        for model in models_to_clear:
            try:
                count = model.objects.count()
                model.objects.all().delete()
                self.stdout.write(f'  Cleared {count} {model.__name__} records')
            except Exception as e:
                self.stdout.write(f'  Warning: Could not clear {model.__name__}: {e}')

    def create_countries_and_regions(self):
        """Create sample countries and regions"""
        self.stdout.write('🌍 Creating countries and regions...')
        
        # Create regions
        regions_data = [
            {'name': 'North America', 'code': 'NA'},
            {'name': 'Europe', 'code': 'EU'},
            {'name': 'Asia Pacific', 'code': 'APAC'},
            {'name': 'Global', 'code': 'GLOBAL'},
        ]
        
        regions = {}
        for region_data in regions_data:
            region = Region.objects.create(**region_data)
            regions[region.code] = region
            
        # Create countries
        countries_data = [
            {'name': 'United States', 'code': 'US', 'code_3': 'USA', 'region': regions['NA']},
            {'name': 'Canada', 'code': 'CA', 'code_3': 'CAN', 'region': regions['NA']},
            {'name': 'United Kingdom', 'code': 'GB', 'code_3': 'GBR', 'region': regions['EU']},
            {'name': 'Germany', 'code': 'DE', 'code_3': 'DEU', 'region': regions['EU']},
            {'name': 'Japan', 'code': 'JP', 'code_3': 'JPN', 'region': regions['APAC']},
            {'name': 'Australia', 'code': 'AU', 'code_3': 'AUS', 'region': regions['APAC']},
        ]
        
        self.countries = {}
        for country_data in countries_data:
            country = Country.objects.create(**country_data)
            self.countries[country.code] = country
            
        self.stdout.write(f'  Created {len(regions)} regions and {len(self.countries)} countries')

    def create_categories(self):
        """Create blog categories"""
        self.stdout.write('📂 Creating categories...')
        
        categories_data = [
            {'name': 'Technology', 'slug': 'technology', 'description': 'Latest tech trends and innovations'},
            {'name': 'Design', 'slug': 'design', 'description': 'UI/UX design and creative inspiration'},
            {'name': 'Business', 'slug': 'business', 'description': 'Business strategies and entrepreneurship'},
            {'name': 'Lifestyle', 'slug': 'lifestyle', 'description': 'Health, wellness, and lifestyle tips'},
            {'name': 'Education', 'slug': 'education', 'description': 'Learning resources and educational content'},
            {'name': 'Entertainment', 'slug': 'entertainment', 'description': 'Movies, games, and entertainment news'},
            {'name': 'Travel', 'slug': 'travel', 'description': 'Travel guides and destination reviews'},
        ]
        
        self.categories = []
        for cat_data in categories_data:
            category = Category.objects.create(**cat_data)
            self.categories.append(category)
            
        self.stdout.write(f'  Created {len(self.categories)} categories')

    def create_users(self):
        """Create test users with different roles"""
        self.stdout.write('👥 Creating users...')

        users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True,
                'is_verified': True,
                'role': 'admin',
            },
            {
                'username': 'content_creator1',
                'email': '<EMAIL>',
                'password': 'user123',
                'first_name': 'Alice',
                'last_name': 'Writer',
                'is_staff': False,
                'is_superuser': False,
                'is_verified': True,
                'role': 'content_creator',
            },
            {
                'username': 'content_creator2',
                'email': '<EMAIL>',
                'password': 'user123',
                'first_name': 'Bob',
                'last_name': 'Blogger',
                'is_staff': False,
                'is_superuser': False,
                'is_verified': True,
                'role': 'content_creator',
            },
            {
                'username': 'john_doe',
                'email': '<EMAIL>',
                'password': 'user123',
                'first_name': 'John',
                'last_name': 'Doe',
                'is_staff': False,
                'is_superuser': False,
                'is_verified': True,
                'role': 'regular_user',
            },
            {
                'username': 'jane_smith',
                'email': '<EMAIL>',
                'password': 'user123',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'is_staff': False,
                'is_superuser': False,
                'is_verified': False,
                'role': 'regular_user',
            },
            {
                'username': 'mike_reader',
                'email': '<EMAIL>',
                'password': 'user123',
                'first_name': 'Mike',
                'last_name': 'Reader',
                'is_staff': False,
                'is_superuser': False,
                'is_verified': True,
                'role': 'regular_user',
            },
        ]

        # Import Group for role assignment
        from django.contrib.auth.models import Group

        # Ensure groups exist (create if they don't)
        content_creators_group, _ = Group.objects.get_or_create(name='Content Creators')
        regular_users_group, _ = Group.objects.get_or_create(name='Regular Users')

        self.users = []
        for user_data in users_data:
            password = user_data.pop('password')
            is_verified = user_data.pop('is_verified')
            role = user_data.pop('role', 'regular_user')

            user = User.objects.create_user(**user_data)
            user.set_password(password)

            # Set additional user fields
            user.is_email_verified = is_verified
            user.bio = f'Test bio for {user.first_name} {user.last_name}'
            user.location = random.choice(['New York', 'London', 'Tokyo', 'Sydney'])
            user.website = f'https://{user.username}.example.com'
            user.preferred_country = random.choice(list(self.countries.values()))
            user.save()

            # Assign role based on user_data
            if role == 'content_creator' and not (user.is_staff or user.is_superuser):
                user.groups.add(content_creators_group)
                self.stdout.write(f'  ✍️  Assigned {user.username} to Content Creators group')
            elif role == 'regular_user' and not (user.is_staff or user.is_superuser):
                user.groups.add(regular_users_group)
                self.stdout.write(f'  👤 Assigned {user.username} to Regular Users group')
            elif role == 'admin':
                self.stdout.write(f'  👑 {user.username} is an admin user')

            self.users.append(user)

        self.stdout.write(f'  Created {len(self.users)} users with roles')

    def create_user_profiles(self):
        """Create user profiles for social features"""
        self.stdout.write('👤 Creating user profiles...')

        profiles_created = 0
        for user in self.users:
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'bio': f'I am {user.first_name}, passionate about technology and innovation. Love reading and sharing insights on the latest trends.',
                    'location': random.choice(['New York, USA', 'London, UK', 'Tokyo, Japan', 'Berlin, Germany', 'Sydney, Australia']),
                    'website': f'https://{user.username}.dev' if random.choice([True, False]) else '',
                    'twitter_handle': f'@{user.username}' if random.choice([True, False]) else '',
                    'github_username': user.username if random.choice([True, False]) else '',
                    'is_public': random.choice([True, True, True, False]),  # 75% public
                    'show_email': random.choice([True, False]),
                    'show_reading_activity': random.choice([True, True, False]),  # 66% show activity
                    'is_verified': user.is_staff or random.choice([True, False, False]),  # Staff always verified, others 33%
                }
            )
            if created:
                profiles_created += 1

        self.stdout.write(f'  Created {profiles_created} user profiles')

    def create_gamification_data(self):
        """Create gamification badges and transactions"""
        self.stdout.write('🎮 Creating gamification data...')

        # Create badges
        badges_data = [
            {'name': 'First Post', 'description': 'Published your first post', 'icon': 'first_post.png', 'points_reward': 50},
            {'name': 'Social Butterfly', 'description': 'Made 10 comments', 'icon': 'social.png', 'points_reward': 100},
            {'name': 'Popular Author', 'description': 'Got 100 likes on posts', 'icon': 'popular.png', 'points_reward': 200},
            {'name': 'Daily Reader', 'description': 'Read posts for 7 consecutive days', 'icon': 'reader.png', 'points_reward': 150},
            {'name': 'Community Helper', 'description': 'Helped other users', 'icon': 'helper.png', 'points_reward': 300},
        ]

        self.badges = []
        for badge_data in badges_data:
            badge = Badge.objects.create(**badge_data)
            self.badges.append(badge)

        # Assign badges and create point transactions for users
        for user in self.users:
            # Assign random badges
            user_badges = random.sample(self.badges, random.randint(1, 3))
            total_points = 0

            for badge in user_badges:
                UserBadge.objects.create(
                    user=user,
                    badge=badge,
                    earned_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )

                # Create point transaction for badge
                PointTransaction.objects.create(
                    user=user,
                    transaction_type='earned',
                    points=badge.points_reward,
                    description=f'Badge earned: {badge.name}',
                    created_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )
                total_points += badge.points_reward

            # Add some random point transactions
            for _ in range(random.randint(2, 5)):
                transaction_type = random.choice(['earned', 'spent'])
                points = random.randint(10, 100)
                if transaction_type == 'spent':
                    points = -points

                PointTransaction.objects.create(
                    user=user,
                    transaction_type=transaction_type,
                    points=points,
                    description=f'Test {transaction_type} transaction',
                    created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                )
                total_points += points

            # Update user with total points (if field exists)
            if hasattr(user, 'total_points'):
                user.total_points = max(0, total_points)
                user.save()

        self.stdout.write(f'  Created {len(self.badges)} badges and point transactions')

    def create_monetization_data(self):
        """Create monetization items and subscriptions"""
        self.stdout.write('💰 Creating monetization data...')

        # Create virtual items
        virtual_items_data = [
            {'name': 'Reading Boost', 'description': 'Double reading points for 24 hours', 'price': Decimal('1.99'), 'category': 'temporary'},
            {'name': 'Comment Highlight', 'description': 'Highlight your comments', 'price': Decimal('0.99'), 'category': 'cosmetic'},
            {'name': 'Profile Badge', 'description': 'Special profile badge', 'price': Decimal('2.99'), 'category': 'cosmetic'},
            {'name': 'Ad-Free Experience', 'description': 'Remove ads for 7 days', 'price': Decimal('4.99'), 'category': 'functional'},
        ]

        self.virtual_items = []
        for item_data in virtual_items_data:
            item = VirtualItem.objects.create(**item_data)
            self.virtual_items.append(item)

        # Create premium subscriptions
        subscription_data = [
            {
                'user': self.users[0],
                'plan': 'monthly',
                'start_date': timezone.now() - timedelta(days=15),
                'end_date': timezone.now() + timedelta(days=15),
                'status': 'active',
                'total_paid': Decimal('9.99')
            },
        ]

        self.subscriptions = []
        for sub_data in subscription_data:
            subscription = PremiumSubscription.objects.create(**sub_data)
            self.subscriptions.append(subscription)

        # Create PayPal rewards
        rewards_data = [
            {'name': 'PayPal Cash $5', 'description': '$5 PayPal cash reward', 'reward_type': 'milestone', 'points_required': 1000, 'usd_amount': Decimal('5.00')},
            {'name': 'PayPal Cash $10', 'description': '$10 PayPal cash reward', 'reward_type': 'milestone', 'points_required': 2000, 'usd_amount': Decimal('10.00')},
            {'name': 'Level Achievement Bonus', 'description': 'Bonus for reaching new level', 'reward_type': 'level', 'points_required': 500, 'usd_amount': Decimal('2.50')},
        ]

        self.rewards = []
        for reward_data in rewards_data:
            reward = PayPalReward.objects.create(**reward_data)
            self.rewards.append(reward)

        # Create some user rewards (redeemed rewards)
        for user in self.users[:2]:  # Only for first 2 users
            reward = random.choice(self.rewards)
            UserPayPalReward.objects.create(
                user=user,
                reward=reward,
                paypal_email=user.email,
                usd_amount=reward.usd_amount,
                status='paid',
                user_points_at_claim=random.randint(1000, 5000),
                user_level_at_claim=random.randint(1, 5)
            )

        self.stdout.write(f'  Created {len(self.virtual_items)} virtual items, {len(self.subscriptions)} subscriptions, and {len(self.rewards)} PayPal rewards')

    def create_referral_system(self):
        """Create referral codes and relationships"""
        self.stdout.write('🔗 Creating referral system data...')

        # Create referral codes for each user
        referral_codes_created = 0
        for user in self.users:
            # Generate unique referral code
            code = f"{user.username.upper()[:4]}{random.randint(1000, 9999)}"
            referral_code, created = UserReferralCode.objects.get_or_create(
                user=user,
                defaults={
                    'code': code,
                    'usage_count': random.randint(0, 5)
                }
            )
            if created:
                referral_codes_created += 1

        # Create referral relationships
        referral_relationships_created = 0
        for i, user in enumerate(self.users[1:], 1):  # Skip first user as they can't be referred
            # 70% chance of being referred by someone
            if random.choice([True, True, True, False, False, False, False]):
                referrer = random.choice(self.users[:i])  # Can only be referred by earlier users
                referrer_code = UserReferralCode.objects.get(user=referrer)

                # Create comprehensive referral relationship
                referral, created = ReferralProgram.objects.get_or_create(
                    referrer=referrer,
                    referee=user,
                    defaults={
                        'referral_code_used': referrer_code.code,
                        'total_revenue_generated': Decimal(str(random.uniform(0, 100))),
                        'referee_reached_level_5': random.choice([True, False]),
                        'referee_went_premium': random.choice([True, False]),
                        'referee_made_purchase': random.choice([True, False]),
                        'join_reward_given': random.choice([True, False]),
                        'level_5_reward_given': random.choice([True, False]),
                        'premium_reward_given': random.choice([True, False]),
                    }
                )
                if created:
                    referral_relationships_created += 1
                    # Update referrer's code usage count
                    referrer_code.usage_count += 1
                    referrer_code.save()

        # Create referral tracking events and rewards
        referral_events_created = 0
        referral_programs = ReferralProgram.objects.all()

        for referral in referral_programs:
            # Create tracking events for each referral
            events = [
                {'event_type': 'signup', 'points_awarded': 100, 'transaction_type': 'bonus'},
                {'event_type': 'first_post', 'points_awarded': 50, 'transaction_type': 'bonus'},
                {'event_type': 'first_purchase', 'points_awarded': 200, 'transaction_type': 'bonus'},
                {'event_type': 'monthly_active', 'points_awarded': 25, 'transaction_type': 'bonus'},
            ]

            # Each referral gets 2-4 tracking events
            selected_events = random.sample(events, random.randint(2, 4))

            for event in selected_events:
                # Create point transaction for referrer
                PointTransaction.objects.create(
                    user=referral.referrer,
                    transaction_type=event['transaction_type'],
                    points=event['points_awarded'],
                    description=f"Referral reward: {event['event_type']} by {referral.referee.username}",
                    related_object_type='referral',
                    related_object_id=referral.id,
                )
                referral_events_created += 1

        # Create referral leaderboard data by updating user stats
        referral_leaders_updated = 0
        for user in self.users:
            # Count referrals made by this user
            referrals_made = ReferralProgram.objects.filter(referrer=user).count()
            successful_referrals = ReferralProgram.objects.filter(
                referrer=user,
                referee_made_purchase=True
            ).count()

            if referrals_made > 0:
                # Update user profile with referral stats (if profile exists)
                try:
                    profile = user.userprofile
                    # Add referral stats to bio if not already there
                    if 'referrals' not in profile.bio.lower():
                        profile.bio += f" | 🎯 {referrals_made} referrals made, {successful_referrals} successful"
                        profile.save()
                        referral_leaders_updated += 1
                except:
                    pass  # Profile might not exist

        self.stdout.write(f'  Created {referral_codes_created} referral codes and {referral_relationships_created} referral relationships')
        self.stdout.write(f'  Created {referral_events_created} referral tracking events and updated {referral_leaders_updated} referral leader profiles')

    def create_blockchain_data(self):
        """Create blockchain networks, wallets, and transactions"""
        self.stdout.write('⛓️ Creating blockchain data...')

        # Create blockchain networks
        networks_data = [
            {'name': 'ethereum', 'chain_id': 1, 'rpc_url': 'https://mainnet.infura.io/v3/...', 'explorer_url': 'https://etherscan.io', 'native_token': 'ETH'},
            {'name': 'polygon', 'chain_id': 137, 'rpc_url': 'https://polygon-rpc.com/', 'explorer_url': 'https://polygonscan.com', 'native_token': 'MATIC'},
            {'name': 'ethereum_testnet', 'chain_id': 11155111, 'rpc_url': 'https://sepolia.infura.io/v3/...', 'explorer_url': 'https://sepolia.etherscan.io', 'native_token': 'ETH'},
        ]

        networks_created = 0
        self.blockchain_networks = []
        for network_data in networks_data:
            network, created = BlockchainNetwork.objects.get_or_create(
                name=network_data['name'],
                defaults=network_data
            )
            if created:
                networks_created += 1
            self.blockchain_networks.append(network)

        # Create smart contracts
        contracts_data = [
            {'network': self.blockchain_networks[0], 'name': 'Trendy Token', 'address': '******************************************', 'contract_type': 'token', 'abi': {}},
            {'network': self.blockchain_networks[0], 'name': 'Trendy NFT Collection', 'address': '******************************************', 'contract_type': 'nft', 'abi': {}},
            {'network': self.blockchain_networks[1], 'name': 'Trendy Rewards', 'address': '******************************************', 'contract_type': 'token', 'abi': {}},
        ]

        contracts_created = 0
        self.smart_contracts = []
        for contract_data in contracts_data:
            contract, created = SmartContract.objects.get_or_create(
                address=contract_data['address'],
                defaults=contract_data
            )
            if created:
                contracts_created += 1
            self.smart_contracts.append(contract)

        # Create user wallet addresses
        wallet_addresses_created = 0
        for user in self.users:
            for network in self.blockchain_networks:
                # 60% chance of having a wallet on each network
                if random.choice([True, True, True, False, False]):
                    address = f"0x{''.join(random.choices('0123456789abcdef', k=40))}"
                    wallet_address, created = UserWalletAddress.objects.get_or_create(
                        user=user,
                        network=network,
                        defaults={
                            'address': address,
                            'is_primary': network == self.blockchain_networks[0],  # Ethereum as primary
                        }
                    )
                    if created:
                        wallet_addresses_created += 1

        # Create blockchain transactions
        transactions_created = 0
        user_wallets = UserWalletAddress.objects.all()

        for wallet in user_wallets:
            # Create 2-5 transactions per wallet
            transaction_count = random.randint(2, 5)

            for _ in range(transaction_count):
                transaction_type = random.choice(['token_reward', 'token_transfer', 'nft_mint', 'marketplace_buy'])

                BlockchainTransaction.objects.create(
                    user=wallet.user,
                    network=wallet.network,
                    blockchain_hash=f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
                    transaction_type=transaction_type,
                    from_address=wallet.address if transaction_type in ['token_transfer', 'marketplace_buy'] else f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    to_address=f"0x{''.join(random.choices('0123456789abcdef', k=40))}" if transaction_type in ['token_transfer', 'marketplace_buy'] else wallet.address,
                    amount=Decimal(str(random.uniform(0.001, 10.0))),
                    gas_limit=random.randint(21000, 200000),
                    gas_used=random.randint(21000, 200000),
                    gas_price=random.randint(10000000000, 100000000000),  # In wei
                    transaction_fee=Decimal(str(random.uniform(0.001, 0.1))),
                    block_number=random.randint(18000000, 19000000),
                    confirmations=random.randint(12, 100),
                    status='confirmed',
                    description=f"Test {transaction_type.replace('_', ' ').title()} transaction",
                )
                transactions_created += 1

        # Create token balances
        token_balances_created = 0
        token_contracts = [c for c in self.smart_contracts if c.contract_type == 'token']

        for wallet in user_wallets:
            for contract in token_contracts:
                # 70% chance of having tokens from each contract
                if random.choice([True, True, True, False, False, False, False]):
                    balance = Decimal(str(random.uniform(0, 10000)))
                    staked_balance = Decimal(str(random.uniform(0, float(balance)/2)))

                    TokenBalance.objects.create(
                        user=wallet.user,
                        network=wallet.network,
                        contract=contract,
                        balance=balance,
                        staked_balance=staked_balance,
                    )
                    token_balances_created += 1

        # Create NFT assets
        nft_assets_created = 0
        nft_contracts = [c for c in self.smart_contracts if c.contract_type == 'nft']

        for wallet in user_wallets:
            for contract in nft_contracts:
                # 40% chance of owning NFTs from each collection
                if random.choice([True, True, False, False, False]):
                    # Each user can own 1-3 NFTs from a collection
                    nft_count = random.randint(1, 3)

                    for _ in range(nft_count):
                        token_id = random.randint(1, 10000)

                        NFTAsset.objects.create(
                            user=wallet.user,
                            network=wallet.network,
                            contract=contract,
                            token_id=token_id,
                            name=f"{contract.name} #{token_id}",
                            description=f"A unique NFT from the {contract.name} collection",
                            image_url=f"https://api.trendy.com/nft/{contract.address}/{token_id}/image",
                            external_url=f"https://trendy.com/nft/{contract.address}/{token_id}",
                            rarity=random.randint(1, 5),  # 1-5 rarity levels
                            attributes={
                                'background': random.choice(['Blue', 'Red', 'Green', 'Purple', 'Gold']),
                                'eyes': random.choice(['Normal', 'Laser', 'Rainbow', 'Closed']),
                                'mouth': random.choice(['Smile', 'Frown', 'Open', 'Tongue']),
                                'rarity_score': round(random.uniform(0.1, 100.0), 2)
                            },
                        )
                        nft_assets_created += 1

        self.stdout.write(f'  Created {networks_created} networks, {contracts_created} contracts, {wallet_addresses_created} wallet addresses')
        self.stdout.write(f'  Created {transactions_created} blockchain transactions, {token_balances_created} token balances, and {nft_assets_created} NFT assets')

    def create_advertising_data(self):
        """Create advertising settings, networks, and content"""
        self.stdout.write('📺 Creating advertising data...')

        # Create ad settings
        ad_settings = AdSettings.objects.create(
            ads_enabled=True,
            max_ads_per_session=5,
            min_time_between_ads=600,  # 10 minutes in seconds
            rewarded_ads_enabled=True,
            sponsored_posts_enabled=True
        )

        # Create ad networks
        networks_data = [
            {'name': 'Google AdMob', 'network_type': 'google_admob', 'is_active': True},
            {'name': 'Facebook Audience Network', 'network_type': 'facebook_audience', 'is_active': True},
            {'name': 'Unity Ads', 'network_type': 'unity_ads', 'is_active': True},
        ]

        self.ad_networks = []
        for network_data in networks_data:
            network = AdNetwork.objects.create(**network_data)
            self.ad_networks.append(network)

        # Create ad placements
        placements_data = [
            {'name': 'Home Feed Banner', 'location': 'home_feed_top', 'placement_type': 'banner', 'is_active': True},
            {'name': 'Post Detail Interstitial', 'location': 'post_detail_bottom', 'placement_type': 'interstitial', 'is_active': True},
            {'name': 'Rewarded Video', 'location': 'rewards_page', 'placement_type': 'rewarded_video', 'is_active': True},
            {'name': 'Profile Native Ad', 'location': 'profile_page', 'placement_type': 'native', 'is_active': True},
        ]

        self.ad_placements = []
        for placement_data in placements_data:
            placement = AdPlacement.objects.create(**placement_data)
            self.ad_placements.append(placement)

        # Create rewarded ads
        rewarded_ads_data = [
            {
                'name': 'Watch & Earn',
                'description': 'Watch video to earn points',
                'points_reward': 25,
                'reward_type': 'points',
                'ad_placement': self.ad_placements[2],  # Rewarded Video placement
                'is_active': True
            },
            {
                'name': 'Survey Reward',
                'description': 'Complete survey for bonus points',
                'points_reward': 50,
                'reward_type': 'points',
                'ad_placement': self.ad_placements[2],  # Rewarded Video placement
                'is_active': True
            },
        ]

        self.rewarded_ads = []
        for ad_data in rewarded_ads_data:
            ad = RewardedAd.objects.create(**ad_data)
            self.rewarded_ads.append(ad)

        # Create sponsored content
        sponsored_content_data = [
            {
                'title': 'Discover Amazing Tech Gadgets',
                'content': 'Check out the latest tech innovations that will change your life.',
                'sponsor_name': 'TechCorp',
                'sponsor_type': 'brand',
                'target_url': 'https://example.com/tech-gadgets',
                'status': 'active',
                'end_date': timezone.now() + timedelta(days=30),
                'total_budget': Decimal('100.00'),
            },
            {
                'title': 'Learn Design Skills Online',
                'content': 'Master UI/UX design with our comprehensive online courses.',
                'sponsor_name': 'DesignAcademy',
                'sponsor_type': 'service',
                'target_url': 'https://example.com/design-courses',
                'status': 'active',
                'end_date': timezone.now() + timedelta(days=30),
                'total_budget': Decimal('150.00'),
            },
        ]

        self.sponsored_content = []
        for content_data in sponsored_content_data:
            content = SponsoredPost.objects.create(**content_data)
            self.sponsored_content.append(content)

        self.stdout.write(f'  Created ad settings, {len(self.ad_networks)} networks, {len(self.ad_placements)} placements, {len(self.rewarded_ads)} rewarded ads, and {len(self.sponsored_content)} sponsored content')

    def create_payment_system(self):
        """Create PayPal accounts, payment settings, and user profiles"""
        self.stdout.write('💰 Creating payment system data...')

        # Create PayPal account
        paypal_account, created = PayPalAccount.objects.get_or_create(
            name='Trendy App PayPal',
            defaults={
                'account_type': 'business',
                'environment': 'sandbox',
                'client_id': 'test_client_id_123456789',
                'client_secret': 'test_client_secret_987654321',
                'business_email': '<EMAIL>',
                'business_name': 'Trendy App LLC',
                'webhook_url': 'https://api.trendy.com/webhooks/paypal/',
                'is_active': True,
                'auto_accept_payments': True,
            }
        )

        # Create payment settings
        payment_settings, settings_created = PaymentSettings.objects.get_or_create(
            pk=1,
            defaults={
                'paypal_mode': 'sandbox',
                'minimum_payment': Decimal('1.00'),
                'maximum_payment': Decimal('500.00'),
                'minimum_payout': Decimal('5.00'),
                'maximum_daily_payout': Decimal('1000.00'),
                'payout_processing_fee': Decimal('0.30'),
                'auto_process_payouts': True,
                'batch_size': 50,
                'batch_frequency_hours': 24,
                'require_email_verification': True,
                'webhook_verification': True,
                'admin_email_notifications': True,
                'user_email_notifications': True,
                'payments_enabled': True,
                'payouts_enabled': True,
            }
        )

        # Create user PayPal profiles
        profiles_created = 0
        for user in self.users:
            # 60% of users have PayPal profiles
            if random.choice([True, True, True, False, False]):
                profile, created = UserPayPalProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'paypal_email': f'{user.username}@paypal.com',
                        'paypal_account_verified': random.choice([True, True, False]),  # 66% verified
                        'auto_accept_payments': True,
                        'notification_preferences': {'email_on_payment': True, 'email_on_payout': True},
                        'total_payments_received': Decimal(str(random.uniform(0, 100))),
                        'total_payments_made': Decimal(str(random.uniform(0, 50))),
                        'successful_transactions': random.randint(0, 20),
                        'failed_transactions': random.randint(0, 3),
                    }
                )
                if created:
                    profiles_created += 1

        self.stdout.write(f'  Created PayPal account, payment settings, and {profiles_created} user PayPal profiles')

    def create_posts(self):
        """Create blog posts with varied content"""
        self.stdout.write('📝 Creating blog posts...')

        posts_data = [
            {
                'title': 'The Future of Artificial Intelligence in 2024',
                'content': 'Artificial Intelligence continues to evolve rapidly, transforming industries and reshaping how we work and live. In this comprehensive guide, we explore the latest AI trends, breakthrough technologies, and their potential impact on society. From machine learning advancements to ethical AI considerations, discover what the future holds for artificial intelligence.',
                'category': 'technology',
                'regions': ['US', 'CA', 'GB'],
                'media_type': 'text',
            },
            {
                'title': 'Modern Web Design Principles for Better UX',
                'content': 'Creating exceptional user experiences requires understanding fundamental design principles. This article covers essential UX/UI concepts including responsive design, accessibility, color theory, and user psychology. Learn how to create websites that not only look great but also provide intuitive and engaging user experiences.',
                'category': 'design',
                'regions': ['US', 'GB', 'DE'],
                'media_type': 'image',
            },
            {
                'title': 'Building a Successful Startup: Lessons Learned',
                'content': 'Starting a business is challenging but rewarding. This post shares practical insights from successful entrepreneurs, covering everything from idea validation to scaling operations. Discover common pitfalls to avoid and strategies that can help turn your startup vision into reality.',
                'category': 'business',
                'regions': ['US', 'CA'],
                'media_type': 'text',
            },
            {
                'title': 'Healthy Living: Simple Daily Habits That Make a Difference',
                'content': 'Small changes in daily routines can lead to significant improvements in health and well-being. This guide presents evidence-based habits that are easy to implement and maintain. From nutrition tips to exercise routines, learn how to build a healthier lifestyle step by step.',
                'category': 'lifestyle',
                'regions': ['AU', 'GB'],
                'media_type': 'video',
            },
            {
                'title': 'Online Learning Revolution: Education in the Digital Age',
                'content': 'The education landscape has transformed dramatically with digital technologies. This article explores how online learning platforms, virtual classrooms, and AI-powered tutoring are making education more accessible and personalized than ever before.',
                'category': 'education',
                'regions': ['GLOBAL'],
                'media_type': 'text',
            },
            {
                'title': 'Top Gaming Trends to Watch This Year',
                'content': 'The gaming industry continues to innovate with new technologies and gameplay experiences. From virtual reality to cloud gaming, discover the trends that are shaping the future of interactive entertainment and what gamers can expect in the coming months.',
                'category': 'entertainment',
                'regions': ['JP', 'US'],
                'media_type': 'video',
            },
            {
                'title': 'Hidden Gems: Underrated Travel Destinations',
                'content': 'While popular tourist destinations have their charm, some of the most memorable travel experiences come from exploring lesser-known places. This guide reveals hidden gems around the world that offer unique cultures, stunning landscapes, and authentic local experiences.',
                'category': 'travel',
                'regions': ['GLOBAL'],
                'media_type': 'image',
            },
            {
                'title': 'Cybersecurity Best Practices for Remote Workers',
                'content': 'With remote work becoming the norm, cybersecurity has never been more important. This comprehensive guide covers essential security practices, tools, and strategies to protect your data and maintain privacy while working from anywhere.',
                'category': 'technology',
                'regions': ['US', 'CA', 'GB', 'DE'],
                'media_type': 'text',
            },
        ]

        self.posts = []
        category_map = {cat.slug: cat for cat in self.categories}

        for i, post_data in enumerate(posts_data):
            # Get category
            category = category_map.get(post_data['category'])
            if not category:
                continue

            # Create post
            post = Post.objects.create(
                title=post_data['title'],
                content=post_data['content'],
                category=category,
                author=random.choice(self.users),
                status='published',
                created_at=timezone.now() - timedelta(days=random.randint(1, 30)),
                updated_at=timezone.now() - timedelta(days=random.randint(0, 5)),
            )

            # Add target countries
            target_countries = []
            for region_code in post_data['regions']:
                if region_code == 'GLOBAL':
                    target_countries.extend(self.countries.values())
                    break
                elif region_code in self.countries:
                    target_countries.append(self.countries[region_code])

            if target_countries:
                post.target_countries.set(target_countries)

            # Create media item based on type
            if post_data['media_type'] == 'image':
                PostMedia.objects.create(
                    post=post,
                    media_type='image',
                    image_url=f'https://picsum.photos/800/600?random={i}',
                    caption=f'Sample image for {post.title}'
                )
            elif post_data['media_type'] == 'video':
                PostMedia.objects.create(
                    post=post,
                    media_type='video',
                    video_url=f'https://sample-videos.com/zip/10/mp4/SampleVideo_{i+1}.mp4',
                    caption=f'Sample video for {post.title}'
                )

            self.posts.append(post)

        self.stdout.write(f'  Created {len(self.posts)} blog posts with media')

    def create_analytics_data(self):
        """Create reading sessions and content analytics"""
        self.stdout.write('📊 Creating analytics data...')

        # Create content analytics for each post
        content_analytics_created = 0
        for post in self.posts:
            word_count = len(post.content.split())
            analytics, created = ContentAnalytics.objects.get_or_create(
                post=post,
                defaults={
                    'estimated_reading_time': max(60, word_count // 200 * 60),  # ~200 words per minute, in seconds
                    'complexity_score': random.uniform(10.0, 90.0),
                    'word_count': word_count,
                    'sentence_count': max(1, word_count // 15),  # ~15 words per sentence
                    'paragraph_count': max(1, word_count // 100),  # ~100 words per paragraph
                }
            )
            if created:
                content_analytics_created += 1

        # Create reading sessions
        reading_sessions_created = 0
        for post in self.posts:
            # Each post gets 3-8 reading sessions from different users
            session_count = random.randint(3, 8)
            selected_users = random.sample(self.users, min(session_count, len(self.users)))

            for user in selected_users:
                session, created = ReadingSession.objects.get_or_create(
                    user=user,
                    post=post,
                    defaults={
                        'session_duration': random.randint(30, 600),  # 30 seconds to 10 minutes
                        'progress_percentage': random.uniform(10.0, 100.0),
                        'reading_speed_wpm': random.randint(150, 300),
                        'scroll_depth': random.uniform(0.2, 1.0),
                        'is_completed': random.choice([True, True, False]),  # 66% complete
                        'device_type': random.choice(['mobile', 'tablet', 'desktop']),
                    }
                )
                if created:
                    reading_sessions_created += 1

        self.stdout.write(f'  Created {content_analytics_created} content analytics and {reading_sessions_created} reading sessions')

    def create_interactive_content(self):
        """Create polls and quizzes"""  # and code playgrounds"""
        self.stdout.write('🎮 Creating interactive content...')

        interactive_blocks_created = 0
        polls_created = 0
        quizzes_created = 0
        # code_playgrounds_created = 0

        # Add interactive content to some posts
        for post in self.posts[:5]:  # Add to first 5 posts
            # Create 1-2 interactive blocks per post, ensuring no duplicates
            available_types = ['poll', 'quiz']  # , 'code']
            block_count = random.randint(1, min(2, len(available_types)))
            selected_types = random.sample(available_types, block_count)

            for position, block_type in enumerate(selected_types):

                # Create interactive block
                block = InteractiveBlock.objects.create(
                    post=post,
                    block_type=block_type,
                    title=f'{block_type.title()} for {post.title[:30]}...',
                    description=f'Interactive {block_type} content related to this post.',
                    position=position,
                    is_active=True,
                    metadata={'created_by': 'test_data_generator'}
                )
                interactive_blocks_created += 1

                if block_type == 'poll':
                    # Create poll
                    poll = Poll.objects.create(
                        interactive_block=block,
                        question=f'What do you think about {post.title[:40]}?',
                        allow_multiple_choices=random.choice([True, False]),
                        show_results_immediately=True,
                        is_anonymous=random.choice([True, False]),
                        total_votes=random.randint(5, 50),
                        unique_voters=random.randint(3, 30)
                    )
                    polls_created += 1

                    # Create poll options
                    options = ['Strongly Agree', 'Agree', 'Neutral', 'Disagree', 'Strongly Disagree']
                    for i, option_text in enumerate(options):
                        PollOption.objects.create(
                            poll=poll,
                            text=option_text,
                            position=i,
                            vote_count=random.randint(0, 10)
                        )

                elif block_type == 'quiz':
                    # Create quiz
                    quiz = Quiz.objects.create(
                        interactive_block=block,
                        instructions='Test your knowledge with this quick quiz!',
                        time_limit=300,  # 5 minutes
                        show_correct_answers=True,
                        randomize_questions=False,
                        passing_score=70,
                        total_attempts=random.randint(10, 100),
                        average_score=random.uniform(60, 85)
                    )
                    quizzes_created += 1

                    # Create quiz questions
                    questions = [
                        'What is the main topic of this article?',
                        'Which technology is mentioned most frequently?',
                        'What year was this trend first observed?'
                    ]

                    for i, question_text in enumerate(questions):
                        question = QuizQuestion.objects.create(
                            quiz=quiz,
                            question_type='multiple_choice',
                            question_text=question_text,
                            explanation='This is the correct answer based on the article content.',
                            points=1,
                            position=i
                        )

                        # Create answer options
                        answers = ['Option A', 'Option B (Correct)', 'Option C', 'Option D']
                        for j, answer_text in enumerate(answers):
                            QuizAnswer.objects.create(
                                question=question,
                                answer_text=answer_text,
                                is_correct=(j == 1),  # Second option is correct
                                position=j
                            )

                # elif block_type == 'code':
                #     # Create code playground
                #     CodePlayground.objects.create(
                #         interactive_block=block,
                #         language=random.choice(['python', 'javascript', 'html', 'css']),
                #         initial_code='# Write your code here\nprint("Hello, World!")',
                #         expected_output='Hello, World!',
                #         instructions='Try modifying the code and see what happens!',
                #         is_editable=True,
                #         show_line_numbers=True
                #     )
                #     code_playgrounds_created += 1

        self.stdout.write(f'  Created {interactive_blocks_created} interactive blocks: {polls_created} polls, {quizzes_created} quizzes')  # , {code_playgrounds_created} code playgrounds')

    def create_comments(self):
        """Create nested comment threads"""
        self.stdout.write('💬 Creating comments...')

        comments_data = [
            "Great article! This really helped me understand the topic better.",
            "Thanks for sharing this valuable information.",
            "I have a different perspective on this. What do you think about...",
            "This is exactly what I was looking for. Very helpful!",
            "Could you elaborate more on the second point?",
            "Interesting read, but I think there's more to consider.",
            "Love the practical examples you provided.",
            "This changed my mind about the subject completely.",
            "Well researched and clearly explained.",
            "I disagree with some points, but overall good content.",
        ]

        reply_data = [
            "Thanks for your feedback!",
            "I appreciate your perspective.",
            "That's a great point to consider.",
            "You're absolutely right about that.",
            "Let me clarify that section...",
            "I'll look into that further.",
            "Good question! Here's what I think...",
            "Thanks for the kind words!",
        ]

        total_comments = 0

        # Create comments for each post
        for post in self.posts:
            num_comments = random.randint(2, 6)
            post_comments = []

            # Create top-level comments
            for _ in range(num_comments):
                comment = Comment.objects.create(
                    post=post,
                    author=random.choice(self.users),
                    content=random.choice(comments_data),
                    created_at=timezone.now() - timedelta(days=random.randint(0, 10))
                )
                post_comments.append(comment)
                total_comments += 1

                # Create replies (nested comments)
                if random.choice([True, False]):  # 50% chance of replies
                    num_replies = random.randint(1, 3)
                    for _ in range(num_replies):
                        reply = Comment.objects.create(
                            post=post,
                            parent=comment,
                            author=random.choice(self.users),
                            content=random.choice(reply_data),
                            created_at=timezone.now() - timedelta(days=random.randint(0, 5))
                        )
                        total_comments += 1

                        # Occasionally create nested replies
                        if random.choice([True, False, False]):  # 33% chance
                            nested_reply = Comment.objects.create(
                                post=post,
                                parent=reply,
                                author=random.choice(self.users),
                                content=random.choice(reply_data),
                                created_at=timezone.now() - timedelta(days=random.randint(0, 3))
                            )
                            total_comments += 1

        self.stdout.write(f'  Created {total_comments} comments with nested threads')

    def create_voice_features(self):
        """Create voice comments, AI writing sessions, and TTS/STT data"""
        self.stdout.write('🎤 Creating voice features data...')

        voice_comments_created = 0
        ai_sessions_created = 0
        tts_requests_created = 0
        stt_sessions_created = 0

        # Create voice comments for some posts
        for post in self.posts[:3]:  # Add voice comments to first 3 posts
            # Each post gets 1-2 voice comments
            comment_count = random.randint(1, 2)
            selected_users = random.sample(self.users, min(comment_count, len(self.users)))

            for user in selected_users:
                voice_comment = VoiceComment.objects.create(
                    post=post,
                    user=user,
                    audio_file=f'voice_comments/{post.id}/comment_{user.id}.mp3',  # Fake file path
                    duration_seconds=random.randint(15, 120),  # 15 seconds to 2 minutes
                    file_size=random.randint(50000, 500000),  # 50KB to 500KB
                    transcription=f'This is a voice comment by {user.first_name} about {post.title[:30]}...',
                    is_transcribed=True,
                    transcription_confidence=random.uniform(0.7, 0.95),
                    like_count=random.randint(0, 5),
                    is_active=True,
                    is_approved=True
                )
                voice_comments_created += 1

                # Create some likes for voice comments
                if random.choice([True, False]):
                    likers = random.sample([u for u in self.users if u != user], random.randint(1, 3))
                    for liker in likers:
                        VoiceCommentLike.objects.create(
                            voice_comment=voice_comment,
                            user=liker
                        )

                # Create STT session for this voice comment
                SpeechToTextSession.objects.create(
                    user=user,
                    voice_comment=voice_comment,
                    audio_duration=voice_comment.duration_seconds,
                    transcription_accuracy=voice_comment.transcription_confidence,
                    language_detected='en-US',
                    processing_time=random.uniform(1.0, 5.0),
                    success=True
                )
                stt_sessions_created += 1

        # Create AI writing sessions
        for user in self.users[:4]:  # First 4 users have AI writing sessions
            for post in random.sample(self.posts, 2):  # 2 sessions per user
                session_id = f'ai_session_{user.id}_{post.id}_{random.randint(1000, 9999)}'
                AIWritingSession.objects.create(
                    user=user,
                    post=post,
                    session_id=session_id,
                    content_before='Original draft content...',
                    content_after='Improved content with AI assistance...',
                    suggestions_count=random.randint(5, 20),
                    suggestions_accepted=random.randint(2, 15),
                    completion_requests=random.randint(1, 5),
                    improvement_requests=random.randint(1, 8),
                    session_duration=random.randint(300, 1800),  # 5-30 minutes
                    words_added=random.randint(50, 300),
                    readability_improvement=random.uniform(0.1, 0.4),
                    ended_at=timezone.now() - timedelta(minutes=random.randint(10, 1440))
                )
                ai_sessions_created += 1

        # Create TTS requests
        for post in self.posts:
            # Each post gets 1-3 TTS requests
            request_count = random.randint(1, 3)
            for _ in range(request_count):
                user = random.choice(self.users) if random.choice([True, False]) else None  # Some anonymous
                TextToSpeechRequest.objects.create(
                    user=user,
                    post=post,
                    text_length=len(post.content),
                    language=random.choice(['en-US', 'en-GB', 'es-ES', 'fr-FR']),
                    voice_settings={'speed': random.uniform(0.8, 1.2), 'pitch': random.uniform(0.9, 1.1)},
                    duration_seconds=random.randint(60, 600),  # 1-10 minutes
                    completed=random.choice([True, True, True, False])  # 75% completed
                )
                tts_requests_created += 1

        self.stdout.write(f'  Created {voice_comments_created} voice comments, {ai_sessions_created} AI sessions, {tts_requests_created} TTS requests, {stt_sessions_created} STT sessions')

    def create_social_interactions(self):
        """Create follows and likes"""
        self.stdout.write('👥 Creating social interactions...')

        # Create follow relationships
        follows_created = 0
        for user in self.users:
            # Each user follows 1-2 other users
            other_users = [u for u in self.users if u != user]
            users_to_follow = random.sample(other_users, random.randint(1, min(2, len(other_users))))

            for followed_user in users_to_follow:
                Follow.objects.get_or_create(
                    follower=user,
                    following=followed_user,
                    defaults={'created_at': timezone.now() - timedelta(days=random.randint(1, 30))}
                )
                follows_created += 1

        # Create likes for posts (using ManyToMany relationship)
        likes_created = 0
        for post in self.posts:
            # Each post gets likes from 1-3 users
            num_likes = random.randint(1, min(3, len(self.users)))
            users_who_like = random.sample(self.users, num_likes)

            post.likes.set(users_who_like)
            likes_created += len(users_who_like)

        self.stdout.write(f'  Created {follows_created} follow relationships and {likes_created} post likes')

    def create_wallet_data(self):
        """Create wallet and transaction data"""
        self.stdout.write('💳 Creating wallet data...')

        # Create wallet settings
        wallet_settings = WalletSettings.objects.create(
            minimum_withdrawal=Decimal('5.00'),
            maximum_withdrawal=Decimal('500.00'),
            withdrawal_fee_percentage=Decimal('2.5'),
            wallets_enabled=True,
            deposits_enabled=True,
            withdrawals_enabled=True,
            require_verification=True,
            daily_withdrawal_limit=Decimal('100.00'),
            monthly_withdrawal_limit=Decimal('1000.00')
        )

        # Create wallets and transactions for users
        wallets_created = 0
        transactions_created = 0

        for user in self.users:
            # Create wallet
            wallet, created = UserWallet.objects.get_or_create(
                user=user,
                defaults={'balance': Decimal(str(random.uniform(0, 50)))}
            )
            wallets_created += 1

            # Create transaction history
            transaction_types = ['credit', 'debit']
            purposes = ['deposit', 'withdrawal', 'purchase_subscription', 'purchase_points', 'reward_payout']

            current_balance = wallet.balance
            for _ in range(random.randint(3, 8)):
                transaction_type = random.choice(transaction_types)
                purpose = random.choice(purposes)

                if transaction_type == 'credit':
                    amount = Decimal(str(random.uniform(1, 20)))
                    description = f'Money added - {purpose}'
                    balance_before = current_balance
                    balance_after = current_balance + amount
                else:
                    amount = Decimal(str(random.uniform(1, min(15, float(current_balance)))))
                    description = f'Money spent - {purpose}'
                    balance_before = current_balance
                    balance_after = current_balance - amount

                WalletTransaction.objects.create(
                    wallet=wallet,
                    transaction_type=transaction_type,
                    purpose=purpose,
                    amount=amount,
                    balance_before=balance_before,
                    balance_after=balance_after,
                    status='completed',
                    description=description,
                    created_at=timezone.now() - timedelta(days=random.randint(1, 60))
                )
                current_balance = balance_after
                transactions_created += 1

        self.stdout.write(f'  Created wallet settings, {wallets_created} wallets, and {transactions_created} transactions')
