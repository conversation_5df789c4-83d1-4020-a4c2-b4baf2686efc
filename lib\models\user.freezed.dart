// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  int get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get email =>
      throw _privateConstructorUsedError; // Made optional with default empty string
  @JsonKey(name: 'first_name')
  String get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_name')
  String get lastName => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatar_url')
  String? get avatarUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone_number')
  String? get phoneNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'date_of_birth')
  String? get dateOfBirth => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  @JsonKey(name: 'twitter_url')
  String? get twitterUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'linkedin_url')
  String? get linkedinUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'github_url')
  String? get githubUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_email_verified')
  bool get isEmailVerified => throw _privateConstructorUsedError;
  @JsonKey(name: 'receive_email_notifications')
  bool get receiveEmailNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'receive_push_notifications')
  bool get receivePushNotifications => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_profile_public')
  bool get isProfilePublic => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_staff')
  bool get isStaff => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_superuser')
  bool get isSuperuser => throw _privateConstructorUsedError;
  @JsonKey(name: 'date_joined')
  String? get dateJoined => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  String? get updatedAt =>
      throw _privateConstructorUsedError; // Regional preferences
  @JsonKey(name: 'preferred_country')
  Country? get preferredCountry => throw _privateConstructorUsedError;
  @JsonKey(name: 'detected_country')
  Country? get detectedCountry => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_global_content')
  bool get showGlobalContent => throw _privateConstructorUsedError;
  @JsonKey(name: 'auto_detect_location')
  bool get autoDetectLocation =>
      throw _privateConstructorUsedError; // Role-based properties
  String get role => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_content_creator')
  bool get isContentCreator => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_regular_user')
  bool get isRegularUser => throw _privateConstructorUsedError;
  @JsonKey(name: 'can_create_content')
  bool get canCreateContent => throw _privateConstructorUsedError;
  @JsonKey(name: 'can_moderate_content')
  bool get canModerateContent => throw _privateConstructorUsedError;
  List<String> get groups => throw _privateConstructorUsedError;
  Map<String, bool> get permissions => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'first_name') String firstName,
      @JsonKey(name: 'last_name') String lastName,
      String? bio,
      @JsonKey(name: 'avatar_url') String? avatarUrl,
      @JsonKey(name: 'phone_number') String? phoneNumber,
      @JsonKey(name: 'date_of_birth') String? dateOfBirth,
      String? location,
      String? website,
      @JsonKey(name: 'twitter_url') String? twitterUrl,
      @JsonKey(name: 'linkedin_url') String? linkedinUrl,
      @JsonKey(name: 'github_url') String? githubUrl,
      @JsonKey(name: 'is_email_verified') bool isEmailVerified,
      @JsonKey(name: 'receive_email_notifications')
      bool receiveEmailNotifications,
      @JsonKey(name: 'receive_push_notifications')
      bool receivePushNotifications,
      @JsonKey(name: 'is_profile_public') bool isProfilePublic,
      @JsonKey(name: 'is_staff') bool isStaff,
      @JsonKey(name: 'is_superuser') bool isSuperuser,
      @JsonKey(name: 'date_joined') String? dateJoined,
      @JsonKey(name: 'updated_at') String? updatedAt,
      @JsonKey(name: 'preferred_country') Country? preferredCountry,
      @JsonKey(name: 'detected_country') Country? detectedCountry,
      @JsonKey(name: 'show_global_content') bool showGlobalContent,
      @JsonKey(name: 'auto_detect_location') bool autoDetectLocation,
      String role,
      @JsonKey(name: 'is_content_creator') bool isContentCreator,
      @JsonKey(name: 'is_regular_user') bool isRegularUser,
      @JsonKey(name: 'can_create_content') bool canCreateContent,
      @JsonKey(name: 'can_moderate_content') bool canModerateContent,
      List<String> groups,
      Map<String, bool> permissions});

  $CountryCopyWith<$Res>? get preferredCountry;
  $CountryCopyWith<$Res>? get detectedCountry;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? bio = freezed,
    Object? avatarUrl = freezed,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? location = freezed,
    Object? website = freezed,
    Object? twitterUrl = freezed,
    Object? linkedinUrl = freezed,
    Object? githubUrl = freezed,
    Object? isEmailVerified = null,
    Object? receiveEmailNotifications = null,
    Object? receivePushNotifications = null,
    Object? isProfilePublic = null,
    Object? isStaff = null,
    Object? isSuperuser = null,
    Object? dateJoined = freezed,
    Object? updatedAt = freezed,
    Object? preferredCountry = freezed,
    Object? detectedCountry = freezed,
    Object? showGlobalContent = null,
    Object? autoDetectLocation = null,
    Object? role = null,
    Object? isContentCreator = null,
    Object? isRegularUser = null,
    Object? canCreateContent = null,
    Object? canModerateContent = null,
    Object? groups = null,
    Object? permissions = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      bio: freezed == bio
          ? _value.bio
          : bio // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
      twitterUrl: freezed == twitterUrl
          ? _value.twitterUrl
          : twitterUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      linkedinUrl: freezed == linkedinUrl
          ? _value.linkedinUrl
          : linkedinUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      githubUrl: freezed == githubUrl
          ? _value.githubUrl
          : githubUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      receiveEmailNotifications: null == receiveEmailNotifications
          ? _value.receiveEmailNotifications
          : receiveEmailNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      receivePushNotifications: null == receivePushNotifications
          ? _value.receivePushNotifications
          : receivePushNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      isProfilePublic: null == isProfilePublic
          ? _value.isProfilePublic
          : isProfilePublic // ignore: cast_nullable_to_non_nullable
              as bool,
      isStaff: null == isStaff
          ? _value.isStaff
          : isStaff // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuperuser: null == isSuperuser
          ? _value.isSuperuser
          : isSuperuser // ignore: cast_nullable_to_non_nullable
              as bool,
      dateJoined: freezed == dateJoined
          ? _value.dateJoined
          : dateJoined // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredCountry: freezed == preferredCountry
          ? _value.preferredCountry
          : preferredCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      detectedCountry: freezed == detectedCountry
          ? _value.detectedCountry
          : detectedCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      showGlobalContent: null == showGlobalContent
          ? _value.showGlobalContent
          : showGlobalContent // ignore: cast_nullable_to_non_nullable
              as bool,
      autoDetectLocation: null == autoDetectLocation
          ? _value.autoDetectLocation
          : autoDetectLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      isContentCreator: null == isContentCreator
          ? _value.isContentCreator
          : isContentCreator // ignore: cast_nullable_to_non_nullable
              as bool,
      isRegularUser: null == isRegularUser
          ? _value.isRegularUser
          : isRegularUser // ignore: cast_nullable_to_non_nullable
              as bool,
      canCreateContent: null == canCreateContent
          ? _value.canCreateContent
          : canCreateContent // ignore: cast_nullable_to_non_nullable
              as bool,
      canModerateContent: null == canModerateContent
          ? _value.canModerateContent
          : canModerateContent // ignore: cast_nullable_to_non_nullable
              as bool,
      groups: null == groups
          ? _value.groups
          : groups // ignore: cast_nullable_to_non_nullable
              as List<String>,
      permissions: null == permissions
          ? _value.permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CountryCopyWith<$Res>? get preferredCountry {
    if (_value.preferredCountry == null) {
      return null;
    }

    return $CountryCopyWith<$Res>(_value.preferredCountry!, (value) {
      return _then(_value.copyWith(preferredCountry: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CountryCopyWith<$Res>? get detectedCountry {
    if (_value.detectedCountry == null) {
      return null;
    }

    return $CountryCopyWith<$Res>(_value.detectedCountry!, (value) {
      return _then(_value.copyWith(detectedCountry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'first_name') String firstName,
      @JsonKey(name: 'last_name') String lastName,
      String? bio,
      @JsonKey(name: 'avatar_url') String? avatarUrl,
      @JsonKey(name: 'phone_number') String? phoneNumber,
      @JsonKey(name: 'date_of_birth') String? dateOfBirth,
      String? location,
      String? website,
      @JsonKey(name: 'twitter_url') String? twitterUrl,
      @JsonKey(name: 'linkedin_url') String? linkedinUrl,
      @JsonKey(name: 'github_url') String? githubUrl,
      @JsonKey(name: 'is_email_verified') bool isEmailVerified,
      @JsonKey(name: 'receive_email_notifications')
      bool receiveEmailNotifications,
      @JsonKey(name: 'receive_push_notifications')
      bool receivePushNotifications,
      @JsonKey(name: 'is_profile_public') bool isProfilePublic,
      @JsonKey(name: 'is_staff') bool isStaff,
      @JsonKey(name: 'is_superuser') bool isSuperuser,
      @JsonKey(name: 'date_joined') String? dateJoined,
      @JsonKey(name: 'updated_at') String? updatedAt,
      @JsonKey(name: 'preferred_country') Country? preferredCountry,
      @JsonKey(name: 'detected_country') Country? detectedCountry,
      @JsonKey(name: 'show_global_content') bool showGlobalContent,
      @JsonKey(name: 'auto_detect_location') bool autoDetectLocation,
      String role,
      @JsonKey(name: 'is_content_creator') bool isContentCreator,
      @JsonKey(name: 'is_regular_user') bool isRegularUser,
      @JsonKey(name: 'can_create_content') bool canCreateContent,
      @JsonKey(name: 'can_moderate_content') bool canModerateContent,
      List<String> groups,
      Map<String, bool> permissions});

  @override
  $CountryCopyWith<$Res>? get preferredCountry;
  @override
  $CountryCopyWith<$Res>? get detectedCountry;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? bio = freezed,
    Object? avatarUrl = freezed,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? location = freezed,
    Object? website = freezed,
    Object? twitterUrl = freezed,
    Object? linkedinUrl = freezed,
    Object? githubUrl = freezed,
    Object? isEmailVerified = null,
    Object? receiveEmailNotifications = null,
    Object? receivePushNotifications = null,
    Object? isProfilePublic = null,
    Object? isStaff = null,
    Object? isSuperuser = null,
    Object? dateJoined = freezed,
    Object? updatedAt = freezed,
    Object? preferredCountry = freezed,
    Object? detectedCountry = freezed,
    Object? showGlobalContent = null,
    Object? autoDetectLocation = null,
    Object? role = null,
    Object? isContentCreator = null,
    Object? isRegularUser = null,
    Object? canCreateContent = null,
    Object? canModerateContent = null,
    Object? groups = null,
    Object? permissions = null,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      bio: freezed == bio
          ? _value.bio
          : bio // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
      twitterUrl: freezed == twitterUrl
          ? _value.twitterUrl
          : twitterUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      linkedinUrl: freezed == linkedinUrl
          ? _value.linkedinUrl
          : linkedinUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      githubUrl: freezed == githubUrl
          ? _value.githubUrl
          : githubUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      receiveEmailNotifications: null == receiveEmailNotifications
          ? _value.receiveEmailNotifications
          : receiveEmailNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      receivePushNotifications: null == receivePushNotifications
          ? _value.receivePushNotifications
          : receivePushNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      isProfilePublic: null == isProfilePublic
          ? _value.isProfilePublic
          : isProfilePublic // ignore: cast_nullable_to_non_nullable
              as bool,
      isStaff: null == isStaff
          ? _value.isStaff
          : isStaff // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuperuser: null == isSuperuser
          ? _value.isSuperuser
          : isSuperuser // ignore: cast_nullable_to_non_nullable
              as bool,
      dateJoined: freezed == dateJoined
          ? _value.dateJoined
          : dateJoined // ignore: cast_nullable_to_non_nullable
              as String?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredCountry: freezed == preferredCountry
          ? _value.preferredCountry
          : preferredCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      detectedCountry: freezed == detectedCountry
          ? _value.detectedCountry
          : detectedCountry // ignore: cast_nullable_to_non_nullable
              as Country?,
      showGlobalContent: null == showGlobalContent
          ? _value.showGlobalContent
          : showGlobalContent // ignore: cast_nullable_to_non_nullable
              as bool,
      autoDetectLocation: null == autoDetectLocation
          ? _value.autoDetectLocation
          : autoDetectLocation // ignore: cast_nullable_to_non_nullable
              as bool,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      isContentCreator: null == isContentCreator
          ? _value.isContentCreator
          : isContentCreator // ignore: cast_nullable_to_non_nullable
              as bool,
      isRegularUser: null == isRegularUser
          ? _value.isRegularUser
          : isRegularUser // ignore: cast_nullable_to_non_nullable
              as bool,
      canCreateContent: null == canCreateContent
          ? _value.canCreateContent
          : canCreateContent // ignore: cast_nullable_to_non_nullable
              as bool,
      canModerateContent: null == canModerateContent
          ? _value.canModerateContent
          : canModerateContent // ignore: cast_nullable_to_non_nullable
              as bool,
      groups: null == groups
          ? _value._groups
          : groups // ignore: cast_nullable_to_non_nullable
              as List<String>,
      permissions: null == permissions
          ? _value._permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl extends _User {
  const _$UserImpl(
      {required this.id,
      required this.username,
      this.email = '',
      @JsonKey(name: 'first_name') this.firstName = '',
      @JsonKey(name: 'last_name') this.lastName = '',
      this.bio,
      @JsonKey(name: 'avatar_url') this.avatarUrl,
      @JsonKey(name: 'phone_number') this.phoneNumber,
      @JsonKey(name: 'date_of_birth') this.dateOfBirth,
      this.location,
      this.website,
      @JsonKey(name: 'twitter_url') this.twitterUrl,
      @JsonKey(name: 'linkedin_url') this.linkedinUrl,
      @JsonKey(name: 'github_url') this.githubUrl,
      @JsonKey(name: 'is_email_verified') this.isEmailVerified = false,
      @JsonKey(name: 'receive_email_notifications')
      this.receiveEmailNotifications = true,
      @JsonKey(name: 'receive_push_notifications')
      this.receivePushNotifications = true,
      @JsonKey(name: 'is_profile_public') this.isProfilePublic = true,
      @JsonKey(name: 'is_staff') this.isStaff = false,
      @JsonKey(name: 'is_superuser') this.isSuperuser = false,
      @JsonKey(name: 'date_joined') this.dateJoined,
      @JsonKey(name: 'updated_at') this.updatedAt,
      @JsonKey(name: 'preferred_country') this.preferredCountry,
      @JsonKey(name: 'detected_country') this.detectedCountry,
      @JsonKey(name: 'show_global_content') this.showGlobalContent = true,
      @JsonKey(name: 'auto_detect_location') this.autoDetectLocation = true,
      this.role = 'regular_user',
      @JsonKey(name: 'is_content_creator') this.isContentCreator = false,
      @JsonKey(name: 'is_regular_user') this.isRegularUser = true,
      @JsonKey(name: 'can_create_content') this.canCreateContent = false,
      @JsonKey(name: 'can_moderate_content') this.canModerateContent = false,
      final List<String> groups = const <String>[],
      final Map<String, bool> permissions = const <String, bool>{}})
      : _groups = groups,
        _permissions = permissions,
        super._();

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final int id;
  @override
  final String username;
  @override
  @JsonKey()
  final String email;
// Made optional with default empty string
  @override
  @JsonKey(name: 'first_name')
  final String firstName;
  @override
  @JsonKey(name: 'last_name')
  final String lastName;
  @override
  final String? bio;
  @override
  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;
  @override
  @JsonKey(name: 'phone_number')
  final String? phoneNumber;
  @override
  @JsonKey(name: 'date_of_birth')
  final String? dateOfBirth;
  @override
  final String? location;
  @override
  final String? website;
  @override
  @JsonKey(name: 'twitter_url')
  final String? twitterUrl;
  @override
  @JsonKey(name: 'linkedin_url')
  final String? linkedinUrl;
  @override
  @JsonKey(name: 'github_url')
  final String? githubUrl;
  @override
  @JsonKey(name: 'is_email_verified')
  final bool isEmailVerified;
  @override
  @JsonKey(name: 'receive_email_notifications')
  final bool receiveEmailNotifications;
  @override
  @JsonKey(name: 'receive_push_notifications')
  final bool receivePushNotifications;
  @override
  @JsonKey(name: 'is_profile_public')
  final bool isProfilePublic;
  @override
  @JsonKey(name: 'is_staff')
  final bool isStaff;
  @override
  @JsonKey(name: 'is_superuser')
  final bool isSuperuser;
  @override
  @JsonKey(name: 'date_joined')
  final String? dateJoined;
  @override
  @JsonKey(name: 'updated_at')
  final String? updatedAt;
// Regional preferences
  @override
  @JsonKey(name: 'preferred_country')
  final Country? preferredCountry;
  @override
  @JsonKey(name: 'detected_country')
  final Country? detectedCountry;
  @override
  @JsonKey(name: 'show_global_content')
  final bool showGlobalContent;
  @override
  @JsonKey(name: 'auto_detect_location')
  final bool autoDetectLocation;
// Role-based properties
  @override
  @JsonKey()
  final String role;
  @override
  @JsonKey(name: 'is_content_creator')
  final bool isContentCreator;
  @override
  @JsonKey(name: 'is_regular_user')
  final bool isRegularUser;
  @override
  @JsonKey(name: 'can_create_content')
  final bool canCreateContent;
  @override
  @JsonKey(name: 'can_moderate_content')
  final bool canModerateContent;
  final List<String> _groups;
  @override
  @JsonKey()
  List<String> get groups {
    if (_groups is EqualUnmodifiableListView) return _groups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_groups);
  }

  final Map<String, bool> _permissions;
  @override
  @JsonKey()
  Map<String, bool> get permissions {
    if (_permissions is EqualUnmodifiableMapView) return _permissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_permissions);
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, email: $email, firstName: $firstName, lastName: $lastName, bio: $bio, avatarUrl: $avatarUrl, phoneNumber: $phoneNumber, dateOfBirth: $dateOfBirth, location: $location, website: $website, twitterUrl: $twitterUrl, linkedinUrl: $linkedinUrl, githubUrl: $githubUrl, isEmailVerified: $isEmailVerified, receiveEmailNotifications: $receiveEmailNotifications, receivePushNotifications: $receivePushNotifications, isProfilePublic: $isProfilePublic, isStaff: $isStaff, isSuperuser: $isSuperuser, dateJoined: $dateJoined, updatedAt: $updatedAt, preferredCountry: $preferredCountry, detectedCountry: $detectedCountry, showGlobalContent: $showGlobalContent, autoDetectLocation: $autoDetectLocation, role: $role, isContentCreator: $isContentCreator, isRegularUser: $isRegularUser, canCreateContent: $canCreateContent, canModerateContent: $canModerateContent, groups: $groups, permissions: $permissions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.twitterUrl, twitterUrl) ||
                other.twitterUrl == twitterUrl) &&
            (identical(other.linkedinUrl, linkedinUrl) ||
                other.linkedinUrl == linkedinUrl) &&
            (identical(other.githubUrl, githubUrl) ||
                other.githubUrl == githubUrl) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.receiveEmailNotifications,
                    receiveEmailNotifications) ||
                other.receiveEmailNotifications == receiveEmailNotifications) &&
            (identical(
                    other.receivePushNotifications, receivePushNotifications) ||
                other.receivePushNotifications == receivePushNotifications) &&
            (identical(other.isProfilePublic, isProfilePublic) ||
                other.isProfilePublic == isProfilePublic) &&
            (identical(other.isStaff, isStaff) || other.isStaff == isStaff) &&
            (identical(other.isSuperuser, isSuperuser) ||
                other.isSuperuser == isSuperuser) &&
            (identical(other.dateJoined, dateJoined) ||
                other.dateJoined == dateJoined) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.preferredCountry, preferredCountry) ||
                other.preferredCountry == preferredCountry) &&
            (identical(other.detectedCountry, detectedCountry) ||
                other.detectedCountry == detectedCountry) &&
            (identical(other.showGlobalContent, showGlobalContent) ||
                other.showGlobalContent == showGlobalContent) &&
            (identical(other.autoDetectLocation, autoDetectLocation) ||
                other.autoDetectLocation == autoDetectLocation) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.isContentCreator, isContentCreator) ||
                other.isContentCreator == isContentCreator) &&
            (identical(other.isRegularUser, isRegularUser) ||
                other.isRegularUser == isRegularUser) &&
            (identical(other.canCreateContent, canCreateContent) ||
                other.canCreateContent == canCreateContent) &&
            (identical(other.canModerateContent, canModerateContent) ||
                other.canModerateContent == canModerateContent) &&
            const DeepCollectionEquality().equals(other._groups, _groups) &&
            const DeepCollectionEquality()
                .equals(other._permissions, _permissions));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        username,
        email,
        firstName,
        lastName,
        bio,
        avatarUrl,
        phoneNumber,
        dateOfBirth,
        location,
        website,
        twitterUrl,
        linkedinUrl,
        githubUrl,
        isEmailVerified,
        receiveEmailNotifications,
        receivePushNotifications,
        isProfilePublic,
        isStaff,
        isSuperuser,
        dateJoined,
        updatedAt,
        preferredCountry,
        detectedCountry,
        showGlobalContent,
        autoDetectLocation,
        role,
        isContentCreator,
        isRegularUser,
        canCreateContent,
        canModerateContent,
        const DeepCollectionEquality().hash(_groups),
        const DeepCollectionEquality().hash(_permissions)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User extends User {
  const factory _User(
      {required final int id,
      required final String username,
      final String email,
      @JsonKey(name: 'first_name') final String firstName,
      @JsonKey(name: 'last_name') final String lastName,
      final String? bio,
      @JsonKey(name: 'avatar_url') final String? avatarUrl,
      @JsonKey(name: 'phone_number') final String? phoneNumber,
      @JsonKey(name: 'date_of_birth') final String? dateOfBirth,
      final String? location,
      final String? website,
      @JsonKey(name: 'twitter_url') final String? twitterUrl,
      @JsonKey(name: 'linkedin_url') final String? linkedinUrl,
      @JsonKey(name: 'github_url') final String? githubUrl,
      @JsonKey(name: 'is_email_verified') final bool isEmailVerified,
      @JsonKey(name: 'receive_email_notifications')
      final bool receiveEmailNotifications,
      @JsonKey(name: 'receive_push_notifications')
      final bool receivePushNotifications,
      @JsonKey(name: 'is_profile_public') final bool isProfilePublic,
      @JsonKey(name: 'is_staff') final bool isStaff,
      @JsonKey(name: 'is_superuser') final bool isSuperuser,
      @JsonKey(name: 'date_joined') final String? dateJoined,
      @JsonKey(name: 'updated_at') final String? updatedAt,
      @JsonKey(name: 'preferred_country') final Country? preferredCountry,
      @JsonKey(name: 'detected_country') final Country? detectedCountry,
      @JsonKey(name: 'show_global_content') final bool showGlobalContent,
      @JsonKey(name: 'auto_detect_location') final bool autoDetectLocation,
      final String role,
      @JsonKey(name: 'is_content_creator') final bool isContentCreator,
      @JsonKey(name: 'is_regular_user') final bool isRegularUser,
      @JsonKey(name: 'can_create_content') final bool canCreateContent,
      @JsonKey(name: 'can_moderate_content') final bool canModerateContent,
      final List<String> groups,
      final Map<String, bool> permissions}) = _$UserImpl;
  const _User._() : super._();

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  int get id;
  @override
  String get username;
  @override
  String get email;
  @override // Made optional with default empty string
  @JsonKey(name: 'first_name')
  String get firstName;
  @override
  @JsonKey(name: 'last_name')
  String get lastName;
  @override
  String? get bio;
  @override
  @JsonKey(name: 'avatar_url')
  String? get avatarUrl;
  @override
  @JsonKey(name: 'phone_number')
  String? get phoneNumber;
  @override
  @JsonKey(name: 'date_of_birth')
  String? get dateOfBirth;
  @override
  String? get location;
  @override
  String? get website;
  @override
  @JsonKey(name: 'twitter_url')
  String? get twitterUrl;
  @override
  @JsonKey(name: 'linkedin_url')
  String? get linkedinUrl;
  @override
  @JsonKey(name: 'github_url')
  String? get githubUrl;
  @override
  @JsonKey(name: 'is_email_verified')
  bool get isEmailVerified;
  @override
  @JsonKey(name: 'receive_email_notifications')
  bool get receiveEmailNotifications;
  @override
  @JsonKey(name: 'receive_push_notifications')
  bool get receivePushNotifications;
  @override
  @JsonKey(name: 'is_profile_public')
  bool get isProfilePublic;
  @override
  @JsonKey(name: 'is_staff')
  bool get isStaff;
  @override
  @JsonKey(name: 'is_superuser')
  bool get isSuperuser;
  @override
  @JsonKey(name: 'date_joined')
  String? get dateJoined;
  @override
  @JsonKey(name: 'updated_at')
  String? get updatedAt;
  @override // Regional preferences
  @JsonKey(name: 'preferred_country')
  Country? get preferredCountry;
  @override
  @JsonKey(name: 'detected_country')
  Country? get detectedCountry;
  @override
  @JsonKey(name: 'show_global_content')
  bool get showGlobalContent;
  @override
  @JsonKey(name: 'auto_detect_location')
  bool get autoDetectLocation;
  @override // Role-based properties
  String get role;
  @override
  @JsonKey(name: 'is_content_creator')
  bool get isContentCreator;
  @override
  @JsonKey(name: 'is_regular_user')
  bool get isRegularUser;
  @override
  @JsonKey(name: 'can_create_content')
  bool get canCreateContent;
  @override
  @JsonKey(name: 'can_moderate_content')
  bool get canModerateContent;
  @override
  List<String> get groups;
  @override
  Map<String, bool> get permissions;
  @override
  @JsonKey(ignore: true)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) {
  return _UserProfile.fromJson(json);
}

/// @nodoc
mixin _$UserProfile {
  int get id => throw _privateConstructorUsedError;
  String get username => throw _privateConstructorUsedError;
  String get email =>
      throw _privateConstructorUsedError; // Made optional with default empty string
  @JsonKey(name: 'first_name')
  String get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_name')
  String get lastName => throw _privateConstructorUsedError;
  String? get bio => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatar_url')
  String? get avatarUrl => throw _privateConstructorUsedError;
  String? get avatar => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get website => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_email_verified')
  bool get isEmailVerified => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_profile_public')
  bool get isProfilePublic => throw _privateConstructorUsedError;
  @JsonKey(name: 'date_joined')
  String? get dateJoined => throw _privateConstructorUsedError; // Social fields
  @JsonKey(name: 'followers_count')
  int get followersCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'following_count')
  int get followingCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'posts_count')
  int get postsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_following')
  bool get isFollowing => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserProfileCopyWith<UserProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserProfileCopyWith<$Res> {
  factory $UserProfileCopyWith(
          UserProfile value, $Res Function(UserProfile) then) =
      _$UserProfileCopyWithImpl<$Res, UserProfile>;
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'first_name') String firstName,
      @JsonKey(name: 'last_name') String lastName,
      String? bio,
      @JsonKey(name: 'avatar_url') String? avatarUrl,
      String? avatar,
      String? location,
      String? website,
      @JsonKey(name: 'is_email_verified') bool isEmailVerified,
      @JsonKey(name: 'is_profile_public') bool isProfilePublic,
      @JsonKey(name: 'date_joined') String? dateJoined,
      @JsonKey(name: 'followers_count') int followersCount,
      @JsonKey(name: 'following_count') int followingCount,
      @JsonKey(name: 'posts_count') int postsCount,
      @JsonKey(name: 'is_following') bool isFollowing});
}

/// @nodoc
class _$UserProfileCopyWithImpl<$Res, $Val extends UserProfile>
    implements $UserProfileCopyWith<$Res> {
  _$UserProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? bio = freezed,
    Object? avatarUrl = freezed,
    Object? avatar = freezed,
    Object? location = freezed,
    Object? website = freezed,
    Object? isEmailVerified = null,
    Object? isProfilePublic = null,
    Object? dateJoined = freezed,
    Object? followersCount = null,
    Object? followingCount = null,
    Object? postsCount = null,
    Object? isFollowing = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      bio: freezed == bio
          ? _value.bio
          : bio // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      isProfilePublic: null == isProfilePublic
          ? _value.isProfilePublic
          : isProfilePublic // ignore: cast_nullable_to_non_nullable
              as bool,
      dateJoined: freezed == dateJoined
          ? _value.dateJoined
          : dateJoined // ignore: cast_nullable_to_non_nullable
              as String?,
      followersCount: null == followersCount
          ? _value.followersCount
          : followersCount // ignore: cast_nullable_to_non_nullable
              as int,
      followingCount: null == followingCount
          ? _value.followingCount
          : followingCount // ignore: cast_nullable_to_non_nullable
              as int,
      postsCount: null == postsCount
          ? _value.postsCount
          : postsCount // ignore: cast_nullable_to_non_nullable
              as int,
      isFollowing: null == isFollowing
          ? _value.isFollowing
          : isFollowing // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserProfileImplCopyWith<$Res>
    implements $UserProfileCopyWith<$Res> {
  factory _$$UserProfileImplCopyWith(
          _$UserProfileImpl value, $Res Function(_$UserProfileImpl) then) =
      __$$UserProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String username,
      String email,
      @JsonKey(name: 'first_name') String firstName,
      @JsonKey(name: 'last_name') String lastName,
      String? bio,
      @JsonKey(name: 'avatar_url') String? avatarUrl,
      String? avatar,
      String? location,
      String? website,
      @JsonKey(name: 'is_email_verified') bool isEmailVerified,
      @JsonKey(name: 'is_profile_public') bool isProfilePublic,
      @JsonKey(name: 'date_joined') String? dateJoined,
      @JsonKey(name: 'followers_count') int followersCount,
      @JsonKey(name: 'following_count') int followingCount,
      @JsonKey(name: 'posts_count') int postsCount,
      @JsonKey(name: 'is_following') bool isFollowing});
}

/// @nodoc
class __$$UserProfileImplCopyWithImpl<$Res>
    extends _$UserProfileCopyWithImpl<$Res, _$UserProfileImpl>
    implements _$$UserProfileImplCopyWith<$Res> {
  __$$UserProfileImplCopyWithImpl(
      _$UserProfileImpl _value, $Res Function(_$UserProfileImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? username = null,
    Object? email = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? bio = freezed,
    Object? avatarUrl = freezed,
    Object? avatar = freezed,
    Object? location = freezed,
    Object? website = freezed,
    Object? isEmailVerified = null,
    Object? isProfilePublic = null,
    Object? dateJoined = freezed,
    Object? followersCount = null,
    Object? followingCount = null,
    Object? postsCount = null,
    Object? isFollowing = null,
  }) {
    return _then(_$UserProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      bio: freezed == bio
          ? _value.bio
          : bio // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
      isEmailVerified: null == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      isProfilePublic: null == isProfilePublic
          ? _value.isProfilePublic
          : isProfilePublic // ignore: cast_nullable_to_non_nullable
              as bool,
      dateJoined: freezed == dateJoined
          ? _value.dateJoined
          : dateJoined // ignore: cast_nullable_to_non_nullable
              as String?,
      followersCount: null == followersCount
          ? _value.followersCount
          : followersCount // ignore: cast_nullable_to_non_nullable
              as int,
      followingCount: null == followingCount
          ? _value.followingCount
          : followingCount // ignore: cast_nullable_to_non_nullable
              as int,
      postsCount: null == postsCount
          ? _value.postsCount
          : postsCount // ignore: cast_nullable_to_non_nullable
              as int,
      isFollowing: null == isFollowing
          ? _value.isFollowing
          : isFollowing // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserProfileImpl extends _UserProfile {
  const _$UserProfileImpl(
      {required this.id,
      required this.username,
      this.email = '',
      @JsonKey(name: 'first_name') this.firstName = '',
      @JsonKey(name: 'last_name') this.lastName = '',
      this.bio,
      @JsonKey(name: 'avatar_url') this.avatarUrl,
      this.avatar,
      this.location,
      this.website,
      @JsonKey(name: 'is_email_verified') this.isEmailVerified = false,
      @JsonKey(name: 'is_profile_public') this.isProfilePublic = true,
      @JsonKey(name: 'date_joined') this.dateJoined,
      @JsonKey(name: 'followers_count') this.followersCount = 0,
      @JsonKey(name: 'following_count') this.followingCount = 0,
      @JsonKey(name: 'posts_count') this.postsCount = 0,
      @JsonKey(name: 'is_following') this.isFollowing = false})
      : super._();

  factory _$UserProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserProfileImplFromJson(json);

  @override
  final int id;
  @override
  final String username;
  @override
  @JsonKey()
  final String email;
// Made optional with default empty string
  @override
  @JsonKey(name: 'first_name')
  final String firstName;
  @override
  @JsonKey(name: 'last_name')
  final String lastName;
  @override
  final String? bio;
  @override
  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;
  @override
  final String? avatar;
  @override
  final String? location;
  @override
  final String? website;
  @override
  @JsonKey(name: 'is_email_verified')
  final bool isEmailVerified;
  @override
  @JsonKey(name: 'is_profile_public')
  final bool isProfilePublic;
  @override
  @JsonKey(name: 'date_joined')
  final String? dateJoined;
// Social fields
  @override
  @JsonKey(name: 'followers_count')
  final int followersCount;
  @override
  @JsonKey(name: 'following_count')
  final int followingCount;
  @override
  @JsonKey(name: 'posts_count')
  final int postsCount;
  @override
  @JsonKey(name: 'is_following')
  final bool isFollowing;

  @override
  String toString() {
    return 'UserProfile(id: $id, username: $username, email: $email, firstName: $firstName, lastName: $lastName, bio: $bio, avatarUrl: $avatarUrl, avatar: $avatar, location: $location, website: $website, isEmailVerified: $isEmailVerified, isProfilePublic: $isProfilePublic, dateJoined: $dateJoined, followersCount: $followersCount, followingCount: $followingCount, postsCount: $postsCount, isFollowing: $isFollowing)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.bio, bio) || other.bio == bio) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.isProfilePublic, isProfilePublic) ||
                other.isProfilePublic == isProfilePublic) &&
            (identical(other.dateJoined, dateJoined) ||
                other.dateJoined == dateJoined) &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.postsCount, postsCount) ||
                other.postsCount == postsCount) &&
            (identical(other.isFollowing, isFollowing) ||
                other.isFollowing == isFollowing));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      username,
      email,
      firstName,
      lastName,
      bio,
      avatarUrl,
      avatar,
      location,
      website,
      isEmailVerified,
      isProfilePublic,
      dateJoined,
      followersCount,
      followingCount,
      postsCount,
      isFollowing);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      __$$UserProfileImplCopyWithImpl<_$UserProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserProfileImplToJson(
      this,
    );
  }
}

abstract class _UserProfile extends UserProfile {
  const factory _UserProfile(
          {required final int id,
          required final String username,
          final String email,
          @JsonKey(name: 'first_name') final String firstName,
          @JsonKey(name: 'last_name') final String lastName,
          final String? bio,
          @JsonKey(name: 'avatar_url') final String? avatarUrl,
          final String? avatar,
          final String? location,
          final String? website,
          @JsonKey(name: 'is_email_verified') final bool isEmailVerified,
          @JsonKey(name: 'is_profile_public') final bool isProfilePublic,
          @JsonKey(name: 'date_joined') final String? dateJoined,
          @JsonKey(name: 'followers_count') final int followersCount,
          @JsonKey(name: 'following_count') final int followingCount,
          @JsonKey(name: 'posts_count') final int postsCount,
          @JsonKey(name: 'is_following') final bool isFollowing}) =
      _$UserProfileImpl;
  const _UserProfile._() : super._();

  factory _UserProfile.fromJson(Map<String, dynamic> json) =
      _$UserProfileImpl.fromJson;

  @override
  int get id;
  @override
  String get username;
  @override
  String get email;
  @override // Made optional with default empty string
  @JsonKey(name: 'first_name')
  String get firstName;
  @override
  @JsonKey(name: 'last_name')
  String get lastName;
  @override
  String? get bio;
  @override
  @JsonKey(name: 'avatar_url')
  String? get avatarUrl;
  @override
  String? get avatar;
  @override
  String? get location;
  @override
  String? get website;
  @override
  @JsonKey(name: 'is_email_verified')
  bool get isEmailVerified;
  @override
  @JsonKey(name: 'is_profile_public')
  bool get isProfilePublic;
  @override
  @JsonKey(name: 'date_joined')
  String? get dateJoined;
  @override // Social fields
  @JsonKey(name: 'followers_count')
  int get followersCount;
  @override
  @JsonKey(name: 'following_count')
  int get followingCount;
  @override
  @JsonKey(name: 'posts_count')
  int get postsCount;
  @override
  @JsonKey(name: 'is_following')
  bool get isFollowing;
  @override
  @JsonKey(ignore: true)
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
