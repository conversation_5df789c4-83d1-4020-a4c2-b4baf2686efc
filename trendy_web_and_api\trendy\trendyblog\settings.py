"""
Django settings for trendyblog project.

Generated by 'django-admin startproject' using Django 5.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
import os
import environ

from dotenv import load_dotenv
load_dotenv()

# Initialize environment variables
env = environ.Env()
environ.Env.read_env()  # Reading .env file

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# SECRET_KEY = 'django-insecure-z((@-^iyvi!wg11=#h1&2f3k!k6)7%8f7ssck=v&(_tl+a%25p'
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-development-key-change-in-production')

# SECURITY WARNING: don't run with debug turned on in production!
# DEBUG = True
DEBUG = env.bool('DEBUG')

# ALLOWED_HOSTS = ['*']
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', 'localhost,127.0.0.1,.onrender.com,**************,*').split(',')



# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'core',  # System maintenance and feature management
    'blog',
    'accounts',
    'analytics',
    'interactive',
    'voice_features',
    'gamification',
    'monetization',
    'payments',
    'wallet',
    'social',
    'blockchain',
    'advertising',  # Added advertising app
    'ai_writing',  # AI writing assistance
    'regional',  # Regional content filtering
    'messaging',  # User-to-user messaging
    'crispy_forms',
    'crispy_bootstrap5',
    'rest_framework.authtoken',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'core.middleware.MaintenanceMiddleware',  # System maintenance
    'core.middleware.FeatureToggleMiddleware',  # Feature toggles
    'django.contrib.messages.middleware.MessageMiddleware',
    'core.middleware.FeatureToggleContextMiddleware',  # Template context
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'trendyblog.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'trendyblog.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

# Database configuration
# Use environment variable to determine database type
DATABASE_URL = os.environ.get('DATABASE_URL')

if DATABASE_URL:
    if DATABASE_URL.startswith('sqlite'):
        # SQLite database
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': BASE_DIR / 'db.sqlite3',
            }
        }
    else:
        # Production: Use Neon PostgreSQL
        # Parse DATABASE_URL manually since dj_database_url is not available
        import urllib.parse as urlparse
        url = urlparse.urlparse(DATABASE_URL)
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.postgresql',
                'NAME': url.path[1:],
                'USER': url.username,
                'PASSWORD': url.password,
                'HOST': url.hostname,
                'PORT': url.port,
                'OPTIONS': {
                    'sslmode': 'require',
                },
            }
        }
else:
    # Development: Use SQLite
    DATABASES = {
        "default": {
            "ENGINE": 'django.db.backends.sqlite3',
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'

# Always define STATIC_ROOT for collectstatic command
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# In development, also define STATICFILES_DIRS for serving static files
if DEBUG:
    STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Email Configuration
EMAIL_BACKEND = os.environ.get('EMAIL_BACKEND', 'django.core.mail.backends.console.EmailBackend')
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True') == 'True'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')
SERVER_EMAIL = os.environ.get('SERVER_EMAIL', '<EMAIL>')

# Frontend URL for email links
FRONTEND_URL = os.environ.get('FRONTEND_URL', 'http://localhost:3000')

# Email settings for development
if DEBUG:
    # Use console backend for development to see emails in terminal
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
    print("📧 Email Backend: Console (emails will appear in terminal)")
else:
    print(f"📧 Email Backend: SMTP ({EMAIL_HOST})")
    print(f"📧 From Email: {DEFAULT_FROM_EMAIL}")

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# CORS settings for Flutter app
if DEBUG:
    # Development: Allow all origins
    CORS_ALLOW_ALL_ORIGINS = True
else:
    # Production: Restrict to specific origins
    CORS_ALLOWED_ORIGINS = [
        "https://your-flutter-app-domain.com",  # Replace with your Flutter app domain
        "https://trendy-blog.vercel.app",       # Example deployment domain
        "https://localhost:3000",               # Local Flutter development
    ]
    CORS_ALLOW_CREDENTIALS = True

# Custom User Model
AUTH_USER_MODEL = 'accounts.CustomUser'

# Authentication backends
AUTHENTICATION_BACKENDS = [
    'accounts.backends.EmailOrUsernameModelBackend',
    'django.contrib.auth.backends.ModelBackend',
]

# Authentication settings
LOGIN_REDIRECT_URL = 'home'
LOGOUT_REDIRECT_URL = 'home'
LOGIN_URL = 'login'



# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework.authentication.TokenAuthentication',

    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticatedOrReadOnly',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'ORDERING_PARAM': 'ordering',  # Add this
    
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    }

}

# Cache configuration - using database cache for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'cache_table',
    }
}

# For production, you can use Redis:
# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.redis.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379',
#     }
# }



# Crispy Forms Configuration
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Production Security Settings
if not DEBUG:
    # Security settings for production
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_REFERRER_POLICY = 'same-origin'
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    X_FRAME_OPTIONS = 'DENY'

    # Force HTTPS
    USE_TLS = True

    # Logging configuration for production
    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'verbose': {
                'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
                'style': '{',
            },
        },
        'handlers': {
            'file': {
                'level': 'INFO',
                'class': 'logging.FileHandler',
                'filename': BASE_DIR / 'logs' / 'django.log',
                'formatter': 'verbose',
            },
            'console': {
                'level': 'INFO',
                'class': 'logging.StreamHandler',
                'formatter': 'verbose',
            },
        },
        'root': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
        },
        'loggers': {
            'django': {
                'handlers': ['console', 'file'],
                'level': 'INFO',
                'propagate': False,
            },
        },
    }

    # Create logs directory if it doesn't exist
    os.makedirs(BASE_DIR / 'logs', exist_ok=True)

# Blockchain Configuration
BLOCKCHAIN_ADMIN_PRIVATE_KEY = os.environ.get('BLOCKCHAIN_ADMIN_PRIVATE_KEY', '0x' + '1' * 64)  # Development key
WALLET_ENCRYPTION_KEY = os.environ.get('WALLET_ENCRYPTION_KEY', 'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg=')

# Blockchain Networks
BLOCKCHAIN_NETWORKS = {
    'polygon': {
        'name': 'Polygon',
        'chain_id': 137,
        'rpc_url': 'https://polygon-rpc.com',
        'explorer_url': 'https://polygonscan.com',
        'native_token': 'MATIC',
        'gas_price_gwei': 30,
    },
    'polygon_testnet': {
        'name': 'Polygon Mumbai',
        'chain_id': 80001,
        'rpc_url': 'https://rpc-mumbai.maticvigil.com',
        'explorer_url': 'https://mumbai.polygonscan.com',
        'native_token': 'MATIC',
        'gas_price_gwei': 20,
    },
    'bsc': {
        'name': 'Binance Smart Chain',
        'chain_id': 56,
        'rpc_url': 'https://bsc-dataseed.binance.org',
        'explorer_url': 'https://bscscan.com',
        'native_token': 'BNB',
        'gas_price_gwei': 5,
    },
}

# Default blockchain network for development
DEFAULT_BLOCKCHAIN_NETWORK = 'polygon_testnet'

# Smart Contract Addresses (will be set after deployment)
SMART_CONTRACTS = {
    'trendy_token': {
        'polygon_testnet': '',  # Will be filled after deployment
        'polygon': '',
        'bsc': '',
    },
    'trendy_achievements': {
        'polygon_testnet': '',  # Will be filled after deployment
        'polygon': '',
        'bsc': '',
    },
}

# AI Writing Assistant Settings
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY', '')  # Set this in your environment variables
ANTHROPIC_API_KEY = os.environ.get('ANTHROPIC_API_KEY', '')  # Alternative AI provider
