from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .services import BlockchainService
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=User)
def create_blockchain_wallet(sender, instance, created, **kwargs):
    """Create an inactive blockchain wallet when a new user is created"""
    if created:
        try:
            # Check if blockchain networks are set up
            from .models import BlockchainNetwork
            from django.conf import settings

            default_network_name = getattr(settings, 'DEFAULT_BLOCKCHAIN_NETWORK', 'polygon_testnet')

            if not BlockchainNetwork.objects.filter(name=default_network_name, is_active=True).exists():
                logger.warning(f"No blockchain networks configured. Skipping wallet creation for user {instance.username}. Run 'python manage.py setup_blockchain' to set up blockchain networks.")
                return

            # Create inactive blockchain wallet for new users
            blockchain_service = BlockchainService()
            wallet = blockchain_service.create_user_wallet(instance, is_active=False)

            if wallet:
                logger.info(f"Created inactive blockchain wallet for user {instance.username}: {wallet.address}")

                # Send activation code (in production, this would send an email)
                activation_code, message = blockchain_service.send_activation_code(instance)
                if activation_code:
                    logger.info(f"Activation code generated for {instance.username}: {activation_code}")
                else:
                    logger.error(f"Failed to generate activation code for {instance.username}: {message}")
            else:
                logger.error(f"Failed to create blockchain wallet for user {instance.username}")

        except Exception as e:
            logger.error(f"Error creating blockchain wallet for user {instance.username}: {str(e)}")
