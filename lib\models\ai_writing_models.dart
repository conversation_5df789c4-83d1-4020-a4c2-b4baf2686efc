import 'package:freezed_annotation/freezed_annotation.dart';

part 'ai_writing_models.freezed.dart';
part 'ai_writing_models.g.dart';

@freezed
class AIWritingPreferences with _$AIWritingPreferences {
  const factory AIWritingPreferences({
    @Default('professional') String preferredTone,
    @Default('blog') String preferredStyle,
    @Default('general audience') String targetAudience,
    @Default(true) bool enableGrammarSuggestions,
    @Default(true) bool enableSeoSuggestions,
    @Default(true) bool enableContentGeneration,
    @Default(true) bool enableReadabilityAnalysis,
    @Default(true) bool enableAutoComplete,
    @Default(800) int preferredWordCount,
    @Default(true) bool includeReferences,
    @Default(true) bool includeImagesSuggestions,
  }) = _AIWritingPreferences;

  factory AIWritingPreferences.fromJson(Map<String, dynamic> json) =>
      _$AIWritingPreferencesFromJson(json);
}

@freezed
class ContentIdea with _$ContentIdea {
  const factory ContentIdea({
    required String title,
    String? description,
    List<String>? tags,
  }) = _ContentIdea;

  factory ContentIdea.fromJson(Map<String, dynamic> json) =>
      _$ContentIdeaFromJson(json);
}

@freezed
class ContentOutline with _$ContentOutline {
  const factory ContentOutline({
    required List<String> introduction,
    required List<OutlineSection> mainSections,
    required List<String> conclusion,
    required int estimatedWordCount,
  }) = _ContentOutline;

  factory ContentOutline.fromJson(Map<String, dynamic> json) =>
      _$ContentOutlineFromJson(json);
}

@freezed
class OutlineSection with _$OutlineSection {
  const factory OutlineSection({
    required String title,
    required List<String> points,
  }) = _OutlineSection;

  factory OutlineSection.fromJson(Map<String, dynamic> json) =>
      _$OutlineSectionFromJson(json);
}

@freezed
class GrammarImprovement with _$GrammarImprovement {
  const factory GrammarImprovement({
    required String improvedText,
    required List<TextChange> changes,
    required double readabilityScore,
  }) = _GrammarImprovement;

  factory GrammarImprovement.fromJson(Map<String, dynamic> json) =>
      _$GrammarImprovementFromJson(json);
}

@freezed
class TextChange with _$TextChange {
  const factory TextChange({
    required String original,
    required String improved,
    required String reason,
  }) = _TextChange;

  factory TextChange.fromJson(Map<String, dynamic> json) =>
      _$TextChangeFromJson(json);
}

@freezed
class SEOSuggestions with _$SEOSuggestions {
  const factory SEOSuggestions({
    required List<String> titleSuggestions,
    required String metaDescription,
    required List<String> keywords,
    required List<String> contentSuggestions,
    required List<String> readabilityIssues,
  }) = _SEOSuggestions;

  factory SEOSuggestions.fromJson(Map<String, dynamic> json) =>
      _$SEOSuggestionsFromJson(json);
}

@freezed
class ReadabilityAnalysis with _$ReadabilityAnalysis {
  const factory ReadabilityAnalysis({
    required int wordCount,
    required int sentenceCount,
    required int paragraphCount,
    required double avgWordsPerSentence,
    required double avgSentencesPerParagraph,
    required double readabilityScore,
    required String readingLevel,
    required int estimatedReadingTime,
    required List<String> suggestions,
  }) = _ReadabilityAnalysis;

  factory ReadabilityAnalysis.fromJson(Map<String, dynamic> json) =>
      _$ReadabilityAnalysisFromJson(json);
}

@freezed
class AIWritingSession with _$AIWritingSession {
  const factory AIWritingSession({
    required String id,
    String? postId,
    required Map<String, dynamic> sessionData,
    @Default('active') String status,
    @Default(0) int suggestionsGenerated,
    @Default(0) int suggestionsAccepted,
    @Default(0) int wordsGenerated,
    @Default(0) int timeSavedMinutes,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _AIWritingSession;

  factory AIWritingSession.fromJson(Map<String, dynamic> json) =>
      _$AIWritingSessionFromJson(json);
}

@freezed
class ContentSuggestion with _$ContentSuggestion {
  const factory ContentSuggestion({
    required String id,
    required String sessionId,
    required String suggestionType,
    required String originalText,
    required String suggestedText,
    required String explanation,
    required double confidenceScore,
    int? startPosition,
    int? endPosition,
    @Default('pending') String status,
    String? userFeedback,
    required DateTime createdAt,
  }) = _ContentSuggestion;

  factory ContentSuggestion.fromJson(Map<String, dynamic> json) =>
      _$ContentSuggestionFromJson(json);
}

@freezed
class ContentTemplate with _$ContentTemplate {
  const factory ContentTemplate({
    required String id,
    required String name,
    required String description,
    required String templateContent,
    required String category,
    required int estimatedWordCount,
    required String difficultyLevel,
    required bool isPublic,
    required int usageCount,
    required DateTime createdAt,
  }) = _ContentTemplate;

  factory ContentTemplate.fromJson(Map<String, dynamic> json) =>
      _$ContentTemplateFromJson(json);
}

@freezed
class AIUsageAnalytics with _$AIUsageAnalytics {
  const factory AIUsageAnalytics({
    required String featureUsed,
    required int usageCount,
    required int totalTimeSavedMinutes,
    required int totalWordsGenerated,
    required int year,
    required int month,
  }) = _AIUsageAnalytics;

  factory AIUsageAnalytics.fromJson(Map<String, dynamic> json) =>
      _$AIUsageAnalyticsFromJson(json);
}

// Request models
@freezed
class ContentIdeaRequest with _$ContentIdeaRequest {
  const factory ContentIdeaRequest({
    required String topic,
    @Default(5) int count,
  }) = _ContentIdeaRequest;

  factory ContentIdeaRequest.fromJson(Map<String, dynamic> json) =>
      _$ContentIdeaRequestFromJson(json);
}

@freezed
class ContentOutlineRequest with _$ContentOutlineRequest {
  const factory ContentOutlineRequest({
    required String title,
  }) = _ContentOutlineRequest;

  factory ContentOutlineRequest.fromJson(Map<String, dynamic> json) =>
      _$ContentOutlineRequestFromJson(json);
}

@freezed
class GrammarImprovementRequest with _$GrammarImprovementRequest {
  const factory GrammarImprovementRequest({
    required String text,
  }) = _GrammarImprovementRequest;

  factory GrammarImprovementRequest.fromJson(Map<String, dynamic> json) =>
      _$GrammarImprovementRequestFromJson(json);
}

@freezed
class SEOSuggestionsRequest with _$SEOSuggestionsRequest {
  const factory SEOSuggestionsRequest({
    required String content,
    String? title,
  }) = _SEOSuggestionsRequest;

  factory SEOSuggestionsRequest.fromJson(Map<String, dynamic> json) =>
      _$SEOSuggestionsRequestFromJson(json);
}

@freezed
class TextCompletionRequest with _$TextCompletionRequest {
  const factory TextCompletionRequest({
    required String partialText,
    String? context,
  }) = _TextCompletionRequest;

  factory TextCompletionRequest.fromJson(Map<String, dynamic> json) =>
      _$TextCompletionRequestFromJson(json);
}

@freezed
class ReadabilityAnalysisRequest with _$ReadabilityAnalysisRequest {
  const factory ReadabilityAnalysisRequest({
    required String content,
  }) = _ReadabilityAnalysisRequest;

  factory ReadabilityAnalysisRequest.fromJson(Map<String, dynamic> json) =>
      _$ReadabilityAnalysisRequestFromJson(json);
}
