#!/usr/bin/env python
"""
Test script to verify notifications API is working
"""
import requests
import json

# API base URL
BASE_URL = "http://localhost:8000/api/v1/accounts"

def test_notifications_api():
    """Test the notifications API endpoints"""
    
    print("🔔 Testing Notifications API")
    print("=" * 50)
    
    # Test login first to get token
    print("\n1. Logging in to get authentication token...")
    login_data = {
        "email_or_username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/login/", json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            print(f"   ✅ Login successful, token: {token[:20]}...")
        else:
            print(f"   ❌ Login failed: {login_response.status_code}")
            print(f"   Response: {login_response.text}")
            return
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return
    
    # Set up headers with token
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test getting notifications
    print("\n2. Fetching notifications...")
    try:
        notifications_response = requests.get(f"{BASE_URL}/notifications/", headers=headers)
        if notifications_response.status_code == 200:
            notifications_data = notifications_response.json()
            print(f"   ✅ Notifications fetched successfully")
            print(f"   📊 Total notifications: {notifications_data.get('count', 0)}")
            
            results = notifications_data.get('results', [])
            if results:
                print(f"   📋 First few notifications:")
                for i, notification in enumerate(results[:3]):
                    print(f"      {i+1}. {notification.get('title')} - {notification.get('notification_type')}")
                    print(f"         📝 {notification.get('message')}")
                    print(f"         📅 {notification.get('created_at')}")
                    print(f"         👁️ Read: {notification.get('is_read')}")
                    print()
            else:
                print("   📭 No notifications found")
        else:
            print(f"   ❌ Failed to fetch notifications: {notifications_response.status_code}")
            print(f"   Response: {notifications_response.text}")
    except Exception as e:
        print(f"   ❌ Error fetching notifications: {e}")
    
    # Test notification count
    print("\n3. Getting notification count...")
    try:
        count_response = requests.get(f"{BASE_URL}/notifications/count/", headers=headers)
        if count_response.status_code == 200:
            count_data = count_response.json()
            print(f"   ✅ Unread notifications: {count_data.get('unread_count', 0)}")
        else:
            print(f"   ❌ Failed to get notification count: {count_response.status_code}")
    except Exception as e:
        print(f"   ❌ Error getting notification count: {e}")
    
    # Test marking all as read
    print("\n4. Testing mark all as read...")
    try:
        mark_read_response = requests.post(f"{BASE_URL}/notifications/mark-all-read/", headers=headers)
        if mark_read_response.status_code == 200:
            print(f"   ✅ All notifications marked as read")
            
            # Check count again
            count_response = requests.get(f"{BASE_URL}/notifications/count/", headers=headers)
            if count_response.status_code == 200:
                count_data = count_response.json()
                print(f"   📊 Unread notifications after marking all read: {count_data.get('unread_count', 0)}")
        else:
            print(f"   ❌ Failed to mark all as read: {mark_read_response.status_code}")
    except Exception as e:
        print(f"   ❌ Error marking all as read: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Notifications API test completed!")

if __name__ == '__main__':
    test_notifications_api()
