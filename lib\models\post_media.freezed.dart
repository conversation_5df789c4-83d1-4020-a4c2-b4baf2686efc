// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'post_media.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PostMedia _$PostMediaFromJson(Map<String, dynamic> json) {
  return _PostMedia.fromJson(json);
}

/// @nodoc
mixin _$PostMedia {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'media_type')
  String get mediaType => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  @JsonKey(name: 'image_url')
  String? get imageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'image_url_full')
  String? get imageUrlFull => throw _privateConstructorUsedError;
  String? get video => throw _privateConstructorUsedError;
  @JsonKey(name: 'video_url')
  String? get videoUrl => throw _privateConstructorUsedError;
  String? get thumbnail => throw _privateConstructorUsedError;
  String get caption => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int get order => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PostMediaCopyWith<PostMedia> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PostMediaCopyWith<$Res> {
  factory $PostMediaCopyWith(PostMedia value, $Res Function(PostMedia) then) =
      _$PostMediaCopyWithImpl<$Res, PostMedia>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'media_type') String mediaType,
      String? image,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'image_url_full') String? imageUrlFull,
      String? video,
      @JsonKey(name: 'video_url') String? videoUrl,
      String? thumbnail,
      String caption,
      String? title,
      String? description,
      int order,
      @JsonKey(name: 'created_at') DateTime createdAt});
}

/// @nodoc
class _$PostMediaCopyWithImpl<$Res, $Val extends PostMedia>
    implements $PostMediaCopyWith<$Res> {
  _$PostMediaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mediaType = null,
    Object? image = freezed,
    Object? imageUrl = freezed,
    Object? imageUrlFull = freezed,
    Object? video = freezed,
    Object? videoUrl = freezed,
    Object? thumbnail = freezed,
    Object? caption = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? order = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      mediaType: null == mediaType
          ? _value.mediaType
          : mediaType // ignore: cast_nullable_to_non_nullable
              as String,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrlFull: freezed == imageUrlFull
          ? _value.imageUrlFull
          : imageUrlFull // ignore: cast_nullable_to_non_nullable
              as String?,
      video: freezed == video
          ? _value.video
          : video // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      caption: null == caption
          ? _value.caption
          : caption // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PostMediaImplCopyWith<$Res>
    implements $PostMediaCopyWith<$Res> {
  factory _$$PostMediaImplCopyWith(
          _$PostMediaImpl value, $Res Function(_$PostMediaImpl) then) =
      __$$PostMediaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'media_type') String mediaType,
      String? image,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'image_url_full') String? imageUrlFull,
      String? video,
      @JsonKey(name: 'video_url') String? videoUrl,
      String? thumbnail,
      String caption,
      String? title,
      String? description,
      int order,
      @JsonKey(name: 'created_at') DateTime createdAt});
}

/// @nodoc
class __$$PostMediaImplCopyWithImpl<$Res>
    extends _$PostMediaCopyWithImpl<$Res, _$PostMediaImpl>
    implements _$$PostMediaImplCopyWith<$Res> {
  __$$PostMediaImplCopyWithImpl(
      _$PostMediaImpl _value, $Res Function(_$PostMediaImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? mediaType = null,
    Object? image = freezed,
    Object? imageUrl = freezed,
    Object? imageUrlFull = freezed,
    Object? video = freezed,
    Object? videoUrl = freezed,
    Object? thumbnail = freezed,
    Object? caption = null,
    Object? title = freezed,
    Object? description = freezed,
    Object? order = null,
    Object? createdAt = null,
  }) {
    return _then(_$PostMediaImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      mediaType: null == mediaType
          ? _value.mediaType
          : mediaType // ignore: cast_nullable_to_non_nullable
              as String,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrlFull: freezed == imageUrlFull
          ? _value.imageUrlFull
          : imageUrlFull // ignore: cast_nullable_to_non_nullable
              as String?,
      video: freezed == video
          ? _value.video
          : video // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      caption: null == caption
          ? _value.caption
          : caption // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      order: null == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PostMediaImpl implements _PostMedia {
  const _$PostMediaImpl(
      {required this.id,
      @JsonKey(name: 'media_type') this.mediaType = 'image',
      this.image,
      @JsonKey(name: 'image_url') this.imageUrl,
      @JsonKey(name: 'image_url_full') this.imageUrlFull,
      this.video,
      @JsonKey(name: 'video_url') this.videoUrl,
      this.thumbnail,
      this.caption = '',
      this.title,
      this.description,
      this.order = 0,
      @JsonKey(name: 'created_at') required this.createdAt});

  factory _$PostMediaImpl.fromJson(Map<String, dynamic> json) =>
      _$$PostMediaImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'media_type')
  final String mediaType;
  @override
  final String? image;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey(name: 'image_url_full')
  final String? imageUrlFull;
  @override
  final String? video;
  @override
  @JsonKey(name: 'video_url')
  final String? videoUrl;
  @override
  final String? thumbnail;
  @override
  @JsonKey()
  final String caption;
  @override
  final String? title;
  @override
  final String? description;
  @override
  @JsonKey()
  final int order;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @override
  String toString() {
    return 'PostMedia(id: $id, mediaType: $mediaType, image: $image, imageUrl: $imageUrl, imageUrlFull: $imageUrlFull, video: $video, videoUrl: $videoUrl, thumbnail: $thumbnail, caption: $caption, title: $title, description: $description, order: $order, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PostMediaImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.mediaType, mediaType) ||
                other.mediaType == mediaType) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.imageUrlFull, imageUrlFull) ||
                other.imageUrlFull == imageUrlFull) &&
            (identical(other.video, video) || other.video == video) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.thumbnail, thumbnail) ||
                other.thumbnail == thumbnail) &&
            (identical(other.caption, caption) || other.caption == caption) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      mediaType,
      image,
      imageUrl,
      imageUrlFull,
      video,
      videoUrl,
      thumbnail,
      caption,
      title,
      description,
      order,
      createdAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PostMediaImplCopyWith<_$PostMediaImpl> get copyWith =>
      __$$PostMediaImplCopyWithImpl<_$PostMediaImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PostMediaImplToJson(
      this,
    );
  }
}

abstract class _PostMedia implements PostMedia {
  const factory _PostMedia(
          {required final int id,
          @JsonKey(name: 'media_type') final String mediaType,
          final String? image,
          @JsonKey(name: 'image_url') final String? imageUrl,
          @JsonKey(name: 'image_url_full') final String? imageUrlFull,
          final String? video,
          @JsonKey(name: 'video_url') final String? videoUrl,
          final String? thumbnail,
          final String caption,
          final String? title,
          final String? description,
          final int order,
          @JsonKey(name: 'created_at') required final DateTime createdAt}) =
      _$PostMediaImpl;

  factory _PostMedia.fromJson(Map<String, dynamic> json) =
      _$PostMediaImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'media_type')
  String get mediaType;
  @override
  String? get image;
  @override
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @override
  @JsonKey(name: 'image_url_full')
  String? get imageUrlFull;
  @override
  String? get video;
  @override
  @JsonKey(name: 'video_url')
  String? get videoUrl;
  @override
  String? get thumbnail;
  @override
  String get caption;
  @override
  String? get title;
  @override
  String? get description;
  @override
  int get order;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @JsonKey(ignore: true)
  _$$PostMediaImplCopyWith<_$PostMediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
