import 'package:dio/dio.dart';
import '../config/api_config.dart';
import '../models/interactive_block.dart';
import '../models/poll.dart';
import '../models/quiz.dart';
import '../services/platform_storage_service.dart';

class InteractiveService {
  late final Dio _dio;

  InteractiveService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      sendTimeout: ApiConfig.sendTimeout,
      validateStatus: (status) {
        return status != null && status < 500;
      },
    ));

    // Add interceptor to automatically include authentication headers
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _getToken();
        print('DEBUG INTERCEPTOR: Token ${token != null ? "EXISTS" : "NULL"}');
        if (token != null) {
          options.headers['Authorization'] = 'Token $token';
          print('DEBUG INTERCEPTOR: Added Authorization header');
        } else {
          print('DEBUG INTERCEPTOR: No token found, skipping auth header');
        }
        print('DEBUG INTERCEPTOR: Request URL: ${options.uri}');
        print('DEBUG INTERCEPTOR: Request headers: ${options.headers}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        print('DEBUG INTERCEPTOR: Response status: ${response.statusCode}');
        handler.next(response);
      },
      onError: (error, handler) {
        print('DEBUG INTERCEPTOR: Error: ${error.message}');
        print('DEBUG INTERCEPTOR: Error response: ${error.response?.data}');
        handler.next(error);
      },
    ));
  }

  Future<String?> _getToken() async {
    return await PlatformStorageService.getSecureData('token');
  }

  // Debug method to check authentication
  Future<Map<String, dynamic>> debugPollAuth(int pollId) async {
    try {
      // Debug: Check token before making request
      final token = await _getToken();
      print(
          'DEBUG: Token for debug request: ${token != null ? "EXISTS" : "NULL"}');

      final response =
          await _dio.get('/api/v1/interactive/polls/$pollId/debug/');

      print('DEBUG: Debug response status: ${response.statusCode}');
      print('DEBUG: Debug response data: ${response.data}');

      if (response.statusCode == 200) {
        return response.data;
      }

      throw Exception('Failed to debug poll auth');
    } catch (e) {
      print('Error debugging poll auth: $e');
      rethrow;
    }
  }

  // Get all interactive blocks for a post
  Future<List<InteractiveBlock>> getPostInteractiveBlocks(int postId) async {
    try {
      final response =
          await _dio.get('/api/v1/interactive/posts/$postId/blocks/');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => InteractiveBlock.fromJson(json)).toList();
      }

      throw Exception('Failed to load interactive blocks');
    } catch (e) {
      print('Error fetching interactive blocks: $e');
      rethrow;
    }
  }

  // Get a specific interactive block
  Future<InteractiveBlock> getInteractiveBlock(int blockId) async {
    try {
      final response = await _dio.get('/api/v1/interactive/blocks/$blockId/');

      if (response.statusCode == 200) {
        return InteractiveBlock.fromJson(response.data);
      }

      throw Exception('Failed to load interactive block');
    } catch (e) {
      print('Error fetching interactive block: $e');
      rethrow;
    }
  }

  // Vote on a poll
  Future<Poll> votePoll(int pollId, List<int> optionIds) async {
    try {
      // Debug: Check token before making request
      final token = await _getToken();
      print(
          'DEBUG: Token for poll voting: ${token != null ? "EXISTS" : "NULL"}');
      print('DEBUG: Voting on poll $pollId with options: $optionIds');

      final response = await _dio.post(
        '/api/v1/interactive/polls/$pollId/vote/',
        data: {
          'option_ids': optionIds,
        },
      );

      print('DEBUG: Vote response status: ${response.statusCode}');
      print('DEBUG: Vote response data: ${response.data}');

      if (response.statusCode == 200) {
        return Poll.fromJson(response.data['poll']);
      }

      throw Exception('Failed to vote on poll');
    } catch (e) {
      print('Error voting on poll: $e');
      rethrow;
    }
  }

  // Get poll results
  Future<Poll> getPollResults(int pollId) async {
    try {
      final response =
          await _dio.get('/api/v1/interactive/polls/$pollId/results/');

      if (response.statusCode == 200) {
        return Poll.fromJson(response.data);
      }

      throw Exception('Failed to get poll results');
    } catch (e) {
      print('Error fetching poll results: $e');
      rethrow;
    }
  }

  // Submit quiz answers
  Future<QuizAttempt> submitQuiz(
    int quizId,
    Map<String, List<int>> answers,
    int timeTaken,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/interactive/quizzes/$quizId/submit/',
        data: {
          'answers': answers,
          'time_taken': timeTaken,
        },
      );

      if (response.statusCode == 200) {
        return QuizAttempt.fromJson(response.data['attempt']);
      }

      throw Exception('Failed to submit quiz');
    } catch (e) {
      print('Error submitting quiz: $e');
      rethrow;
    }
  }

  // Get user's quiz attempts
  Future<List<QuizAttempt>> getQuizAttempts(int quizId) async {
    try {
      final response =
          await _dio.get('/api/v1/interactive/quizzes/$quizId/attempts/');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return data.map((json) => QuizAttempt.fromJson(json)).toList();
      }

      throw Exception('Failed to load quiz attempts');
    } catch (e) {
      print('Error fetching quiz attempts: $e');
      rethrow;
    }
  }

  // Create a new interactive block (for authors)
  Future<InteractiveBlock> createInteractiveBlock(
    int postId,
    InteractiveBlockType blockType,
    String title,
    String description,
    int position, {
    CreatePoll? pollData,
    Map<String, dynamic>? quizData,
    Map<String, dynamic>? codeData,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final data = {
        'block_type': blockType.value,
        'title': title,
        'description': description,
        'position': position,
        'metadata': metadata ?? {},
      };

      if (pollData != null && blockType == InteractiveBlockType.poll) {
        data['poll_data'] = pollData.toJson();
      }

      if (quizData != null && blockType == InteractiveBlockType.quiz) {
        data['quiz_data'] = quizData;
      }

      if (codeData != null && blockType == InteractiveBlockType.code) {
        data['code_data'] = codeData;
      }

      final response = await _dio.post(
        '/api/v1/interactive/posts/$postId/blocks/create/',
        data: data,
      );

      if (response.statusCode == 201) {
        return InteractiveBlock.fromJson(response.data);
      }

      throw Exception('Failed to create interactive block');
    } catch (e) {
      print('Error creating interactive block: $e');
      rethrow;
    }
  }

  // Delete an interactive block (for authors)
  Future<void> deleteInteractiveBlock(int blockId) async {
    try {
      final response =
          await _dio.delete('/api/v1/interactive/blocks/$blockId/delete/');

      if (response.statusCode != 200) {
        throw Exception('Failed to delete interactive block');
      }
    } catch (e) {
      print('Error deleting interactive block: $e');
      rethrow;
    }
  }

  // Helper method to check if user has voted on a poll
  bool hasUserVoted(Poll poll) {
    return poll.userVote.isNotEmpty;
  }

  // Helper method to get user's selected options
  List<int> getUserSelectedOptions(Poll poll) {
    return poll.userVote.map((vote) => vote.option).toList();
  }

  // Helper method to calculate quiz score percentage
  double calculateQuizScorePercentage(QuizAttempt attempt) {
    if (attempt.totalPoints == 0) return 0.0;
    return (attempt.earnedPoints / attempt.totalPoints) * 100;
  }

  // Helper method to format time taken
  String formatTimeTaken(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '${minutes}m ${remainingSeconds}s';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '${hours}h ${minutes}m';
    }
  }

  // Helper method to get quiz difficulty level
  String getQuizDifficultyLevel(Quiz quiz) {
    if (quiz.passingScore >= 90) return 'Expert';
    if (quiz.passingScore >= 80) return 'Advanced';
    if (quiz.passingScore >= 70) return 'Intermediate';
    if (quiz.passingScore >= 60) return 'Beginner';
    return 'Easy';
  }

  // Helper method to get poll status
  String getPollStatus(Poll poll) {
    if (!poll.isActive) return 'Inactive';
    if (poll.isExpired) return 'Expired';
    if (hasUserVoted(poll)) return 'Voted';
    return 'Active';
  }
}
