#!/usr/bin/env python3
"""
🔍 Trendy App Setup Verification Script
This script verifies that the development environment is properly set up.
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_status(message, status="info"):
    colors = {
        "success": "\033[92m✅",
        "error": "\033[91m❌", 
        "warning": "\033[93m⚠️",
        "info": "\033[94mℹ️"
    }
    reset = "\033[0m"
    print(f"{colors.get(status, colors['info'])} {message}{reset}")

def check_file_exists(file_path, description):
    if os.path.exists(file_path):
        print_status(f"{description} exists", "success")
        return True
    else:
        print_status(f"{description} missing: {file_path}", "error")
        return False

def check_django_setup():
    print("\n🔍 Checking Django setup...")
    
    django_dir = "trendy_web_and_api/trendy"
    if not os.path.exists(django_dir):
        print_status("Django directory not found", "error")
        return False
    
    # Check key files
    checks = [
        (f"{django_dir}/manage.py", "Django manage.py"),
        (f"{django_dir}/venv", "Virtual environment"),
        (f"{django_dir}/requirements.txt", "Requirements file"),
        (f"{django_dir}/fixtures", "Fixtures directory"),
        (f"{django_dir}/fixtures/users_data.json", "Users fixture"),
        (f"{django_dir}/fixtures/wallet_data.json", "Wallet fixture"),
        (f"{django_dir}/fixtures/blog_data.json", "Blog fixture"),
    ]
    
    all_good = True
    for file_path, description in checks:
        if not check_file_exists(file_path, description):
            all_good = False
    
    return all_good

def check_flutter_setup():
    print("\n🔍 Checking Flutter setup...")
    
    checks = [
        ("pubspec.yaml", "Flutter pubspec.yaml"),
        ("lib/main.dart", "Flutter main.dart"),
        ("lib/config/api_config.dart", "API configuration"),
        ("lib/services/api_service.dart", "API service"),
    ]
    
    all_good = True
    for file_path, description in checks:
        if not check_file_exists(file_path, description):
            all_good = False
    
    return all_good

def check_api_config():
    print("\n🔍 Checking API configuration...")
    
    try:
        with open("lib/config/api_config.dart", "r") as f:
            content = f.read()
            
        if "**************:8000" in content:
            print_status("API configured for network access", "success")
            return True
        elif "localhost" in content or "127.0.0.1" in content:
            print_status("API configured for local access only", "warning")
            return True
        else:
            print_status("API configuration unclear", "warning")
            return False
            
    except Exception as e:
        print_status(f"Could not read API config: {e}", "error")
        return False

def check_git_config():
    print("\n🔍 Checking Git configuration...")
    
    if not check_file_exists(".gitignore", "Git ignore file"):
        return False
    
    try:
        with open(".gitignore", "r") as f:
            content = f.read()
        
        if "*.sqlite3" in content:
            print_status("SQLite files properly ignored", "success")
        else:
            print_status("SQLite files not ignored - may cause conflicts", "warning")
        
        if "fixtures/" in content and "!trendy_web_and_api/trendy/fixtures/" in content:
            print_status("Fixtures properly configured for Git", "success")
        else:
            print_status("Fixtures Git configuration unclear", "warning")
            
        return True
        
    except Exception as e:
        print_status(f"Could not read .gitignore: {e}", "error")
        return False

def check_setup_scripts():
    print("\n🔍 Checking setup scripts...")
    
    checks = [
        ("setup_new_machine.sh", "Automated setup script"),
        ("SETUP_INSTRUCTIONS.md", "Setup instructions"),
        ("DATABASE_SETUP.md", "Database setup guide"),
        ("TEAM_COLLABORATION_GUIDE.md", "Team collaboration guide"),
    ]
    
    all_good = True
    for file_path, description in checks:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # Check if setup script is executable
    if os.path.exists("setup_new_machine.sh"):
        if os.access("setup_new_machine.sh", os.X_OK):
            print_status("Setup script is executable", "success")
        else:
            print_status("Setup script not executable (run: chmod +x setup_new_machine.sh)", "warning")
    
    return all_good

def main():
    print("🚀 Trendy App Setup Verification")
    print("=" * 50)
    
    # Change to project root if we're in a subdirectory
    if os.path.exists("trendy_web_and_api"):
        os.chdir(".")
    elif os.path.exists("../trendy_web_and_api"):
        os.chdir("..")
    elif os.path.exists("../../trendy_web_and_api"):
        os.chdir("../..")
    
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Run all checks
    checks = [
        check_django_setup(),
        check_flutter_setup(), 
        check_api_config(),
        check_git_config(),
        check_setup_scripts(),
    ]
    
    print("\n" + "=" * 50)
    
    if all(checks):
        print_status("🎉 All checks passed! Setup looks good.", "success")
        print("\n📋 Next steps:")
        print("   1. cd trendy_web_and_api/trendy")
        print("   2. source venv/bin/activate")
        print("   3. python manage.py runserver 0.0.0.0:8000")
        print("   4. In another terminal: flutter run")
        return 0
    else:
        print_status("❌ Some checks failed. Please review the issues above.", "error")
        print("\n🛠️ To fix issues:")
        print("   1. Run: ./setup_new_machine.sh")
        print("   2. Or follow SETUP_INSTRUCTIONS.md")
        return 1

if __name__ == "__main__":
    sys.exit(main())
