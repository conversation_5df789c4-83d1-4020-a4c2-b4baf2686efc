#!/bin/bash

# 🧹 Trendy App System Reset Script
# This script completely resets the database, migrations, and adds fresh sample data

set -e  # Exit on any error

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🧹 Trendy App System Reset Script${NC}"
echo "=========================================="
echo -e "${YELLOW}⚠️  WARNING: This will DELETE ALL DATA!${NC}"
echo ""
echo "This script will:"
echo "  1. 🗑️  Delete all migrations"
echo "  2. 🗑️  Delete database file"
echo "  3. 🆕 Create fresh migrations"
echo "  4. 🆕 Apply migrations"
echo "  5. 👥 Create sample users"
echo "  6. 📊 Add sample data"
echo "  7. 💾 Create backup"
echo ""

# Confirmation prompt
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}Operation cancelled.${NC}"
    exit 1
fi

echo -e "${BLUE}🚀 Starting system reset...${NC}"

# Navigate to Django project directory
cd trendy_web_and_api/trendy

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${RED}❌ Virtual environment not found!${NC}"
    echo -e "${BLUE}Creating virtual environment...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate
echo -e "${GREEN}✅ Virtual environment activated${NC}"

# Step 1: Delete all migrations
echo -e "${BLUE}🗑️  Step 1: Deleting all migrations...${NC}"
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
find . -path "*/migrations/*.pyc" -delete
echo -e "${GREEN}✅ Migrations deleted${NC}"

# Step 2: Delete database
echo -e "${BLUE}🗑️  Step 2: Deleting database...${NC}"
if [ -f "db.sqlite3" ]; then
    rm -f db.sqlite3
    echo -e "${GREEN}✅ Database deleted${NC}"
else
    echo -e "${YELLOW}⚠️  Database file not found${NC}"
fi

# Step 3: Create fresh migrations
echo -e "${BLUE}🆕 Step 3: Creating fresh migrations...${NC}"
python manage.py makemigrations
echo -e "${GREEN}✅ Fresh migrations created${NC}"

# Step 4: Apply migrations
echo -e "${BLUE}🆕 Step 4: Applying migrations...${NC}"
python manage.py migrate
echo -e "${GREEN}✅ Migrations applied${NC}"

# Step 5: Create sample users and data
echo -e "${BLUE}👥 Step 5: Creating sample users and data...${NC}"
python manage.py shell -c "
from accounts.models import CustomUser
from wallet.models import UserWallet, WalletTransaction
from blog.models import Category, Post
from gamification.models import UserLevel, PointTransaction
from monetization.models import MonetizationSettings
from django.contrib.auth.hashers import make_password
from decimal import Decimal

print('👥 Creating users...')

# Create admin user
admin_user = CustomUser.objects.create(
    username='admin',
    email='<EMAIL>',
    password=make_password('admin123'),
    first_name='Admin',
    last_name='User',
    is_staff=True,
    is_superuser=True,
    is_active=True,
    is_email_verified=True
)
print(f'✅ Created admin: admin / admin123')

# Create test users
test_users_data = [
    ('sarah_johnson', '<EMAIL>', 'Sarah', 'Johnson'),
    ('mike_chen', '<EMAIL>', 'Mike', 'Chen'),
    ('alex_rivera', '<EMAIL>', 'Alex', 'Rivera'),
    ('testuser', '<EMAIL>', 'Test', 'User'),
    ('demo_user', '<EMAIL>', 'Demo', 'User')
]

created_users = []
for username, email, first_name, last_name in test_users_data:
    user = CustomUser.objects.create(
        username=username,
        email=email,
        password=make_password('password123'),
        first_name=first_name,
        last_name=last_name,
        is_active=True,
        is_email_verified=True
    )
    created_users.append(user)
    print(f'✅ Created user: {username} / password123')

print(f'💰 Adding money to wallets...')
# Fund all wallets
all_users = [admin_user] + created_users
for user in all_users:
    wallet = UserWallet.objects.get(user=user)
    wallet.balance = Decimal('100.00')
    wallet.save()
    
    WalletTransaction.objects.create(
        wallet=wallet,
        transaction_type='credit',
        amount=Decimal('100.00'),
        balance_before=Decimal('0.00'),
        balance_after=wallet.balance,
        purpose='Initial Balance',
        description='System reset - starting balance',
        status='completed'
    )
    print(f'✅ Added \$100.00 to {user.username}')

print(f'📝 Creating blog content...')
# Create categories
categories_data = [
    ('Technology', 'Latest tech trends and innovations'),
    ('Lifestyle', 'Tips for better living'),
    ('Business', 'Business insights and strategies'),
    ('Health', 'Health and wellness tips'),
    ('Travel', 'Travel guides and experiences'),
    ('Entertainment', 'Movies, music, and fun content'),
    ('Education', 'Learning and development resources')
]

for name, description in categories_data:
    category, created = Category.objects.get_or_create(
        name=name,
        defaults={'description': description}
    )
    if created:
        print(f'✅ Created category: {name}')

# Create sample posts
tech_category = Category.objects.get(name='Technology')
lifestyle_category = Category.objects.get(name='Lifestyle')
business_category = Category.objects.get(name='Business')

posts_data = [
    {
        'title': 'Welcome to Trendy App - Your Social Money Platform',
        'content': 'Welcome to Trendy! Here you can earn real money by reading posts, engaging with content, and inviting friends. Start your journey to financial freedom today!',
        'category': tech_category,
        'author': admin_user,
        'status': 'published'
    },
    {
        'title': 'How to Earn Points and Money',
        'content': 'Discover all the ways to earn points: read posts (+10 points), comment (+5 points), like posts (+2 points), and refer friends (+100 points). Convert points to real money!',
        'category': lifestyle_category,
        'author': admin_user,
        'status': 'published'
    },
    {
        'title': 'Wallet Features and Premium Benefits',
        'content': 'Your wallet stores real money! Add funds, make purchases, and withdraw earnings. Premium users get 2x points and exclusive features for just \$9.99/month.',
        'category': business_category,
        'author': admin_user,
        'status': 'published'
    },
    {
        'title': 'Referral Program - Earn \$5 per Friend',
        'content': 'Invite friends and earn money! Get \$2 when they join, \$2 when they reach level 5, and \$5 when they go premium. Share your code and start earning!',
        'category': business_category,
        'author': created_users[0] if created_users else admin_user,
        'status': 'published'
    },
    {
        'title': 'Community Guidelines and Best Practices',
        'content': 'Be respectful, engage meaningfully, and help build a positive community. Quality interactions earn more points than quantity!',
        'category': lifestyle_category,
        'author': created_users[1] if len(created_users) > 1 else admin_user,
        'status': 'published'
    }
]

for post_data in posts_data:
    post = Post.objects.create(**post_data)
    print(f'✅ Created post: {post.title}')

print(f'🎮 Setting up gamification...')
# Setup gamification for all users
for user in all_users:
    user_level, created = UserLevel.objects.get_or_create(
        user=user,
        defaults={
            'total_points': 150,
            'current_level': 2,
            'reading_streak': 3
        }
    )
    
    if created:
        PointTransaction.objects.create(
            user=user,
            points=150,
            transaction_type='welcome',
            description='System reset - welcome bonus points'
        )
        print(f'✅ Setup gamification for {user.username}')

print(f'⚙️  Setting up monetization...')
# Setup monetization settings
settings = MonetizationSettings.get_settings()
print(f'✅ Monetization settings configured')

print(f'📊 Final summary:')
print(f'   👥 Users: {CustomUser.objects.count()}')
print(f'   💰 Wallets: {UserWallet.objects.count()}')
print(f'   📝 Posts: {Post.objects.count()}')
print(f'   📂 Categories: {Category.objects.count()}')
print(f'   🎮 User Levels: {UserLevel.objects.count()}')
print(f'   💳 Transactions: {WalletTransaction.objects.count()}')
"

echo -e "${GREEN}✅ Sample data created${NC}"

# Step 6: Create backup
echo -e "${BLUE}💾 Step 6: Creating backup...${NC}"
cd ../../
./backup_database.sh
echo -e "${GREEN}✅ Backup created${NC}"

echo ""
echo -e "${PURPLE}🎉 SYSTEM RESET COMPLETED SUCCESSFULLY!${NC}"
echo "=========================================="
echo -e "${GREEN}✅ Fresh database with sample data ready${NC}"
echo ""
echo -e "${BLUE}📊 What's been created:${NC}"
echo "  👥 6 users (1 admin + 5 test users)"
echo "  💰 \$100 in each wallet"
echo "  📝 5 sample blog posts"
echo "  📂 7 content categories"
echo "  🎮 Gamification setup (150 points each)"
echo "  ⚙️  Monetization settings configured"
echo ""
echo -e "${BLUE}🔑 Login credentials:${NC}"
echo "  Admin: admin / admin123"
echo "  Users: sarah_johnson, mike_chen, alex_rivera, testuser, demo_user / password123"
echo ""
echo -e "${BLUE}🌐 Ready to start:${NC}"
echo "  Django: cd trendy_web_and_api/trendy && source venv/bin/activate && python manage.py runserver 0.0.0.0:8000"
echo "  Admin: http://localhost:8000/admin/"
echo "  API: http://localhost:8000/api/v1/"
echo ""
echo -e "${GREEN}Happy coding! 🚀${NC}"
