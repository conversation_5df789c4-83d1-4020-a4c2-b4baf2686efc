import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../models/comment.dart';
import '../providers/provider.dart';
import '../providers/auth_provider.dart';
import 'comment_dialog.dart';
import 'enhanced_login_dialog.dart';

class CommentWidget extends ConsumerStatefulWidget {
  final Comment comment;
  final String slug;
  final int depth;

  const CommentWidget({
    super.key,
    required this.comment,
    required this.slug,
    this.depth = 0,
  });

  @override
  ConsumerState<CommentWidget> createState() => _CommentWidgetState();
}

class _CommentWidgetState extends ConsumerState<CommentWidget> {
  bool _isExpanded = true;
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);
    final isLoggedIn = authState.isAuthenticated;
    final theme = Theme.of(context);

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Padding(
        padding: EdgeInsets.only(left: widget.depth * 16.0),
        child: Card(
          elevation: _isHovered ? 2 : 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: _isHovered ? theme.colorScheme.primary.withOpacity(0.2) : Colors.transparent,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: theme.colorScheme.primary,
                      backgroundImage: widget.comment.author.avatarUrl != null && 
                          widget.comment.author.avatarUrl!.isNotEmpty
                          ? NetworkImage(widget.comment.author.avatarUrl!)
                          : null,
                      child: widget.comment.author.avatarUrl == null || 
                          widget.comment.author.avatarUrl!.isEmpty
                          ? Text(
                              widget.comment.author.username[0].toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.comment.author.username,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            timeago.format(widget.comment.createdAt),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_isHovered && isLoggedIn)
                      IconButton(
                        icon: const Icon(Icons.reply, size: 20),
                        onPressed: () => _showReplyDialog(context),
                        tooltip: 'Reply',
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  widget.comment.content,
                  style: theme.textTheme.bodyLarge,
                ),
                const SizedBox(height: 12),
                _buildLikeReplyRow(context, isLoggedIn),
                if (widget.comment.replies.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildRepliesToggle(),
                ],
                AnimatedCrossFade(
                  firstChild: Column(
                    children: widget.comment.replies.map(
                      (reply) => CommentWidget(
                        comment: reply,
                        slug: widget.slug,
                        depth: widget.depth + 1,
                      ),
                    ).toList(),
                  ),
                  secondChild: const SizedBox.shrink(),
                  crossFadeState: _isExpanded 
                      ? CrossFadeState.showFirst 
                      : CrossFadeState.showSecond,
                  duration: const Duration(milliseconds: 300),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRepliesToggle() {
    final theme = Theme.of(context);
    return TextButton.icon(
      onPressed: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
      icon: Icon(
        _isExpanded ? Icons.expand_less : Icons.expand_more,
        size: 20,
        color: theme.colorScheme.primary,
      ),
      label: Text(
        _isExpanded 
            ? 'Hide ${widget.comment.replies.length} replies'
            : 'Show ${widget.comment.replies.length} replies',
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildLikeReplyRow(BuildContext context, bool isLoggedIn) {
    final theme = Theme.of(context);
    return Row(
      children: [
        if (isLoggedIn)
          IconButton(
            icon: Icon(
              widget.comment.isLiked ? Icons.favorite : Icons.favorite_border,
              size: 20,
              color: widget.comment.isLiked ? Colors.red : theme.colorScheme.primary,
            ),
            onPressed: () => ref
                .read(commentsProvider(widget.slug).notifier)
                .toggleCommentLike(widget.comment.id),
            tooltip: widget.comment.isLiked ? 'Unlike' : 'Like',
          )
        else
          IconButton(
            icon: Icon(
              Icons.favorite_border,
              size: 20,
              color: theme.colorScheme.primary.withOpacity(0.5),
            ),
            onPressed: () => _showLoginPrompt(context),
            tooltip: 'Login to like',
          ),
        Text(
          '${widget.comment.likeCount}',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: 16),
        if (isLoggedIn)
          TextButton.icon(
            onPressed: () => _showReplyDialog(context),
            icon: const Icon(Icons.reply, size: 20),
            label: const Text('Reply'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          )
        else
          TextButton.icon(
            onPressed: () => _showLoginPrompt(context),
            icon: const Icon(Icons.reply, size: 20),
            label: const Text('Reply'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
      ],
    );
  }

  void _showReplyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => CommentDialog(
        slug: widget.slug,
        parentId: widget.comment.id,
      ),
    );
  }

  void _showLoginPrompt(BuildContext context) {
    showEnhancedLoginDialog(
      context,
      action: 'interact with comments',
      title: 'Join the Conversation',
      subtitle: 'Sign in to like comments and join the discussion',
      icon: Icons.chat_bubble_rounded,
    );
  }
}
