#!/usr/bin/env python3
"""
Smart contract deployment script for Trendy blockchain integration
"""

import os
import sys
import django
from web3 import Web3
from eth_account import Account
import json
from decimal import Decimal

# Add the Django project to the Python path
import pathlib
script_dir = pathlib.Path(__file__).parent.absolute()
django_dir = script_dir / 'trendy_web_and_api' / 'trendy'
sys.path.append(str(django_dir))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'trendyblog.settings')

# Setup Django
django.setup()

from django.conf import settings
from blockchain.models import BlockchainNetwork, SmartContract

# Contract ABIs (simplified for demo - in production, compile from Solidity)
TRENDY_TOKEN_ABI = [
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "totalSupply",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "transfer",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    }
]

TRENDY_ACHIEVEMENTS_ABI = [
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "string", "name": "achievementName", "type": "string"},
            {"internalType": "uint256", "name": "rarity", "type": "uint256"}
        ],
        "name": "mintAchievement",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    }
]

class ContractDeployer:
    def __init__(self, network_name='polygon_testnet'):
        self.network_name = network_name
        self.setup_web3()
        self.setup_account()
    
    def setup_web3(self):
        """Setup Web3 connection"""
        network_config = settings.BLOCKCHAIN_NETWORKS[self.network_name]
        self.w3 = Web3(Web3.HTTPProvider(network_config['rpc_url']))
        
        if not self.w3.is_connected():
            raise Exception(f"Failed to connect to {self.network_name}")
        
        print(f"✅ Connected to {network_config['name']}")
        print(f"🔗 RPC URL: {network_config['rpc_url']}")
        print(f"⛽ Gas Price: {network_config['gas_price_gwei']} gwei")
    
    def setup_account(self):
        """Setup deployment account"""
        private_key = settings.BLOCKCHAIN_ADMIN_PRIVATE_KEY
        if private_key.startswith('0x'):
            private_key = private_key[2:]
        
        self.account = Account.from_key(private_key)
        self.address = self.account.address
        
        # Check balance
        balance_wei = self.w3.eth.get_balance(self.address)
        balance_eth = self.w3.from_wei(balance_wei, 'ether')
        
        print(f"💰 Deployer Address: {self.address}")
        print(f"💰 Balance: {balance_eth:.4f} ETH")
        
        if balance_eth < 0.01:
            print("⚠️  Warning: Low balance. You may need testnet tokens.")
            print(f"🚰 Get testnet tokens: https://faucet.polygon.technology/")
    
    def deploy_mock_token(self):
        """Deploy mock TRD token contract (for testing)"""
        print("\n🚀 Deploying Trendy Token (TRD)...")

        # MOCK DEPLOYMENT FOR DEVELOPMENT
        # TODO: Replace with real deployment when testnet is available
        mock_address = "0x" + "1" * 40  # Mock address for demo

        # REAL DEPLOYMENT CODE (COMMENTED FOR FUTURE USE)
        # Uncomment and modify when ready to deploy to actual blockchain
        """
        # Compile contract (you'll need to add solc compilation)
        compiled_contract = compile_contract('TrendyToken.sol')

        # Deploy contract
        contract = self.w3.eth.contract(
            abi=compiled_contract['abi'],
            bytecode=compiled_contract['bytecode']
        )

        # Build transaction
        transaction = contract.constructor().build_transaction({
            'from': self.address,
            'gas': 3000000,
            'gasPrice': self.w3.to_wei(str(settings.BLOCKCHAIN_NETWORKS[self.network_name]['gas_price_gwei']), 'gwei'),
            'nonce': self.w3.eth.get_transaction_count(self.address)
        })

        # Sign and send transaction
        signed_txn = self.w3.eth.account.sign_transaction(transaction, self.account.key)
        tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)

        # Wait for transaction receipt
        tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash)
        contract_address = tx_receipt.contractAddress

        print(f"✅ Real deployment successful: {contract_address}")
        print(f"🔗 Transaction hash: {tx_hash.hex()}")
        """
        
        try:
            # Get or create blockchain network
            network, created = BlockchainNetwork.objects.get_or_create(
                name=self.network_name,
                defaults={
                    'chain_id': settings.BLOCKCHAIN_NETWORKS[self.network_name]['chain_id'],
                    'rpc_url': settings.BLOCKCHAIN_NETWORKS[self.network_name]['rpc_url'],
                    'explorer_url': settings.BLOCKCHAIN_NETWORKS[self.network_name]['explorer_url'],
                    'native_token': settings.BLOCKCHAIN_NETWORKS[self.network_name]['native_token'],
                    'gas_price_gwei': settings.BLOCKCHAIN_NETWORKS[self.network_name]['gas_price_gwei'],
                    'is_active': True
                }
            )
            
            # Create smart contract record
            contract, created = SmartContract.objects.get_or_create(
                network=network,
                contract_type='token',
                name='Trendy Token',
                defaults={
                    'address': mock_address,
                    'abi': TRENDY_TOKEN_ABI,
                    'is_active': True
                }
            )
            
            if created:
                print(f"✅ Token contract deployed: {mock_address}")
                print(f"📝 Contract saved to database")
            else:
                print(f"ℹ️  Token contract already exists: {contract.address}")
            
            return contract
            
        except Exception as e:
            print(f"❌ Error deploying token contract: {str(e)}")
            return None
    
    def deploy_mock_nft(self):
        """Deploy mock NFT achievements contract (for testing)"""
        print("\n🚀 Deploying Trendy Achievements NFT...")

        # MOCK DEPLOYMENT FOR DEVELOPMENT
        # TODO: Replace with real deployment when testnet is available
        mock_address = "0x" + "2" * 40  # Mock address for demo

        # REAL DEPLOYMENT CODE (COMMENTED FOR FUTURE USE)
        # Uncomment and modify when ready to deploy to actual blockchain
        """
        # Compile NFT contract
        compiled_contract = compile_contract('TrendyAchievements.sol')

        # Deploy contract
        contract = self.w3.eth.contract(
            abi=compiled_contract['abi'],
            bytecode=compiled_contract['bytecode']
        )

        # Build transaction
        transaction = contract.constructor().build_transaction({
            'from': self.address,
            'gas': 4000000,  # NFT contracts need more gas
            'gasPrice': self.w3.to_wei(str(settings.BLOCKCHAIN_NETWORKS[self.network_name]['gas_price_gwei']), 'gwei'),
            'nonce': self.w3.eth.get_transaction_count(self.address)
        })

        # Sign and send transaction
        signed_txn = self.w3.eth.account.sign_transaction(transaction, self.account.key)
        tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)

        # Wait for transaction receipt
        tx_receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash)
        contract_address = tx_receipt.contractAddress

        print(f"✅ Real NFT deployment successful: {contract_address}")
        print(f"🔗 Transaction hash: {tx_hash.hex()}")
        """
        
        try:
            network = BlockchainNetwork.objects.get(name=self.network_name)
            
            # Create smart contract record
            contract, created = SmartContract.objects.get_or_create(
                network=network,
                contract_type='nft',
                name='Trendy Achievements',
                defaults={
                    'address': mock_address,
                    'abi': TRENDY_ACHIEVEMENTS_ABI,
                    'is_active': True
                }
            )
            
            if created:
                print(f"✅ NFT contract deployed: {mock_address}")
                print(f"📝 Contract saved to database")
            else:
                print(f"ℹ️  NFT contract already exists: {contract.address}")
            
            return contract
            
        except Exception as e:
            print(f"❌ Error deploying NFT contract: {str(e)}")
            return None
    
    def create_staking_pool(self):
        """Create a default staking pool"""
        print("\n🏊 Creating Default Staking Pool...")
        
        try:
            from blockchain.models import StakingPool
            from django.utils import timezone
            from datetime import timedelta
            
            network = BlockchainNetwork.objects.get(name=self.network_name)
            token_contract = SmartContract.objects.get(
                network=network,
                contract_type='token'
            )
            
            pool, created = StakingPool.objects.get_or_create(
                network=network,
                name='TRD Staking Pool',
                defaults={
                    'contract': token_contract,  # Mock staking contract
                    'token_contract': token_contract,
                    'description': 'Stake your TRD tokens to earn rewards',
                    'apy_percentage': Decimal('15.00'),  # 15% APY
                    'minimum_stake': Decimal('10.00'),   # 10 TRD minimum
                    'maximum_stake': Decimal('10000.00'), # 10,000 TRD maximum
                    'is_active': True,
                    'start_date': timezone.now(),
                    'end_date': timezone.now() + timedelta(days=365)  # 1 year
                }
            )
            
            if created:
                print(f"✅ Staking pool created: {pool.name}")
                print(f"📊 APY: {pool.apy_percentage}%")
                print(f"💰 Min Stake: {pool.minimum_stake} TRD")
            else:
                print(f"ℹ️  Staking pool already exists: {pool.name}")
            
            return pool
            
        except Exception as e:
            print(f"❌ Error creating staking pool: {str(e)}")
            return None
    
    def deploy_all(self):
        """Deploy all contracts and setup"""
        print("🚀 Starting Trendy Blockchain Deployment")
        print("=" * 50)
        
        # Deploy token contract
        token_contract = self.deploy_mock_token()
        if not token_contract:
            print("❌ Token deployment failed. Aborting.")
            return False
        
        # Deploy NFT contract
        nft_contract = self.deploy_mock_nft()
        if not nft_contract:
            print("❌ NFT deployment failed. Aborting.")
            return False
        
        # Create staking pool
        staking_pool = self.create_staking_pool()
        if not staking_pool:
            print("❌ Staking pool creation failed. Aborting.")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 Deployment Complete!")
        print("\n📋 Summary:")
        print(f"🪙 Token Contract: {token_contract.address}")
        print(f"🖼️  NFT Contract: {nft_contract.address}")
        print(f"🏊 Staking Pool: {staking_pool.name}")
        print(f"🌐 Network: {self.network_name}")
        
        print("\n🔧 Next Steps:")
        print("1. Start the Django server: python manage.py runserver")
        print("2. Test blockchain endpoints via API")
        print("3. Integrate with Flutter app")
        print("4. Deploy to production network when ready")
        
        return True

def main():
    """Main deployment function"""
    print("🔗 Trendy Blockchain Contract Deployment")
    print("=" * 50)
    
    # Choose network
    network = input("Enter network (polygon_testnet/polygon/bsc) [polygon_testnet]: ").strip()
    if not network:
        network = 'polygon_testnet'
    
    if network not in settings.BLOCKCHAIN_NETWORKS:
        print(f"❌ Unknown network: {network}")
        return
    
    try:
        deployer = ContractDeployer(network)
        success = deployer.deploy_all()
        
        if success:
            print("\n✅ All contracts deployed successfully!")
        else:
            print("\n❌ Deployment failed!")
            
    except Exception as e:
        print(f"\n❌ Deployment error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
