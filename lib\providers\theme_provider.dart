import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';

enum ThemeMode { light, dark, system }

class ThemeState {
  final ThemeMode themeMode;
  final ThemeData currentTheme;
  final Brightness systemBrightness;

  const ThemeState({
    required this.themeMode,
    required this.currentTheme,
    required this.systemBrightness,
  });

  ThemeState copyWith({
    ThemeMode? themeMode,
    ThemeData? currentTheme,
    Brightness? systemBrightness,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      currentTheme: currentTheme ?? this.currentTheme,
      systemBrightness: systemBrightness ?? this.systemBrightness,
    );
  }

  bool get isDarkMode {
    switch (themeMode) {
      case ThemeMode.light:
        return false;
      case ThemeMode.dark:
        return true;
      case ThemeMode.system:
        return systemBrightness == Brightness.dark;
    }
  }

  String get themeModeString {
    switch (themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  String get themeModeDisplayName {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }
}

class ThemeNotifier extends StateNotifier<ThemeState> {
  static const String _themeKey = 'theme_mode';

  ThemeNotifier() : super(ThemeState(
    themeMode: ThemeMode.system,
    currentTheme: AppTheme.lightTheme,
    systemBrightness: Brightness.light,
  )) {
    _loadThemeMode();
    _updateSystemBrightness();
  }

  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeString = prefs.getString(_themeKey) ?? 'system';
      
      final themeMode = _stringToThemeMode(themeModeString);
      await setThemeMode(themeMode, saveToPrefs: false);
    } catch (e) {
      print('Error loading theme mode: $e');
    }
  }

  Future<void> setThemeMode(ThemeMode themeMode, {bool saveToPrefs = true}) async {
    if (saveToPrefs) {
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_themeKey, _themeModeToString(themeMode));
      } catch (e) {
        print('Error saving theme mode: $e');
      }
    }

    final currentTheme = _getThemeData(themeMode);
    
    state = state.copyWith(
      themeMode: themeMode,
      currentTheme: currentTheme,
    );

    // Update system UI overlay style
    _updateSystemUIOverlayStyle();
  }

  void updateSystemBrightness(Brightness brightness) {
    state = state.copyWith(systemBrightness: brightness);
    
    // Update current theme if in system mode
    if (state.themeMode == ThemeMode.system) {
      final currentTheme = _getThemeData(ThemeMode.system);
      state = state.copyWith(currentTheme: currentTheme);
    }

    _updateSystemUIOverlayStyle();
  }

  void _updateSystemBrightness() {
    // Get initial system brightness
    final window = WidgetsBinding.instance.platformDispatcher;
    final brightness = window.platformBrightness;
    updateSystemBrightness(brightness);
  }

  ThemeData _getThemeData(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return AppTheme.lightTheme;
      case ThemeMode.dark:
        return AppTheme.darkTheme;
      case ThemeMode.system:
        return state.systemBrightness == Brightness.dark 
            ? AppTheme.darkTheme 
            : AppTheme.lightTheme;
    }
  }

  void _updateSystemUIOverlayStyle() {
    final isDark = state.isDarkMode;
    
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: isDark ? AppTheme.darkSurfaceColor : AppTheme.surfaceColor,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      ),
    );
  }

  ThemeMode _stringToThemeMode(String themeModeString) {
    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  String _themeModeToString(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }
}

// Theme Provider
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeState>((ref) {
  return ThemeNotifier();
});

// Helper provider for current theme data
final currentThemeProvider = Provider<ThemeData>((ref) {
  final themeState = ref.watch(themeProvider);
  return themeState.currentTheme;
});

// Helper provider for dark mode status
final isDarkModeProvider = Provider<bool>((ref) {
  final themeState = ref.watch(themeProvider);
  return themeState.isDarkMode;
});
