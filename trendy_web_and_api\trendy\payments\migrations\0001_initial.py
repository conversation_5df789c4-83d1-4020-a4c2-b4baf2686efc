# Generated by Django 5.2.3 on 2025-06-23 13:28

import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('paypal_mode', models.CharField(choices=[('sandbox', 'Sandbox'), ('live', 'Live')], default='sandbox', max_length=10)),
                ('minimum_payment', models.DecimalField(decimal_places=2, default=Decimal('1.00'), max_digits=10)),
                ('maximum_payment', models.DecimalField(decimal_places=2, default=Decimal('500.00'), max_digits=10)),
                ('minimum_payout', models.DecimalField(decimal_places=2, default=Decimal('5.00'), max_digits=10)),
                ('maximum_daily_payout', models.DecimalField(decimal_places=2, default=Decimal('1000.00'), max_digits=10)),
                ('payout_processing_fee', models.DecimalField(decimal_places=2, default=Decimal('0.30'), max_digits=5)),
                ('auto_process_payouts', models.BooleanField(default=True)),
                ('batch_size', models.PositiveIntegerField(default=50)),
                ('batch_frequency_hours', models.PositiveIntegerField(default=24)),
                ('require_email_verification', models.BooleanField(default=True)),
                ('webhook_verification', models.BooleanField(default=True)),
                ('admin_email_notifications', models.BooleanField(default=True)),
                ('user_email_notifications', models.BooleanField(default=True)),
                ('payments_enabled', models.BooleanField(default=True)),
                ('payouts_enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Payment Settings',
                'verbose_name_plural': 'Payment Settings',
            },
        ),
        migrations.CreateModel(
            name='PayPalAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Trendy App PayPal', max_length=100)),
                ('account_type', models.CharField(choices=[('business', 'Business Account'), ('personal', 'Personal Account')], default='business', max_length=20)),
                ('environment', models.CharField(choices=[('sandbox', 'Sandbox (Testing)'), ('live', 'Live (Production)')], default='sandbox', max_length=10)),
                ('client_id', models.CharField(max_length=200)),
                ('client_secret', models.CharField(max_length=200)),
                ('business_email', models.EmailField(max_length=254)),
                ('business_name', models.CharField(default='Trendy App LLC', max_length=200)),
                ('webhook_id', models.CharField(blank=True, max_length=100)),
                ('webhook_url', models.URLField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('auto_accept_payments', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'PayPal Account',
                'verbose_name_plural': 'PayPal Accounts',
            },
        ),
        migrations.CreateModel(
            name='PaymentTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('incoming', 'Incoming Payment (User to Admin)'), ('outgoing', 'Outgoing Payment (Admin to User)')], max_length=20)),
                ('payment_purpose', models.CharField(choices=[('premium_subscription', 'Premium Subscription'), ('tier_unlock', 'Reward Tier Unlock'), ('point_boost', 'Point Boost Package'), ('virtual_item', 'Virtual Item Purchase'), ('streak_protection', 'Streak Protection'), ('reward_payout', 'Reward Payout'), ('referral_bonus', 'Referral Bonus')], max_length=30)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('paypal_payment_id', models.CharField(blank=True, max_length=100)),
                ('paypal_payer_id', models.CharField(blank=True, max_length=100)),
                ('paypal_batch_id', models.CharField(blank=True, max_length=100)),
                ('paypal_item_id', models.CharField(blank=True, max_length=100)),
                ('payer_email', models.EmailField(blank=True, max_length=254)),
                ('payee_email', models.EmailField(blank=True, max_length=254)),
                ('description', models.TextField()),
                ('reference_id', models.CharField(max_length=100, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='paypal_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PayPalPayoutBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_id', models.CharField(max_length=100, unique=True)),
                ('paypal_batch_id', models.CharField(blank=True, max_length=100)),
                ('batch_status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('success', 'Success'), ('denied', 'Denied'), ('canceled', 'Canceled')], default='pending', max_length=20)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_items', models.PositiveIntegerField()),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['batch_status'], name='payments_pa_batch_s_3cef23_idx'), models.Index(fields=['paypal_batch_id'], name='payments_pa_paypal__28ebfa_idx')],
            },
        ),
        migrations.CreateModel(
            name='PayPalWebhook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('webhook_id', models.CharField(max_length=100)),
                ('event_type', models.CharField(choices=[('PAYMENT.CAPTURE.COMPLETED', 'Payment Capture Completed'), ('PAYMENT.CAPTURE.DENIED', 'Payment Capture Denied'), ('PAYMENTS.PAYMENT.CREATED', 'Payment Created'), ('CHECKOUT.ORDER.APPROVED', 'Order Approved'), ('BILLING.SUBSCRIPTION.CREATED', 'Subscription Created'), ('BILLING.SUBSCRIPTION.CANCELLED', 'Subscription Cancelled'), ('PAYMENTS.PAYOUTS-ITEM.SUCCEEDED', 'Payout Item Succeeded'), ('PAYMENTS.PAYOUTS-ITEM.FAILED', 'Payout Item Failed')], max_length=50)),
                ('event_id', models.CharField(max_length=100, unique=True)),
                ('resource_id', models.CharField(max_length=100)),
                ('resource_type', models.CharField(max_length=50)),
                ('raw_data', models.JSONField()),
                ('processed', models.BooleanField(default=False)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('received_at', models.DateTimeField(auto_now_add=True)),
                ('payment_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='payments.paymenttransaction')),
            ],
            options={
                'ordering': ['-received_at'],
            },
        ),
        migrations.CreateModel(
            name='UserPayPalProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('paypal_email', models.EmailField(max_length=254)),
                ('paypal_account_verified', models.BooleanField(default=False)),
                ('verification_code', models.CharField(blank=True, max_length=10)),
                ('verification_sent_at', models.DateTimeField(blank=True, null=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('auto_accept_payments', models.BooleanField(default=True)),
                ('notification_preferences', models.JSONField(default=dict)),
                ('total_payments_received', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_payments_made', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('successful_transactions', models.PositiveIntegerField(default=0)),
                ('failed_transactions', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='paypal_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['user', 'transaction_type'], name='payments_pa_user_id_8900f2_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['status', 'transaction_type'], name='payments_pa_status_f492ea_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['paypal_payment_id'], name='payments_pa_paypal__c6be2a_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttransaction',
            index=models.Index(fields=['reference_id'], name='payments_pa_referen_47bb89_idx'),
        ),
        migrations.AddIndex(
            model_name='paypalwebhook',
            index=models.Index(fields=['event_type', 'processed'], name='payments_pa_event_t_b51e29_idx'),
        ),
        migrations.AddIndex(
            model_name='paypalwebhook',
            index=models.Index(fields=['resource_id'], name='payments_pa_resourc_9560ef_idx'),
        ),
        migrations.AddIndex(
            model_name='paypalwebhook',
            index=models.Index(fields=['event_id'], name='payments_pa_event_i_fb5fbb_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaypalprofile',
            index=models.Index(fields=['paypal_email'], name='payments_us_paypal__9e90fb_idx'),
        ),
        migrations.AddIndex(
            model_name='userpaypalprofile',
            index=models.Index(fields=['paypal_account_verified'], name='payments_us_paypal__03e10e_idx'),
        ),
    ]
