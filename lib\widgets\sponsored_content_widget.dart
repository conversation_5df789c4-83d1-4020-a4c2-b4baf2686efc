import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:visibility_detector/visibility_detector.dart';
import '../providers/advertising_provider.dart';
import '../theme/app_theme.dart';
import '../models/advertising_models.dart';

class SponsoredContentWidget extends ConsumerStatefulWidget {
  final String? contentType;
  final bool showAsCard;

  const SponsoredContentWidget({
    super.key,
    this.contentType,
    this.showAsCard = true,
  });

  @override
  ConsumerState<SponsoredContentWidget> createState() =>
      _SponsoredContentWidgetState();
}

class _SponsoredContentWidgetState
    extends ConsumerState<SponsoredContentWidget> {
  final Set<String> _impressionTracked = <String>{};

  @override
  Widget build(BuildContext context) {
    final advertisingState = ref.watch(advertisingProvider);
    final sponsoredContent = ref
        .read(advertisingProvider.notifier)
        .getSponsoredContentByType(widget.contentType ?? 'post');

    print(
        'SponsoredContentWidget: contentType=${widget.contentType}, sponsoredContent.length=${sponsoredContent.length}');
    print(
        'SponsoredContentWidget: advertisingState.sponsoredContent.length=${advertisingState.sponsoredContent.length}');

    if (sponsoredContent.isEmpty) {
      print(
          'SponsoredContentWidget: No sponsored content available, returning SizedBox.shrink()');
      return const SizedBox.shrink();
    }

    // Show first available sponsored content
    final content = sponsoredContent.first;

    if (widget.showAsCard) {
      return _buildSponsoredCard(context, ref, content);
    } else {
      return _buildSponsoredBanner(context, ref, content);
    }
  }

  Widget _buildSponsoredCard(
      BuildContext context, WidgetRef ref, SponsoredContent content) {
    return VisibilityDetector(
      key: Key('sponsored_card_${content.id}'),
      onVisibilityChanged: (visibilityInfo) {
        // Track impression when 50% of the ad is visible
        if (visibilityInfo.visibleFraction >= 0.5 &&
            !_impressionTracked.contains(content.id)) {
          _impressionTracked.add(content.id);
          ref
              .read(advertisingProvider.notifier)
              .recordSponsoredContentImpression(content.id);
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _handleContentClick(ref, content),
            borderRadius: BorderRadius.circular(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Sponsored label
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.warningColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: AppTheme.warningColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Sponsored',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.warningColor,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 10,
                                  ),
                        ),
                      ),
                      const Spacer(),
                      if (content.sponsorLogo != null)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Image.network(
                            content.sponsorLogo!,
                            width: 24,
                            height: 24,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const SizedBox.shrink(),
                          ),
                        ),
                      const SizedBox(width: 8),
                      Text(
                        content.sponsorName,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                ),

                // Content image
                if (content.imageUrl != null)
                  ClipRRect(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(12)),
                    child: Image.network(
                      content.imageUrl!,
                      width: double.infinity,
                      height: 200,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: 200,
                        color: AppTheme.backgroundColor,
                        child: const Center(
                          child: Icon(Icons.image_not_supported,
                              color: AppTheme.textTertiary),
                        ),
                      ),
                    ),
                  ),

                // Content text
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        content.title,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        content.content,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 16),

                      // Call to action button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => _handleContentClick(ref, content),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(content.callToAction),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ); // VisibilityDetector child closing
  }

  Widget _buildSponsoredBanner(
      BuildContext context, WidgetRef ref, SponsoredContent content) {
    return VisibilityDetector(
      key: Key('sponsored_banner_${content.id}'),
      onVisibilityChanged: (visibilityInfo) {
        // Track impression when 50% of the ad is visible
        if (visibilityInfo.visibleFraction >= 0.5 &&
            !_impressionTracked.contains(content.id)) {
          _impressionTracked.add(content.id);
          ref
              .read(advertisingProvider.notifier)
              .recordSponsoredContentImpression(content.id);
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.warningColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.warningColor.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () => _handleContentClick(ref, content),
          child: Row(
            children: [
              // Sponsored indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: Text(
                  'AD',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.warningColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 9,
                      ),
                ),
              ),
              const SizedBox(width: 12),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      content.title,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'by ${content.sponsorName}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.textTertiary,
                          ),
                    ),
                  ],
                ),
              ),

              // CTA button
              TextButton(
                onPressed: () => _handleContentClick(ref, content),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
                child: Text(
                  content.callToAction,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ), // Row closing
        ), // InkWell closing
      ), // Container closing
    ); // VisibilityDetector closing
  }

  Future<void> _handleContentClick(
      WidgetRef ref, SponsoredContent content) async {
    // Record click
    await ref
        .read(advertisingProvider.notifier)
        .clickSponsoredContent(content.id);

    // Open target URL
    try {
      String targetUrl = content.targetUrl;

      // Ensure URL has a proper scheme
      if (!targetUrl.startsWith('http://') &&
          !targetUrl.startsWith('https://')) {
        targetUrl = 'https://$targetUrl';
      }

      print('🔗 Attempting to launch URL: $targetUrl');

      final uri = Uri.parse(targetUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        );
        print('✅ Successfully launched URL: $targetUrl');
      } else {
        print('❌ Cannot launch URL: $targetUrl');
        // Show user-friendly message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Unable to open link: $targetUrl'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      print('❌ Error launching URL: $e');
      // Show user-friendly error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to open the link. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class SponsoredContentList extends ConsumerWidget {
  final String? contentType;

  const SponsoredContentList({
    super.key,
    this.contentType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final advertisingState = ref.watch(advertisingProvider);
    final sponsoredContent = ref
        .read(advertisingProvider.notifier)
        .getSponsoredContentByType(contentType ?? 'post');

    if (sponsoredContent.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                'Sponsored Content',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'AD',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.warningColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                ),
              ),
            ],
          ),
        ),
        ...sponsoredContent.map((content) => SponsoredContentWidget(
              contentType: contentType,
              showAsCard: true,
            )),
      ],
    );
  }
}
