import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../models/blockchain_models.dart';
import '../../services/blockchain_service.dart';
import '../../widgets/styled_components.dart';
import '../../theme/app_theme.dart';
import 'nft_detail_screen.dart';

class NFTGalleryScreen extends StatefulWidget {
  const NFTGalleryScreen({super.key});

  @override
  State<NFTGalleryScreen> createState() => _NFTGalleryScreenState();
}

class _NFTGalleryScreenState extends State<NFTGalleryScreen> with TickerProviderStateMixin {
  final BlockchainService _blockchainService = BlockchainService();
  List<NFTAsset> _nfts = [];
  List<NFTAsset> _filteredNFTs = [];
  bool _isLoading = true;
  String _error = '';
  NFTRarity? _selectedRarity;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this); // All + 5 rarities
    _loadNFTs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNFTs() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final nfts = await _blockchainService.getUserNFTs();
      setState(() {
        _nfts = nfts;
        _filteredNFTs = nfts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error loading NFTs: $e';
        _isLoading = false;
      });
    }
  }

  void _filterByRarity(NFTRarity? rarity) {
    setState(() {
      _selectedRarity = rarity;
      if (rarity == null) {
        _filteredNFTs = _nfts;
      } else {
        _filteredNFTs = _nfts.where((nft) => nft.rarityEnum == rarity).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: StyledAppBar(
        title: 'NFT Collection',
        isBlockchain: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNFTs,
            color: AppTheme.blockchainPrimary,
          ),
        ],
      ),
      body: Column(
        children: [
          // Rarity Filter Tabs
          Container(
            color: AppTheme.surfaceColor,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: AppTheme.blockchainPrimary,
              labelColor: AppTheme.blockchainPrimary,
              unselectedLabelColor: AppTheme.textSecondary,
              onTap: (index) {
                if (index == 0) {
                  _filterByRarity(null);
                } else {
                  _filterByRarity(NFTRarity.values[index - 1]);
                }
              },
              tabs: [
                Tab(text: 'All (${_nfts.length})'),
                ...NFTRarity.values.map((rarity) {
                  final count = _nfts.where((nft) => nft.rarityEnum == rarity).length;
                  return Tab(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(rarity.displayName),
                        const SizedBox(width: AppTheme.spacingXs),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppTheme.spacingSm,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.getNFTRarityColor(rarity.value).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(AppTheme.radiusXs),
                          ),
                          child: Text(
                            count.toString(),
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: AppTheme.getNFTRarityColor(rarity.value),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
          // Content
          Expanded(
            child: _isLoading
                ? const StyledLoadingWidget(
                    message: 'Loading NFT collection...',
                    isBlockchain: true,
                  )
                : _error.isNotEmpty
                    ? StyledErrorWidget(
                        message: _error,
                        onRetry: _loadNFTs,
                      )
                    : _filteredNFTs.isEmpty
                        ? _buildEmptyWidget()
                        : _buildNFTGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingLg),
              decoration: BoxDecoration(
                color: AppTheme.nftRarityEpic.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusXl),
              ),
              child: Icon(
                Icons.collections_outlined,
                size: 64,
                color: AppTheme.nftRarityEpic,
              ),
            ),
            const SizedBox(height: AppTheme.spacingLg),
            Text(
              _selectedRarity == null ? 'No NFTs Found' : 'No ${_selectedRarity!.displayName} NFTs',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingSm),
            Text(
              _selectedRarity == null
                  ? 'Start engaging with the platform to earn achievement NFTs!'
                  : 'Keep engaging to unlock ${_selectedRarity!.displayName.toLowerCase()} achievements!',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingXl),
            if (_selectedRarity != null)
              StyledButton(
                text: 'View All NFTs',
                onPressed: () => _filterByRarity(null),
                type: ButtonType.blockchain,
                isFullWidth: true,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNFTGrid() {
    return RefreshIndicator(
      onRefresh: _loadNFTs,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: AnimationLimiter(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: _filteredNFTs.length,
            itemBuilder: (context, index) {
              return AnimationConfiguration.staggeredGrid(
                position: index,
                duration: const Duration(milliseconds: 375),
                columnCount: 2,
                child: ScaleAnimation(
                  child: FadeInAnimation(
                    child: _buildNFTCard(_filteredNFTs[index]),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildNFTCard(NFTAsset nft) {
    final rarityColor = AppTheme.getNFTRarityColor(nft.rarityEnum.value);

    return StyledCard(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => NFTDetailScreen(nft: nft),
          ),
        );
      },
      padding: EdgeInsets.zero,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          border: Border.all(
            color: rarityColor.withOpacity(0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: rarityColor.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // NFT Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(AppTheme.radiusLg - 2),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(AppTheme.radiusLg - 2),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: nft.imageUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppTheme.backgroundColor,
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(rarityColor),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppTheme.backgroundColor,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.image_not_supported,
                            color: AppTheme.textTertiary,
                            size: 32,
                          ),
                          const SizedBox(height: AppTheme.spacingSm),
                          Text(
                            nft.rarityIcon,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // NFT Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Rarity Badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingSm,
                        vertical: AppTheme.spacingXs,
                      ),
                      decoration: BoxDecoration(
                        color: rarityColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(AppTheme.radiusMd),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            nft.rarityIcon,
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: AppTheme.spacingXs),
                          Text(
                            nft.rarityDisplay,
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: rarityColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingSm),
                    // NFT Name
                    Text(
                      nft.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    // Token ID
                    Text(
                      '#${nft.tokenId}',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
