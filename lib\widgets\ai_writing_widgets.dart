import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ai_writing_models.dart';
import '../services/ai_writing_service.dart' hide ReadabilityAnalysis;
import '../models/ai_writing_models.dart' show ReadabilityAnalysis;

// AI Writing Assistant Panel
class AIWritingPanel extends ConsumerStatefulWidget {
  final TextEditingController titleController;
  final TextEditingController contentController;
  final VoidCallback? onSuggestionApplied;

  const AIWritingPanel({
    Key? key,
    required this.titleController,
    required this.contentController,
    this.onSuggestionApplied,
  }) : super(key: key);

  @override
  ConsumerState<AIWritingPanel> createState() => _AIWritingPanelState();
}

class _AIWritingPanelState extends ConsumerState<AIWritingPanel> {
  final AIWritingService _aiService = AIWritingService();
  bool _isExpanded = false;
  bool _isLoading = false;
  String? _currentSuggestion;

  @override
  void initState() {
    super.initState();
    _aiService.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.smart_toy, color: Colors.blue),
            title: const Text('AI Writing Assistant'),
            subtitle: Text(_isExpanded
                ? 'Choose an AI feature below'
                : 'Tap to expand AI tools'),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () => setState(() => _isExpanded = !_isExpanded),
            ),
          ),
          if (_isExpanded) ...[
            const Divider(),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // AI Tools Row
                  Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: [
                      _buildAIButton(
                        'Generate Ideas',
                        Icons.lightbulb_outline,
                        _generateContentIdeas,
                        Colors.orange,
                      ),
                      _buildAIButton(
                        'Create Outline',
                        Icons.list_alt,
                        _generateOutline,
                        Colors.green,
                      ),
                      _buildAIButton(
                        'Improve Grammar',
                        Icons.spellcheck,
                        _improveGrammar,
                        Colors.purple,
                      ),
                      _buildAIButton(
                        'SEO Tips',
                        Icons.search,
                        _generateSEOSuggestions,
                        Colors.blue,
                      ),
                      _buildAIButton(
                        'Check Readability',
                        Icons.visibility,
                        _analyzeReadability,
                        Colors.teal,
                      ),
                      _buildAIButton(
                        'Complete Text',
                        Icons.auto_fix_high,
                        _completeText,
                        Colors.indigo,
                      ),
                    ],
                  ),

                  if (_isLoading) ...[
                    const SizedBox(height: 16),
                    const LinearProgressIndicator(),
                    const SizedBox(height: 8),
                    const Text('AI is working...'),
                  ],

                  if (_currentSuggestion != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.auto_awesome,
                                  color: Colors.blue),
                              const SizedBox(width: 8),
                              const Text(
                                'AI Suggestion',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const Spacer(),
                              IconButton(
                                icon: const Icon(Icons.close),
                                onPressed: () =>
                                    setState(() => _currentSuggestion = null),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(_currentSuggestion!),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAIButton(
      String label, IconData icon, VoidCallback onPressed, Color color) {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : onPressed,
      icon: Icon(icon, size: 18),
      label: Text(label, style: const TextStyle(fontSize: 12)),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Future<void> _generateContentIdeas() async {
    final topic = widget.titleController.text.isEmpty
        ? 'blog post'
        : widget.titleController.text;

    setState(() => _isLoading = true);

    try {
      final ideas = await _aiService.generateContentIdeas(topic, count: 5);
      if (ideas.isNotEmpty) {
        _showIdeasDialog(ideas);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to generate ideas: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateOutline() async {
    final title = widget.titleController.text;
    if (title.isEmpty) {
      _showErrorSnackBar('Please enter a title first');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final outline = await _aiService.generateContentOutline(title);
      if (outline != null) {
        _showOutlineDialog(outline);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to generate outline: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _improveGrammar() async {
    final content = widget.contentController.text;
    if (content.isEmpty) {
      _showErrorSnackBar('Please enter some content first');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final improvement = await _aiService.improveGrammar(content);
      if (improvement != null) {
        _showGrammarImprovementDialog(improvement);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to improve grammar: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateSEOSuggestions() async {
    final content = widget.contentController.text;
    final title = widget.titleController.text;

    if (content.isEmpty) {
      _showErrorSnackBar('Please enter some content first');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final suggestions =
          await _aiService.generateSEOSuggestions(content, title: title);
      if (suggestions != null) {
        _showSEOSuggestionsDialog(suggestions);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to generate SEO suggestions: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _analyzeReadability() async {
    final content = widget.contentController.text;
    if (content.isEmpty) {
      _showErrorSnackBar('Please enter some content first');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final analysis = await _aiService.analyzeReadability(content);
      if (analysis != null) {
        _showReadabilityDialog(analysis);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to analyze readability: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _completeText() async {
    final content = widget.contentController.text;
    if (content.isEmpty) {
      _showErrorSnackBar('Please enter some text to complete');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final completion = await _aiService.completeText(content);
      if (completion.isNotEmpty) {
        setState(() {
          _currentSuggestion = 'Suggested completion: $completion';
        });

        // Show option to apply completion
        _showCompletionDialog(completion);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to complete text: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showIdeasDialog(List<String> ideas) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Content Ideas'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: ideas.length,
            itemBuilder: (context, index) => ListTile(
              leading: const Icon(Icons.lightbulb_outline),
              title: Text(ideas[index]),
              onTap: () {
                widget.titleController.text = ideas[index];
                Navigator.of(context).pop();
                widget.onSuggestionApplied?.call();
              },
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showOutlineDialog(ContentOutline outline) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Content Outline'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Estimated word count: ${outline.estimatedWordCount}'),
                const SizedBox(height: 16),
                const Text('Introduction:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...outline.introduction.map((point) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text('• $point'),
                    )),
                const SizedBox(height: 16),
                const Text('Main Sections:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...outline.mainSections.map((section) => Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(section.title,
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600)),
                          ...section.points.map((point) => Padding(
                                padding:
                                    const EdgeInsets.only(left: 16, top: 2),
                                child: Text('• $point'),
                              )),
                        ],
                      ),
                    )),
                const SizedBox(height: 16),
                const Text('Conclusion:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...outline.conclusion.map((point) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text('• $point'),
                    )),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              // Insert outline into content
              final outlineText = _formatOutlineAsText(outline);
              widget.contentController.text = outlineText;
              Navigator.of(context).pop();
              widget.onSuggestionApplied?.call();
            },
            child: const Text('Use Outline'),
          ),
        ],
      ),
    );
  }

  void _showGrammarImprovementDialog(GrammarImprovement improvement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Grammar Improvements'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  'Readability Score: ${improvement.readabilityScore.toStringAsFixed(1)}/10'),
              const SizedBox(height: 16),
              const Text('Suggested Changes:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: improvement.changes.length,
                  itemBuilder: (context, index) {
                    final change = improvement.changes[index];
                    return Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.arrow_forward, size: 16),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Change ${index + 1}',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.red.shade50,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text('Original: ${change.original}'),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text('Improved: ${change.improved}'),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Reason: ${change.reason}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.contentController.text = improvement.improvedText;
              Navigator.of(context).pop();
              widget.onSuggestionApplied?.call();
            },
            child: const Text('Apply Changes'),
          ),
        ],
      ),
    );
  }

  void _showSEOSuggestionsDialog(SEOSuggestions suggestions) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SEO Suggestions'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Title Suggestions:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...suggestions.titleSuggestions.map((title) => ListTile(
                      dense: true,
                      leading: const Icon(Icons.title, size: 16),
                      title: Text(title),
                      onTap: () {
                        widget.titleController.text = title;
                        Navigator.of(context).pop();
                        widget.onSuggestionApplied?.call();
                      },
                    )),
                const SizedBox(height: 16),
                const Text('Meta Description:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(suggestions.metaDescription),
                ),
                const SizedBox(height: 16),
                const Text('Keywords:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Wrap(
                  spacing: 4,
                  children: suggestions.keywords
                      .map((keyword) => Chip(
                            label: Text(keyword),
                            backgroundColor: Colors.green.shade100,
                          ))
                      .toList(),
                ),
                const SizedBox(height: 16),
                const Text('Content Suggestions:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...suggestions.contentSuggestions.map((suggestion) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text('• $suggestion'),
                    )),
                const SizedBox(height: 16),
                const Text('Readability Issues:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                ...suggestions.readabilityIssues.map((issue) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text('• $issue',
                          style: TextStyle(color: Colors.orange.shade700)),
                    )),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showReadabilityDialog(ReadabilityAnalysis analysis) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Readability Analysis'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAnalysisRow('Word Count', analysis.wordCount.toString()),
              _buildAnalysisRow(
                  'Sentence Count', analysis.sentenceCount.toString()),
              _buildAnalysisRow(
                  'Paragraph Count', analysis.paragraphCount.toString()),
              _buildAnalysisRow('Avg Words/Sentence',
                  analysis.avgWordsPerSentence.toStringAsFixed(1)),
              _buildAnalysisRow('Reading Level', analysis.readingLevel),
              _buildAnalysisRow(
                  'Reading Time', '${analysis.estimatedReadingTime} min'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getScoreColor(analysis.readabilityScore)
                      .withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getScoreIcon(analysis.readabilityScore),
                      color: _getScoreColor(analysis.readabilityScore),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Readability Score: ${analysis.readabilityScore.toStringAsFixed(1)}/100',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _getScoreColor(analysis.readabilityScore),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text('Suggestions:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              ...analysis.suggestions.map((suggestion) => Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Text('• $suggestion'),
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showCompletionDialog(String completion) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Text Completion'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('AI suggests completing your text with:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(completion),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.contentController.text += completion;
              Navigator.of(context).pop();
              widget.onSuggestionApplied?.call();
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  IconData _getScoreIcon(double score) {
    if (score >= 80) return Icons.check_circle;
    if (score >= 60) return Icons.warning;
    return Icons.error;
  }

  String _formatOutlineAsText(ContentOutline outline) {
    final buffer = StringBuffer();

    buffer.writeln('# Introduction');
    for (final point in outline.introduction) {
      buffer.writeln('- $point');
    }
    buffer.writeln();

    for (final section in outline.mainSections) {
      buffer.writeln('## ${section.title}');
      for (final point in section.points) {
        buffer.writeln('- $point');
      }
      buffer.writeln();
    }

    buffer.writeln('# Conclusion');
    for (final point in outline.conclusion) {
      buffer.writeln('- $point');
    }

    return buffer.toString();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
