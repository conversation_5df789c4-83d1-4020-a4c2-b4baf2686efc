import 'package:flutter/material.dart';
import '../services/payment_status_service.dart';
import '../services/error_reporting_service.dart';

/// Enhanced loading dialog for payments with progress tracking
class PaymentLoadingDialog extends StatefulWidget {
  final String title;
  final String? subtitle;
  final Stream<PaymentStatus>? statusStream;
  final VoidCallback? onCancel;

  const PaymentLoadingDialog({
    Key? key,
    required this.title,
    this.subtitle,
    this.statusStream,
    this.onCancel,
  }) : super(key: key);

  @override
  State<PaymentLoadingDialog> createState() => _PaymentLoadingDialogState();
}

class _PaymentLoadingDialogState extends State<PaymentLoadingDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  PaymentStatus? _currentStatus;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController);

    // Listen to payment status updates
    widget.statusStream?.listen(
      (status) {
        if (mounted) {
          setState(() {
            _currentStatus = status;
          });

          // Auto-close dialog when payment is completed or failed
          if (status.isCompleted || status.isFailed) {
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                Navigator.of(context).pop(status);
              }
            });
          }
        }
      },
      onError: (error) {
        if (mounted) {
          Navigator.of(context).pop();
          ErrorReportingService.showPaymentErrorSnackbar(
            context,
            error,
            errorContext: 'payment_processing',
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        widget.onCancel?.call();
        return true;
      },
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status icon
            _buildStatusIcon(),
            const SizedBox(height: 16),
            
            // Title
            Text(
              widget.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (widget.subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                widget.subtitle!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            // Status message
            if (_currentStatus != null) ...[
              const SizedBox(height: 16),
              _buildStatusMessage(),
            ],
            
            const SizedBox(height: 24),
            
            // Cancel button (only show if payment is still processing)
            if (widget.onCancel != null && 
                (_currentStatus == null || _currentStatus!.isPending || _currentStatus!.isProcessing))
              TextButton(
                onPressed: () {
                  widget.onCancel?.call();
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    if (_currentStatus?.isCompleted == true) {
      return const Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 48,
      );
    } else if (_currentStatus?.isFailed == true) {
      return const Icon(
        Icons.error,
        color: Colors.red,
        size: 48,
      );
    } else {
      return AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _animation.value * 2 * 3.14159,
            child: const Icon(
              Icons.sync,
              color: Colors.blue,
              size: 48,
            ),
          );
        },
      );
    }
  }

  Widget _buildStatusMessage() {
    final status = _currentStatus!;
    
    String message;
    Color color;
    
    if (status.isCompleted) {
      message = 'Payment completed successfully!';
      color = Colors.green;
    } else if (status.isFailed) {
      message = status.errorMessage ?? 'Payment failed';
      color = Colors.red;
    } else if (status.isProcessing) {
      message = 'Processing payment...';
      color = Colors.orange;
    } else {
      message = 'Waiting for payment confirmation...';
      color = Colors.blue;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        message,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

/// Enhanced success dialog for completed payments
class PaymentSuccessDialog extends StatelessWidget {
  final String title;
  final String message;
  final String? amount;
  final VoidCallback? onContinue;

  const PaymentSuccessDialog({
    Key? key,
    required this.title,
    required this.message,
    this.amount,
    this.onContinue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Success animation
          TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 600),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 64,
                ),
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          
          if (amount != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                amount!,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 24),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onContinue?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Continue'),
            ),
          ),
        ],
      ),
    );
  }
}

/// Enhanced error dialog for failed payments
class PaymentErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;

  const PaymentErrorDialog({
    Key? key,
    required this.title,
    required this.message,
    this.onRetry,
    this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          
          const SizedBox(height: 16),
          
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          Row(
            children: [
              if (onCancel != null)
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      onCancel?.call();
                    },
                    child: const Text('Cancel'),
                  ),
                ),
              
              if (onRetry != null) ...[
                if (onCancel != null) const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      onRetry?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
