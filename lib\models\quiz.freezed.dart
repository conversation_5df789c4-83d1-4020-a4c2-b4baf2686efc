// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Quiz _$QuizFromJson(Map<String, dynamic> json) {
  return _Quiz.fromJson(json);
}

/// @nodoc
mixin _$Quiz {
  int get id => throw _privateConstructorUsedError;
  String get instructions => throw _privateConstructorUsedError;
  @JsonKey(name: 'time_limit')
  int? get timeLimit => throw _privateConstructorUsedError;
  @JsonKey(name: 'show_correct_answers')
  bool get showCorrectAnswers => throw _privateConstructorUsedError;
  @JsonKey(name: 'randomize_questions')
  bool get randomizeQuestions => throw _privateConstructorUsedError;
  @JsonKey(name: 'passing_score')
  int get passingScore => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_attempts')
  int get totalAttempts => throw _privateConstructorUsedError;
  @JsonKey(name: 'average_score')
  double get averageScore => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  List<QuizQuestion> get questions => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_attempts')
  List<QuizAttempt> get userAttempts => throw _privateConstructorUsedError;
  @JsonKey(name: 'best_attempt')
  QuizAttempt? get bestAttempt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizCopyWith<Quiz> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizCopyWith<$Res> {
  factory $QuizCopyWith(Quiz value, $Res Function(Quiz) then) =
      _$QuizCopyWithImpl<$Res, Quiz>;
  @useResult
  $Res call(
      {int id,
      String instructions,
      @JsonKey(name: 'time_limit') int? timeLimit,
      @JsonKey(name: 'show_correct_answers') bool showCorrectAnswers,
      @JsonKey(name: 'randomize_questions') bool randomizeQuestions,
      @JsonKey(name: 'passing_score') int passingScore,
      @JsonKey(name: 'total_attempts') int totalAttempts,
      @JsonKey(name: 'average_score') double averageScore,
      @JsonKey(name: 'created_at') DateTime createdAt,
      List<QuizQuestion> questions,
      @JsonKey(name: 'user_attempts') List<QuizAttempt> userAttempts,
      @JsonKey(name: 'best_attempt') QuizAttempt? bestAttempt});

  $QuizAttemptCopyWith<$Res>? get bestAttempt;
}

/// @nodoc
class _$QuizCopyWithImpl<$Res, $Val extends Quiz>
    implements $QuizCopyWith<$Res> {
  _$QuizCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? instructions = null,
    Object? timeLimit = freezed,
    Object? showCorrectAnswers = null,
    Object? randomizeQuestions = null,
    Object? passingScore = null,
    Object? totalAttempts = null,
    Object? averageScore = null,
    Object? createdAt = null,
    Object? questions = null,
    Object? userAttempts = null,
    Object? bestAttempt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      timeLimit: freezed == timeLimit
          ? _value.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      showCorrectAnswers: null == showCorrectAnswers
          ? _value.showCorrectAnswers
          : showCorrectAnswers // ignore: cast_nullable_to_non_nullable
              as bool,
      randomizeQuestions: null == randomizeQuestions
          ? _value.randomizeQuestions
          : randomizeQuestions // ignore: cast_nullable_to_non_nullable
              as bool,
      passingScore: null == passingScore
          ? _value.passingScore
          : passingScore // ignore: cast_nullable_to_non_nullable
              as int,
      totalAttempts: null == totalAttempts
          ? _value.totalAttempts
          : totalAttempts // ignore: cast_nullable_to_non_nullable
              as int,
      averageScore: null == averageScore
          ? _value.averageScore
          : averageScore // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      questions: null == questions
          ? _value.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<QuizQuestion>,
      userAttempts: null == userAttempts
          ? _value.userAttempts
          : userAttempts // ignore: cast_nullable_to_non_nullable
              as List<QuizAttempt>,
      bestAttempt: freezed == bestAttempt
          ? _value.bestAttempt
          : bestAttempt // ignore: cast_nullable_to_non_nullable
              as QuizAttempt?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $QuizAttemptCopyWith<$Res>? get bestAttempt {
    if (_value.bestAttempt == null) {
      return null;
    }

    return $QuizAttemptCopyWith<$Res>(_value.bestAttempt!, (value) {
      return _then(_value.copyWith(bestAttempt: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QuizImplCopyWith<$Res> implements $QuizCopyWith<$Res> {
  factory _$$QuizImplCopyWith(
          _$QuizImpl value, $Res Function(_$QuizImpl) then) =
      __$$QuizImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String instructions,
      @JsonKey(name: 'time_limit') int? timeLimit,
      @JsonKey(name: 'show_correct_answers') bool showCorrectAnswers,
      @JsonKey(name: 'randomize_questions') bool randomizeQuestions,
      @JsonKey(name: 'passing_score') int passingScore,
      @JsonKey(name: 'total_attempts') int totalAttempts,
      @JsonKey(name: 'average_score') double averageScore,
      @JsonKey(name: 'created_at') DateTime createdAt,
      List<QuizQuestion> questions,
      @JsonKey(name: 'user_attempts') List<QuizAttempt> userAttempts,
      @JsonKey(name: 'best_attempt') QuizAttempt? bestAttempt});

  @override
  $QuizAttemptCopyWith<$Res>? get bestAttempt;
}

/// @nodoc
class __$$QuizImplCopyWithImpl<$Res>
    extends _$QuizCopyWithImpl<$Res, _$QuizImpl>
    implements _$$QuizImplCopyWith<$Res> {
  __$$QuizImplCopyWithImpl(_$QuizImpl _value, $Res Function(_$QuizImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? instructions = null,
    Object? timeLimit = freezed,
    Object? showCorrectAnswers = null,
    Object? randomizeQuestions = null,
    Object? passingScore = null,
    Object? totalAttempts = null,
    Object? averageScore = null,
    Object? createdAt = null,
    Object? questions = null,
    Object? userAttempts = null,
    Object? bestAttempt = freezed,
  }) {
    return _then(_$QuizImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      timeLimit: freezed == timeLimit
          ? _value.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      showCorrectAnswers: null == showCorrectAnswers
          ? _value.showCorrectAnswers
          : showCorrectAnswers // ignore: cast_nullable_to_non_nullable
              as bool,
      randomizeQuestions: null == randomizeQuestions
          ? _value.randomizeQuestions
          : randomizeQuestions // ignore: cast_nullable_to_non_nullable
              as bool,
      passingScore: null == passingScore
          ? _value.passingScore
          : passingScore // ignore: cast_nullable_to_non_nullable
              as int,
      totalAttempts: null == totalAttempts
          ? _value.totalAttempts
          : totalAttempts // ignore: cast_nullable_to_non_nullable
              as int,
      averageScore: null == averageScore
          ? _value.averageScore
          : averageScore // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      questions: null == questions
          ? _value._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<QuizQuestion>,
      userAttempts: null == userAttempts
          ? _value._userAttempts
          : userAttempts // ignore: cast_nullable_to_non_nullable
              as List<QuizAttempt>,
      bestAttempt: freezed == bestAttempt
          ? _value.bestAttempt
          : bestAttempt // ignore: cast_nullable_to_non_nullable
              as QuizAttempt?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizImpl implements _Quiz {
  const _$QuizImpl(
      {required this.id,
      this.instructions = '',
      @JsonKey(name: 'time_limit') this.timeLimit,
      @JsonKey(name: 'show_correct_answers') this.showCorrectAnswers = true,
      @JsonKey(name: 'randomize_questions') this.randomizeQuestions = false,
      @JsonKey(name: 'passing_score') this.passingScore = 70,
      @JsonKey(name: 'total_attempts') this.totalAttempts = 0,
      @JsonKey(name: 'average_score') this.averageScore = 0.0,
      @JsonKey(name: 'created_at') required this.createdAt,
      final List<QuizQuestion> questions = const [],
      @JsonKey(name: 'user_attempts')
      final List<QuizAttempt> userAttempts = const [],
      @JsonKey(name: 'best_attempt') this.bestAttempt})
      : _questions = questions,
        _userAttempts = userAttempts;

  factory _$QuizImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey()
  final String instructions;
  @override
  @JsonKey(name: 'time_limit')
  final int? timeLimit;
  @override
  @JsonKey(name: 'show_correct_answers')
  final bool showCorrectAnswers;
  @override
  @JsonKey(name: 'randomize_questions')
  final bool randomizeQuestions;
  @override
  @JsonKey(name: 'passing_score')
  final int passingScore;
  @override
  @JsonKey(name: 'total_attempts')
  final int totalAttempts;
  @override
  @JsonKey(name: 'average_score')
  final double averageScore;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  final List<QuizQuestion> _questions;
  @override
  @JsonKey()
  List<QuizQuestion> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  final List<QuizAttempt> _userAttempts;
  @override
  @JsonKey(name: 'user_attempts')
  List<QuizAttempt> get userAttempts {
    if (_userAttempts is EqualUnmodifiableListView) return _userAttempts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_userAttempts);
  }

  @override
  @JsonKey(name: 'best_attempt')
  final QuizAttempt? bestAttempt;

  @override
  String toString() {
    return 'Quiz(id: $id, instructions: $instructions, timeLimit: $timeLimit, showCorrectAnswers: $showCorrectAnswers, randomizeQuestions: $randomizeQuestions, passingScore: $passingScore, totalAttempts: $totalAttempts, averageScore: $averageScore, createdAt: $createdAt, questions: $questions, userAttempts: $userAttempts, bestAttempt: $bestAttempt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.instructions, instructions) ||
                other.instructions == instructions) &&
            (identical(other.timeLimit, timeLimit) ||
                other.timeLimit == timeLimit) &&
            (identical(other.showCorrectAnswers, showCorrectAnswers) ||
                other.showCorrectAnswers == showCorrectAnswers) &&
            (identical(other.randomizeQuestions, randomizeQuestions) ||
                other.randomizeQuestions == randomizeQuestions) &&
            (identical(other.passingScore, passingScore) ||
                other.passingScore == passingScore) &&
            (identical(other.totalAttempts, totalAttempts) ||
                other.totalAttempts == totalAttempts) &&
            (identical(other.averageScore, averageScore) ||
                other.averageScore == averageScore) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            const DeepCollectionEquality()
                .equals(other._questions, _questions) &&
            const DeepCollectionEquality()
                .equals(other._userAttempts, _userAttempts) &&
            (identical(other.bestAttempt, bestAttempt) ||
                other.bestAttempt == bestAttempt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      instructions,
      timeLimit,
      showCorrectAnswers,
      randomizeQuestions,
      passingScore,
      totalAttempts,
      averageScore,
      createdAt,
      const DeepCollectionEquality().hash(_questions),
      const DeepCollectionEquality().hash(_userAttempts),
      bestAttempt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizImplCopyWith<_$QuizImpl> get copyWith =>
      __$$QuizImplCopyWithImpl<_$QuizImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizImplToJson(
      this,
    );
  }
}

abstract class _Quiz implements Quiz {
  const factory _Quiz(
          {required final int id,
          final String instructions,
          @JsonKey(name: 'time_limit') final int? timeLimit,
          @JsonKey(name: 'show_correct_answers') final bool showCorrectAnswers,
          @JsonKey(name: 'randomize_questions') final bool randomizeQuestions,
          @JsonKey(name: 'passing_score') final int passingScore,
          @JsonKey(name: 'total_attempts') final int totalAttempts,
          @JsonKey(name: 'average_score') final double averageScore,
          @JsonKey(name: 'created_at') required final DateTime createdAt,
          final List<QuizQuestion> questions,
          @JsonKey(name: 'user_attempts') final List<QuizAttempt> userAttempts,
          @JsonKey(name: 'best_attempt') final QuizAttempt? bestAttempt}) =
      _$QuizImpl;

  factory _Quiz.fromJson(Map<String, dynamic> json) = _$QuizImpl.fromJson;

  @override
  int get id;
  @override
  String get instructions;
  @override
  @JsonKey(name: 'time_limit')
  int? get timeLimit;
  @override
  @JsonKey(name: 'show_correct_answers')
  bool get showCorrectAnswers;
  @override
  @JsonKey(name: 'randomize_questions')
  bool get randomizeQuestions;
  @override
  @JsonKey(name: 'passing_score')
  int get passingScore;
  @override
  @JsonKey(name: 'total_attempts')
  int get totalAttempts;
  @override
  @JsonKey(name: 'average_score')
  double get averageScore;
  @override
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  List<QuizQuestion> get questions;
  @override
  @JsonKey(name: 'user_attempts')
  List<QuizAttempt> get userAttempts;
  @override
  @JsonKey(name: 'best_attempt')
  QuizAttempt? get bestAttempt;
  @override
  @JsonKey(ignore: true)
  _$$QuizImplCopyWith<_$QuizImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuizQuestion _$QuizQuestionFromJson(Map<String, dynamic> json) {
  return _QuizQuestion.fromJson(json);
}

/// @nodoc
mixin _$QuizQuestion {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'question_type')
  String get questionType => throw _privateConstructorUsedError;
  @JsonKey(name: 'question_text')
  String get questionText => throw _privateConstructorUsedError;
  String get explanation => throw _privateConstructorUsedError;
  int get points => throw _privateConstructorUsedError;
  int get position => throw _privateConstructorUsedError;
  List<QuizAnswer> get answers => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizQuestionCopyWith<QuizQuestion> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizQuestionCopyWith<$Res> {
  factory $QuizQuestionCopyWith(
          QuizQuestion value, $Res Function(QuizQuestion) then) =
      _$QuizQuestionCopyWithImpl<$Res, QuizQuestion>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'question_type') String questionType,
      @JsonKey(name: 'question_text') String questionText,
      String explanation,
      int points,
      int position,
      List<QuizAnswer> answers});
}

/// @nodoc
class _$QuizQuestionCopyWithImpl<$Res, $Val extends QuizQuestion>
    implements $QuizQuestionCopyWith<$Res> {
  _$QuizQuestionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? questionType = null,
    Object? questionText = null,
    Object? explanation = null,
    Object? points = null,
    Object? position = null,
    Object? answers = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      questionType: null == questionType
          ? _value.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as String,
      questionText: null == questionText
          ? _value.questionText
          : questionText // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: null == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String,
      points: null == points
          ? _value.points
          : points // ignore: cast_nullable_to_non_nullable
              as int,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
      answers: null == answers
          ? _value.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<QuizAnswer>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizQuestionImplCopyWith<$Res>
    implements $QuizQuestionCopyWith<$Res> {
  factory _$$QuizQuestionImplCopyWith(
          _$QuizQuestionImpl value, $Res Function(_$QuizQuestionImpl) then) =
      __$$QuizQuestionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'question_type') String questionType,
      @JsonKey(name: 'question_text') String questionText,
      String explanation,
      int points,
      int position,
      List<QuizAnswer> answers});
}

/// @nodoc
class __$$QuizQuestionImplCopyWithImpl<$Res>
    extends _$QuizQuestionCopyWithImpl<$Res, _$QuizQuestionImpl>
    implements _$$QuizQuestionImplCopyWith<$Res> {
  __$$QuizQuestionImplCopyWithImpl(
      _$QuizQuestionImpl _value, $Res Function(_$QuizQuestionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? questionType = null,
    Object? questionText = null,
    Object? explanation = null,
    Object? points = null,
    Object? position = null,
    Object? answers = null,
  }) {
    return _then(_$QuizQuestionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      questionType: null == questionType
          ? _value.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as String,
      questionText: null == questionText
          ? _value.questionText
          : questionText // ignore: cast_nullable_to_non_nullable
              as String,
      explanation: null == explanation
          ? _value.explanation
          : explanation // ignore: cast_nullable_to_non_nullable
              as String,
      points: null == points
          ? _value.points
          : points // ignore: cast_nullable_to_non_nullable
              as int,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
      answers: null == answers
          ? _value._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<QuizAnswer>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizQuestionImpl implements _QuizQuestion {
  const _$QuizQuestionImpl(
      {required this.id,
      @JsonKey(name: 'question_type') this.questionType = 'multiple_choice',
      @JsonKey(name: 'question_text') required this.questionText,
      this.explanation = '',
      this.points = 1,
      this.position = 0,
      final List<QuizAnswer> answers = const []})
      : _answers = answers;

  factory _$QuizQuestionImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizQuestionImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'question_type')
  final String questionType;
  @override
  @JsonKey(name: 'question_text')
  final String questionText;
  @override
  @JsonKey()
  final String explanation;
  @override
  @JsonKey()
  final int points;
  @override
  @JsonKey()
  final int position;
  final List<QuizAnswer> _answers;
  @override
  @JsonKey()
  List<QuizAnswer> get answers {
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answers);
  }

  @override
  String toString() {
    return 'QuizQuestion(id: $id, questionType: $questionType, questionText: $questionText, explanation: $explanation, points: $points, position: $position, answers: $answers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizQuestionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.questionType, questionType) ||
                other.questionType == questionType) &&
            (identical(other.questionText, questionText) ||
                other.questionText == questionText) &&
            (identical(other.explanation, explanation) ||
                other.explanation == explanation) &&
            (identical(other.points, points) || other.points == points) &&
            (identical(other.position, position) ||
                other.position == position) &&
            const DeepCollectionEquality().equals(other._answers, _answers));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      questionType,
      questionText,
      explanation,
      points,
      position,
      const DeepCollectionEquality().hash(_answers));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizQuestionImplCopyWith<_$QuizQuestionImpl> get copyWith =>
      __$$QuizQuestionImplCopyWithImpl<_$QuizQuestionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizQuestionImplToJson(
      this,
    );
  }
}

abstract class _QuizQuestion implements QuizQuestion {
  const factory _QuizQuestion(
      {required final int id,
      @JsonKey(name: 'question_type') final String questionType,
      @JsonKey(name: 'question_text') required final String questionText,
      final String explanation,
      final int points,
      final int position,
      final List<QuizAnswer> answers}) = _$QuizQuestionImpl;

  factory _QuizQuestion.fromJson(Map<String, dynamic> json) =
      _$QuizQuestionImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'question_type')
  String get questionType;
  @override
  @JsonKey(name: 'question_text')
  String get questionText;
  @override
  String get explanation;
  @override
  int get points;
  @override
  int get position;
  @override
  List<QuizAnswer> get answers;
  @override
  @JsonKey(ignore: true)
  _$$QuizQuestionImplCopyWith<_$QuizQuestionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuizAnswer _$QuizAnswerFromJson(Map<String, dynamic> json) {
  return _QuizAnswer.fromJson(json);
}

/// @nodoc
mixin _$QuizAnswer {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'answer_text')
  String get answerText => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_correct')
  bool get isCorrect => throw _privateConstructorUsedError;
  int get position => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizAnswerCopyWith<QuizAnswer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizAnswerCopyWith<$Res> {
  factory $QuizAnswerCopyWith(
          QuizAnswer value, $Res Function(QuizAnswer) then) =
      _$QuizAnswerCopyWithImpl<$Res, QuizAnswer>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'answer_text') String answerText,
      @JsonKey(name: 'is_correct') bool isCorrect,
      int position});
}

/// @nodoc
class _$QuizAnswerCopyWithImpl<$Res, $Val extends QuizAnswer>
    implements $QuizAnswerCopyWith<$Res> {
  _$QuizAnswerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? answerText = null,
    Object? isCorrect = null,
    Object? position = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      answerText: null == answerText
          ? _value.answerText
          : answerText // ignore: cast_nullable_to_non_nullable
              as String,
      isCorrect: null == isCorrect
          ? _value.isCorrect
          : isCorrect // ignore: cast_nullable_to_non_nullable
              as bool,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizAnswerImplCopyWith<$Res>
    implements $QuizAnswerCopyWith<$Res> {
  factory _$$QuizAnswerImplCopyWith(
          _$QuizAnswerImpl value, $Res Function(_$QuizAnswerImpl) then) =
      __$$QuizAnswerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'answer_text') String answerText,
      @JsonKey(name: 'is_correct') bool isCorrect,
      int position});
}

/// @nodoc
class __$$QuizAnswerImplCopyWithImpl<$Res>
    extends _$QuizAnswerCopyWithImpl<$Res, _$QuizAnswerImpl>
    implements _$$QuizAnswerImplCopyWith<$Res> {
  __$$QuizAnswerImplCopyWithImpl(
      _$QuizAnswerImpl _value, $Res Function(_$QuizAnswerImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? answerText = null,
    Object? isCorrect = null,
    Object? position = null,
  }) {
    return _then(_$QuizAnswerImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      answerText: null == answerText
          ? _value.answerText
          : answerText // ignore: cast_nullable_to_non_nullable
              as String,
      isCorrect: null == isCorrect
          ? _value.isCorrect
          : isCorrect // ignore: cast_nullable_to_non_nullable
              as bool,
      position: null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizAnswerImpl implements _QuizAnswer {
  const _$QuizAnswerImpl(
      {required this.id,
      @JsonKey(name: 'answer_text') required this.answerText,
      @JsonKey(name: 'is_correct') this.isCorrect = false,
      this.position = 0});

  factory _$QuizAnswerImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizAnswerImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'answer_text')
  final String answerText;
  @override
  @JsonKey(name: 'is_correct')
  final bool isCorrect;
  @override
  @JsonKey()
  final int position;

  @override
  String toString() {
    return 'QuizAnswer(id: $id, answerText: $answerText, isCorrect: $isCorrect, position: $position)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizAnswerImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.answerText, answerText) ||
                other.answerText == answerText) &&
            (identical(other.isCorrect, isCorrect) ||
                other.isCorrect == isCorrect) &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, answerText, isCorrect, position);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizAnswerImplCopyWith<_$QuizAnswerImpl> get copyWith =>
      __$$QuizAnswerImplCopyWithImpl<_$QuizAnswerImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizAnswerImplToJson(
      this,
    );
  }
}

abstract class _QuizAnswer implements QuizAnswer {
  const factory _QuizAnswer(
      {required final int id,
      @JsonKey(name: 'answer_text') required final String answerText,
      @JsonKey(name: 'is_correct') final bool isCorrect,
      final int position}) = _$QuizAnswerImpl;

  factory _QuizAnswer.fromJson(Map<String, dynamic> json) =
      _$QuizAnswerImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'answer_text')
  String get answerText;
  @override
  @JsonKey(name: 'is_correct')
  bool get isCorrect;
  @override
  int get position;
  @override
  @JsonKey(ignore: true)
  _$$QuizAnswerImplCopyWith<_$QuizAnswerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuizAttempt _$QuizAttemptFromJson(Map<String, dynamic> json) {
  return _QuizAttempt.fromJson(json);
}

/// @nodoc
mixin _$QuizAttempt {
  int get id => throw _privateConstructorUsedError;
  double get score => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_points')
  int get totalPoints => throw _privateConstructorUsedError;
  @JsonKey(name: 'earned_points')
  int get earnedPoints => throw _privateConstructorUsedError;
  @JsonKey(name: 'time_taken')
  int get timeTaken => throw _privateConstructorUsedError;
  @JsonKey(name: 'completed_at')
  DateTime get completedAt => throw _privateConstructorUsedError;
  Map<String, dynamic> get answers => throw _privateConstructorUsedError;
  bool get passed => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $QuizAttemptCopyWith<QuizAttempt> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuizAttemptCopyWith<$Res> {
  factory $QuizAttemptCopyWith(
          QuizAttempt value, $Res Function(QuizAttempt) then) =
      _$QuizAttemptCopyWithImpl<$Res, QuizAttempt>;
  @useResult
  $Res call(
      {int id,
      double score,
      @JsonKey(name: 'total_points') int totalPoints,
      @JsonKey(name: 'earned_points') int earnedPoints,
      @JsonKey(name: 'time_taken') int timeTaken,
      @JsonKey(name: 'completed_at') DateTime completedAt,
      Map<String, dynamic> answers,
      bool passed});
}

/// @nodoc
class _$QuizAttemptCopyWithImpl<$Res, $Val extends QuizAttempt>
    implements $QuizAttemptCopyWith<$Res> {
  _$QuizAttemptCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? score = null,
    Object? totalPoints = null,
    Object? earnedPoints = null,
    Object? timeTaken = null,
    Object? completedAt = null,
    Object? answers = null,
    Object? passed = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as double,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      earnedPoints: null == earnedPoints
          ? _value.earnedPoints
          : earnedPoints // ignore: cast_nullable_to_non_nullable
              as int,
      timeTaken: null == timeTaken
          ? _value.timeTaken
          : timeTaken // ignore: cast_nullable_to_non_nullable
              as int,
      completedAt: null == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      answers: null == answers
          ? _value.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      passed: null == passed
          ? _value.passed
          : passed // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuizAttemptImplCopyWith<$Res>
    implements $QuizAttemptCopyWith<$Res> {
  factory _$$QuizAttemptImplCopyWith(
          _$QuizAttemptImpl value, $Res Function(_$QuizAttemptImpl) then) =
      __$$QuizAttemptImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      double score,
      @JsonKey(name: 'total_points') int totalPoints,
      @JsonKey(name: 'earned_points') int earnedPoints,
      @JsonKey(name: 'time_taken') int timeTaken,
      @JsonKey(name: 'completed_at') DateTime completedAt,
      Map<String, dynamic> answers,
      bool passed});
}

/// @nodoc
class __$$QuizAttemptImplCopyWithImpl<$Res>
    extends _$QuizAttemptCopyWithImpl<$Res, _$QuizAttemptImpl>
    implements _$$QuizAttemptImplCopyWith<$Res> {
  __$$QuizAttemptImplCopyWithImpl(
      _$QuizAttemptImpl _value, $Res Function(_$QuizAttemptImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? score = null,
    Object? totalPoints = null,
    Object? earnedPoints = null,
    Object? timeTaken = null,
    Object? completedAt = null,
    Object? answers = null,
    Object? passed = null,
  }) {
    return _then(_$QuizAttemptImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      score: null == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as double,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
      earnedPoints: null == earnedPoints
          ? _value.earnedPoints
          : earnedPoints // ignore: cast_nullable_to_non_nullable
              as int,
      timeTaken: null == timeTaken
          ? _value.timeTaken
          : timeTaken // ignore: cast_nullable_to_non_nullable
              as int,
      completedAt: null == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      answers: null == answers
          ? _value._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      passed: null == passed
          ? _value.passed
          : passed // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuizAttemptImpl implements _QuizAttempt {
  const _$QuizAttemptImpl(
      {required this.id,
      this.score = 0.0,
      @JsonKey(name: 'total_points') this.totalPoints = 0,
      @JsonKey(name: 'earned_points') this.earnedPoints = 0,
      @JsonKey(name: 'time_taken') this.timeTaken = 0,
      @JsonKey(name: 'completed_at') required this.completedAt,
      final Map<String, dynamic> answers = const {},
      this.passed = false})
      : _answers = answers;

  factory _$QuizAttemptImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuizAttemptImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey()
  final double score;
  @override
  @JsonKey(name: 'total_points')
  final int totalPoints;
  @override
  @JsonKey(name: 'earned_points')
  final int earnedPoints;
  @override
  @JsonKey(name: 'time_taken')
  final int timeTaken;
  @override
  @JsonKey(name: 'completed_at')
  final DateTime completedAt;
  final Map<String, dynamic> _answers;
  @override
  @JsonKey()
  Map<String, dynamic> get answers {
    if (_answers is EqualUnmodifiableMapView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_answers);
  }

  @override
  @JsonKey()
  final bool passed;

  @override
  String toString() {
    return 'QuizAttempt(id: $id, score: $score, totalPoints: $totalPoints, earnedPoints: $earnedPoints, timeTaken: $timeTaken, completedAt: $completedAt, answers: $answers, passed: $passed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuizAttemptImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints) &&
            (identical(other.earnedPoints, earnedPoints) ||
                other.earnedPoints == earnedPoints) &&
            (identical(other.timeTaken, timeTaken) ||
                other.timeTaken == timeTaken) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            const DeepCollectionEquality().equals(other._answers, _answers) &&
            (identical(other.passed, passed) || other.passed == passed));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      score,
      totalPoints,
      earnedPoints,
      timeTaken,
      completedAt,
      const DeepCollectionEquality().hash(_answers),
      passed);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$QuizAttemptImplCopyWith<_$QuizAttemptImpl> get copyWith =>
      __$$QuizAttemptImplCopyWithImpl<_$QuizAttemptImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuizAttemptImplToJson(
      this,
    );
  }
}

abstract class _QuizAttempt implements QuizAttempt {
  const factory _QuizAttempt(
      {required final int id,
      final double score,
      @JsonKey(name: 'total_points') final int totalPoints,
      @JsonKey(name: 'earned_points') final int earnedPoints,
      @JsonKey(name: 'time_taken') final int timeTaken,
      @JsonKey(name: 'completed_at') required final DateTime completedAt,
      final Map<String, dynamic> answers,
      final bool passed}) = _$QuizAttemptImpl;

  factory _QuizAttempt.fromJson(Map<String, dynamic> json) =
      _$QuizAttemptImpl.fromJson;

  @override
  int get id;
  @override
  double get score;
  @override
  @JsonKey(name: 'total_points')
  int get totalPoints;
  @override
  @JsonKey(name: 'earned_points')
  int get earnedPoints;
  @override
  @JsonKey(name: 'time_taken')
  int get timeTaken;
  @override
  @JsonKey(name: 'completed_at')
  DateTime get completedAt;
  @override
  Map<String, dynamic> get answers;
  @override
  bool get passed;
  @override
  @JsonKey(ignore: true)
  _$$QuizAttemptImplCopyWith<_$QuizAttemptImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
