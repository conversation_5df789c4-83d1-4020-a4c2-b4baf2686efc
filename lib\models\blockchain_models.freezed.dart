// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'blockchain_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BlockchainWallet _$BlockchainWalletFromJson(Map<String, dynamic> json) {
  return _BlockchainWallet.fromJson(json);
}

/// @nodoc
mixin _$BlockchainWallet {
  String get address => throw _privateConstructorUsedError;
  String get network => throw _privateConstructorUsedError;
  bool get isPrimary => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  List<TokenBalance> get balances => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get activatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BlockchainWalletCopyWith<BlockchainWallet> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlockchainWalletCopyWith<$Res> {
  factory $BlockchainWalletCopyWith(
          BlockchainWallet value, $Res Function(BlockchainWallet) then) =
      _$BlockchainWalletCopyWithImpl<$Res, BlockchainWallet>;
  @useResult
  $Res call(
      {String address,
      String network,
      bool isPrimary,
      DateTime createdAt,
      List<TokenBalance> balances,
      bool isActive,
      DateTime? activatedAt});
}

/// @nodoc
class _$BlockchainWalletCopyWithImpl<$Res, $Val extends BlockchainWallet>
    implements $BlockchainWalletCopyWith<$Res> {
  _$BlockchainWalletCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
    Object? network = null,
    Object? isPrimary = null,
    Object? createdAt = null,
    Object? balances = null,
    Object? isActive = null,
    Object? activatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      network: null == network
          ? _value.network
          : network // ignore: cast_nullable_to_non_nullable
              as String,
      isPrimary: null == isPrimary
          ? _value.isPrimary
          : isPrimary // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      balances: null == balances
          ? _value.balances
          : balances // ignore: cast_nullable_to_non_nullable
              as List<TokenBalance>,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      activatedAt: freezed == activatedAt
          ? _value.activatedAt
          : activatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlockchainWalletImplCopyWith<$Res>
    implements $BlockchainWalletCopyWith<$Res> {
  factory _$$BlockchainWalletImplCopyWith(_$BlockchainWalletImpl value,
          $Res Function(_$BlockchainWalletImpl) then) =
      __$$BlockchainWalletImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String address,
      String network,
      bool isPrimary,
      DateTime createdAt,
      List<TokenBalance> balances,
      bool isActive,
      DateTime? activatedAt});
}

/// @nodoc
class __$$BlockchainWalletImplCopyWithImpl<$Res>
    extends _$BlockchainWalletCopyWithImpl<$Res, _$BlockchainWalletImpl>
    implements _$$BlockchainWalletImplCopyWith<$Res> {
  __$$BlockchainWalletImplCopyWithImpl(_$BlockchainWalletImpl _value,
      $Res Function(_$BlockchainWalletImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
    Object? network = null,
    Object? isPrimary = null,
    Object? createdAt = null,
    Object? balances = null,
    Object? isActive = null,
    Object? activatedAt = freezed,
  }) {
    return _then(_$BlockchainWalletImpl(
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      network: null == network
          ? _value.network
          : network // ignore: cast_nullable_to_non_nullable
              as String,
      isPrimary: null == isPrimary
          ? _value.isPrimary
          : isPrimary // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      balances: null == balances
          ? _value._balances
          : balances // ignore: cast_nullable_to_non_nullable
              as List<TokenBalance>,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      activatedAt: freezed == activatedAt
          ? _value.activatedAt
          : activatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BlockchainWalletImpl implements _BlockchainWallet {
  const _$BlockchainWalletImpl(
      {required this.address,
      required this.network,
      required this.isPrimary,
      required this.createdAt,
      required final List<TokenBalance> balances,
      this.isActive = false,
      this.activatedAt})
      : _balances = balances;

  factory _$BlockchainWalletImpl.fromJson(Map<String, dynamic> json) =>
      _$$BlockchainWalletImplFromJson(json);

  @override
  final String address;
  @override
  final String network;
  @override
  final bool isPrimary;
  @override
  final DateTime createdAt;
  final List<TokenBalance> _balances;
  @override
  List<TokenBalance> get balances {
    if (_balances is EqualUnmodifiableListView) return _balances;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_balances);
  }

  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? activatedAt;

  @override
  String toString() {
    return 'BlockchainWallet(address: $address, network: $network, isPrimary: $isPrimary, createdAt: $createdAt, balances: $balances, isActive: $isActive, activatedAt: $activatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlockchainWalletImpl &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.network, network) || other.network == network) &&
            (identical(other.isPrimary, isPrimary) ||
                other.isPrimary == isPrimary) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            const DeepCollectionEquality().equals(other._balances, _balances) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.activatedAt, activatedAt) ||
                other.activatedAt == activatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      address,
      network,
      isPrimary,
      createdAt,
      const DeepCollectionEquality().hash(_balances),
      isActive,
      activatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BlockchainWalletImplCopyWith<_$BlockchainWalletImpl> get copyWith =>
      __$$BlockchainWalletImplCopyWithImpl<_$BlockchainWalletImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BlockchainWalletImplToJson(
      this,
    );
  }
}

abstract class _BlockchainWallet implements BlockchainWallet {
  const factory _BlockchainWallet(
      {required final String address,
      required final String network,
      required final bool isPrimary,
      required final DateTime createdAt,
      required final List<TokenBalance> balances,
      final bool isActive,
      final DateTime? activatedAt}) = _$BlockchainWalletImpl;

  factory _BlockchainWallet.fromJson(Map<String, dynamic> json) =
      _$BlockchainWalletImpl.fromJson;

  @override
  String get address;
  @override
  String get network;
  @override
  bool get isPrimary;
  @override
  DateTime get createdAt;
  @override
  List<TokenBalance> get balances;
  @override
  bool get isActive;
  @override
  DateTime? get activatedAt;
  @override
  @JsonKey(ignore: true)
  _$$BlockchainWalletImplCopyWith<_$BlockchainWalletImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TokenBalance _$TokenBalanceFromJson(Map<String, dynamic> json) {
  return _TokenBalance.fromJson(json);
}

/// @nodoc
mixin _$TokenBalance {
  String get contractName => throw _privateConstructorUsedError;
  String get contractAddress => throw _privateConstructorUsedError;
  String get balance => throw _privateConstructorUsedError;
  String get stakedBalance => throw _privateConstructorUsedError;
  String get totalBalance => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TokenBalanceCopyWith<TokenBalance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TokenBalanceCopyWith<$Res> {
  factory $TokenBalanceCopyWith(
          TokenBalance value, $Res Function(TokenBalance) then) =
      _$TokenBalanceCopyWithImpl<$Res, TokenBalance>;
  @useResult
  $Res call(
      {String contractName,
      String contractAddress,
      String balance,
      String stakedBalance,
      String totalBalance});
}

/// @nodoc
class _$TokenBalanceCopyWithImpl<$Res, $Val extends TokenBalance>
    implements $TokenBalanceCopyWith<$Res> {
  _$TokenBalanceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractName = null,
    Object? contractAddress = null,
    Object? balance = null,
    Object? stakedBalance = null,
    Object? totalBalance = null,
  }) {
    return _then(_value.copyWith(
      contractName: null == contractName
          ? _value.contractName
          : contractName // ignore: cast_nullable_to_non_nullable
              as String,
      contractAddress: null == contractAddress
          ? _value.contractAddress
          : contractAddress // ignore: cast_nullable_to_non_nullable
              as String,
      balance: null == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as String,
      stakedBalance: null == stakedBalance
          ? _value.stakedBalance
          : stakedBalance // ignore: cast_nullable_to_non_nullable
              as String,
      totalBalance: null == totalBalance
          ? _value.totalBalance
          : totalBalance // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TokenBalanceImplCopyWith<$Res>
    implements $TokenBalanceCopyWith<$Res> {
  factory _$$TokenBalanceImplCopyWith(
          _$TokenBalanceImpl value, $Res Function(_$TokenBalanceImpl) then) =
      __$$TokenBalanceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String contractName,
      String contractAddress,
      String balance,
      String stakedBalance,
      String totalBalance});
}

/// @nodoc
class __$$TokenBalanceImplCopyWithImpl<$Res>
    extends _$TokenBalanceCopyWithImpl<$Res, _$TokenBalanceImpl>
    implements _$$TokenBalanceImplCopyWith<$Res> {
  __$$TokenBalanceImplCopyWithImpl(
      _$TokenBalanceImpl _value, $Res Function(_$TokenBalanceImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractName = null,
    Object? contractAddress = null,
    Object? balance = null,
    Object? stakedBalance = null,
    Object? totalBalance = null,
  }) {
    return _then(_$TokenBalanceImpl(
      contractName: null == contractName
          ? _value.contractName
          : contractName // ignore: cast_nullable_to_non_nullable
              as String,
      contractAddress: null == contractAddress
          ? _value.contractAddress
          : contractAddress // ignore: cast_nullable_to_non_nullable
              as String,
      balance: null == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as String,
      stakedBalance: null == stakedBalance
          ? _value.stakedBalance
          : stakedBalance // ignore: cast_nullable_to_non_nullable
              as String,
      totalBalance: null == totalBalance
          ? _value.totalBalance
          : totalBalance // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TokenBalanceImpl implements _TokenBalance {
  const _$TokenBalanceImpl(
      {required this.contractName,
      required this.contractAddress,
      required this.balance,
      required this.stakedBalance,
      required this.totalBalance});

  factory _$TokenBalanceImpl.fromJson(Map<String, dynamic> json) =>
      _$$TokenBalanceImplFromJson(json);

  @override
  final String contractName;
  @override
  final String contractAddress;
  @override
  final String balance;
  @override
  final String stakedBalance;
  @override
  final String totalBalance;

  @override
  String toString() {
    return 'TokenBalance(contractName: $contractName, contractAddress: $contractAddress, balance: $balance, stakedBalance: $stakedBalance, totalBalance: $totalBalance)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TokenBalanceImpl &&
            (identical(other.contractName, contractName) ||
                other.contractName == contractName) &&
            (identical(other.contractAddress, contractAddress) ||
                other.contractAddress == contractAddress) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.stakedBalance, stakedBalance) ||
                other.stakedBalance == stakedBalance) &&
            (identical(other.totalBalance, totalBalance) ||
                other.totalBalance == totalBalance));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, contractName, contractAddress,
      balance, stakedBalance, totalBalance);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TokenBalanceImplCopyWith<_$TokenBalanceImpl> get copyWith =>
      __$$TokenBalanceImplCopyWithImpl<_$TokenBalanceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TokenBalanceImplToJson(
      this,
    );
  }
}

abstract class _TokenBalance implements TokenBalance {
  const factory _TokenBalance(
      {required final String contractName,
      required final String contractAddress,
      required final String balance,
      required final String stakedBalance,
      required final String totalBalance}) = _$TokenBalanceImpl;

  factory _TokenBalance.fromJson(Map<String, dynamic> json) =
      _$TokenBalanceImpl.fromJson;

  @override
  String get contractName;
  @override
  String get contractAddress;
  @override
  String get balance;
  @override
  String get stakedBalance;
  @override
  String get totalBalance;
  @override
  @JsonKey(ignore: true)
  _$$TokenBalanceImplCopyWith<_$TokenBalanceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NFTAsset _$NFTAssetFromJson(Map<String, dynamic> json) {
  return _NFTAsset.fromJson(json);
}

/// @nodoc
mixin _$NFTAsset {
  int get id => throw _privateConstructorUsedError;
  int get tokenId => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  int get rarity => throw _privateConstructorUsedError;
  String get rarityDisplay => throw _privateConstructorUsedError;
  String get contractAddress => throw _privateConstructorUsedError;
  String get network => throw _privateConstructorUsedError;
  DateTime get mintedAt => throw _privateConstructorUsedError;
  Map<String, dynamic> get attributes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $NFTAssetCopyWith<NFTAsset> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NFTAssetCopyWith<$Res> {
  factory $NFTAssetCopyWith(NFTAsset value, $Res Function(NFTAsset) then) =
      _$NFTAssetCopyWithImpl<$Res, NFTAsset>;
  @useResult
  $Res call(
      {int id,
      int tokenId,
      String name,
      String description,
      String imageUrl,
      int rarity,
      String rarityDisplay,
      String contractAddress,
      String network,
      DateTime mintedAt,
      Map<String, dynamic> attributes});
}

/// @nodoc
class _$NFTAssetCopyWithImpl<$Res, $Val extends NFTAsset>
    implements $NFTAssetCopyWith<$Res> {
  _$NFTAssetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? tokenId = null,
    Object? name = null,
    Object? description = null,
    Object? imageUrl = null,
    Object? rarity = null,
    Object? rarityDisplay = null,
    Object? contractAddress = null,
    Object? network = null,
    Object? mintedAt = null,
    Object? attributes = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      tokenId: null == tokenId
          ? _value.tokenId
          : tokenId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      rarity: null == rarity
          ? _value.rarity
          : rarity // ignore: cast_nullable_to_non_nullable
              as int,
      rarityDisplay: null == rarityDisplay
          ? _value.rarityDisplay
          : rarityDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      contractAddress: null == contractAddress
          ? _value.contractAddress
          : contractAddress // ignore: cast_nullable_to_non_nullable
              as String,
      network: null == network
          ? _value.network
          : network // ignore: cast_nullable_to_non_nullable
              as String,
      mintedAt: null == mintedAt
          ? _value.mintedAt
          : mintedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      attributes: null == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NFTAssetImplCopyWith<$Res>
    implements $NFTAssetCopyWith<$Res> {
  factory _$$NFTAssetImplCopyWith(
          _$NFTAssetImpl value, $Res Function(_$NFTAssetImpl) then) =
      __$$NFTAssetImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      int tokenId,
      String name,
      String description,
      String imageUrl,
      int rarity,
      String rarityDisplay,
      String contractAddress,
      String network,
      DateTime mintedAt,
      Map<String, dynamic> attributes});
}

/// @nodoc
class __$$NFTAssetImplCopyWithImpl<$Res>
    extends _$NFTAssetCopyWithImpl<$Res, _$NFTAssetImpl>
    implements _$$NFTAssetImplCopyWith<$Res> {
  __$$NFTAssetImplCopyWithImpl(
      _$NFTAssetImpl _value, $Res Function(_$NFTAssetImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? tokenId = null,
    Object? name = null,
    Object? description = null,
    Object? imageUrl = null,
    Object? rarity = null,
    Object? rarityDisplay = null,
    Object? contractAddress = null,
    Object? network = null,
    Object? mintedAt = null,
    Object? attributes = null,
  }) {
    return _then(_$NFTAssetImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      tokenId: null == tokenId
          ? _value.tokenId
          : tokenId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      rarity: null == rarity
          ? _value.rarity
          : rarity // ignore: cast_nullable_to_non_nullable
              as int,
      rarityDisplay: null == rarityDisplay
          ? _value.rarityDisplay
          : rarityDisplay // ignore: cast_nullable_to_non_nullable
              as String,
      contractAddress: null == contractAddress
          ? _value.contractAddress
          : contractAddress // ignore: cast_nullable_to_non_nullable
              as String,
      network: null == network
          ? _value.network
          : network // ignore: cast_nullable_to_non_nullable
              as String,
      mintedAt: null == mintedAt
          ? _value.mintedAt
          : mintedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      attributes: null == attributes
          ? _value._attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NFTAssetImpl implements _NFTAsset {
  const _$NFTAssetImpl(
      {required this.id,
      required this.tokenId,
      required this.name,
      required this.description,
      required this.imageUrl,
      required this.rarity,
      required this.rarityDisplay,
      required this.contractAddress,
      required this.network,
      required this.mintedAt,
      required final Map<String, dynamic> attributes})
      : _attributes = attributes;

  factory _$NFTAssetImpl.fromJson(Map<String, dynamic> json) =>
      _$$NFTAssetImplFromJson(json);

  @override
  final int id;
  @override
  final int tokenId;
  @override
  final String name;
  @override
  final String description;
  @override
  final String imageUrl;
  @override
  final int rarity;
  @override
  final String rarityDisplay;
  @override
  final String contractAddress;
  @override
  final String network;
  @override
  final DateTime mintedAt;
  final Map<String, dynamic> _attributes;
  @override
  Map<String, dynamic> get attributes {
    if (_attributes is EqualUnmodifiableMapView) return _attributes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_attributes);
  }

  @override
  String toString() {
    return 'NFTAsset(id: $id, tokenId: $tokenId, name: $name, description: $description, imageUrl: $imageUrl, rarity: $rarity, rarityDisplay: $rarityDisplay, contractAddress: $contractAddress, network: $network, mintedAt: $mintedAt, attributes: $attributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NFTAssetImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.tokenId, tokenId) || other.tokenId == tokenId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.rarity, rarity) || other.rarity == rarity) &&
            (identical(other.rarityDisplay, rarityDisplay) ||
                other.rarityDisplay == rarityDisplay) &&
            (identical(other.contractAddress, contractAddress) ||
                other.contractAddress == contractAddress) &&
            (identical(other.network, network) || other.network == network) &&
            (identical(other.mintedAt, mintedAt) ||
                other.mintedAt == mintedAt) &&
            const DeepCollectionEquality()
                .equals(other._attributes, _attributes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      tokenId,
      name,
      description,
      imageUrl,
      rarity,
      rarityDisplay,
      contractAddress,
      network,
      mintedAt,
      const DeepCollectionEquality().hash(_attributes));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NFTAssetImplCopyWith<_$NFTAssetImpl> get copyWith =>
      __$$NFTAssetImplCopyWithImpl<_$NFTAssetImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NFTAssetImplToJson(
      this,
    );
  }
}

abstract class _NFTAsset implements NFTAsset {
  const factory _NFTAsset(
      {required final int id,
      required final int tokenId,
      required final String name,
      required final String description,
      required final String imageUrl,
      required final int rarity,
      required final String rarityDisplay,
      required final String contractAddress,
      required final String network,
      required final DateTime mintedAt,
      required final Map<String, dynamic> attributes}) = _$NFTAssetImpl;

  factory _NFTAsset.fromJson(Map<String, dynamic> json) =
      _$NFTAssetImpl.fromJson;

  @override
  int get id;
  @override
  int get tokenId;
  @override
  String get name;
  @override
  String get description;
  @override
  String get imageUrl;
  @override
  int get rarity;
  @override
  String get rarityDisplay;
  @override
  String get contractAddress;
  @override
  String get network;
  @override
  DateTime get mintedAt;
  @override
  Map<String, dynamic> get attributes;
  @override
  @JsonKey(ignore: true)
  _$$NFTAssetImplCopyWith<_$NFTAssetImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StakingPool _$StakingPoolFromJson(Map<String, dynamic> json) {
  return _StakingPool.fromJson(json);
}

/// @nodoc
mixin _$StakingPool {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get apyPercentage => throw _privateConstructorUsedError;
  String get minimumStake => throw _privateConstructorUsedError;
  String? get maximumStake => throw _privateConstructorUsedError;
  String get totalStaked => throw _privateConstructorUsedError;
  int get activeStakers => throw _privateConstructorUsedError;
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StakingPoolCopyWith<StakingPool> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StakingPoolCopyWith<$Res> {
  factory $StakingPoolCopyWith(
          StakingPool value, $Res Function(StakingPool) then) =
      _$StakingPoolCopyWithImpl<$Res, StakingPool>;
  @useResult
  $Res call(
      {int id,
      String name,
      String description,
      String apyPercentage,
      String minimumStake,
      String? maximumStake,
      String totalStaked,
      int activeStakers,
      DateTime startDate,
      DateTime? endDate});
}

/// @nodoc
class _$StakingPoolCopyWithImpl<$Res, $Val extends StakingPool>
    implements $StakingPoolCopyWith<$Res> {
  _$StakingPoolCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? apyPercentage = null,
    Object? minimumStake = null,
    Object? maximumStake = freezed,
    Object? totalStaked = null,
    Object? activeStakers = null,
    Object? startDate = null,
    Object? endDate = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      apyPercentage: null == apyPercentage
          ? _value.apyPercentage
          : apyPercentage // ignore: cast_nullable_to_non_nullable
              as String,
      minimumStake: null == minimumStake
          ? _value.minimumStake
          : minimumStake // ignore: cast_nullable_to_non_nullable
              as String,
      maximumStake: freezed == maximumStake
          ? _value.maximumStake
          : maximumStake // ignore: cast_nullable_to_non_nullable
              as String?,
      totalStaked: null == totalStaked
          ? _value.totalStaked
          : totalStaked // ignore: cast_nullable_to_non_nullable
              as String,
      activeStakers: null == activeStakers
          ? _value.activeStakers
          : activeStakers // ignore: cast_nullable_to_non_nullable
              as int,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StakingPoolImplCopyWith<$Res>
    implements $StakingPoolCopyWith<$Res> {
  factory _$$StakingPoolImplCopyWith(
          _$StakingPoolImpl value, $Res Function(_$StakingPoolImpl) then) =
      __$$StakingPoolImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      String description,
      String apyPercentage,
      String minimumStake,
      String? maximumStake,
      String totalStaked,
      int activeStakers,
      DateTime startDate,
      DateTime? endDate});
}

/// @nodoc
class __$$StakingPoolImplCopyWithImpl<$Res>
    extends _$StakingPoolCopyWithImpl<$Res, _$StakingPoolImpl>
    implements _$$StakingPoolImplCopyWith<$Res> {
  __$$StakingPoolImplCopyWithImpl(
      _$StakingPoolImpl _value, $Res Function(_$StakingPoolImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? apyPercentage = null,
    Object? minimumStake = null,
    Object? maximumStake = freezed,
    Object? totalStaked = null,
    Object? activeStakers = null,
    Object? startDate = null,
    Object? endDate = freezed,
  }) {
    return _then(_$StakingPoolImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      apyPercentage: null == apyPercentage
          ? _value.apyPercentage
          : apyPercentage // ignore: cast_nullable_to_non_nullable
              as String,
      minimumStake: null == minimumStake
          ? _value.minimumStake
          : minimumStake // ignore: cast_nullable_to_non_nullable
              as String,
      maximumStake: freezed == maximumStake
          ? _value.maximumStake
          : maximumStake // ignore: cast_nullable_to_non_nullable
              as String?,
      totalStaked: null == totalStaked
          ? _value.totalStaked
          : totalStaked // ignore: cast_nullable_to_non_nullable
              as String,
      activeStakers: null == activeStakers
          ? _value.activeStakers
          : activeStakers // ignore: cast_nullable_to_non_nullable
              as int,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StakingPoolImpl implements _StakingPool {
  const _$StakingPoolImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.apyPercentage,
      required this.minimumStake,
      this.maximumStake,
      required this.totalStaked,
      required this.activeStakers,
      required this.startDate,
      this.endDate});

  factory _$StakingPoolImpl.fromJson(Map<String, dynamic> json) =>
      _$$StakingPoolImplFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String apyPercentage;
  @override
  final String minimumStake;
  @override
  final String? maximumStake;
  @override
  final String totalStaked;
  @override
  final int activeStakers;
  @override
  final DateTime startDate;
  @override
  final DateTime? endDate;

  @override
  String toString() {
    return 'StakingPool(id: $id, name: $name, description: $description, apyPercentage: $apyPercentage, minimumStake: $minimumStake, maximumStake: $maximumStake, totalStaked: $totalStaked, activeStakers: $activeStakers, startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StakingPoolImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.apyPercentage, apyPercentage) ||
                other.apyPercentage == apyPercentage) &&
            (identical(other.minimumStake, minimumStake) ||
                other.minimumStake == minimumStake) &&
            (identical(other.maximumStake, maximumStake) ||
                other.maximumStake == maximumStake) &&
            (identical(other.totalStaked, totalStaked) ||
                other.totalStaked == totalStaked) &&
            (identical(other.activeStakers, activeStakers) ||
                other.activeStakers == activeStakers) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      apyPercentage,
      minimumStake,
      maximumStake,
      totalStaked,
      activeStakers,
      startDate,
      endDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StakingPoolImplCopyWith<_$StakingPoolImpl> get copyWith =>
      __$$StakingPoolImplCopyWithImpl<_$StakingPoolImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StakingPoolImplToJson(
      this,
    );
  }
}

abstract class _StakingPool implements StakingPool {
  const factory _StakingPool(
      {required final int id,
      required final String name,
      required final String description,
      required final String apyPercentage,
      required final String minimumStake,
      final String? maximumStake,
      required final String totalStaked,
      required final int activeStakers,
      required final DateTime startDate,
      final DateTime? endDate}) = _$StakingPoolImpl;

  factory _StakingPool.fromJson(Map<String, dynamic> json) =
      _$StakingPoolImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String get apyPercentage;
  @override
  String get minimumStake;
  @override
  String? get maximumStake;
  @override
  String get totalStaked;
  @override
  int get activeStakers;
  @override
  DateTime get startDate;
  @override
  DateTime? get endDate;
  @override
  @JsonKey(ignore: true)
  _$$StakingPoolImplCopyWith<_$StakingPoolImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserStake _$UserStakeFromJson(Map<String, dynamic> json) {
  return _UserStake.fromJson(json);
}

/// @nodoc
mixin _$UserStake {
  int get id => throw _privateConstructorUsedError;
  String get poolName => throw _privateConstructorUsedError;
  String get amountStaked => throw _privateConstructorUsedError;
  String get rewardsEarned => throw _privateConstructorUsedError;
  String get pendingRewards => throw _privateConstructorUsedError;
  String get apyPercentage => throw _privateConstructorUsedError;
  DateTime get stakedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserStakeCopyWith<UserStake> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserStakeCopyWith<$Res> {
  factory $UserStakeCopyWith(UserStake value, $Res Function(UserStake) then) =
      _$UserStakeCopyWithImpl<$Res, UserStake>;
  @useResult
  $Res call(
      {int id,
      String poolName,
      String amountStaked,
      String rewardsEarned,
      String pendingRewards,
      String apyPercentage,
      DateTime stakedAt});
}

/// @nodoc
class _$UserStakeCopyWithImpl<$Res, $Val extends UserStake>
    implements $UserStakeCopyWith<$Res> {
  _$UserStakeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? poolName = null,
    Object? amountStaked = null,
    Object? rewardsEarned = null,
    Object? pendingRewards = null,
    Object? apyPercentage = null,
    Object? stakedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      poolName: null == poolName
          ? _value.poolName
          : poolName // ignore: cast_nullable_to_non_nullable
              as String,
      amountStaked: null == amountStaked
          ? _value.amountStaked
          : amountStaked // ignore: cast_nullable_to_non_nullable
              as String,
      rewardsEarned: null == rewardsEarned
          ? _value.rewardsEarned
          : rewardsEarned // ignore: cast_nullable_to_non_nullable
              as String,
      pendingRewards: null == pendingRewards
          ? _value.pendingRewards
          : pendingRewards // ignore: cast_nullable_to_non_nullable
              as String,
      apyPercentage: null == apyPercentage
          ? _value.apyPercentage
          : apyPercentage // ignore: cast_nullable_to_non_nullable
              as String,
      stakedAt: null == stakedAt
          ? _value.stakedAt
          : stakedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserStakeImplCopyWith<$Res>
    implements $UserStakeCopyWith<$Res> {
  factory _$$UserStakeImplCopyWith(
          _$UserStakeImpl value, $Res Function(_$UserStakeImpl) then) =
      __$$UserStakeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String poolName,
      String amountStaked,
      String rewardsEarned,
      String pendingRewards,
      String apyPercentage,
      DateTime stakedAt});
}

/// @nodoc
class __$$UserStakeImplCopyWithImpl<$Res>
    extends _$UserStakeCopyWithImpl<$Res, _$UserStakeImpl>
    implements _$$UserStakeImplCopyWith<$Res> {
  __$$UserStakeImplCopyWithImpl(
      _$UserStakeImpl _value, $Res Function(_$UserStakeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? poolName = null,
    Object? amountStaked = null,
    Object? rewardsEarned = null,
    Object? pendingRewards = null,
    Object? apyPercentage = null,
    Object? stakedAt = null,
  }) {
    return _then(_$UserStakeImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      poolName: null == poolName
          ? _value.poolName
          : poolName // ignore: cast_nullable_to_non_nullable
              as String,
      amountStaked: null == amountStaked
          ? _value.amountStaked
          : amountStaked // ignore: cast_nullable_to_non_nullable
              as String,
      rewardsEarned: null == rewardsEarned
          ? _value.rewardsEarned
          : rewardsEarned // ignore: cast_nullable_to_non_nullable
              as String,
      pendingRewards: null == pendingRewards
          ? _value.pendingRewards
          : pendingRewards // ignore: cast_nullable_to_non_nullable
              as String,
      apyPercentage: null == apyPercentage
          ? _value.apyPercentage
          : apyPercentage // ignore: cast_nullable_to_non_nullable
              as String,
      stakedAt: null == stakedAt
          ? _value.stakedAt
          : stakedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserStakeImpl implements _UserStake {
  const _$UserStakeImpl(
      {required this.id,
      required this.poolName,
      required this.amountStaked,
      required this.rewardsEarned,
      required this.pendingRewards,
      required this.apyPercentage,
      required this.stakedAt});

  factory _$UserStakeImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserStakeImplFromJson(json);

  @override
  final int id;
  @override
  final String poolName;
  @override
  final String amountStaked;
  @override
  final String rewardsEarned;
  @override
  final String pendingRewards;
  @override
  final String apyPercentage;
  @override
  final DateTime stakedAt;

  @override
  String toString() {
    return 'UserStake(id: $id, poolName: $poolName, amountStaked: $amountStaked, rewardsEarned: $rewardsEarned, pendingRewards: $pendingRewards, apyPercentage: $apyPercentage, stakedAt: $stakedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserStakeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.poolName, poolName) ||
                other.poolName == poolName) &&
            (identical(other.amountStaked, amountStaked) ||
                other.amountStaked == amountStaked) &&
            (identical(other.rewardsEarned, rewardsEarned) ||
                other.rewardsEarned == rewardsEarned) &&
            (identical(other.pendingRewards, pendingRewards) ||
                other.pendingRewards == pendingRewards) &&
            (identical(other.apyPercentage, apyPercentage) ||
                other.apyPercentage == apyPercentage) &&
            (identical(other.stakedAt, stakedAt) ||
                other.stakedAt == stakedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, poolName, amountStaked,
      rewardsEarned, pendingRewards, apyPercentage, stakedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserStakeImplCopyWith<_$UserStakeImpl> get copyWith =>
      __$$UserStakeImplCopyWithImpl<_$UserStakeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserStakeImplToJson(
      this,
    );
  }
}

abstract class _UserStake implements UserStake {
  const factory _UserStake(
      {required final int id,
      required final String poolName,
      required final String amountStaked,
      required final String rewardsEarned,
      required final String pendingRewards,
      required final String apyPercentage,
      required final DateTime stakedAt}) = _$UserStakeImpl;

  factory _UserStake.fromJson(Map<String, dynamic> json) =
      _$UserStakeImpl.fromJson;

  @override
  int get id;
  @override
  String get poolName;
  @override
  String get amountStaked;
  @override
  String get rewardsEarned;
  @override
  String get pendingRewards;
  @override
  String get apyPercentage;
  @override
  DateTime get stakedAt;
  @override
  @JsonKey(ignore: true)
  _$$UserStakeImplCopyWith<_$UserStakeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BlockchainTransaction _$BlockchainTransactionFromJson(
    Map<String, dynamic> json) {
  return _BlockchainTransaction.fromJson(json);
}

/// @nodoc
mixin _$BlockchainTransaction {
  String get id => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String? get amount => throw _privateConstructorUsedError;
  String? get txHash => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BlockchainTransactionCopyWith<BlockchainTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlockchainTransactionCopyWith<$Res> {
  factory $BlockchainTransactionCopyWith(BlockchainTransaction value,
          $Res Function(BlockchainTransaction) then) =
      _$BlockchainTransactionCopyWithImpl<$Res, BlockchainTransaction>;
  @useResult
  $Res call(
      {String id,
      String type,
      String status,
      String? amount,
      String? txHash,
      DateTime createdAt,
      String? description});
}

/// @nodoc
class _$BlockchainTransactionCopyWithImpl<$Res,
        $Val extends BlockchainTransaction>
    implements $BlockchainTransactionCopyWith<$Res> {
  _$BlockchainTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? status = null,
    Object? amount = freezed,
    Object? txHash = freezed,
    Object? createdAt = null,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      txHash: freezed == txHash
          ? _value.txHash
          : txHash // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlockchainTransactionImplCopyWith<$Res>
    implements $BlockchainTransactionCopyWith<$Res> {
  factory _$$BlockchainTransactionImplCopyWith(
          _$BlockchainTransactionImpl value,
          $Res Function(_$BlockchainTransactionImpl) then) =
      __$$BlockchainTransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String type,
      String status,
      String? amount,
      String? txHash,
      DateTime createdAt,
      String? description});
}

/// @nodoc
class __$$BlockchainTransactionImplCopyWithImpl<$Res>
    extends _$BlockchainTransactionCopyWithImpl<$Res,
        _$BlockchainTransactionImpl>
    implements _$$BlockchainTransactionImplCopyWith<$Res> {
  __$$BlockchainTransactionImplCopyWithImpl(_$BlockchainTransactionImpl _value,
      $Res Function(_$BlockchainTransactionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? status = null,
    Object? amount = freezed,
    Object? txHash = freezed,
    Object? createdAt = null,
    Object? description = freezed,
  }) {
    return _then(_$BlockchainTransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      txHash: freezed == txHash
          ? _value.txHash
          : txHash // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BlockchainTransactionImpl implements _BlockchainTransaction {
  const _$BlockchainTransactionImpl(
      {required this.id,
      required this.type,
      required this.status,
      required this.amount,
      required this.txHash,
      required this.createdAt,
      this.description});

  factory _$BlockchainTransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$BlockchainTransactionImplFromJson(json);

  @override
  final String id;
  @override
  final String type;
  @override
  final String status;
  @override
  final String? amount;
  @override
  final String? txHash;
  @override
  final DateTime createdAt;
  @override
  final String? description;

  @override
  String toString() {
    return 'BlockchainTransaction(id: $id, type: $type, status: $status, amount: $amount, txHash: $txHash, createdAt: $createdAt, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlockchainTransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.txHash, txHash) || other.txHash == txHash) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, type, status, amount, txHash, createdAt, description);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BlockchainTransactionImplCopyWith<_$BlockchainTransactionImpl>
      get copyWith => __$$BlockchainTransactionImplCopyWithImpl<
          _$BlockchainTransactionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BlockchainTransactionImplToJson(
      this,
    );
  }
}

abstract class _BlockchainTransaction implements BlockchainTransaction {
  const factory _BlockchainTransaction(
      {required final String id,
      required final String type,
      required final String status,
      required final String? amount,
      required final String? txHash,
      required final DateTime createdAt,
      final String? description}) = _$BlockchainTransactionImpl;

  factory _BlockchainTransaction.fromJson(Map<String, dynamic> json) =
      _$BlockchainTransactionImpl.fromJson;

  @override
  String get id;
  @override
  String get type;
  @override
  String get status;
  @override
  String? get amount;
  @override
  String? get txHash;
  @override
  DateTime get createdAt;
  @override
  String? get description;
  @override
  @JsonKey(ignore: true)
  _$$BlockchainTransactionImplCopyWith<_$BlockchainTransactionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AchievementNotification _$AchievementNotificationFromJson(
    Map<String, dynamic> json) {
  return _AchievementNotification.fromJson(json);
}

/// @nodoc
mixin _$AchievementNotification {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  int get rarity => throw _privateConstructorUsedError;
  String get imageUrl => throw _privateConstructorUsedError;
  Map<String, dynamic> get rewards => throw _privateConstructorUsedError;
  DateTime get unlockedAt => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AchievementNotificationCopyWith<AchievementNotification> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AchievementNotificationCopyWith<$Res> {
  factory $AchievementNotificationCopyWith(AchievementNotification value,
          $Res Function(AchievementNotification) then) =
      _$AchievementNotificationCopyWithImpl<$Res, AchievementNotification>;
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      int rarity,
      String imageUrl,
      Map<String, dynamic> rewards,
      DateTime unlockedAt,
      bool isRead});
}

/// @nodoc
class _$AchievementNotificationCopyWithImpl<$Res,
        $Val extends AchievementNotification>
    implements $AchievementNotificationCopyWith<$Res> {
  _$AchievementNotificationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? rarity = null,
    Object? imageUrl = null,
    Object? rewards = null,
    Object? unlockedAt = null,
    Object? isRead = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      rarity: null == rarity
          ? _value.rarity
          : rarity // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      rewards: null == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      unlockedAt: null == unlockedAt
          ? _value.unlockedAt
          : unlockedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AchievementNotificationImplCopyWith<$Res>
    implements $AchievementNotificationCopyWith<$Res> {
  factory _$$AchievementNotificationImplCopyWith(
          _$AchievementNotificationImpl value,
          $Res Function(_$AchievementNotificationImpl) then) =
      __$$AchievementNotificationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String description,
      int rarity,
      String imageUrl,
      Map<String, dynamic> rewards,
      DateTime unlockedAt,
      bool isRead});
}

/// @nodoc
class __$$AchievementNotificationImplCopyWithImpl<$Res>
    extends _$AchievementNotificationCopyWithImpl<$Res,
        _$AchievementNotificationImpl>
    implements _$$AchievementNotificationImplCopyWith<$Res> {
  __$$AchievementNotificationImplCopyWithImpl(
      _$AchievementNotificationImpl _value,
      $Res Function(_$AchievementNotificationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? rarity = null,
    Object? imageUrl = null,
    Object? rewards = null,
    Object? unlockedAt = null,
    Object? isRead = null,
  }) {
    return _then(_$AchievementNotificationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      rarity: null == rarity
          ? _value.rarity
          : rarity // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      rewards: null == rewards
          ? _value._rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      unlockedAt: null == unlockedAt
          ? _value.unlockedAt
          : unlockedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AchievementNotificationImpl implements _AchievementNotification {
  const _$AchievementNotificationImpl(
      {required this.id,
      required this.name,
      required this.description,
      required this.rarity,
      required this.imageUrl,
      required final Map<String, dynamic> rewards,
      required this.unlockedAt,
      this.isRead = false})
      : _rewards = rewards;

  factory _$AchievementNotificationImpl.fromJson(Map<String, dynamic> json) =>
      _$$AchievementNotificationImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final int rarity;
  @override
  final String imageUrl;
  final Map<String, dynamic> _rewards;
  @override
  Map<String, dynamic> get rewards {
    if (_rewards is EqualUnmodifiableMapView) return _rewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_rewards);
  }

  @override
  final DateTime unlockedAt;
  @override
  @JsonKey()
  final bool isRead;

  @override
  String toString() {
    return 'AchievementNotification(id: $id, name: $name, description: $description, rarity: $rarity, imageUrl: $imageUrl, rewards: $rewards, unlockedAt: $unlockedAt, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AchievementNotificationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.rarity, rarity) || other.rarity == rarity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality().equals(other._rewards, _rewards) &&
            (identical(other.unlockedAt, unlockedAt) ||
                other.unlockedAt == unlockedAt) &&
            (identical(other.isRead, isRead) || other.isRead == isRead));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      rarity,
      imageUrl,
      const DeepCollectionEquality().hash(_rewards),
      unlockedAt,
      isRead);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AchievementNotificationImplCopyWith<_$AchievementNotificationImpl>
      get copyWith => __$$AchievementNotificationImplCopyWithImpl<
          _$AchievementNotificationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AchievementNotificationImplToJson(
      this,
    );
  }
}

abstract class _AchievementNotification implements AchievementNotification {
  const factory _AchievementNotification(
      {required final String id,
      required final String name,
      required final String description,
      required final int rarity,
      required final String imageUrl,
      required final Map<String, dynamic> rewards,
      required final DateTime unlockedAt,
      final bool isRead}) = _$AchievementNotificationImpl;

  factory _AchievementNotification.fromJson(Map<String, dynamic> json) =
      _$AchievementNotificationImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  int get rarity;
  @override
  String get imageUrl;
  @override
  Map<String, dynamic> get rewards;
  @override
  DateTime get unlockedAt;
  @override
  bool get isRead;
  @override
  @JsonKey(ignore: true)
  _$$AchievementNotificationImplCopyWith<_$AchievementNotificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

BlockchainApiResponse _$BlockchainApiResponseFromJson(
    Map<String, dynamic> json) {
  return _BlockchainApiResponse.fromJson(json);
}

/// @nodoc
mixin _$BlockchainApiResponse {
  bool get success => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  Map<String, dynamic>? get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BlockchainApiResponseCopyWith<BlockchainApiResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BlockchainApiResponseCopyWith<$Res> {
  factory $BlockchainApiResponseCopyWith(BlockchainApiResponse value,
          $Res Function(BlockchainApiResponse) then) =
      _$BlockchainApiResponseCopyWithImpl<$Res, BlockchainApiResponse>;
  @useResult
  $Res call({bool success, String message, Map<String, dynamic>? data});
}

/// @nodoc
class _$BlockchainApiResponseCopyWithImpl<$Res,
        $Val extends BlockchainApiResponse>
    implements $BlockchainApiResponseCopyWith<$Res> {
  _$BlockchainApiResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BlockchainApiResponseImplCopyWith<$Res>
    implements $BlockchainApiResponseCopyWith<$Res> {
  factory _$$BlockchainApiResponseImplCopyWith(
          _$BlockchainApiResponseImpl value,
          $Res Function(_$BlockchainApiResponseImpl) then) =
      __$$BlockchainApiResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, String message, Map<String, dynamic>? data});
}

/// @nodoc
class __$$BlockchainApiResponseImplCopyWithImpl<$Res>
    extends _$BlockchainApiResponseCopyWithImpl<$Res,
        _$BlockchainApiResponseImpl>
    implements _$$BlockchainApiResponseImplCopyWith<$Res> {
  __$$BlockchainApiResponseImplCopyWithImpl(_$BlockchainApiResponseImpl _value,
      $Res Function(_$BlockchainApiResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? data = freezed,
  }) {
    return _then(_$BlockchainApiResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BlockchainApiResponseImpl implements _BlockchainApiResponse {
  const _$BlockchainApiResponseImpl(
      {required this.success,
      required this.message,
      final Map<String, dynamic>? data})
      : _data = data;

  factory _$BlockchainApiResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BlockchainApiResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String message;
  final Map<String, dynamic>? _data;
  @override
  Map<String, dynamic>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'BlockchainApiResponse(success: $success, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BlockchainApiResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, success, message,
      const DeepCollectionEquality().hash(_data));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BlockchainApiResponseImplCopyWith<_$BlockchainApiResponseImpl>
      get copyWith => __$$BlockchainApiResponseImplCopyWithImpl<
          _$BlockchainApiResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BlockchainApiResponseImplToJson(
      this,
    );
  }
}

abstract class _BlockchainApiResponse implements BlockchainApiResponse {
  const factory _BlockchainApiResponse(
      {required final bool success,
      required final String message,
      final Map<String, dynamic>? data}) = _$BlockchainApiResponseImpl;

  factory _BlockchainApiResponse.fromJson(Map<String, dynamic> json) =
      _$BlockchainApiResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String get message;
  @override
  Map<String, dynamic>? get data;
  @override
  @JsonKey(ignore: true)
  _$$BlockchainApiResponseImplCopyWith<_$BlockchainApiResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
