"""
Advertising app signals
"""

from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import RewardedAd, AdImpression


@receiver(post_save, sender=AdImpression)
def handle_rewarded_ad_completion(sender, instance, created, **kwargs):
    """
    Handle rewarded ad completion and point awarding
    """
    if created and instance.impression_type == 'completed':
        # Award points to user through gamification system
        try:
            from gamification.services import GamificationService
            service = GamificationService()
            # Award points based on ad placement
            if hasattr(instance.ad_placement, 'points_reward'):
                service.award_points(
                    user=instance.user,
                    points=instance.ad_placement.points_reward,
                    reason=f"Watched rewarded ad: {instance.ad_placement.name}",
                    category='advertising'
                )
        except ImportError:
            # Gamification service not available
            pass


@receiver(post_save, sender=AdImpression)
def handle_ad_impression(sender, instance, created, **kwargs):
    """
    Handle ad impression tracking and analytics
    """
    if created:
        # Update placement statistics
        placement = instance.ad_placement
        # Note: placement statistics would be updated here if the model had these fields
        # placement.total_impressions += 1
        # placement.save(update_fields=['total_impressions'])
        
        # Update ad network statistics
        if instance.ad_network:
            # Could add network-specific tracking here
            pass


@receiver(pre_delete, sender=AdImpression)
def cleanup_ad_impression(sender, instance, **kwargs):
    """
    Clean up any related data when ad impression is deleted
    """
    # Could add cleanup logic here if needed
    pass
