import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';
import '../models/auth_models.dart';
import '../services/api_service.dart';

// Enhanced Authentication State
class AuthState {
  final User? user;
  final String? token;
  final bool isLoading;
  final String? error;
  final bool isEmailVerified;
  final bool? isFirstLogin;

  const AuthState({
    this.user,
    this.token,
    this.isLoading = false,
    this.error,
    this.isEmailVerified = false,
    this.isFirstLogin,
  });

  AuthState copyWith({
    User? user,
    String? token,
    bool? isLoading,
    String? error,
    bool? isEmailVerified,
    bool? isFirstLogin,
  }) {
    return AuthState(
      user: user ?? this.user,
      token: token ?? this.token,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
    );
  }

  bool get isAuthenticated => user != null && token != null;
}

// Enhanced Authentication Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final ApiService _apiService;

  AuthNotifier(this._apiService) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);
    try {
      // First, check if we have a stored token
      final token = await _apiService.getStoredToken();

      if (token != null) {
        // We have a token, now verify it's still valid by getting user data
        final user = await _apiService.getCurrentUser();
        if (user != null) {
          // Token is valid, restore full auth state
          state = state.copyWith(
            user: user,
            token: token,  // ✅ Restore token to state
            isLoading: false,
            isEmailVerified: user.isEmailVerified,
          );
          print('✅ Authentication restored successfully for user: ${user.username}');
        } else {
          // Token exists but is invalid, clear it
          await _apiService.clearToken();
          state = state.copyWith(isLoading: false);
          print('❌ Stored token was invalid, cleared authentication');
        }
      } else {
        // No token stored, user is not authenticated
        state = state.copyWith(isLoading: false);
        print('ℹ️ No stored authentication token found');
      }
    } catch (e) {
      print('❌ Error checking auth status: $e');
      // If there's an error, clear any potentially invalid token
      await _apiService.clearToken();
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> login(String emailOrUsername, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final request = LoginRequest(
        emailOrUsername: emailOrUsername,
        password: password,
      );
      final response = await _apiService.loginWithRequest(request);

      // Save token to secure storage for API service
      await _apiService.setToken(response.token);

      // Check if this is the user's first login
      final isFirstLogin = await _apiService.getIsFirstLogin();

      state = state.copyWith(
        user: response.user,
        token: response.token,
        isLoading: false,
        isEmailVerified: response.user.isEmailVerified,
        isFirstLogin: isFirstLogin,
      );
    } catch (e) {
      String errorMessage = 'Login failed';
      if (e.toString().contains('Invalid credentials')) {
        errorMessage = 'Invalid email/username or password';
      } else if (e.toString().contains('verify your email')) {
        errorMessage = 'Please verify your email address before logging in';
      } else if (e.toString().contains('Account is disabled')) {
        errorMessage = 'Your account has been disabled';
      }

      state = state.copyWith(isLoading: false, error: errorMessage);
      rethrow;
    }
  }

  Future<void> register({
    required String username,
    required String email,
    required String password,
    required String passwordConfirm,
    String? firstName,
    String? lastName,
    String? referralCode,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final request = RegisterRequest(
        username: username,
        email: email,
        password: password,
        passwordConfirm: passwordConfirm,
        firstName: firstName,
        lastName: lastName,
        referralCode: referralCode,
      );
      final response = await _apiService.registerWithRequest(request);

      // Save token to secure storage for API service
      await _apiService.setToken(response.token);

      state = state.copyWith(
        user: response.user,
        token: response.token,
        isLoading: false,
        isEmailVerified: response.user.isEmailVerified,
        isFirstLogin: true, // New registrations are always first login
      );
    } catch (e) {
      String errorMessage = 'Registration failed';
      if (e.toString().contains('already exists')) {
        errorMessage = 'Username or email already exists';
      } else if (e.toString().contains('password')) {
        errorMessage = 'Password does not meet requirements';
      } else if (e.toString().contains('email')) {
        errorMessage = 'Please enter a valid email address';
      }

      state = state.copyWith(isLoading: false, error: errorMessage);
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      // Call logout API endpoint
      await _apiService.logout();
    } catch (e) {
      print('API logout failed: $e');
      // Continue with local logout even if API call fails
    }

    try {
      // Clear all local authentication data
      await _apiService.clearToken();
      await _apiService.clearUserData();
    } catch (e) {
      print('Error clearing local auth data: $e');
    }

    // Reset auth state
    state = const AuthState();
    print('✅ User logged out successfully');
  }

  Future<void> changePassword({
    required String oldPassword,
    required String newPassword,
    required String newPasswordConfirm,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final request = ChangePasswordRequest(
        oldPassword: oldPassword,
        newPassword: newPassword,
        newPasswordConfirm: newPasswordConfirm,
      );
      await _apiService.changePassword(request);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> requestPasswordReset(String email) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final request = PasswordResetRequest(email: email);
      await _apiService.requestPasswordReset(request);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> confirmPasswordReset({
    required String token,
    required String newPassword,
    required String newPasswordConfirm,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final request = PasswordResetConfirmRequest(
        token: token,
        newPassword: newPassword,
        newPasswordConfirm: newPasswordConfirm,
      );
      await _apiService.confirmPasswordReset(request);
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> verifyEmail(String token) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final request = EmailVerificationRequest(token: token);
      await _apiService.verifyEmail(request);
      
      // Update user's email verification status
      if (state.user != null) {
        final updatedUser = state.user!.copyWith(isEmailVerified: true);
        state = state.copyWith(
          user: updatedUser,
          isLoading: false,
          isEmailVerified: true,
        );
      } else {
        state = state.copyWith(isLoading: false, isEmailVerified: true);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> resendEmailVerification() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _apiService.resendEmailVerification();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> updateProfile(Map<String, dynamic> profileData) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final updatedUser = await _apiService.updateUserProfile(profileData);
      state = state.copyWith(
        user: updatedUser,
        isLoading: false,
        isEmailVerified: updatedUser.isEmailVerified,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }

  Future<void> refreshAuthState() async {
    // Force refresh of authentication state
    await _checkAuthStatus();
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Enhanced Auth Provider
final enhancedAuthProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return AuthNotifier(apiService);
});

// API Service Provider
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});
