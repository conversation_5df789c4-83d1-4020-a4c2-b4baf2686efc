import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';
import '../providers/store_provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/referral_provider.dart';
import '../providers/points_provider.dart' as points;
import '../theme/app_theme.dart';
import '../models/reward_models.dart';
import '../services/error_reporting_service.dart';
import '../services/payment_status_service.dart';
import '../widgets/enhanced_payment_widgets.dart';
import '../widgets/enhanced_store_widgets.dart';
import '../providers/wallet_provider.dart';
import '../screens/point_conversion_screen.dart';

class StoreScreen extends ConsumerStatefulWidget {
  const StoreScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<StoreScreen> createState() => _StoreScreenState();
}

class _StoreScreenState extends ConsumerState<StoreScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);

    // Load dynamic data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && ref.read(enhancedAuthProvider).isAuthenticated) {
        ref.read(storeProvider.notifier).loadVirtualItems();
        ref.read(storeProvider.notifier).loadPointBoosts();
        ref.read(referralProvider.notifier).loadReferralData();
        ref.read(storeProvider.notifier).loadPremiumStatus();
        ref.read(gamificationProvider.notifier).loadUserLevel();
        ref.read(points.unifiedPointsProvider.notifier).refreshAll();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(enhancedAuthProvider);

    if (!authState.isAuthenticated) {
      return _buildUnauthenticatedView();
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          '🛍️ Trendy Store',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        actions: [
          Consumer(
            builder: (context, ref, child) {
              final pointsState = ref.watch(points.unifiedPointsProvider);
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Store Points
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.shopping_cart,
                            color: Colors.white, size: 14),
                        const SizedBox(width: 4),
                        Text(
                          pointsState.storePoints.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Gamification Points
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.stars, color: Colors.white, size: 14),
                        const SizedBox(width: 4),
                        Text(
                          pointsState.gamificationPoints.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Convert Button
                  IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const PointConversionScreen(),
                        ),
                      );
                    },
                    icon: const Icon(
                      Icons.swap_horiz,
                      color: AppTheme.primaryColor,
                    ),
                    tooltip: 'Convert Points',
                  ),
                ],
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppTheme.primaryColor,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Premium'),
            Tab(text: 'Point Boosts'),
            Tab(text: 'Rewards'),
            Tab(text: 'Referral'),
            Tab(text: 'Cosmetics'),
            Tab(text: 'Functional'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPremiumTab(),
          _buildPointBoostsTab(),
          _buildRewardsTab(),
          _buildReferralTab(),
          _buildCosmeticsTab(),
          _buildFunctionalTab(),
        ],
      ),
    );
  }

  Widget _buildUnauthenticatedView() {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          '🛍️ Trendy Store',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.store,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Unlock Premium Features!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Join Trendy to access premium subscriptions, point boosts, and exclusive items.',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/auth');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Join Trendy & Access Store',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPremiumTab() {
    final storeState = ref.watch(storeProvider);
    final isPremium = storeState.premiumSubscription != null &&
        storeState.premiumSubscription!.status == 'active';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Premium benefits header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.purple[400]!, Colors.purple[600]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                Text(
                  isPremium ? '👑 Premium Active' : '👑 Trendy Premium',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  isPremium
                      ? 'You\'re enjoying all premium benefits!'
                      : 'Earn 2x points and unlock exclusive features!',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (isPremium && storeState.premiumSubscription != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    'Expires: ${_formatDate(storeState.premiumSubscription!.endDate)}',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Premium plans
          _buildPremiumPlan(
            'Monthly',
            9.99,
            'month',
            'Most Popular',
            Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildPremiumPlan(
            'Quarterly',
            24.99,
            '3 months',
            '15% Off',
            Colors.orange,
          ),
          const SizedBox(height: 12),
          _buildPremiumPlan(
            'Yearly',
            69.99,
            'year',
            '30% Off - Best Value',
            Colors.green,
          ),
          const SizedBox(height: 20),

          // Premium benefits
          _buildPremiumBenefits(),
        ],
      ),
    );
  }

  Widget _buildPremiumPlan(
      String title, double price, String period, String badge, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          badge,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '\$${price.toStringAsFixed(2)} / $period',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => _upgradeToPremium(title, price),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Upgrade'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumBenefits() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '✨ Premium Benefits',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildBenefit('2x Point Multiplier', 'Earn points twice as fast',
                Icons.speed),
            _buildBenefit('+15 Daily Bonus', 'vs +5 for free users',
                Icons.calendar_today),
            _buildBenefit('Unlimited Voice Comments', 'vs 3/day for free users',
                Icons.mic),
            _buildBenefit(
                'Exclusive Rewards', 'Access premium-only rewards', Icons.star),
            _buildBenefit(
                'Priority Processing', '24hr vs 3-5 days', Icons.flash_on),
            _buildBenefit('Premium Badge', 'Show your status', Icons.verified),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefit(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointBoostsTab() {
    final storeState = ref.watch(storeProvider);
    final boosts = storeState.pointBoosts;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          '⚡ Point Boost Packages',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Get instant points to reach your rewards faster!',
          style: TextStyle(
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 20),

        // Show loading state
        if (storeState.isLoading && boosts.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else if (storeState.error != null && boosts.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load point boosts',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(storeProvider.notifier).loadPointBoosts();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          )
        else if (boosts.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text('No point boosts available'),
            ),
          )
        else
          ...boosts.map((boost) => _buildBoostCard(boost)),
      ],
    );
  }

  Widget _buildBoostCard(PointBoostPackage boost) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border:
            boost.isPopular ? Border.all(color: Colors.orange, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.flash_on,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        boost.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (boost.isPopular) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'Popular',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${boost.totalPoints} points instantly',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${boost.price.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  key: ValueKey('boost_button_${boost.id}'),
                  onPressed: () => _purchaseBoost(boost),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Text('Buy'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCosmeticsTab() {
    final storeState = ref.watch(storeProvider);
    final cosmetics = ref.watch(cosmeticItemsProvider);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          '🎨 Cosmetic Items',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Customize your profile and stand out!',
          style: TextStyle(
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 20),

        // Show loading state
        if (storeState.isLoading && cosmetics.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else if (storeState.error != null && cosmetics.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load cosmetic items',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(storeProvider.notifier).loadVirtualItems();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          )
        else if (cosmetics.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text('No cosmetic items available'),
            ),
          )
        else
          ...cosmetics.map((item) => _buildCosmeticItemCard(item)),
      ],
    );
  }

  Widget _buildCosmeticItemCard(VirtualItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.purple[100],
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.palette, // Default icon for cosmetic items
                color: Colors.purple,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${item.price.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  key: ValueKey('cosmetic_button_${item.id}'),
                  onPressed:
                      item.isActive ? () => _purchaseVirtualItem(item) : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple[600],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Text('Buy'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionalTab() {
    final storeState = ref.watch(storeProvider);
    final functional = ref.watch(functionalItemsProvider);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          '⚙️ Functional Items',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Boost your progress and earnings!',
          style: TextStyle(
            color: AppTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 20),

        // Show loading state
        if (storeState.isLoading && functional.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else if (storeState.error != null && functional.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load functional items',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      ref.read(storeProvider.notifier).loadVirtualItems();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          )
        else if (functional.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text('No functional items available'),
            ),
          )
        else
          ...functional.map((item) => _buildFunctionalItemCard(item)),
      ],
    );
  }

  Widget _buildFunctionalItemCard(VirtualItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.settings, // Default icon for functional items
                color: Colors.green,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${item.price.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  key: ValueKey('functional_button_${item.id}'),
                  onPressed:
                      item.isActive ? () => _purchaseVirtualItem(item) : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                  child: const Text('Buy'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _upgradeToPremium(String plan, double price) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upgrade to Premium ($plan)'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Upgrade to Premium for \$${price.toStringAsFixed(2)} and start earning 2x points immediately?',
            ),
            const SizedBox(height: 16),
            const Text(
              'You\'ll get:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Text('• 2x point multiplier'),
            const Text('• +15 daily streak bonus'),
            const Text('• Unlimited voice comments'),
            const Text('• Exclusive rewards access'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processDirectUpgrade(plan, price);
            },
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  void _processDirectUpgrade(String plan, double price) async {
    try {
      // Convert plan name to lowercase for backend compatibility
      final backendPlan = plan.toLowerCase();

      // Create subscription via store provider
      final storeNotifier = ref.read(storeProvider.notifier);
      final result = await storeNotifier.createPremiumSubscription(backendPlan);

      if (!mounted) return;

      if (result['success'] == true) {
        final data = result['data'];

        if (data != null && data['wallet_payment'] == true) {
          // Wallet payment successful
          _showEnhancedPaymentSuccess(
            'Premium Activated!',
            'Your $plan subscription is now active.',
            '\$${price.toStringAsFixed(2)}',
          );
        } else if (data != null && data['payment_required'] == true) {
          // External payment required - show enhanced loading with status tracking
          final transactionId = data['transaction_id']?.toString();
          if (transactionId != null) {
            _showEnhancedPaymentLoading(
              'Processing Payment',
              'Please complete the payment to activate your subscription.',
              transactionId,
            );
          } else {
            _showExternalPaymentRequired(plan, price, data);
          }
        } else {
          // Fallback success
          _showEnhancedPaymentSuccess(
            'Premium Activated!',
            'Your $plan subscription is now active.',
            '\$${price.toStringAsFixed(2)}',
          );
        }
      } else {
        _showEnhancedPaymentError(
          'Subscription Failed',
          result['message'] ??
              'Failed to create subscription. Please try again.',
          onRetry: () => _processDirectUpgrade(plan, price),
        );
      }
    } catch (e) {
      if (!mounted) return;
      _showEnhancedPaymentError(
        'Subscription Error',
        ErrorReportingService.getPaymentErrorMessage(e,
            context: 'premium_subscription'),
        onRetry: () => _processDirectUpgrade(plan, price),
      );
    }
  }

  void _showExternalPaymentRequired(
      String plan, double price, Map<String, dynamic> data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('💰 Insufficient Wallet Balance'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
                'Your wallet balance is insufficient for this subscription.'),
            const SizedBox(height: 8),
            Text('Required: \$${price.toStringAsFixed(2)}'),
            Text('Available: \$${data['wallet_balance'] ?? '0.00'}'),
            const SizedBox(height: 16),
            const Text(
                'Please add funds to your wallet or use an external payment method.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to wallet screen to add funds
              Navigator.pushNamed(context, '/wallet');
            },
            child: const Text('Add Funds'),
          ),
        ],
      ),
    );
  }

  void _showPaymentSuccess(String plan, double price) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Premium Activated!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.diamond,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Welcome to Premium!',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Your $plan subscription is now active. You\'re now earning 2x points!',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ElevatedButton(
              onPressed: () {
                Navigator.pop(context); // Close success dialog
                // Reload store data to reflect premium status
                ref.read(storeProvider.notifier).loadPremiumStatus();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Start Earning 2x Points!',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showPaymentError(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('❌ Payment Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/premium');
            },
            child: const Text('Try Different Plan'),
          ),
        ],
      ),
    );
  }

  void _purchaseBoost(PointBoostPackage boost) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Purchase ${boost.name}'),
        content: Text(
          'Buy ${boost.totalPoints} points for \$${boost.price.toStringAsFixed(2)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _purchasePointBoost(boost);
            },
            child: const Text('Buy'),
          ),
        ],
      ),
    );
  }

  void _purchaseVirtualItem(VirtualItem item) async {
    final pointsState = ref.read(points.unifiedPointsProvider);

    // Check if user has enough store points
    final itemCost = item.price.toInt(); // Convert price to store points
    if (pointsState.storePoints < itemCost) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Insufficient store points! You need $itemCost but have ${pointsState.storePoints}.'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Purchase ${item.name}'),
        content: Text(
            'Are you sure you want to purchase this item for $itemCost store points?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Purchase'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Processing purchase...'),
          ],
        ),
      ),
    );

    try {
      // Call backend API to purchase item with store points
      final response =
          await ref.read(apiServiceProvider).purchaseVirtualItem(item.id);

      if (response['success'] == true) {
        // Refresh unified points to sync across all screens
        await ref.read(points.unifiedPointsProvider.notifier).refreshAll();

        // Close loading dialog
        if (mounted) navigator.pop();

        // Show success message
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                  '${item.name} purchased successfully! $itemCost store points deducted.'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }

        // Reload store data
        ref.read(storeProvider.notifier).loadVirtualItems();
      } else {
        throw Exception(response['error'] ?? 'Purchase failed');
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) navigator.pop();

      // Show user-friendly error message
      if (mounted) {
        ErrorReportingService.showPaymentErrorSnackbar(context, e,
            errorContext: 'virtual_item');
      }
    }
  }

  void _redirectToPayment(String type, String itemName, double price,
      Map<String, dynamic> metadata) {
    // Navigate to payment screen with payment intent
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Payment Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Purchase $itemName for \$${price.toStringAsFixed(2)}?',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            const Text(
              'You will be redirected to the payment screen to complete your purchase.',
              style: TextStyle(
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to premium screen for payment processing
              Navigator.pushNamed(
                context,
                '/premium',
                arguments: {
                  'payment_intent': type,
                  'item_name': itemName,
                  'price': price,
                  'metadata': metadata,
                },
              ).then((_) {
                // Reload store data when returning from payment
                ref.read(storeProvider.notifier).loadVirtualItems();
                ref.read(storeProvider.notifier).loadPointBoosts();
              });
            },
            child: const Text('Continue to Payment'),
          ),
        ],
      ),
    );
  }

  void _purchaseItem(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Purchase ${item['name']}'),
        content: Text(
          'Buy ${item['name']} for \$${item['price'].toStringAsFixed(2)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${item['name']} purchased successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Buy'),
          ),
        ],
      ),
    );
  }

  Widget _buildRewardsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final gamificationState = ref.watch(gamificationProvider);
        final userLevel = gamificationState.userLevel;
        final userPoints = ref.watch(currentPointsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User stats card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    const Text(
                      '🏆 Your Progress',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(
                            'Level',
                            userLevel?.currentLevel.toString() ?? '1',
                            Icons.trending_up),
                        _buildStatItem(
                            'Points', userPoints.toString(), Icons.stars),
                        _buildStatItem(
                            'Rank',
                            _getLevelTitle(userLevel?.currentLevel ?? 1),
                            Icons.emoji_events),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // Available rewards
              const Text(
                '🎁 Available Rewards',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),

              // Sample rewards
              _buildRewardCard(
                  '\$5 PayPal Cash', 1000, Icons.payment, Colors.green),
              _buildRewardCard(
                  '\$10 PayPal Cash', 2000, Icons.payment, Colors.blue),
              _buildRewardCard(
                  '\$25 PayPal Cash', 5000, Icons.payment, Colors.purple),
              _buildRewardCard(
                  'Premium Month Free', 1500, Icons.star, Colors.orange),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildRewardCard(
      String title, int pointsCost, IconData icon, Color color) {
    final userPoints = ref.watch(currentPointsProvider);
    final canAfford = userPoints >= pointsCost;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: canAfford ? color : Colors.grey[300]!,
          width: 2,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '$pointsCost points required',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            key: ValueKey('reward_button_${title}_$pointsCost'),
            onPressed: canAfford ? () => _claimReward(title, pointsCost) : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: canAfford ? color : Colors.grey[300],
              foregroundColor: Colors.white,
            ),
            child:
                Text(canAfford ? 'Claim' : 'Need ${pointsCost - userPoints}'),
          ),
        ],
      ),
    );
  }

  Widget _buildReferralTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Referral header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange[400]!, Colors.orange[600]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Column(
              children: [
                Text(
                  '🤝 Refer Friends',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Earn money when your friends join and engage!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Referral code
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Consumer(
                  builder: (context, ref, child) {
                    final referralState = ref.watch(referralProvider);

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Your Referral Code',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (referralState.isLoading)
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else if (referralState.error != null)
                          IconButton(
                            onPressed: () {
                              ref
                                  .read(referralProvider.notifier)
                                  .loadReferralData();
                            },
                            icon: const Icon(Icons.refresh, size: 20),
                            tooltip: 'Retry loading referral code',
                          ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Consumer(
                          builder: (context, ref, child) {
                            final referralState = ref.watch(referralProvider);
                            String displayText;
                            Color textColor = Colors.black;

                            if (referralState.isLoading) {
                              displayText = 'Loading...';
                              textColor = Colors.grey;
                            } else if (referralState.error != null) {
                              displayText = 'Error loading code';
                              textColor = Colors.red;
                            } else if (referralState
                                    .stats.referralCode?.isNotEmpty ==
                                true) {
                              displayText = referralState.stats.referralCode!;
                            } else {
                              displayText = 'No code available';
                              textColor = Colors.grey;
                            }

                            return Text(
                              displayText,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'monospace',
                                color: textColor,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      key: const ValueKey('referral_copy_button'),
                      onPressed: () {
                        final referralCode =
                            ref.read(referralProvider).stats.referralCode ?? '';
                        if (referralCode.isNotEmpty &&
                            referralCode != 'Loading...') {
                          Clipboard.setData(ClipboardData(text: referralCode));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content:
                                  Text('Referral code copied to clipboard!'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                  'Referral code not loaded yet. Please wait...'),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
                      },
                      child: const Text('Copy'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Referral rewards
          const Text(
            '💰 Referral Rewards',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          _buildReferralReward('Friend Joins', '\$2.00',
              'When someone uses your code', Icons.person_add),
          _buildReferralReward('Friend Reaches Level 5', '\$2.00',
              'When they become active', Icons.trending_up),
          _buildReferralReward(
              'Friend Goes Premium', '\$5.00', 'When they upgrade', Icons.star),
        ],
      ),
    );
  }

  Widget _buildReferralReward(
      String title, String amount, String description, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.orange, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  void _claimReward(String title, int pointsCost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Claim $title'),
        content: Text(
            'Are you sure you want to claim this reward for $pointsCost points?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();

              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => const AlertDialog(
                  content: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Processing reward...'),
                    ],
                  ),
                ),
              );

              try {
                // Deduct points from gamification provider
                await ref
                    .read(gamificationProvider.notifier)
                    .spendPoints(pointsCost, 'Reward: $title');

                // Refresh unified points to sync across all screens
                await ref
                    .read(points.unifiedPointsProvider.notifier)
                    .refreshAll();

                // Close loading dialog
                if (mounted) navigator.pop();

                // Show success message
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                          '$title claimed! $pointsCost points deducted. Check your PayPal for payment.'),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 4),
                    ),
                  );
                }
              } catch (e) {
                // Close loading dialog
                if (mounted) navigator.pop();

                // Show user-friendly error message
                if (mounted) {
                  ErrorReportingService.showPaymentErrorSnackbar(context, e,
                      errorContext: 'payout_request');
                }
              }
            },
            child: const Text('Claim'),
          ),
        ],
      ),
    );
  }

  String _getLevelTitle(int level) {
    if (level >= 50) return 'Legend';
    if (level >= 40) return 'Master';
    if (level >= 30) return 'Expert';
    if (level >= 20) return 'Advanced';
    if (level >= 10) return 'Intermediate';
    if (level >= 5) return 'Novice';
    return 'Beginner';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference == 0) return 'Today';
    if (difference == 1) return 'Tomorrow';
    if (difference < 7) return '${difference}d';
    if (difference < 30) return '${(difference / 7).floor()}w';
    return '${(difference / 30).floor()}m';
  }

  // Enhanced payment dialog methods
  void _showEnhancedPaymentLoading(
      String title, String subtitle, String transactionId) {
    final statusStream =
        PaymentStatusService().trackPaymentStatus(transactionId);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PaymentLoadingDialog(
        title: title,
        subtitle: subtitle,
        statusStream: statusStream,
        onCancel: () {
          PaymentStatusService().stopTracking(transactionId);
        },
      ),
    ).then((result) {
      if (result is PaymentStatus) {
        if (result.isCompleted) {
          _showEnhancedPaymentSuccess(
            'Payment Completed!',
            'Your payment has been processed successfully.',
            '\$${result.amount.toStringAsFixed(2)}',
          );
        } else if (result.isFailed) {
          _showEnhancedPaymentError(
            'Payment Failed',
            result.errorMessage ?? 'Payment could not be completed.',
          );
        }
      }
    });
  }

  void _showEnhancedPaymentSuccess(
      String title, String message, String? amount) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PaymentSuccessDialog(
        title: title,
        message: message,
        amount: amount,
        onContinue: () {
          // Refresh store data
          ref.read(storeProvider.notifier).loadPremiumStatus();
          ref.read(points.unifiedPointsProvider.notifier).refreshAll();
        },
      ),
    );
  }

  void _showEnhancedPaymentError(String title, String message,
      {VoidCallback? onRetry}) {
    showDialog(
      context: context,
      builder: (context) => PaymentErrorDialog(
        title: title,
        message: message,
        onRetry: onRetry,
        onCancel: () {
          // Optional: Navigate back or refresh data
        },
      ),
    );
  }

  // Point boost purchasing functionality
  Future<void> _purchasePointBoost(PointBoostPackage boost) async {
    try {
      // Show payment method selection dialog
      final paymentMethod = await _showPaymentMethodDialog();
      if (paymentMethod == null) return;

      if (paymentMethod == 'wallet') {
        await _purchasePointBoostWithWallet(boost);
      } else {
        // For external payments, ask for PayPal email first
        await _showPayPalEmailDialogForPointBoost(boost, paymentMethod);
      }
    } catch (e) {
      if (mounted) {
        ErrorReportingService.showPaymentErrorSnackbar(context, e,
            errorContext: 'point_boost');
      }
    }
  }

  Future<String?> _showPaymentMethodDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Payment Method'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading:
                  const Icon(Icons.account_balance_wallet, color: Colors.green),
              title: const Text('Wallet Balance'),
              subtitle: const Text('Use your wallet funds'),
              onTap: () => Navigator.pop(context, 'wallet'),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.payment, color: Colors.blue),
              title: const Text('PayPal'),
              subtitle: const Text('Pay with PayPal'),
              onTap: () => Navigator.pop(context, 'paypal'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Future<void> _purchasePointBoostWithWallet(PointBoostPackage boost) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Processing wallet payment...'),
            ],
          ),
        ),
      );

      // Purchase using API service
      final response = await ref.read(apiServiceProvider).purchasePointBoost(
            boost.id,
            paymentMethod: 'wallet',
          );

      if (!mounted) return;
      Navigator.pop(context); // Close loading dialog

      if (response['success'] == true) {
        // Success - show success message
        _showEnhancedPaymentSuccess(
          'Points Added!',
          'Successfully purchased ${boost.totalPoints} points from your wallet!',
          '\$${boost.price.toStringAsFixed(2)}',
        );

        // Refresh data
        ref.read(storeProvider.notifier).loadPointBoosts();
        ref.read(points.unifiedPointsProvider.notifier).refreshAll();
        ref.read(walletProvider.notifier).loadWalletOverview();
      } else {
        // Handle wallet payment failure
        final message = response['message'] ?? 'Wallet payment failed';
        if (message.toLowerCase().contains('insufficient')) {
          _showInsufficientFundsDialog(boost);
        } else {
          _showEnhancedPaymentError(
            'Wallet Payment Failed',
            message,
            onRetry: () => _purchasePointBoostWithWallet(boost),
          );
        }
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.pop(context); // Close loading dialog

      ErrorReportingService.showPaymentErrorSnackbar(context, e,
          errorContext: 'point_boost');
    }
  }

  Future<void> _showPayPalEmailDialogForPointBoost(
      PointBoostPackage boost, String paymentMethod) async {
    if (!mounted) return;

    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.payment, color: Colors.blue),
            SizedBox(width: 8),
            Text('PayPal Payment'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Purchasing ${boost.totalPoints} points for \$${boost.price.toStringAsFixed(2)}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('Enter your PayPal email address:'),
              const SizedBox(height: 12),
              TextField(
                controller: emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'PayPal Email',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                  helperText: 'This will be used for the payment',
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.security, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'You will be redirected to PayPal to complete the secure payment.',
                        style: TextStyle(fontSize: 12, color: Colors.orange),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final email = emailController.text.trim();
              if (email.isEmpty || !email.contains('@')) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a valid PayPal email address'),
                    backgroundColor: Colors.orange,
                  ),
                );
                return;
              }
              Navigator.pop(context);
              await _purchasePointBoostWithExternalPayment(
                  boost, paymentMethod, email);
            },
            child: const Text('Continue to PayPal'),
          ),
        ],
      ),
    );
  }

  Future<void> _purchasePointBoostWithExternalPayment(
      PointBoostPackage boost, String paymentMethod,
      [String? paypalEmail]) async {
    try {
      // Purchase using API service with external payment
      final response = await ref.read(apiServiceProvider).purchasePointBoost(
            boost.id,
            paymentMethod: paymentMethod,
            paypalEmail: paypalEmail,
          );

      if (!mounted) return;

      if (response['success'] == true && response['payment_required'] == true) {
        // External payment required - show enhanced loading with status tracking
        final transactionId = response['transaction_id']?.toString();
        if (transactionId != null) {
          _showEnhancedPaymentLoading(
            'Processing Payment',
            'Please complete the PayPal payment to receive your points.',
            transactionId,
          );
        } else {
          // Fallback to basic external payment flow
          _showPointBoostExternalPaymentRequired(boost, response);
        }
      } else if (response['success'] == true) {
        // Immediate success
        _showEnhancedPaymentSuccess(
          'Points Added!',
          'Successfully purchased ${boost.totalPoints} points!',
          '\$${boost.price.toStringAsFixed(2)}',
        );

        // Refresh data
        ref.read(storeProvider.notifier).loadPointBoosts();
        ref.read(points.unifiedPointsProvider.notifier).refreshAll();
      } else {
        _showEnhancedPaymentError(
          'Payment Failed',
          response['message'] ?? 'Failed to process payment',
          onRetry: () => _purchasePointBoostWithExternalPayment(
              boost, paymentMethod, paypalEmail),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ErrorReportingService.showPaymentErrorSnackbar(context, e,
          errorContext: 'point_boost');
    }
  }

  void _showInsufficientFundsDialog(PointBoostPackage boost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.account_balance_wallet, color: Colors.orange),
            SizedBox(width: 8),
            Text('Insufficient Wallet Balance'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
                'You need \$${boost.price.toStringAsFixed(2)} to purchase this point boost.'),
            const SizedBox(height: 16),
            const Text(
                'Would you like to add money to your wallet or use PayPal instead?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showAddMoneyDialog(boost.price);
            },
            child: const Text('Add Money'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showPayPalEmailDialogForPointBoost(boost, 'paypal');
            },
            child: const Text('Use PayPal'),
          ),
        ],
      ),
    );
  }

  void _showAddMoneyDialog(double minimumAmount) {
    final amountController = TextEditingController(
      text: minimumAmount.toStringAsFixed(2),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.account_balance_wallet, color: Colors.green),
            SizedBox(width: 8),
            Text('Add Money to Wallet'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Enter the amount you want to add to your wallet:'),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                decoration: const InputDecoration(
                  labelText: 'Amount',
                  prefixText: '\$',
                  border: OutlineInputBorder(),
                  helperText: 'Minimum: \$1.00',
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'You will be redirected to PayPal to complete the payment.',
                        style: TextStyle(fontSize: 12, color: Colors.blue),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text);
              if (amount != null && amount >= 1.0) {
                Navigator.pop(context);
                await _showPayPalEmailDialog(amount);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content:
                        Text('Please enter a valid amount (minimum \$1.00)'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  Future<void> _showPayPalEmailDialog(double amount) async {
    if (!mounted) return;

    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.payment, color: Colors.blue),
              SizedBox(width: 8),
              Text('PayPal Payment'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Adding \$${amount.toStringAsFixed(2)} to your wallet',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text('Enter your PayPal email address:'),
                const SizedBox(height: 12),
                TextField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'PayPal Email',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.email),
                    helperText: 'This will be used for the payment',
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.security, color: Colors.orange, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'You will be redirected to PayPal to complete the secure payment.',
                          style: TextStyle(fontSize: 12, color: Colors.orange),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final email = emailController.text.trim();
                if (email.isEmpty || !email.contains('@')) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content:
                          Text('Please enter a valid PayPal email address'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                  return;
                }
                Navigator.pop(context);
                await _addMoneyToWallet(amount, email);
              },
              child: const Text('Continue to PayPal'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addMoneyToWallet(double amount, [String? paypalEmail]) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Creating wallet deposit...'),
            ],
          ),
        ),
      );

      // Create wallet deposit
      final result = await ref
          .read(walletProvider.notifier)
          .createDeposit(amount, 'paypal');

      if (!mounted) return;
      Navigator.pop(context); // Close loading dialog

      if (result['success'] == true) {
        _showEnhancedPaymentSuccess(
          'Deposit Created!',
          'Please complete the PayPal payment to add money to your wallet.',
          '\$${amount.toStringAsFixed(2)}',
        );
      } else {
        _showEnhancedPaymentError(
          'Deposit Failed',
          result['message'] ?? 'Failed to create wallet deposit',
          onRetry: () => _addMoneyToWallet(amount),
        );
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.pop(context); // Close loading dialog

      ErrorReportingService.showPaymentErrorSnackbar(context, e,
          errorContext: 'wallet_deposit');
    }
  }

  void _showPointBoostExternalPaymentRequired(
      PointBoostPackage boost, Map<String, dynamic> response) {
    // Fallback external payment dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Complete Payment'),
        content: Text(
            'Please complete the external payment to receive ${boost.totalPoints} points.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
