# Generated by Django 5.1.7 on 2025-06-25 00:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gamification', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EngagementSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('max_posts_read_per_day', models.PositiveIntegerField(default=50)),
                ('max_comments_per_day', models.PositiveIntegerField(default=20)),
                ('max_likes_per_day', models.PositiveIntegerField(default=100)),
                ('max_actions_per_minute', models.PositiveIntegerField(default=30)),
                ('min_reading_time_seconds', models.PositiveIntegerField(default=10)),
                ('min_scroll_percentage', models.FloatField(default=30.0)),
                ('like_cooldown', models.PositiveIntegerField(default=2)),
                ('comment_cooldown', models.PositiveIntegerField(default=30)),
                ('enable_fraud_detection', models.BooleanField(default=True)),
                ('auto_flag_suspicious', models.BooleanField(default=True)),
                ('reading_points', models.PositiveIntegerField(default=5)),
                ('comment_points', models.PositiveIntegerField(default=10)),
                ('like_points', models.PositiveIntegerField(default=2)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Engagement Settings',
                'verbose_name_plural': 'Engagement Settings',
            },
        ),
        migrations.CreateModel(
            name='EngagementHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('engagement_type', models.CharField(choices=[('like', 'Like'), ('comment', 'Comment'), ('share', 'Share'), ('follow', 'Follow'), ('voice_comment', 'Voice Comment')], max_length=20)),
                ('target_type', models.CharField(max_length=50)),
                ('target_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('points_awarded', models.PositiveIntegerField(default=0)),
                ('is_valid', models.BooleanField(default=True)),
                ('time_since_last_action', models.DurationField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='engagement_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'engagement_type', 'created_at'], name='gamificatio_user_id_23a44a_idx'), models.Index(fields=['target_type', 'target_id'], name='gamificatio_target__06b298_idx'), models.Index(fields=['created_at'], name='gamificatio_created_28d4b6_idx')],
            },
        ),
        migrations.CreateModel(
            name='PostReadingHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('post_id', models.PositiveIntegerField()),
                ('first_read_at', models.DateTimeField(auto_now_add=True)),
                ('last_read_at', models.DateTimeField(auto_now=True)),
                ('read_count', models.PositiveIntegerField(default=1)),
                ('points_awarded', models.PositiveIntegerField(default=0)),
                ('reward_given', models.BooleanField(default=False)),
                ('time_spent_seconds', models.PositiveIntegerField(default=0)),
                ('scroll_percentage', models.FloatField(default=0.0)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reading_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'post_id'], name='gamificatio_user_id_4794ce_idx'), models.Index(fields=['user', 'reward_given'], name='gamificatio_user_id_5e9db3_idx'), models.Index(fields=['first_read_at'], name='gamificatio_first_r_78df45_idx')],
                'unique_together': {('user', 'post_id')},
            },
        ),
        migrations.CreateModel(
            name='SuspiciousActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('rapid_reading', 'Rapid Reading'), ('excessive_likes', 'Excessive Likes'), ('spam_comments', 'Spam Comments'), ('bot_behavior', 'Bot-like Behavior'), ('duplicate_rewards', 'Duplicate Reward Attempts'), ('rate_limit_exceeded', 'Rate Limit Exceeded')], max_length=30)),
                ('description', models.TextField()),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=20)),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_suspicious_activities', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suspicious_activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'activity_type'], name='gamificatio_user_id_230c9e_idx'), models.Index(fields=['severity', 'is_resolved'], name='gamificatio_severit_3e7be6_idx'), models.Index(fields=['created_at'], name='gamificatio_created_19bbd0_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserEngagementTracker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('posts_read_today', models.PositiveIntegerField(default=0)),
                ('last_reading_reset', models.DateField(auto_now_add=True)),
                ('comments_today', models.PositiveIntegerField(default=0)),
                ('likes_today', models.PositiveIntegerField(default=0)),
                ('last_engagement_reset', models.DateField(auto_now_add=True)),
                ('is_flagged', models.BooleanField(default=False)),
                ('flag_reason', models.CharField(blank=True, max_length=200)),
                ('flagged_at', models.DateTimeField(blank=True, null=True)),
                ('last_activity_timestamp', models.DateTimeField(auto_now=True)),
                ('activity_count_last_minute', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='engagement_tracker', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'last_reading_reset'], name='gamificatio_user_id_9ad3a7_idx'), models.Index(fields=['is_flagged'], name='gamificatio_is_flag_ec3482_idx')],
            },
        ),
    ]
