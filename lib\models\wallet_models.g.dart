// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$WalletOverviewImpl _$$WalletOverviewImplFromJson(Map<String, dynamic> json) =>
    _$WalletOverviewImpl(
      wallet: WalletInfo.fromJson(json['wallet'] as Map<String, dynamic>),
      recentTransactions: (json['recent_transactions'] as List<dynamic>?)
              ?.map(
                  (e) => WalletTransaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      pendingDeposits: (json['pending_deposits'] as num?)?.toInt() ?? 0,
      pendingWithdrawals: (json['pending_withdrawals'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$WalletOverviewImplToJson(
        _$WalletOverviewImpl instance) =>
    <String, dynamic>{
      'wallet': instance.wallet,
      'recent_transactions': instance.recentTransactions,
      'pending_deposits': instance.pendingDeposits,
      'pending_withdrawals': instance.pendingWithdrawals,
    };

_$WalletInfoImpl _$$WalletInfoImplFromJson(Map<String, dynamic> json) =>
    _$WalletInfoImpl(
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      formattedBalance: json['formatted_balance'] as String? ?? '\$0.00',
      isActive: json['is_active'] as bool? ?? true,
      isVerified: json['is_verified'] as bool? ?? false,
      dailySpent: (json['daily_spent'] as num?)?.toDouble() ?? 0.0,
      monthlySpent: (json['monthly_spent'] as num?)?.toDouble() ?? 0.0,
      dailyLimit: (json['daily_limit'] as num?)?.toDouble() ?? 100.0,
      monthlyLimit: (json['monthly_limit'] as num?)?.toDouble() ?? 1000.0,
      totalTransactions: (json['total_transactions'] as num?)?.toInt() ?? 0,
      totalCredits: (json['total_credits'] as num?)?.toInt() ?? 0,
      totalDebits: (json['total_debits'] as num?)?.toInt() ?? 0,
      totalDeposited: (json['total_deposited'] as num?)?.toDouble() ?? 0.0,
      totalSpent: (json['total_spent'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$WalletInfoImplToJson(_$WalletInfoImpl instance) =>
    <String, dynamic>{
      'balance': instance.balance,
      'formatted_balance': instance.formattedBalance,
      'is_active': instance.isActive,
      'is_verified': instance.isVerified,
      'daily_spent': instance.dailySpent,
      'monthly_spent': instance.monthlySpent,
      'daily_limit': instance.dailyLimit,
      'monthly_limit': instance.monthlyLimit,
      'total_transactions': instance.totalTransactions,
      'total_credits': instance.totalCredits,
      'total_debits': instance.totalDebits,
      'total_deposited': instance.totalDeposited,
      'total_spent': instance.totalSpent,
    };

_$WalletTransactionImpl _$$WalletTransactionImplFromJson(
        Map<String, dynamic> json) =>
    _$WalletTransactionImpl(
      id: json['id'] as String? ?? '',
      type: json['type'] as String? ?? '',
      purpose: json['purpose'] as String? ?? '',
      amount: json['amount'] as String? ?? '0.00',
      formattedAmount: json['formattedAmount'] as String? ?? '0.00',
      balanceAfter: json['balanceAfter'] as String? ?? '0.00',
      status: json['status'] as String? ?? 'pending',
      description: json['description'] as String? ?? '',
      createdAt: json['createdAt'] as String? ?? '',
      completedAt: json['completedAt'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      referenceId: json['referenceId'] as String?,
    );

Map<String, dynamic> _$$WalletTransactionImplToJson(
        _$WalletTransactionImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'purpose': instance.purpose,
      'amount': instance.amount,
      'formattedAmount': instance.formattedAmount,
      'balanceAfter': instance.balanceAfter,
      'status': instance.status,
      'description': instance.description,
      'createdAt': instance.createdAt,
      'completedAt': instance.completedAt,
      'paymentMethod': instance.paymentMethod,
      'referenceId': instance.referenceId,
    };

_$WalletDepositRequestImpl _$$WalletDepositRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$WalletDepositRequestImpl(
      depositRequestId: json['depositRequestId'] as String? ?? '',
      paymentData: json['paymentData'] as Map<String, dynamic>? ?? const {},
      message: json['message'] as String? ?? '',
    );

Map<String, dynamic> _$$WalletDepositRequestImplToJson(
        _$WalletDepositRequestImpl instance) =>
    <String, dynamic>{
      'depositRequestId': instance.depositRequestId,
      'paymentData': instance.paymentData,
      'message': instance.message,
    };

_$WalletWithdrawalRequestImpl _$$WalletWithdrawalRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$WalletWithdrawalRequestImpl(
      withdrawalRequestId: json['withdrawalRequestId'] as String? ?? '',
      message: json['message'] as String? ?? '',
    );

Map<String, dynamic> _$$WalletWithdrawalRequestImplToJson(
        _$WalletWithdrawalRequestImpl instance) =>
    <String, dynamic>{
      'withdrawalRequestId': instance.withdrawalRequestId,
      'message': instance.message,
    };

_$WalletSettingsImpl _$$WalletSettingsImplFromJson(Map<String, dynamic> json) =>
    _$WalletSettingsImpl(
      minimumDeposit: json['minimumDeposit'] as String? ?? '5.00',
      maximumDeposit: json['maximumDeposit'] as String? ?? '500.00',
      minimumWithdrawal: json['minimumWithdrawal'] as String? ?? '10.00',
      maximumWithdrawal: json['maximumWithdrawal'] as String? ?? '1000.00',
      withdrawalFeePercentage:
          json['withdrawalFeePercentage'] as String? ?? '0.00',
      withdrawalFeeFixed: json['withdrawalFeeFixed'] as String? ?? '0.00',
      depositFeePercentage: json['depositFeePercentage'] as String? ?? '0.00',
      walletsEnabled: json['walletsEnabled'] as bool? ?? true,
      depositsEnabled: json['depositsEnabled'] as bool? ?? true,
      withdrawalsEnabled: json['withdrawalsEnabled'] as bool? ?? true,
      requireVerification: json['requireVerification'] as bool? ?? false,
      dailyWithdrawalLimit: json['dailyWithdrawalLimit'] as String? ?? '100.00',
      monthlyWithdrawalLimit:
          json['monthlyWithdrawalLimit'] as String? ?? '1000.00',
    );

Map<String, dynamic> _$$WalletSettingsImplToJson(
        _$WalletSettingsImpl instance) =>
    <String, dynamic>{
      'minimumDeposit': instance.minimumDeposit,
      'maximumDeposit': instance.maximumDeposit,
      'minimumWithdrawal': instance.minimumWithdrawal,
      'maximumWithdrawal': instance.maximumWithdrawal,
      'withdrawalFeePercentage': instance.withdrawalFeePercentage,
      'withdrawalFeeFixed': instance.withdrawalFeeFixed,
      'depositFeePercentage': instance.depositFeePercentage,
      'walletsEnabled': instance.walletsEnabled,
      'depositsEnabled': instance.depositsEnabled,
      'withdrawalsEnabled': instance.withdrawalsEnabled,
      'requireVerification': instance.requireVerification,
      'dailyWithdrawalLimit': instance.dailyWithdrawalLimit,
      'monthlyWithdrawalLimit': instance.monthlyWithdrawalLimit,
    };

_$WalletSpendResultImpl _$$WalletSpendResultImplFromJson(
        Map<String, dynamic> json) =>
    _$WalletSpendResultImpl(
      transactionId: json['transactionId'] as String? ?? '',
      newBalance: json['newBalance'] as String? ?? '0.00',
      formattedBalance: json['formattedBalance'] as String? ?? '0.00',
      message: json['message'] as String? ?? '',
    );

Map<String, dynamic> _$$WalletSpendResultImplToJson(
        _$WalletSpendResultImpl instance) =>
    <String, dynamic>{
      'transactionId': instance.transactionId,
      'newBalance': instance.newBalance,
      'formattedBalance': instance.formattedBalance,
      'message': instance.message,
    };

_$WalletStateImpl _$$WalletStateImplFromJson(Map<String, dynamic> json) =>
    _$WalletStateImpl(
      overview: json['overview'] == null
          ? null
          : WalletOverview.fromJson(json['overview'] as Map<String, dynamic>),
      settings: json['settings'] == null
          ? null
          : WalletSettings.fromJson(json['settings'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
      transactions: (json['transactions'] as List<dynamic>?)
              ?.map(
                  (e) => WalletTransaction.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      hasMoreTransactions: json['hasMoreTransactions'] as bool? ?? false,
    );

Map<String, dynamic> _$$WalletStateImplToJson(_$WalletStateImpl instance) =>
    <String, dynamic>{
      'overview': instance.overview,
      'settings': instance.settings,
      'isLoading': instance.isLoading,
      'error': instance.error,
      'transactions': instance.transactions,
      'hasMoreTransactions': instance.hasMoreTransactions,
    };
