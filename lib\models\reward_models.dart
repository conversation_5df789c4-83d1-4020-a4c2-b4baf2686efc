import 'package:freezed_annotation/freezed_annotation.dart';

part 'reward_models.freezed.dart';
part 'reward_models.g.dart';

@freezed
class PayPalReward with _$PayPalReward {
  const factory PayPalReward({
    required String id,
    required String name,
    required String description,
    required double amount,
    required int pointsRequired,
    required String tier,
    required bool isActive,
    @Default(false) bool isLimitedTime,
    DateTime? expiresAt,
    @Default(0) int maxClaims,
    @Default(0) int currentClaims,
  }) = _PayPalReward;

  factory PayPalReward.fromJson(Map<String, dynamic> json) =>
      _$PayPalRewardFromJson(json);
}

@freezed
class UserPayPalReward with _$UserPayPalReward {
  const factory UserPayPalReward({
    required String id,
    required String userId,
    required String rewardId,
    required PayPalReward reward,
    required double amount,
    required String status, // 'pending', 'approved', 'completed', 'failed'
    required String paypalEmail,
    DateTime? claimedAt,
    DateTime? approvedAt,
    DateTime? completedAt,
    String? paypalTransactionId,
    String? adminNotes,
  }) = _UserPayPalReward;

  factory UserPayPalReward.fromJson(Map<String, dynamic> json) =>
      _$UserPayPalRewardFromJson(json);
}

@freezed
class UserPayPalProfile with _$UserPayPalProfile {
  const factory UserPayPalProfile({
    required String id,
    required String userId,
    required String paypalEmail,
    required bool isVerified,
    DateTime? verifiedAt,
    @Default(0.0) double totalEarnings,
    @Default(0) int totalRewardsClaimed,
    @Default(true) bool isActive,
  }) = _UserPayPalProfile;

  factory UserPayPalProfile.fromJson(Map<String, dynamic> json) =>
      _$UserPayPalProfileFromJson(json);
}

@freezed
class ReferralData with _$ReferralData {
  const factory ReferralData({
    required String id,
    required String referrerId,
    required String refereeId,
    required String referralCode,
    required String friendName,
    required int friendLevel,
    required DateTime joinedAt,
    required double earnedAmount,
    required bool wentPremium,
    @Default(false) bool reachedLevel5,
    @Default(false) bool madePurchase,
    @Default(0.0) double totalRevenue,
  }) = _ReferralData;

  factory ReferralData.fromJson(Map<String, dynamic> json) =>
      _$ReferralDataFromJson(json);
}

@freezed
class ReferralStats with _$ReferralStats {
  const factory ReferralStats({
    @Default(0) int totalReferrals,
    @Default(0.0) double totalEarned,
    @Default(0) int premiumReferrals,
    @Default(0) int activeReferrals,
    @Default(0) int level5Referrals,
    String? referralCode,
  }) = _ReferralStats;

  factory ReferralStats.fromJson(Map<String, dynamic> json) =>
      _$ReferralStatsFromJson(json);
}

@freezed
class VirtualItem with _$VirtualItem {
  const factory VirtualItem({
    required String id,
    required String name,
    required String description,
    required String category, // 'cosmetic', 'functional', 'boost'
    required double price,
    required String currency,
    required bool isActive,
    @Default(false) bool isLimitedTime,
    DateTime? expiresAt,
    @Default(0) int maxPurchases,
    Map<String, dynamic>? effects,
    String? imageUrl,
  }) = _VirtualItem;

  factory VirtualItem.fromJson(Map<String, dynamic> json) =>
      _$VirtualItemFromJson(json);
}

@freezed
class UserVirtualItem with _$UserVirtualItem {
  const factory UserVirtualItem({
    required String id,
    required String userId,
    required String itemId,
    required VirtualItem item,
    required DateTime purchasedAt,
    required bool isActive,
    DateTime? expiresAt,
    @Default(0) int usesRemaining,
  }) = _UserVirtualItem;

  factory UserVirtualItem.fromJson(Map<String, dynamic> json) =>
      _$UserVirtualItemFromJson(json);
}

@freezed
class PointBoostPackage with _$PointBoostPackage {
  const factory PointBoostPackage({
    required String id,
    required String name,
    required String description,
    required int basePoints,
    required int bonusPoints,
    required int totalPoints,
    required double price,
    required String currency,
    required bool isActive,
    @Default(false) bool isPopular,
    @Default(false) bool isLimitedTime,
    DateTime? expiresAt,
  }) = _PointBoostPackage;

  factory PointBoostPackage.fromJson(Map<String, dynamic> json) =>
      _$PointBoostPackageFromJson(json);
}

@freezed
class PremiumSubscription with _$PremiumSubscription {
  const factory PremiumSubscription({
    required String id,
    required String userId,
    required String plan, // 'monthly', 'quarterly', 'yearly'
    required String status, // 'active', 'cancelled', 'expired'
    required DateTime startDate,
    required DateTime endDate,
    required double monthlyPrice,
    required double totalPaid,
    DateTime? lastPaymentDate,
    DateTime? nextPaymentDate,
    @Default(2.0) double pointMultiplier,
    @Default(15) int dailyStreakBonus,
    @Default(-1) int voiceCommentsLimit, // -1 = unlimited
  }) = _PremiumSubscription;

  factory PremiumSubscription.fromJson(Map<String, dynamic> json) =>
      _$PremiumSubscriptionFromJson(json);
}

@freezed
class PaymentTransaction with _$PaymentTransaction {
  const factory PaymentTransaction({
    required String id,
    required String userId,
    required String transactionType, // 'incoming', 'outgoing'
    required String paymentPurpose,
    required double amount,
    required String currency,
    required String status, // 'pending', 'processing', 'completed', 'failed'
    required DateTime createdAt,
    DateTime? processedAt,
    DateTime? completedAt,
    String? paypalPaymentId,
    String? paypalPayerId,
    String? referenceId,
    String? description,
    String? errorMessage,
  }) = _PaymentTransaction;

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) =>
      _$PaymentTransactionFromJson(json);
}

@freezed
class AdStats with _$AdStats {
  const factory AdStats({
    @Default(0) int todayImpressions,
    @Default(0) int todayPoints,
    @Default(0) int dailyLimitRemaining,
    @Default(0) int totalImpressions,
    @Default(0) int totalClicks,
    @Default(0) int totalPointsEarned,
    @Default(0) int completedRewardedAds,
    @Default(0.0) double clickThroughRate,
    @Default(0) int maxDailyAdPoints,
    @Default(0) int maxAdsPerSession,
    @Default(0) int minTimeBetweenAds,
  }) = _AdStats;

  factory AdStats.fromJson(Map<String, dynamic> json) =>
      _$AdStatsFromJson(json);
}
