# 🗄️ SQLite Database Sharing Guide

## **🎯 Simple SQLite Sharing for Development**

Since you want to use SQLite, here's the easiest way to share your database across machines without Git conflicts.

## **📦 Method 1: Backup & Restore Scripts**

### **Creating a Backup**
```bash
# Create a backup of your current database
./backup_database.sh

# This creates:
# - database_backups/db_backup_YYYYMMDD_HHMMSS.sqlite3
# - database_backups/latest_backup.sqlite3
# - database_backups/data_dump_YYYYMMDD_HHMMSS.json
```

### **Restoring on Another Machine**
```bash
# Restore the latest backup
./restore_database.sh latest

# Or restore a specific backup
./restore_database.sh db_backup_20241223_143022.sqlite3
```

## **📁 Method 2: Manual File Copy**

### **On Source Machine (where you have data):**
```bash
# 1. Navigate to your project
cd trendy/trendy_web_and_api/trendy

# 2. Copy the database file
cp db.sqlite3 ~/Desktop/trendy_database_backup.sqlite3

# 3. Share this file via:
#    - USB drive
#    - Email attachment
#    - Cloud storage (Dropbox, Google Drive)
#    - Network share
```

### **On Target Machine (where you want the data):**
```bash
# 1. Navigate to your project
cd trendy/trendy_web_and_api/trendy

# 2. Stop Django server if running
# Ctrl+C to stop server

# 3. Backup current database (optional)
cp db.sqlite3 db.sqlite3.backup

# 4. Copy the new database
cp ~/Desktop/trendy_database_backup.sqlite3 db.sqlite3

# 5. Start Django server
python manage.py runserver 0.0.0.0:8000
```

## **☁️ Method 3: Cloud Storage Sync**

### **Setup (One Time)**
```bash
# Create a shared folder in your cloud storage
# Example: Dropbox/TrendyApp/database/

# Create symbolic link to cloud folder
cd trendy/trendy_web_and_api/trendy
ln -s ~/Dropbox/TrendyApp/database/db.sqlite3 db.sqlite3
```

### **Daily Usage**
- Database automatically syncs via cloud storage
- ⚠️ **Warning**: Don't run Django on multiple machines simultaneously
- Always stop Django before switching machines

## **🔄 Method 4: Git LFS (Large File Storage)**

### **Setup Git LFS**
```bash
# Install Git LFS
git lfs install

# Track SQLite files
git lfs track "*.sqlite3"
git add .gitattributes

# Add and commit database
git add trendy_web_and_api/trendy/db.sqlite3
git commit -m "Add SQLite database via LFS"
git push
```

### **On Other Machines**
```bash
# Clone with LFS
git clone <repository-url>
cd trendy

# Pull LFS files
git lfs pull
```

## **📋 Recommended Workflow**

### **For Small Teams (2-3 people):**
**Use Method 1 (Backup Scripts)**

```bash
# Person A (has latest data):
./backup_database.sh
# Share the backup file via Slack/email

# Person B (wants latest data):
# Download the backup file to database_backups/
./restore_database.sh latest
```

### **For Solo Development:**
**Use Method 2 (Manual Copy)**

```bash
# When switching machines:
# 1. Copy db.sqlite3 to USB/cloud
# 2. Copy to new machine
# 3. Continue development
```

## **⚠️ Important Notes**

### **Database Safety**
- **Always backup** before restoring
- **Stop Django server** before copying database files
- **Don't run Django simultaneously** on multiple machines with same database

### **File Locations**
```
trendy/
├── trendy_web_and_api/
│   └── trendy/
│       └── db.sqlite3          # Your main database
├── database_backups/           # Backup files
│   ├── latest_backup.sqlite3   # Latest backup
│   └── db_backup_*.sqlite3     # Timestamped backups
├── backup_database.sh          # Backup script
└── restore_database.sh         # Restore script
```

### **Git Configuration**
```gitignore
# In .gitignore
*.sqlite3
*.db

# But allow backups in specific folder (optional)
!database_backups/sample_data.sqlite3
```

## **🧪 Testing Your Setup**

### **Test Backup Script**
```bash
./backup_database.sh
# Should create files in database_backups/
```

### **Test Restore Script**
```bash
./restore_database.sh latest
# Should restore your database
```

### **Verify Data**
```bash
cd trendy_web_and_api/trendy
source venv/bin/activate
python manage.py shell -c "
from accounts.models import CustomUser
print(f'Users: {CustomUser.objects.count()}')
"
```

## **🚀 Quick Start Commands**

### **Create Backup**
```bash
./backup_database.sh
```

### **Share Database**
```bash
# Copy backup file to shared location
cp database_backups/latest_backup.sqlite3 /path/to/shared/folder/
```

### **Get Latest Database**
```bash
# Copy from shared location
cp /path/to/shared/folder/latest_backup.sqlite3 database_backups/
./restore_database.sh latest
```

## **💡 Pro Tips**

### **Regular Backups**
```bash
# Add to crontab for automatic backups
# Run backup every day at 6 PM
0 18 * * * cd /path/to/trendy && ./backup_database.sh
```

### **Database Versioning**
```bash
# Name backups with features
cp database_backups/latest_backup.sqlite3 database_backups/before_wallet_feature.sqlite3
```

### **Quick Database Reset**
```bash
# Reset to clean state
rm trendy_web_and_api/trendy/db.sqlite3
cd trendy_web_and_api/trendy
python manage.py migrate
python manage.py createsuperuser
```

## **🎯 Summary**

**For SQLite development, use the backup/restore scripts:**

1. **Create backup**: `./backup_database.sh`
2. **Share backup file** via your preferred method
3. **Restore on other machine**: `./restore_database.sh latest`
4. **Continue development** with same data

This approach gives you:
- ✅ **Full data preservation**
- ✅ **No Git conflicts**
- ✅ **Easy sharing**
- ✅ **Backup safety**
- ✅ **Simple workflow**

Your SQLite database will work perfectly across all machines! 🗄️✨
